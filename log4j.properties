log4j.rootLogger = INFO,A2,A1

#if you want to open the trace from open source,just add   #  ahead of line
log4j.logger.org = ERROR
log4j.logger.uk = ERROR

log4j.appender.A1 = org.apache.log4j.ConsoleAppender
log4j.appender.A1.layout = org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern =%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %c  - %m%n

log4j.appender.A2=org.apache.log4j.DailyRollingFileAppender
log4j.appender.A2.DatePattern='_'yyyyMMdd'.log'
#don't modify the file property
log4j.appender.A2.File=D:/ecology/ecology/ecology/log/ecology
log4j.appender.A2.layout=org.apache.log4j.PatternLayout
log4j.appender.A2.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %c  - %m%n

log4j.appender.A3=org.apache.log4j.RollingFileAppender
log4j.appender.A3.Threshold=ERROR
log4j.appender.A3.MaxFileSize=10MB
log4j.appender.A3.MaxBackupIndex=10
#don't modify the file property
log4j.appender.A3.File=D:/ecology/ecology/ecology/log/weaver.log
log4j.appender.A3.layout=org.apache.log4j.PatternLayout
log4j.appender.A3.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %c  - %m%n

log4j.logger.integration=INFO,ERROR,integration
log4j.appender.integration=org.apache.log4j.DailyRollingFileAppender
log4j.appender.integration.DatePattern='_'yyyyMMdd'.log'
log4j.appender.integration.File=D:/ecology/ecology/ecology/log/integration/integration.log
log4j.appender.integration.layout=org.apache.log4j.PatternLayout
log4j.appender.integration.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.integration.Threshold = INFO
log4j.additivity.integration=false

log4j.appender.ERROR=org.apache.log4j.DailyRollingFileAppender
log4j.appender.ERROR.DatePattern='_'yyyyMMdd'.log'
log4j.appender.ERROR.File=D:/ecology/ecology/ecology/log/error/error.log
log4j.appender.ERROR.layout=org.apache.log4j.PatternLayout
log4j.appender.ERROR.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.integration.Threshold = INFO


log4j.logger.portal=INFO,ERROR,portal
log4j.appender.portal=org.apache.log4j.DailyRollingFileAppender
log4j.appender.portal.DatePattern='_'yyyyMMdd'.log'
log4j.appender.portal.File=D:/ecology/ecology/ecology/log/portal/portal.log
log4j.appender.portal.layout=org.apache.log4j.PatternLayout
log4j.appender.portal.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.integration.Threshold = INFO
log4j.additivity.portal=false


log4j.logger.formmode=ERROR,formmode
log4j.appender.formmode=org.apache.log4j.DailyRollingFileAppender
log4j.appender.formmode.DatePattern='_'yyyyMMdd'.log'
log4j.appender.formmode.File=D:/ecology/ecology/ecology/log/formmode/formmode.log
log4j.appender.formmode.layout=org.apache.log4j.PatternLayout
log4j.appender.formmode.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.formmode.Threshold = INFO
log4j.additivity.formmode=false

log4j.logger.multilang=INFO,multilang
log4j.appender.multilang=org.apache.log4j.DailyRollingFileAppender
log4j.appender.multilang.DatePattern='_'yyyyMMdd'.log'
log4j.appender.multilang.File=D:/ecology/ecology/ecology/log/multilang/multilang.log
log4j.appender.multilang.layout=org.apache.log4j.PatternLayout
log4j.appender.multilang.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.multilang.Threshold = INFO
log4j.additivity.multilang=false

log4j.logger.hrmkq=INFO,hrmkq
log4j.appender.hrmkq=org.apache.log4j.DailyRollingFileAppender
log4j.appender.hrmkq.DatePattern='_'yyyyMMdd'.log'
log4j.appender.hrmkq.File=D:/ecology/ecology/ecology/log/hrmkq/hrmkq.log
log4j.appender.hrmkq.layout=org.apache.log4j.PatternLayout
log4j.appender.hrmkq.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.multilang.Threshold = INFO
log4j.additivity.hrmkq=false

log4j.logger.backup=INFO,ERROR,backup
log4j.appender.backup=org.apache.log4j.DailyRollingFileAppender
log4j.appender.backup.DatePattern='_'yyyyMMdd'.log'
log4j.appender.backup.File=D:/ecology/ecology/ecology/log/backup/backup.log
log4j.appender.backup.layout=org.apache.log4j.PatternLayout
log4j.appender.backup.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [Thread:%t] %m%n
#log4j.appender.backup.Threshold = INFO
log4j.additivity.backup=false