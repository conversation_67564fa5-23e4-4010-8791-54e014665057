<%@ page language="java" contentType="text/html; charset=GBK"%>
<%@ page import="weaver.cluster.*"%>
<%@ page import="sun.misc.BASE64Encoder"%>
<%@ page import="sun.misc.BASE64Decoder"%>
<%@ page import="java.io.*"%>
<%@ page import="weaver.general.*"%>
<%@ page import="org.apache.commons.configuration.*"%>
<%@ page import="org.apache.commons.configuration.reloading.*"%>
<%@ page import="java.util.logging.*"%>
<%@ page import="java.util.regex.*"%>


<%!
 class SerialKiller extends ObjectInputStream {

    private final XMLConfiguration config;
    private final FileChangedReloadingStrategy reloadStrategy;
     final Logger LOGGER = Logger.getLogger(SerialKiller.class.getName());
    Handler fileHandler;
    String logFile;
   boolean logEnabled;
    String[] blacklist;
    String[] whitelist;
    boolean profiling;    

    /**
     * SerialKiller constructor, returns instance of ObjectInputStream
     *
     * @param inputStream The original InputStream, used by your service to receive serialized objects
     * @param configFile The location of the config file (absolute path)
     * @throws java.io.IOException File I/O exception
     * @throws org.apache.commons.configuration.ConfigurationException Config exception
     */
    public SerialKiller(InputStream inputStream, String configFile) throws IOException, ConfigurationException {
        
        super(inputStream);
        configFile = GCONST.getPropertyPath() + "serialkiller.conf";
        config = new XMLConfiguration(configFile);
        reloadStrategy = new FileChangedReloadingStrategy(); 
        reloadStrategy.setRefreshDelay(config.getLong("refresh", 6000));
        config.setReloadingStrategy(reloadStrategy);
        
        blacklist = config.getStringArray("blacklist.regexp");
        whitelist = config.getStringArray("whitelist.regexp");
        profiling = config.getBoolean("mode.profiling", false);
        logEnabled = false;        
   
        if(logEnabled){
            logFile = config.getString("logging.logfile", "/tmp/serialkiller.log");
            fileHandler  = new FileHandler(logFile, true);
            LOGGER.addHandler(fileHandler);
            LOGGER.setLevel(Level.ALL);
        }
    }

    @Override
    protected Class<?> resolveClass(ObjectStreamClass serialInput) throws IOException, ClassNotFoundException {

        //Enforce SerialKiller's blacklist
        for (String blackRegExp : blacklist) {
            Pattern blackPattern = Pattern.compile(blackRegExp);
            Matcher blackMatcher = blackPattern.matcher(serialInput.getName());
            if (blackMatcher.find()) {
                if (profiling){
                    //Reporting mode 
                   // LOGGER.log(Level.FINE, "Blacklist match: ''{0}''", serialInput.getName());
                }else{
                    //Blocking mode
                   // LOGGER.log(Level.SEVERE, "Blocked by blacklist ''{0}''. Match found for ''{1}''", new Object[]{blackRegExp, serialInput.getName()});
                    throw new InvalidClassException("Class blocked by SK: '" + serialInput.getName() + "'");
                }
            }
        }

        //Enforce SerialKiller's whitelist
        boolean safeClass = false;
        for (String whiteRegExp : whitelist) {
            Pattern whitePattern = Pattern.compile(whiteRegExp);
            Matcher whiteMatcher = whitePattern.matcher(serialInput.getName());
            if (whiteMatcher.find()) {
                safeClass = true;
                
                if (profiling){
                    //Reporting mode 
                 //   LOGGER.log(Level.FINE, "Whitelist match: ''{0}''", serialInput.getName());
                }
            }
        }
        if (!safeClass && !profiling) {
            //Blocking mode
          //  LOGGER.log(Level.SEVERE, "Blocked by whitelist. No match found for ''{0}''", serialInput.getName());
            throw new InvalidClassException("Class blocked by SK: '" + serialInput.getName() + "'");
        }

        return super.resolveClass(serialInput);
    }
}


%>

<%
    String data = request.getParameter("data");

    //System.out.println(data);
	if(data ==null) {
		return;
	}
    BASE64Decoder decoder = new BASE64Decoder();
    byte[] byteData = decoder.decodeBuffer(data);

    ByteArrayInputStream bis = new ByteArrayInputStream(byteData);


    ObjectInputStream ois = new SerialKiller(bis,"");

    CacheMessage message = (CacheMessage) ois.readObject();
    
 new BaseBean().writeLog(" An cluster-----------------message received: " + message.getAction() + "->" + message.getCacheType());

    CacheManager cm = CacheManager.getInstance();
    cm.handleNotification(message);
%>
