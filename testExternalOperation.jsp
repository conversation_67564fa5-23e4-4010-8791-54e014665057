<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.io.*" %>
<%@ page import="weaver.conn.*" %>
<%@ page import="weaver.general.*" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<title>testResult</title>

<LINK href="/css/Weaver_wev8.css" type=text/css rel=STYLESHEET>
</head>
<body>
<% 
        String dataSourceId = Util.null2String(request.getParameter("dataSourceId")).trim();
		String schema = Util.null2String(request.getParameter("schema")).trim();
		String tableName = Util.null2String(request.getParameter("tableName")).trim();
		if(!"".equals(dataSourceId)){
					long checkstart = System.currentTimeMillis();
					dataSourceId = dataSourceId.trim();
					%>
					 <table>
					 <%
                    if(!"".equals(schema)&&!"".equals(tableName)){  //获取schema下对应表的字段及字段类型
						 Map returnArr = ExternalDataSourceManager.getAllColumnWithTypes(dataSourceId,schema,tableName);
						 %>
						 获取schema=<%=schema%>下<%=tableName%>表的字段及字段类型
						 <tr>
							<th>No</th>
							<th>字段名称</th>
							<th>字段类型</th>
						</tr>
						<%
						 int i=0;
						 for(Object key:returnArr.keySet()){
							 i++;
	    					 String dataTypeName = (String) returnArr.get(key);
							%>
							<tr>
								<td><%=i%></td>
								<td><%=key.toString()%></td>
								<td><%=dataTypeName%></td>
							</tr>
							<%
						 }
					}else if(!"".equals(schema)&&"".equals(tableName)){//获取schema下数据库所有的表及表类型	
					   List<Map<String, Object>> resultList =  ExternalDataSourceManager.getTablesList(dataSourceId,schema);
					   %>
						 获取schema=<%=schema%>下数据库所有的表及表类型
						 <tr>
							<th>No</th>
							<th>表名称</th>
							<th>表类型</th>
						</tr>
						<%
						int i=0;
						for(Map<String, Object> m :resultList){
							i++;
    						String table_name = (String) m.get("table_name");
    						String table_type = (String) m.get("table_type");
							%>
							<tr>
								<td><%=i%></td>
								<td><%=table_name%></td>
								<td><%=table_type%></td>
							</tr>
							<%
					    }					
					}else if("".equals(schema)&&!"".equals(tableName)){  //获取对应表的字段及字段类型
						  Map returnArr = ExternalDataSourceManager.getAllColumnWithTypes(dataSourceId,tableName);
						  %>
						 获取<%=tableName%>表的字段及字段类型
						 <tr>
							<th>No</th>
							<th>字段名称</th>
							<th>字段类型</th>
						</tr>
						<%
						 int i=0;
						  for(Object key:returnArr.keySet()){
							 i++;
	    					 String dataTypeName = (String) returnArr.get(key);
							 %>
							<tr>
								<td><%=i%></td>
								<td><%=key.toString()%></td>
								<td><%=dataTypeName%></td>
							</tr>
							<%
						  }
					
					}else if("".equals(schema)&&"".equals(tableName)){	 //获取数据库所有的表及表类型				
					   List<Map<String, Object>> resultList =  ExternalDataSourceManager.getTablesList(dataSourceId);
					   %>
						 获取schema=<%=schema%>下<%=tableName%>表的字段及字段类型
						 <tr>
							<th>No</th>
							<th>字段名称</th>
							<th>字段类型</th>
						</tr>
						<%
						int i=0;
						for(Map<String, Object> m :resultList){
							i++;
    						String table_name = (String) m.get("table_name");
    						String table_type = (String) m.get("table_type");
							%>
							<tr>
								<td><%=i%></td>
								<td><%=table_name%></td>
								<td><%=table_type%></td>
							</tr>
							<%
						}
					}
					%>
					</table>
					<%
					long checkend = System.currentTimeMillis();
					out.print("执行耗时:"); 
					out.println(checkend-checkstart);
					out.print("ms<br>");
			 }
 %>
	</body>
	</html>