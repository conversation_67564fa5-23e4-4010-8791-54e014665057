<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ page import="weaver.general.GCONST" %>
<%@ page import="org.apache.commons.io.FileUtils" %>
<%@ page import="java.io.*" %>
<%
	String path = GCONST.getRootPath();
	String cloudstore = path + "cloudstore" + File.separator + "config" + File.separator + "devStatic" + File.separator;
	String styleFile = cloudstore + "devModuleStyle.css";
	try {
		//chrome76样式修复
		String content = ".wea-tab .wea-tab-right,\n" +
				".wea-new-top-wapper .wea-new-top .ant-col-10,\n" +
				".wea-left-tree-antd.ant-tree .wea-tree-wrap>a.ant-tree-node-content-wrapper .titleNum,\n" +
				".wea-left-tree-antd.ant-tree .wea-tree-wrap .ant-tree-node-content-wrapper .titleNum>div,\n" +
				".wea-new-top-req-wapper .wea-new-top-req-title>div:last-child {\n" +
				"  top: 0;\n" +
				"}\n";
		String outpath = path + "cloudstore" + File.separator + "resource" + File.separator + "pc" + File.separator + "com" + File.separator + "v1" + File.separator + "ecCom.min.css";

		File style = new File(outpath);
		String origin = FileUtils.readFileToString(style);
		if (origin.indexOf(content) == -1) {
			writeStringToFile(style, content, true);
		}
		out.println("chrome76bug已修复，请清除浏览器缓存!");
	} catch (Exception e) {
		e.printStackTrace();
	}
	
%>
<%!
	public void writeStringToFile(File file, String conent, boolean append) {
		BufferedWriter out = null;
		OutputStreamWriter os = null;
		try {
			os = new OutputStreamWriter(new FileOutputStream(file, append), "UTF-8");
			out = new BufferedWriter(os);
			out.write(conent + "\n");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (out != null) {
					out.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			try {
				if(os != null){
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
%>