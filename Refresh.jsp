<%@ page import="weaver.general.Util" %>
<%
String loginfile = Util.null2String(request.getParameter("loginfile")) ;
String message = Util.null2String(request.getParameter("message")) ;
if(message.equals("175"))		session.invalidate();
%>


<script type="text/javascript" src="/js/init_wev8.js"></script>
<script>
	var language=7
	try{
		language = readCookie("languageidweaver");
	}catch(e){
		language=7
	}
	var sign = "<%=loginfile%>".indexOf("?")>-1?"&":"?";
	
	window.top.location.href = "<%=loginfile%>"+sign+"languageid="+language+"&message=<%=message%>"
</script>