package weaver.interfaces.schedule;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Zlbxlh {
    public Zlbxlh() {
    }

    public static String gethuidan() {
        DateFormat dateFormat_qmsj = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Date date = new Date();
        String jyrq = dateFormat_qmsj.format(date);
        return "zlbxlh" + jyrq;
    }

    public static String getCLBXPay() {
        DateFormat dateFormat_qmsj = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Date date = new Date();
        String jyrq = dateFormat_qmsj.format(date);
        return "fseqno" + jyrq;
    }
}
