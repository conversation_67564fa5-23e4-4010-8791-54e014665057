package weaver.interfaces.workflow.action.cyitce.config;

import weaver.general.GCONST;
import java.io.FileInputStream;
import java.util.Properties;

/**
 * @ClassName: 地址管理池
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-31  10:49
 * @Version: 1.0
 */
public class AddressManagementPool {
    public static String getIpAddress(String sysName)  {
        String ip = "";
        try {
            Properties prop = new Properties();
            prop.load(new FileInputStream(GCONST.getRootPath() + "WEB-INF/prop/weaver_actionCyitce.properties"));

            if("OA".equals(sysName)){
                ip = prop.getProperty("ecology.addr.oa.url");
            }else if("FILESYS".equals(sysName)){
                ip = prop.getProperty("ecology.addr.fileSystem.url");
            }else if("PAAS".equals(sysName)){
                ip = prop.getProperty("ecology.addr.pass.url");
            }else if("PAAS_countfa".equals(sysName)){
                ip = prop.getProperty("ecology.addr.pass_countfa.url");
            }

            return ip;
        }catch (Exception e){
            return "";
        }
    }

    public static String getResourceAddress(String sysName){
        String path = "";

        try {
            Properties prop = new Properties();
            prop.load(new FileInputStream(GCONST.getRootPath() + "WEB-INF/prop/weaver_actionCyitce.properties"));

            if("OA".equals(sysName)){
                path = prop.getProperty("ecology.addr.oa.resource");
            }

            return path;
        }catch (Exception e){
            return "";
        }
    }
}