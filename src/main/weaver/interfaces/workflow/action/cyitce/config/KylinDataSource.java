package weaver.interfaces.workflow.action.cyitce.config;

import weaver.general.StaticObj;
import weaver.interfaces.datasource.DataSource;

import java.sql.*;

/**
 * @Description: kylin连接配置
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/28 10:08
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/28      HONOR          v1.0.0               修改原因
 */
public class KylinDataSource {
    private Connection connection;
    private PreparedStatement ps;
    ResultSet resultSet;

    public KylinDataSource(){
    }

    public void dataSource() throws Exception {
        DataSource ds = (DataSource) StaticObj.getServiceByFullname(("datasource.ssyt_cw"), DataSource.class);
        Connection conn = ds.getConnection();

        connection = conn;
    }

    public void executeQuery(String sql) throws SQLException {
        //预编译SQL
        ps = connection.prepareStatement(sql);
        resultSet = ps.executeQuery();
    }

    public int executeUpdate(String sql) throws SQLException {
        //预编译SQL
        ps = connection.prepareStatement(sql);
        return ps.executeUpdate();
    }

    public boolean execute(String sql) throws SQLException {
        boolean result = false;
        //预编译SQL
        ps = connection.prepareStatement(sql);
        result = ps.execute();
        resultSet = ps.getResultSet();
        return result;
    }

    public String getString(int i) throws SQLException {
        return resultSet.getString(i);
    }

    public String getString(String str) throws SQLException {
        return resultSet.getString(str);
    }

    public String getInt(int i) throws SQLException {
        return resultSet.getString(i);
    }

    public String getInt(String str) throws SQLException {
        return resultSet.getString(str);
    }

    public Boolean next() throws SQLException {
        return resultSet.next();
    }

    public void close() throws SQLException {
        if(connection!=null){
            connection.close();
        }
        if(resultSet!=null){
            resultSet.close();
        }
        if(ps!=null){
            ps.close();
        }
    }
}
