package weaver.interfaces.workflow.action.cyitce.config;

/**
 * @ClassName: BuyContract
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-28  14:37
 * @Version: 1.0
 */
public class BuyContract {
    //服务端
    public static String outUrl = AddressManagementPool.getResourceAddress("OA")+"/interfaces/workflow/action/resources";
//    本地
//    public static String outUrl = "D:/Project/IDEA/weaver/ecology/pubaction/src/main/weaver/interfaces/workflow/action/resources";

    //OA文件上传接口
//    public static String outApiUrl = "https://oa.cyitce.com/api/doc/upload/uploadFile2Doc";
    public static String outApiUrl = AddressManagementPool.getIpAddress("OA")+"/doc/upload/uploadFile2Doc";
}