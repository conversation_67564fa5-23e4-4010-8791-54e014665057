package weaver.interfaces.workflow.action.cyitce.util;

import com.engine.core.exception.ECException;
import com.spire.doc.*;
import com.spire.doc.documents.HorizontalAlignment;
import com.spire.doc.documents.Paragraph;
import com.spire.doc.documents.ParagraphStyle;
import com.spire.doc.documents.TextWrappingStyle;
import com.spire.doc.fields.DocPicture;
import org.apache.commons.imaging.ImageInfo;
import org.apache.commons.imaging.Imaging;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;


import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.List;
import java.util.UUID;

/**
 * @ClassName: spireDoc
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-27  18:01
 * @Version: 1.0
 */
public class WordEditUtils {
    /**
     * @description:文档合并
     * @author: lijianpan
     * @date: 2023/3/1
     * @param: [file_path 主文档, out_path 需要合并的文档, merge_path 合并后的文档]
     * @return: void
     **/
    public static String MergeWord(String file_path,String out_path,String merge_path) throws IOException {
        //创建两个 Document 类的对象顶分别载入 Word 文档
        Document document1 = new Document(file_path);
        Document document2 = new Document(out_path);
        //在第二个文档中循环获取所有节
        for (Object sectionObj : (Iterable) document2.getSections()) {
            Section sec=(Section)sectionObj;
            //在所有节中循环获取所有子对象
            for (Object docObj :(Iterable ) sec.getBody().getChildObjects()) {
                DocumentObject obj=(DocumentObject)docObj;

                //获取第一个文档的最后一节
                Section lastSection = document1.getLastSection();

                //将所有子对象添加到第一个文档的最后一节中
                Body body = lastSection.getBody();
                body.getChildObjects().add(obj.deepClone());
            }
        }
        //保存结果文档
        document1.saveToFile(merge_path, FileFormat.Docx_2010);
        RemoveTag(merge_path,merge_path);
        return merge_path;
    }

    /**
     * @description:多文档合并
     * @author: lijianpan
     * @date: 2023/3/1
     * @param: [merge_path 合并后的文档, args 需要合并的文档]
     * @return: void
     **/
    public static String MergWordList(String merge_path, List<String> args) throws IOException {
        if(args.size()==0){
            return "";
        }else if(args.size()==1){
            if(args.get(0)!=null&&!"".equals(args.get(0))){
                Document doc1 = new Document(args.get(0));
                doc1.saveToFile(merge_path,FileFormat.Docx_2010);
                RemoveTag(merge_path,merge_path);
            }
        }else {
            String indexFile = args.get(0);
            for (int i=1;i<args.size();++i){
                if(args.get(i)!=null&&!"".equals(args.get(i))){
                    Document doc1 = new Document(indexFile);
                    doc1.insertTextFromFile(args.get(i), FileFormat.Docx_2010);
                    doc1.saveToFile(merge_path,FileFormat.Docx_2010);
                    RemoveTag(merge_path,merge_path);
                }
                indexFile = merge_path;
            }
        }

        return merge_path;
    }

    /**
     * @description:创建文档并插入图片
     * @author: lijianpan
     * @date: 2023/3/2
     * @param: [indexParagraphText 标题, ima_path 图片路径, out_path 输出路径]
     * @return: void
     **/
    public static String createDocxMergImage(String indexParagraphText,String ima_path,String out_path) throws IOException {
        //创建Word文档
        Document document = new Document();
        //添加一个section
        Section section = document.addSection();

        //添加3个段落至section
        Paragraph para1 = section.addParagraph();
        para1.appendText(indexParagraphText+":");
        Paragraph para2 = section.addParagraph();
        Paragraph para3 = section.addParagraph();

        //创建 DocPicture 类的对象
        DocPicture picture = new DocPicture(document);
        //从磁盘加载图片
        picture.loadImage(ima_path);

        //设置图片大小
        setWidthAndHeight(picture,ima_path);
        //将图片插入到第三段
        document.getSections().get(0).getParagraphs().get(2).getChildObjects().insert(0,picture);
        //将图片设置为嵌入式
        picture.setTextWrappingStyle( TextWrappingStyle.Inline);

        //设置段落格式
        ParagraphStyle style1 = new ParagraphStyle(document);
        style1.setName("titleStyle");
        style1.getCharacterFormat().setBold(true);
        style1.getCharacterFormat().setTextColor(Color.BLACK);
        style1.getCharacterFormat().setFontName("宋体");
        style1.getCharacterFormat().setFontSize(12f);
        document.getStyles().add(style1);
        para1.applyStyle("titleStyle");

        //设置第一个段落的对齐方式
        para1.getFormat().setHorizontalAlignment(HorizontalAlignment.Left);
        //设置第三个段落的对齐方式
        para3.getFormat().setHorizontalAlignment(HorizontalAlignment.Center);

        //保存文档
        document.saveToFile(out_path, FileFormat.Docx);

        RemoveTag(out_path,out_path);

        return out_path;
    }

    /**
     * @description:创建文档并插入多图片
     * @author: lijianpan
     * @date: 2023/3/2
     * @param: [indexParagraphText 标题, ima_path 图片路径数组, out_path 输出路径]
     * @return: void
     **/
    public static String createDocxMergImage(String indexParagraphText,List<String> ima_path,String out_path) throws IOException {
        //创建Word文档
        Document document = new Document();
        //添加一个section
        Section section = document.addSection();

        //添加3个段落至section
        Paragraph para1 = section.addParagraph();
        para1.appendText(indexParagraphText+":");

        int row = 2;

        for(String img:ima_path){
            section.addParagraph();
            Paragraph para3 = section.addParagraph();
            //创建 DocPicture 类的对象
            DocPicture picture = new DocPicture(document);
            //从磁盘加载图片
            picture.loadImage(img);
            //设置图片大小
            setWidthAndHeight(picture,img);
            //将图片插入到第三段
            document.getSections().get(0).getParagraphs().get(row).getChildObjects().insert(0,picture);
            //将图片设置为嵌入式
            picture.setTextWrappingStyle( TextWrappingStyle.Inline);
            //设置第三个段落的对齐方式
            para3.getFormat().setHorizontalAlignment(HorizontalAlignment.Center);
            //段落
            row=row+2;
        }

        //设置段落格式
        ParagraphStyle style1 = new ParagraphStyle(document);
        style1.setName("titleStyle");
        style1.getCharacterFormat().setBold(true);
        style1.getCharacterFormat().setTextColor(Color.BLACK);
        style1.getCharacterFormat().setFontName("宋体");
        style1.getCharacterFormat().setFontSize(12f);
        document.getStyles().add(style1);
        para1.applyStyle("titleStyle");

        //设置第一个段落的对齐方式
        para1.getFormat().setHorizontalAlignment(HorizontalAlignment.Left);

        //保存文档
        document.saveToFile(out_path, FileFormat.Docx);

        RemoveTag(out_path,out_path);

        return out_path;
    }

    /**
     * @description:删除文档合同/文档创建第一行警告语
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [file_path, out_path]
     * @return: void
     **/
    public static void RemoveTag(String file_path,String out_path) throws IOException {
        //poi默认最小压缩比率为0.01，低于此压缩率会被检测为“压缩炸弹”，带来安全风险，这里是调整文件压缩率限制
        ZipSecureFile.setMinInflateRatio(0.001);

        InputStream is = new FileInputStream(file_path);
        XWPFDocument document = new XWPFDocument(is);
        //以上Spire.Doc 生成的文件会自带警告信息，这里来删除Spire.Doc 的警告
        document.removeBodyElement(0);
        //输出word内容文件流，新输出路径位置
        OutputStream os=new FileOutputStream(out_path);
        try {
            document.write(os);
            System.out.println("生成docx文档成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            is.close();
            os.close();
            document.close();
        }
    }

    public static void setWidthAndHeight(DocPicture picture,String img_path) throws IOException {
        //设置图片宽
        int width_init = 480;
        //设置图片长度最大值
        int height_init = 700;

        int height = 0;

        //图片对象
//        BufferedImage bufferedImage = ImageIO.read(new FileInputStream(img_path));
        try {
            final ImageInfo bufferedImage = Imaging.getImageInfo(new File(img_path));
            float w = bufferedImage.getWidth();
            float h = bufferedImage.getHeight();
            height = Math.round(h/w*width_init)>height_init?height_init:Math.round(h/w*width_init);

            picture.setWidth(width_init);
            picture.setHeight(height);
        }catch (Exception e){
            e.printStackTrace();
            new ECException("WordEditUtils.setWidthAndHeight() error");
        }
    }

    /**
     * @description:pdf转word
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [indexParagraphText, form_path, out_path]
     * @return: java.lang.String
     **/
//    public static String pdfToWord(String form_path,String out_path) throws IOException {
//        //加载PDF
//        PdfDocument pdf = new PdfDocument();
//        pdf.loadFromFile(form_path);
//        //保存为Word格式
//        pdf.saveToFile(out_path, FileFormat.DOCX);
//        //删除所有警告信息
//        removeAllTagByString(out_path);
//        return out_path;
//    }

    public static String wordFirstParagraphAddContent(String indexParagraphText,String path) throws Exception {
        // 创建新的Word文档对象
        XWPFDocument document = new XWPFDocument();

        // 创建一个新的段落对象
        XWPFParagraph p = document.createParagraph();

        //设置段落对其方式
        p.setAlignment(ParagraphAlignment.LEFT);// 设置段落的对齐方式

        // 设置段落格式
        XWPFRun run = p.createRun();
        run.setText(indexParagraphText+":");// 在段落中写入内容
        run.setBold(true);
        run.setColor("FF000000");
        run.setFontFamily("宋体");
        run.setFontSize(12);

        String dex = path.substring(path.lastIndexOf("."));
        if(dex==null&&"".equals(dex)){
            throw new Exception("文档路径出错");
        }
        String name = path.substring(0,path.lastIndexOf("."));
        String path1 = name+ UUID.randomUUID()+dex;

        // 将文档保存到文件中
        FileOutputStream out = new FileOutputStream(path1);
        document.write(out);
        out.close();
        MergeWord(path1,path,path);
        return path;
    }

    public static String removeAllTagByString(String file_path) throws IOException {
        // 读取Word文档
        XWPFDocument document = new XWPFDocument(new FileInputStream(file_path));
        // 获取文档中的所有段落
        List<XWPFParagraph> paras = document.getParagraphs();
        //需要匹配段落
        XWPFParagraph prePara = paras.get(0);
        for (int i=1;i<document.getParagraphs().size();++i){
            XWPFParagraph para = paras.get(i);
            if (para.getCTP().getPPr().getSectPr()==prePara.getCTP().getPPr().getSectPr()) {
                // 匹配到了需要删除的段落，删除该段落
                document.removeBodyElement(i);
            }
        }
        //删除第一页第一段落
        document.removeBodyElement(0);
        document.write(new FileOutputStream(file_path));
        document.close();
        return file_path;
    }

    /**
     * @description:创建文档并插入多图片
     * @author: lijianpan
     * @date: 2023/3/2
     * @param: [ima_path 图片路径数组, out_path 输出路径]
     * @return: void
     **/
    public static String createDocxMergImage(List<String> ima_path,String out_path) throws IOException {
        //创建Word文档
        Document document = new Document();
        //添加一个section
        Section section = document.addSection();
        //添加段落至section
        int row = 1;

        for(String img:ima_path){
            section.addParagraph();
            Paragraph para3 = section.addParagraph();
            //创建 DocPicture 类的对象
            DocPicture picture = new DocPicture(document);
            //从磁盘加载图片
            picture.loadImage(img);
            //设置图片大小
            setWidthAndHeight(picture,img);
            //将图片插入到第三段
            document.getSections().get(0).getParagraphs().get(row).getChildObjects().insert(0,picture);
            //将图片设置为嵌入式
            picture.setTextWrappingStyle( TextWrappingStyle.Inline);
            //设置第三个段落的对齐方式
            para3.getFormat().setHorizontalAlignment(HorizontalAlignment.Center);
            //段落
            row=row+2;
        }

        //保存文档
        document.saveToFile(out_path, FileFormat.Docx);

        RemoveTag(out_path,out_path);

        return out_path;
    }
}
