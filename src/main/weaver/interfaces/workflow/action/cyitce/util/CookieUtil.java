package weaver.interfaces.workflow.action.cyitce.util;

import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;


public class CookieUtil {

    private static String url = AddressManagementPool.getIpAddress("OA")+"/hrm/login/checkLogin";

//    private static String url = "https://oa.cyitce.com/api/hrm/login/checkLogin";//正式服

    /**
     *
     * @description 获取cookie
     * @param username 登录名
     * @param password 登录密码
     * @return
     */
    public static String getCookies(String username, String password) {
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            client = HttpClients.createDefault();
            post = new HttpPost();
            post.addHeader("Content-Type", "application/json");
            builder = new URIBuilder(url);
            builder.addParameter("loginid",username);
            builder.addParameter("userpassword",password);
            post = new HttpPost(builder.build());
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }
            headers = response.getHeaders("Set-Cookie");
            for (int i = 0;i<headers.length;++i){
                if("ecology_JSessionId".equals(headers[i].getElements()[0].getName())){
                    cookieStr = headers[i].getElements()[0].getValue();
                }
            }
            return cookieStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
