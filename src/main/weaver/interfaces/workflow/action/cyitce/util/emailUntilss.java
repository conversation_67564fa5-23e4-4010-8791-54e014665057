package weaver.interfaces.workflow.action.cyitce.util;

import javax.mail.*;
import javax.mail.internet.*;
import java.io.UnsupportedEncodingException;
import java.util.Properties;

/**
 * @ClassName: emailUntils
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-03  16:41
 * @Version: 1.0
 */
public class emailUntilss {
    public static Session createSession(){
        //SMTP服务器地址
        String smtp = "smtp.exmail.qq.com";
        //账号,密码
        String username = "<EMAIL>";
        String password = "Geek81399708";

        //SMTP服务器的连接信息
        Properties props = new Properties();
        props.put("mail.smtp.host",smtp);//SMTP主机名
        props.put("mail.smtp.port",465);//主机端口号
        props.put("mail.smtp.auth","true");//是否需要用户认证
//        props.put("mail.smtp.starttls.enable","true");//启用TLS加密

        //创建Session
        //参数1：SMTP服务的连接信息
        //参数2：用户认证对象（Anthenticator接口的匿名实现类）
        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username,password);
            }
        });

        //开启调试模式
        return session;
    }

//    public static void main(String[] args) throws MessagingException, UnsupportedEncodingException {
//        Session session = createSession();
//
//        MimeMessage msg = new MimeMessage(session);
//        msg.setFrom(new InternetAddress("<EMAIL>","CYIT"));
//        msg.setRecipient(Message.RecipientType.TO,new InternetAddress("<EMAIL>"));
//        msg.setSubject("数藤平台流程信息","utf-8");
//
//        BodyPart textpart = new MimeBodyPart();
//        StringBuilder buf = new StringBuilder();
//        buf.append("<h1>客户满意度数据录入流程-李建潘-2022</h1>"+
//                "<p>流程编号：RWS-15456464</p>\n" +
//                "<p>-------------内容----------------</p>\n" +
//                "<p>考核评价要点:幺点一</p>\n" +
//                "<p>单项总分值:100</p>\n" +
//                "<p>实际考核得分:50</p>\n" +
//                "<p>单项得分占比:50%</p>");
//        textpart.setContent(buf.toString(),"text/html;charset=utf-8");
//
//        Multipart multipart = new MimeMultipart();
//        multipart.addBodyPart(textpart);
//
//        msg.setContent(multipart);
//
//        Transport.send(msg);
//    }
}