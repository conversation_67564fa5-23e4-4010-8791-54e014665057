package weaver.interfaces.workflow.action.cyitce.util;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: MathUtils
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-09-15  15:40
 * @Version: 1.0
 */
public class MathUtils {
    /**
     * @description:将数组元素按顺序分成指定数量的组并返回所有组
     * @author: lijianpan
     * @date: 2023/9/15
     * @param:
     * @return:
     **/
    public static List<List<String>> divideNumbersIntoGroups(List<String> paths, int groups) {
        List<List<String>> numberGroups = new ArrayList<>();

        if (groups <= 0) {
            return numberGroups; // 无效的组数
        }

        int pathsLen = paths.size();
        int groupElementNum = pathsLen/groups;

        if(groupElementNum==0){
            return numberGroups;
        }

        // 初始化每个组
        for (int i = 0; i < groups; i++) {
            numberGroups.add(new ArrayList<>());
        }

        // 将数字分配到每个组中
        int pathsLenNext;
        for (int i = groups; i > 0; --i){
            groupElementNum = pathsLen/i;
            pathsLenNext = pathsLen-groupElementNum;

            for (int j=pathsLenNext;j<pathsLen;j++){
                numberGroups.get(i-1).add(paths.get(j));
            }

            pathsLen = pathsLenNext;
        }

        return numberGroups;
    }
}