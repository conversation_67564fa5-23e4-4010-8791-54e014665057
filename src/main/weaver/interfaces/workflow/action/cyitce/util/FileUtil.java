package weaver.interfaces.workflow.action.cyitce.util;

import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import com.weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.docs.docs.util.DesUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.cyitce.config.BuyContract;
import weaver.interfaces.workflow.action.cyitce.service.HttpRequestManage;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class FileUtil extends BaseBean {

    public static String FileSuffix = ".jpg";

    /**
     * @description:获取OA加密文档
     * @author: lijianpan
     * @date: 2023/3/2
     * @param: [docid 文档id, out_path 输出路径]
     * @return: void
     **/
    public static String GetFileByDocid(String docid,String out_path) throws Exception {
        if("".equals(docid)||docid==null){
            throw new Exception("文档id为空");
        }
        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        String fileSql = "";

        fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                + docid + " order by imagefileid desc";
        rs.execute(fileSql);
        if (rs.next()) {
            filename = Util.null2String(rs.getString("imagefilename"));
            filepath = Util.null2String(rs.getString("filerealpath"));
            return UnZip(filepath,filename,out_path);
        }

        return "";
    }

    /**
     * @description:获取OA加密文档
     * @author: lijianpan
     * @date: 2023/3/2
     * @param: [docid 文档id, out_path 输出路径, fileName 文档名称]
     * @return: void
     **/
    public static String GetFileByDocid(String docid,String out_path,String fileName) throws Exception {
        if("".equals(docid)||docid==null){
            throw new Exception("文档id为空");
        }
        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        String fileSql = "";
        String dex;

        fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                + docid + " order by imagefileid desc";
        rs.execute(fileSql);
        if (rs.next()) {
            filename = Util.null2String(rs.getString("imagefilename"));
            filepath = Util.null2String(rs.getString("filerealpath"));
            dex = filename.substring(filename.lastIndexOf("."));


            return UnZip(filepath,fileName+dex,out_path);
        }

        return "";
    }

    /**
     *
     * 文件转二进制
     * @param filename 文件名
     * @param filepath 文件路径
     * @return
     */
    public static byte[] FileToBinary(String filename,String filepath) {
        byte[] fileByte = null;
        try {
            String FileAllPath =  filepath+"/"+filename;

            File file = new File(FileAllPath);   // 读取到文件
            FileInputStream fis = new FileInputStream(file);  // 把文件读取到输入流中
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int len = -1;
            while ((len = fis.read(b)) != -1) {
                bos.write(b, 0, len);   // 输出到这个字节数组输出流。
            }
            fileByte = bos.toByteArray();   // 该输出流的当前内容，作为字节数组。
            return fileByte;  // 返回二进制文件流
        } catch (Exception e) {
            return fileByte;
        }
    }

    /**
     * @Description:  解压文件到新目录
     * @param path 文件路径
     * @param realname 解压后文件名称
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/30 11:03
     */
    private static String UnZip(String path, String realname,String out_path) {
        int count = -1;
        int index = -1;

        if(!"/".equals(out_path.substring(out_path.length()-1))){
            out_path = out_path+"/";
        }

        BufferedOutputStream bos = null;   // 用于接收到文件的输出流
        ZipEntry entry = null;          // zip压缩实例
        FileInputStream fis = null;
        ZipInputStream zis = null;

        File f = null;
        FileOutputStream fos = null;
        String temp = "";
        String filePath = "";

        try {

            fis = new FileInputStream(path);   //  读取文件流
            zis = new ZipInputStream(new BufferedInputStream(fis));  // 该类实现了以ZIP文件格式读取文件的输入流过滤器。 包括对压缩和未压缩条目的支持(读取到缓冲流)。

            while ((entry = zis.getNextEntry()) != null) {   // 文件实例是否为空
                byte[] data = new byte[2048];
//                temp = entry.getName();
//                index = temp.lastIndexOf("."); // 文件的后缀名，得到文件类型
//                if (index > -1){
//                    realname = realname+temp.substring(index);  // 拼接后缀名，得到文件的全名
//                }
                filePath = out_path + realname;// 获取到文件的全路径（全新的保存在系统）
                f = new File(filePath);
                f.createNewFile();  // 抽象路径名命名的新的空文件
                fos = new FileOutputStream(f);   // 文件输出流
                bos = new BufferedOutputStream(fos, 2048);    //把文件的输出流写入到   文件的缓存输出流
				/*从当前ZIP条目读取到字节数组,文件的大小
				 * b - 读取数据的缓冲区
				off - 目的地阵列中的起始偏移量 b
				len - 读取的最大字节数 */
                while ((count = zis.read(data, 0, 2048)) != -1) {
                    bos.write(data, 0, count);  // 把文件的   以缓存输出流的形式写出去
                }
            }

            return filePath;
        } catch (Exception e) {
            return "";
        }finally {
            try {
                bos.flush();   // 刷新缓冲输出流。
                bos.close();  // 关闭缓存输出流。
                zis.close();  // 关闭此输入流并释放与流相关联的任何系统资源。
            }catch (Exception e){

            }
        }
    }

    /**
     * @description:OA文件信息
     * @author: lijianpan
     * @date: 2023/5/6
     * @param: [docid]
     * @return: Map<String,Object>
     **/
    public static Map<String,String> fileInfo(String docid) throws Exception {
        if("".equals(docid)||docid==null){
            throw new Exception("文档id为空");
        }
        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        String fileSql = "";
        Map<String,String> map = new HashMap<>();

        FileInputStream fis = null;
        ZipInputStream zis = null;
        ZipEntry entry = null;
        ByteArrayOutputStream bos = null;

        try {
            fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                    + docid + " order by imagefileid desc";
            rs.execute(fileSql);
            if (rs.next()) {
                filename = Util.null2String(rs.getString("imagefilename"));
                filepath = Util.null2String(rs.getString("filerealpath"));
//                filename = "12-18-1.jpg";
//                filepath = "C:\\Users\\<USER>\\Desktop\\fsdownload\\7b961e66-0978-4cea-a52f-3956adea5594.zip";

                map.put("filename",filename);
                map.put("type",filename.substring(filename.lastIndexOf(".")));

                fis = new FileInputStream(filepath);   //  读取文件流
                zis = new ZipInputStream(new BufferedInputStream(fis));  // 该类实现了以ZIP文件格式读取文件的输入流过滤器。 包括对压缩和未压缩条目的支持(读取到缓冲流)。
                bos = new ByteArrayOutputStream();

                while ((entry = zis.getNextEntry()) != null) {   // 文件实例是否为空
                    byte[] data = new byte[2048];
                    int len;

                    while ((len=zis.read(data))!=-1){
                        bos.write(data,0,len);
                    }

//                    IOUtils.copy(zis,bos);
                }
                map.put("bytes",new String(bos.toByteArray(), StandardCharsets.UTF_8));
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            bos.close();
            fis.close();
            zis.close();
        }
        return map;
    }

    public static String ToContenType(String dex){
        if("".equals(dex)&&dex==null){
            return "";
        }

        String contenType = "";

        if (".txt".equals(dex)){
            contenType = "text/plain";
        }else if(".html".equals(dex)){
            contenType = "text/html";
        }else if(".js".equals(dex)){
            contenType = "text/javascript";
        }else if(".xml".equals(dex)){
            contenType = "application/xml";
        }else if(".json".equals(dex)){
            contenType = "application/json";
        }else if(".pdf".equals(dex)){
            contenType = "application/pdf";
        }else if(".docx".equals(dex)){
            contenType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        }else if(".doc".equals(dex)){
            contenType = "application/vnd.ms-word";
        }else if(".xlsx".equals(dex)){
            contenType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        }else if(".xls".equals(dex)){
            contenType = "application/vnd.ms-excel";
        }else if(".zip".equals(dex)){
            contenType = "application/zip";
        }else if(".ppt".equals(dex)){
            contenType = "application/vnd.ms-powerpoint";
        }else if(".jpeg".equals(dex)){
            contenType = "image/jpeg";
        }else if(".jpg".equals(dex)){
            contenType = "image/jpeg";
        }else if(".png".equals(dex)){
            contenType = "image/png";
        }else if(".gif".equals(dex)){
            contenType = "image/gif";
        }

        return contenType;
    }

    /**
     * @description:上传文件（OA）
     * @author: lijianpan
     * @date: 2023/8/11
     * @param:
     * @return:
     **/
    public static String uploadToOA(String fileName,String filePath,String requestid) {
        //上传附件接口参数设置
        HttpRequestManage http = new HttpRequestManage();
        String wordId = http.raise(fileName, filePath, BuyContract.outApiUrl, uploadEntity(requestid));

        return wordId;
    }
    /**
     * @description:文件上传（OA）：流程上传文件所需参数
     * @author: lijianpan
     * @date: 2023/8/11
     * @param: [formid, workflowid, creatorid]
     * @return: com.alibaba.fastjson.JSONObject
     **/
    public static String uploadEntity(String requestid){
        JSONObject entity_json = new JSONObject();
        entity_json.put("category", WorkflowManage.getDocCategory(requestid));//存放目录
        entity_json.put("isFirstUploadFile","9");//附件状态
        entity_json.put("workflowid", WorkflowManage.getWorkflowid(requestid));//流程workflowid
        entity_json.put("listType", "list");
        entity_json.put("f_weaver_belongto_userid", WorkflowManage.getRequestCreater(requestid));//附件归属人
        entity_json.put("f_weaver_belongto_usertype", "0");
        return entity_json.toString();
    }
}
