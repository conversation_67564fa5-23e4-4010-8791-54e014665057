package weaver.interfaces.workflow.action.cyitce.util;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: HttpUtil
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-12  13:48
 * @Version: 1.0
 */
public class HttpUtil {
    private static BaseBean log = new BaseBean();

    public static ReturnData httpPost(String url, Map<String,Object> body,Map<String,Object> headers){
        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response = null;

        try {
            client = HttpClients.createDefault();
            post = new HttpPost(url);
            post.setHeader("Content-Type","application/json");
            for (Map.Entry<String, Object> entry:headers.entrySet()){
                post.setHeader(entry.getKey(),entry.getValue().toString());
            }
            post.setEntity(new StringEntity(JSONObject.toJSONString(body), Consts.UTF_8));
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode()!= HttpStatus.SC_OK){
                int code = response.getStatusLine().getStatusCode();
                return ReturnData.error("请求失败,http:"+code);
            }

            HttpEntity entity = response.getEntity();

            if (response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                return ReturnData.error("请求失败",String.valueOf(response.getStatusLine().getStatusCode()));
            }
            String responseBody = "";
            if (entity != null) {
                responseBody = EntityUtils.toString(entity, Consts.UTF_8);
                // 处理响应内容
            }

            return ReturnData.ok(responseBody,String.valueOf(response.getStatusLine().getStatusCode()));
        }catch (Exception e){
            log.writeLog(e.getMessage());
            e.printStackTrace();
            return ReturnData.error(e.getMessage());
        }
    }

    public static ReturnData httpGet(String url,Map<String,Object> headers){
        HttpClient client = null;
        HttpGet get = null;
        HttpResponse response = null;

        try {
            client = HttpClients.createDefault();
            get = new HttpGet(url);
            for (Map.Entry<String, Object> entry:headers.entrySet()){
                get.setHeader(entry.getKey(),entry.getValue().toString());
            }
            response = client.execute(get);

            if(response.getStatusLine().getStatusCode()!= HttpStatus.SC_OK){
                int code = response.getStatusLine().getStatusCode();
                return ReturnData.error("请求失败,http:"+code);
            }

            HttpEntity entity = response.getEntity();

            if (response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                return ReturnData.error("请求失败",String.valueOf(response.getStatusLine().getStatusCode()));
            }
            String responseBody = "";
            if (entity != null) {
                responseBody = EntityUtils.toString(entity, Consts.UTF_8);
                // 处理响应内容
            }

            return ReturnData.ok(responseBody,String.valueOf(response.getStatusLine().getStatusCode()));
        }catch (Exception e){
            log.writeLog(e.getMessage());
            e.printStackTrace();
            return ReturnData.error(e.getMessage());
        }
    }

    public static ReturnData httpGet(String url){
        return httpGet(url,new HashMap<>());
    }
}