package weaver.interfaces.workflow.action.cyitce.util;


import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/13 14:11
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/13      HONOR          v1.0.0               修改原因
 */
public class CommonUtil {

    private final static String ZERO_8 = "0.00000000";
    private final static String ZERO_E_8 = "0E-8";
    private final static String ZERO = "0.00";
    private final static Integer AGE = 18;


    /**
     * @Description: 整数转百分数
     * @param val
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/13 14:45
     */
    public static String IntToPercentage(String val){
        if("".equals(val)||val==null){
            return "";
        }

        if(val.indexOf("%")!=-1){
            return val+"%";
        }else {
            return val;
        }

    }

    /**
     * @Description: 替换特殊字符为空格
     * @param val
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/13 14:48
     */
    public static String ReplaceSpeciaiToBlank(String val){
        if("".equals(val)||val==null){
            return "";
        }

        return val.replaceAll("<br>"," ").replaceAll("&nbsp;"," ");
    }

    /**
     * @Description: 删除文件夹及子文件夹和文件
     * @param file
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/13 14:48
     */
    public static Boolean deleteFile(File file){
        if(file == null||file.exists()){
            return false;
        }

        File[] files = file.listFiles();

        for (File f:files){
            if(f.isDirectory()){
                deleteFile(f);
            }else {
                f.delete();
            }
        }

        file.delete();
        return true;
    }

    public static void setPropertyValue(Object source) throws IllegalAccessException{

        //断言 如果source为空报异常
        assert source != null;

        //返回Class中所有的字段，包括私有字段
        Field[] fields = source.getClass().getDeclaredFields();

        for (Field field : fields){
            //让我们在用反射时可以访问私有变量,否则会报错
            field.setAccessible(true);
            /**
             * Field的getModifiers()方法返回int类型值表示该字段的修饰符.其中，
             *              该修饰符是java.lang.reflect.Modifier的静态属性.
             * 对应表如下：
             *      STATIC: 8
             *      PUBLIC: 1
             *      PRIVATE: 2
             *      PROTECTED: 4
             */
            //如果是带有“static”的变量，直接跳过
            if((field.getModifiers() & Modifier.STATIC) > 0){
                continue;
            }

            Class<?> type = field.getType();

            Object value = field.get(source);

            //如果是String类型
            if(type.equals(String.class)){
                if(value != null){
                    field.set(source, ReplaceSpeciaiToBlank((String) value));
                }
            }
        }
    }

    /**
     * @Description: sql语句参数拼接
     * @param sql
     * @param strs 参数
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/11/8 14:48
     */
    public static String getSqlByWhere(String sql,Object[] strs){
        StringBuilder strB = new StringBuilder(sql);
        for (Object s:strs){
            strB.replace(strB.indexOf("?"),strB.indexOf("?")+1,"'"+s+"'");
        }
        return strB.toString();
    }

    public static Float parseFloatAndInit(String str){
        if("".equals(str)||str==null){
            return 0f;
        }

        return Float.parseFloat(str);
    }

    public static String nullToStr(String str,String replace){
        if(str==null||"".equals(str)){
            return replace;
        }
        return str;
    }

    public static boolean isNull(String str){
        if(str==null||"".equals(str)){
            return true;
        }
        return false;
    }

    /**
     * @description:小数格式化（删除小数点右边的无效零）
     * @author: lijianpan
     * @date: 2023/8/9
     * @param: [str]
     * @return: java.lang.String
     **/
    public static String saveSmallNum(String str){
        String dexStr = "";
        String num = str;
        if(str.lastIndexOf(".")!=-1){
            num = str.substring(0,str.lastIndexOf("."));
            dexStr = str.substring(str.lastIndexOf(".")+1);
            int len = dexStr.length()-1;

            while (len>=0&&"0".equals(dexStr.substring(len,len+1))){
                --len;
            }

            if(len>=0){
                dexStr = "."+dexStr.substring(0,len+1);
            }else {
                dexStr = "";
            }
        }

        return num+dexStr;
    }
}
