package weaver.interfaces.workflow.action.cyitce.util.freemarker;

import freemarker.template.Configuration;
import freemarker.template.Template;
import weaver.interfaces.workflow.action.cyitce.config.BuyContract;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/15 12:25
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/15      HONOR          v1.0.0               修改原因
 */
public class FreeMarkUtils {

    public static Configuration getConfiguration(String touch) throws IOException {
        //创建配置实例
        Configuration configuration = new Configuration();
        //设置编码
        configuration.setDefaultEncoding("utf-8");
//        configuration.setClassForTemplateLoading(FreeMarkUtils.class, "/weaver/interfaces/workflow/action/resources/template"+touch);//换成自己对应的目录
        configuration.setDirectoryForTemplateLoading(new File(BuyContract.outUrl+"/template"+touch));
        return configuration;
    }

    /**
     * 获取模板字符串输入流
     * @param object  实体类
     * @param templateName  模板名称
     * @param touchUrl 模板所在路径
     * @return
     */
    public static ByteArrayInputStream getFreemarkerContentInputStream(Object object, String templateName,String touchUrl) {
        ByteArrayInputStream in = null;
        try {
            //获取模板
            Template template = getConfiguration(touchUrl).getTemplate(templateName);
            StringWriter swriter = new StringWriter();
            //生成文件
            template.process(object, swriter);

            in = new ByteArrayInputStream(swriter.toString().getBytes("utf-8"));//这里一定要设置utf-8编码 否则导出的word中中文会是乱码
        } catch (Exception e) {
            e.printStackTrace();
        }
        return in;
    }


    private static Configuration getConfiguration2(String touch) throws IOException {
        //创建配置实例
        Configuration configuration = new Configuration();
        //设置编码
        configuration.setDefaultEncoding("utf-8");
        configuration.setDirectoryForTemplateLoading(new File(touch));
        return configuration;
    }

    /**
     * 获取模板字符串输入流
     * @param object  实体类
     * @param templateName  模板名称
     * @param touchUrl 模板所在路径
     * @return
     */
    public static ByteArrayInputStream getFreemarkerContentInputStream2(Object object, String templateName,String touchUrl) throws Exception {
        ByteArrayInputStream in = null;
        try {
            //获取模板
            Template template = getConfiguration2(touchUrl).getTemplate(templateName);
            StringWriter swriter = new StringWriter();
            //生成文件
            template.process(object, swriter);

            in = new ByteArrayInputStream(swriter.toString().getBytes("utf-8"));//这里一定要设置utf-8编码 否则导出的word中中文会是乱码
        } catch (Exception e) {
            e.printStackTrace();
        }
        return in;
    }
}
