package weaver.interfaces.workflow.action.cyitce.util;

import com.spire.pdf.FileFormat;
import com.spire.pdf.PdfDocument;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName: PdfEditUtils
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-06-13  17:26
 * @Version: 1.0
 */
public class PdfEditUtils {
    /**
     * @description:pdf转word
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [indexParagraphText, form_path, out_path]
     * @return: java.lang.String
     **/
    public static String pdfToWord(String form_path,String out_path) throws IOException {
        //加载PDF
        PdfDocument pdf = new PdfDocument();
        pdf.loadFromFile(form_path);
        //保存为Word格式
        pdf.saveToFile(out_path, FileFormat.DOCX);
        //删除所有警告信息
        removeAllTagByString(out_path);
        return out_path;
    }

    public static String removeAllTagByString(String file_path) throws IOException {
        // 读取Word文档
        XWPFDocument document = new XWPFDocument(new FileInputStream(file_path));
        // 获取文档中的所有段落
        List<XWPFParagraph> paras = document.getParagraphs();
        //需要匹配段落
        XWPFParagraph prePara = paras.get(0);
        for (int i=1;i<document.getParagraphs().size();++i){
            XWPFParagraph para = paras.get(i);
            if (para.getCTP().getPPr().getSectPr()==prePara.getCTP().getPPr().getSectPr()) {
                // 匹配到了需要删除的段落，删除该段落
                document.removeBodyElement(i);
            }
        }
        //删除第一页第一段落
        document.removeBodyElement(0);
        document.write(new FileOutputStream(file_path));
        document.close();
        return file_path;
    }
}