package weaver.interfaces.workflow.action.cyitce.util;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.docs.docs.util.DesUtils;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName: FileDownload
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-22  09:36
 * @Version: 1.0
 */
public class FileDownload extends BaseBean {

    public static Map<String,String> getFileDatas(String docid, User user){
        Map<String,String> result = null;

        try {
            result = new HashMap<>();
            RecordSet rs = new RecordSet();
            rs.executeQuery("select c.imagefileid,c.imagefilename from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = ? order by imagefileid desc",docid);
            if(!rs.next()){
                throw new Exception("docid:"+docid+",文件不存在！");
            }

            String imagefilecode = EncodeFileid(rs.getString(1),user);

            result.put("imagefileid",rs.getString(1));
            result.put("imagefilename",rs.getString(2));
            result.put("imagefilecode",imagefilecode);
            result.put("path","https://oa.cyitce.com/weaver/weaver.file.FileDownload?fileid="+imagefilecode+"&download=1");

            return result;
        }catch (Exception e){
            return null;
        }
    }

    public static Map<String,String> getFileDatas(String docid){
        Map<String,String> result = null;

        try {
            result = new HashMap<>();
            RecordSet rs = new RecordSet();
            rs.executeQuery("select c.imagefileid,c.imagefilename from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = ? order by imagefileid desc",docid);
            if(!rs.next()){
                throw new Exception("docid:"+docid+",文件不存在！");
            }

            String imagefilecode = EncodeFileid(rs.getString(1),null);

            result.put("imagefileid",rs.getString(1));
            result.put("imagefilename",rs.getString(2));
            result.put("imagefilecode",imagefilecode);
            result.put("path","https://oa.cyitce.com/weaver/weaver.file.FileDownload?fileid="+imagefilecode+"&download=1");

            return result;
        }catch (Exception e){
            return null;
        }
    }

    /**
     * @description:imagefileid编码
     * @author: lijianpan
     * @date: 2024/8/22
     * @param: [var0 imagefileid, var1 User]
     * @return: java.lang.String
     **/
    public static String EncodeFileid(String var0, User var1) {
        String var2 = "";
        String var3 = "select uuid from file_download_permission where imagefileid = ?";
        RecordSet var4 = new RecordSet();
        var4.executeQuery(var3, new Object[]{var0});
        String var5 = "";
        if (var4.next()) {
            var5 = weaver.general.Util.null2s(var4.getString("uuid"), "");
        }

        if ("".equals(var5) || var5 == null) {
            var5 = UUID.randomUUID().toString();
            String var6 = "insert into file_download_permission(imagefileid,uuid) values(?,?)";
            var4.executeUpdate(var6, new Object[]{var0, var5});
        }

        Calendar var17 = Calendar.getInstance();
        String var7 = weaver.general.Util.add0(var17.get(1), 4);
        String var8 = weaver.general.Util.add0(var17.get(2) + 1, 2);
        int var9 = var17.get(11);
        int var10 = var17.get(12);
        String var11 = "";
        if (var1 != null) {
            var11 = var1.getUID() + "";
        } else {
            var11 = "null";
        }

        String var12 = var11 + "_" + var5 + "_" + var7 + var8 + var9 + var10;
        DesUtils var13 = null;

        try {
            var13 = new DesUtils();
        } catch (Exception var16) {
        }

        try {
            var2 = var13.encrypt(var12);
        } catch (Exception var15) {
        }

        var2 = "a" + var2;
        return var2;
    }
}