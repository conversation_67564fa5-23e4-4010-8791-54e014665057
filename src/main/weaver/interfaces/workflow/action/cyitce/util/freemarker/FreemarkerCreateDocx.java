package weaver.interfaces.workflow.action.cyitce.util.freemarker;

import org.apache.poi.ss.formula.functions.T;
import weaver.interfaces.workflow.action.cyitce.config.BuyContract;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import static java.io.File.separator;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/15 12:28
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/15      HONOR          v1.0.0               修改原因
 */
public class FreemarkerCreateDocx {
    private static String document="document.xml";
    private static String documentXmlRels="document.xml.rels";
    private static String hearder="header1.xml";

    /**
     * @Description: 输出docx
     * @param obj 合同实体类
     * @param outputStream 文件输出路径
     * @param imageInfos 合同图片信息集合
     * @param templateUrl 模板路径
     * @Author:  lijianpan
     * @date: 2022/7/15 15:28
     */
    //outputStream 输出流可以自己定义 浏览器或者文件输出流
    public static void createDocx(Object obj, OutputStream outputStream, List<ImageInfo> imageInfos, String templateUrl,String zipName) {
        ZipOutputStream zipout = null;

        try {
            //图片配置文件模板
            ByteArrayInputStream documentXmlRelsInput = FreeMarkUtils.getFreemarkerContentInputStream(obj, documentXmlRels,templateUrl);

            //内容模板
            ByteArrayInputStream documentInput = FreeMarkUtils.getFreemarkerContentInputStream(obj, document,templateUrl);

            //页眉模板
            ByteArrayInputStream headerInput = FreeMarkUtils.getFreemarkerContentInputStream(obj, hearder,templateUrl);

            //最初设计的模板
//            File docxFile = new File(WordUtils.class.getClassLoader().getResource(template).getPath());
//            File docxFile = new File("/u01/ecology/classbean/weaver/interfaces/workflow/action/resources/template"+templateUrl+"/"+zipName);//换成自己的zip路径
            File docxFile = new File(BuyContract.outUrl+"/template"+templateUrl+"/"+zipName);//换成自己的zip路径
            if (!docxFile.exists()) {
                docxFile.createNewFile();
            }
            ZipFile zipFile = new ZipFile(docxFile);
            Enumeration<? extends ZipEntry> zipEntrys = zipFile.entries();
            zipout = new ZipOutputStream(outputStream);
            //开始覆盖文档------------------
            int len = -1;
            byte[] buffer = new byte[1024];
            while (zipEntrys.hasMoreElements()) {
                ZipEntry next = zipEntrys.nextElement();
                InputStream is = zipFile.getInputStream(next);
                if (next.toString().indexOf("media") < 0) {
                    zipout.putNextEntry(new ZipEntry(next.getName()));
                    if (next.getName().indexOf("document.xml.rels") > 0) { //如果是document.xml.rels由我们输入
                        if (documentXmlRelsInput != null) {
                            while ((len = documentXmlRelsInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            documentXmlRelsInput.close();
                        }
                    }else if ("word/document.xml".equals(next.getName())) {//如果是word/document.xml由我们输入
                        if (documentInput != null) {
                            while ((len = documentInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            documentInput.close();
                        }
                    }else if ("word/header1.xml".equals(next.getName())) {//如果是word/header1.xml由我们输入
                        if (headerInput != null) {
                            while ((len = headerInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            headerInput.close();
                        }
                    } else {
                        while ((len = is.read(buffer)) != -1) {
                            zipout.write(buffer, 0, len);
                        }
                        is.close();
                    }
                }
            }

            //写入图片
            for(ImageInfo im:imageInfos){
                ZipEntry next = new ZipEntry("word" + separator + "media" + separator + im.getName());
                zipout.putNextEntry(new ZipEntry(next.toString()));
                InputStream in = new ByteArrayInputStream(im.getBytes());
                while ((len = in.read(buffer)) != -1) {
                    zipout.write(buffer, 0, len);
                }
                in.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(zipout!=null){
                try {
                    zipout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(outputStream!=null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * @Description: 输出docx
     * @param obj 模型数据对象
     * @param outputStream 文件输出路径
     * @param templateUrl 模板路径
     * @param zipName zip名称
     * @Author:  lijianpan
     * @date: 2022/7/15 15:28
     */
    //outputStream 输出流可以自己定义 浏览器或者文件输出流
    public static void createDocx(Object obj, OutputStream outputStream, String templateUrl,String zipName) {
        ZipOutputStream zipout = null;

        try {
            //图片配置文件模板
            ByteArrayInputStream documentXmlRelsInput = FreeMarkUtils.getFreemarkerContentInputStream2(obj, documentXmlRels,templateUrl);

            //内容模板
            ByteArrayInputStream documentInput = FreeMarkUtils.getFreemarkerContentInputStream2(obj, document,templateUrl);

            if(documentInput==null){
                throw new RuntimeException("document.xml 模板生成失败！");
            }

            //页眉模板
            ByteArrayInputStream headerInput = FreeMarkUtils.getFreemarkerContentInputStream2(obj, hearder,templateUrl);

            //最初设计的模板
            File docxFile = new File(templateUrl+"/"+zipName);//换成自己的zip路径
            if (!docxFile.exists()) {
                docxFile.createNewFile();
            }
            ZipFile zipFile = new ZipFile(docxFile);
            Enumeration<? extends ZipEntry> zipEntrys = zipFile.entries();
            zipout = new ZipOutputStream(outputStream);
            //开始覆盖文档------------------
            int len = -1;
            byte[] buffer = new byte[1024];
            while (zipEntrys.hasMoreElements()) {
                ZipEntry next = zipEntrys.nextElement();
                InputStream is = zipFile.getInputStream(next);
                if (next.toString().indexOf("media") < 0) {
                    zipout.putNextEntry(new ZipEntry(next.getName()));
                    if (next.getName().indexOf("document.xml.rels") > 0) { //如果是document.xml.rels由我们输入
                        if (documentXmlRelsInput != null) {
                            while ((len = documentXmlRelsInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            documentXmlRelsInput.close();
                        }
                    }else if ("word/document.xml".equals(next.getName())) {//如果是word/document.xml由我们输入
                        if (documentInput != null) {
                            while ((len = documentInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            documentInput.close();
                        }
                    }else if ("word/header1.xml".equals(next.getName())) {//如果是word/header1.xml由我们输入
                        if (headerInput != null) {
                            while ((len = headerInput.read(buffer)) != -1) {
                                zipout.write(buffer, 0, len);
                            }
                            headerInput.close();
                        }
                    } else {
                        while ((len = is.read(buffer)) != -1) {
                            zipout.write(buffer, 0, len);
                        }
                        is.close();
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }finally {
            if(zipout!=null){
                try {
                    zipout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(outputStream!=null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}


