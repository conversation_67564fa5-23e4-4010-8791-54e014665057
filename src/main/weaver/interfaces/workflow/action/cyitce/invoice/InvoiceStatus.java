package weaver.interfaces.workflow.action.cyitce.invoice;


import java.security.PublicKey;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;

import weaver.conn.RecordSet;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;


/**
 * 
 * @description 修改发票状态action
 * <AUTHOR>
 * @date 2020/10/13
 */
public class InvoiceStatus extends BaseBean implements Action{

    public String status;

    
    @Override
    public String execute(RequestInfo request) {
        String reimbursement_name=null;
        int workflowid = Integer.parseInt(request.getWorkflowid());
        RecordSet rs=new RecordSet();
        String sql= "select workflowname from workflow_base where id="+workflowid;
        rs.executeQuery(sql);
        while (rs.next()) {
            reimbursement_name = rs.getString("workflowname");//获取流程名
            writeLog("InvoiceStatus-----workflowname:"+reimbursement_name);
        }
        User user = request.getRequestManager().getUser();//获取当前操作用户对象
        
        String stat = Action.SUCCESS;// 数据操作状态
        int invoiceStatus=Integer.parseInt(this.getStatus());
        writeLog("InvoiceStatus-----workflowname:"+invoiceStatus);
        JSONObject entity_json=new JSONObject();
        JSONArray array=new JSONArray();
             
        DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
        if (detailtable.length>0) {
            for (int i = 0; i < detailtable.length; i++) {    
            	 writeLog("InvoiceStatus------明细表2222第"+detailtable.length+"条数据:");
                DetailTable dt = detailtable[i];// 指定明细表               
                Row[] s = dt.getRow();// 当前明细表的所有数据,按行存储             
                for (int j = 0; j < s.length; j++) { 
                    writeLog("InvoiceStatus------明细表33333第"+s.length+"条数据:");
                    writeLog("InvoiceStatus------明细表1第"+j+"条数据:");
                    double reimbursement=0;//报销金额
                    Row r = s[j];// 指定行                    
                    Cell c[] = r.getCell();// 每行数据再按列存储      
                    for (int k = 0; k < c.length; k++) {
                        JSONObject entity_j=new JSONObject();//存放每一条发票数据
                        int entity_id=0;//发票id
                        entity_j.put("reimbursement_name", reimbursement_name);
                        entity_j.put("status", invoiceStatus);
                        Cell c1 = c[k];// 指定列          
                        String name = c1.getName();// 明细字段名称  
                        writeLog("InvoiceStatus------名行表字段名:"+name);
                        if (name.equals("bxje")) {//报销金额
                            reimbursement= Double.parseDouble(c1.getValue());
                            if(invoiceStatus==1) {
                            	entity_j.put("reimbursement", 0);
                            }else {
                            	entity_j.put("reimbursement", reimbursement);
                            }
                           writeLog("InvoiceStatus------bxje:"+reimbursement);
                        }else if (name.equals("fphnew")) {//发票号
                        	writeLog("InvoiceStatus------fph:"+c1.getValue());
                        			if(c1.getValue().equals("")) {
                        				  writeLog("12345----进来了");
                        				 entity_j.put("entity_id", entity_id);
                        			}else {
                        				entity_id=Integer.parseInt(c1.getValue());
                                        entity_j.put("entity_id", entity_id);
                                        writeLog("InvoiceStatus------entity_id:"+entity_id);
                        			}
    
                        }else if (name.equals("fph")) {//发票号
                        	writeLog("InvoiceStatus------fph:"+c1.getValue());
                        			if(c1.getValue().equals("")) {
                        				  writeLog("12345----进来了");
                        				 entity_j.put("entity_id", entity_id);
                        			}else {
                        				entity_id=Integer.parseInt(c1.getValue());
                                        entity_j.put("entity_id", entity_id);
                                        writeLog("InvoiceStatus------entity_id:"+entity_id);
                        			}    
                        }else{
                        	entity_j.put("entity_id", entity_id);
                        	writeLog("INvoiceStatus-----entity_id:"+entity_id);
                        }

                        if(entity_id==0) {
                            continue;
                        }else {
                            array.add(entity_j);
                        }
                    }
                }
            }
        }
        entity_json.put("entity_json", array);
        writeLog("entity_json:"+entity_json.toString());
        
        try {
            String token = getToken(user);
            writeLog("InvoiceStatus------token:"+token);
            if (!token.isEmpty()) {
                String raise = raise(entity_json.toJSONString(),token);
                writeLog("InvoiceStatus------raise:"+raise);
                if(!raise.equals("success")) {
                    request.getRequestManager().setMessagecontent("修改发票失败，请重试！");   
                    stat=Action.FAILURE_AND_CONTINUE;
                }
            }else {
                request.getRequestManager().setMessagecontent("验证失败，请重试！");   
                stat=Action.FAILURE_AND_CONTINUE;
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.getRequestManager().setMessagecontent("操作失败，请重试！"); 
            stat=Action.FAILURE_AND_CONTINUE;
        } 
        return stat;
    }


    public String getStatus() {
        return status;
    }



    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 
     * @description 修改发票状态及报销金额
     * <AUTHOR>
     * @param entity_json 发票信息json
     * @param tokenString token字符串
     */
    public  String raise(String entity_json,String tokenString) { 
        writeLog("raise----进来了");
        String urlString="http://***************:8086/invoice/raise";
        HttpClient client=null;
        PostMethod post=null;
        String result=null;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            //设置编码方式
//            post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "utf-8");
            //添加参数
            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            post.setRequestBody(entity_json);
            //执行
            client.executeMethod(post);
            //接口返回信息
            writeLog("InvoiceStatus----raise----code:"+post.getStatusCode());
            String info = new String(post.getResponseBody(), "utf-8");
            writeLog(info);
            result  =(String) JSONObject.parseObject(info).get("state");
            writeLog("InvoiceStatus----result:"+result);	
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }
    
    /**
     * 
     * @description 通过公钥获取token
     * <AUTHOR>
     * @param
     * @return
     */
    public  String getToken(User user) {
        writeLog("InvoiceStatus----getToken----进来了");
        JSONObject userInfo=new JSONObject();
        userInfo.put("id", user.getUID());
        userInfo.put("email", user.getEmail());
        userInfo.put("mobile", user.getMobile());
        userInfo.put("sex", user.getSex());
        userInfo.put("pwd", user.getPwd());
        userInfo.put("loginid", user.getLoginid());
        //公钥字符串
        String gyString="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCPrHRZg8iILrgVhMXqb+79KqU7j/eb/L7lN6dao+i5EExpX8zOg2XUiudIczDYApj/q7/uSyLf+57ibTvm6YyR+FruLUVXNC0sQqymufC9FgrYOBc4UEEGV8Ajearv1eEcvO0DOU3snz74Fr442Q//xzRn7rBeZPt9lu9B7lx0zwIDAQAB";
        String SendUrl="http://***************:8086/login/getToken";
        HttpClient client=null;
        PostMethod post=null;
        String token="";
        try {
            //获取公钥
            PublicKey publicKey = RSAUtils.getPublicKey(gyString);
            //RSA加密
            String secret = RSAUtils.encrypt(userInfo.toString(), publicKey);
            writeLog("secret:"+secret);
            client=new HttpClient();
            post = new PostMethod(SendUrl);
            //设置编码方式
           // post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
            //添加参数
            post.addParameter("username", user.getLoginid());
            post.addParameter("secret", secret);
            
            //执行
            client.executeMethod(post);
            //接口返回信息
            String result = new String(post.getResponseBody(), "UTF-8");
            JSONObject parseObject = JSONObject.parseObject(result);
            token =(String) parseObject.get("data");
            writeLog("InvoiceStatus------获取到的token:"+token);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return token;
    }
    
    
}
