package weaver.interfaces.workflow.action.cyitce.invoice;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.security.PublicKey;


public class TestJK {
    
    public static void post() { 
        String  entity_json="{\"entity_json\":[{\"reimbursement\":2.0,\"reimbursement_name\":\"发票夹测试流程\",\"entity_id\":960,\"status\":3}]}";
        String tokenString="Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjY4OSwiZXhwIjoxNTk4NTk0NTgzLCJ1c2VybmFtZSI6IndqMDg4NCJ9.m3-2dGwRXZQPiRAILPcUAXivBeZ93A_6I2LoVnB_3OSRDFgSMBEUdoX7YtPNnORoN4B940T3ATAxd14tAvET5Q";
        String urlString="http://122.112.161.89:8086/invoice/raise";
        HttpClient client=null;
        PostMethod post=null;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            
//            //设置编码方式
//            post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "utf-8");
            //添加参数
            post.addRequestHeader("Authorization", tokenString);
            RequestEntity se = new StringRequestEntity (entity_json ,"application/json" ,"UTF-8");
            post.setRequestEntity(se);
            post.setRequestHeader("Content-Type","application/json");
//            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
//            post.setRequestBody(entity_json);
            //执行
            client.executeMethod(post);
            Header[] requestHeaders = post.getRequestHeaders();
            for (int i = 0; i < requestHeaders.length; i++) {
                System.out.println("requestHeaders["+i+"]"+requestHeaders[i]);
            }
            System.out.println(post.getStatusCode());
            //接口返回信息
            String info = new String(post.getResponseBody(), "utf-8");
            System.out.println(info);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
       
    }
    
    
    public static String getToken(String secret) {
        String SendUrl="http://122.112.161.89:8086/login/getToken";
        
        String username="wj0884";
        HttpClient client=null;
        PostMethod post=null;
        String token="";
        
        try {
            client=new HttpClient();
            post = new PostMethod(SendUrl);
            //设置编码方式
            post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
            //添加参数
            post.addParameter("username", username);
            post.addParameter("secret", secret);
            
            //执行
            client.executeMethod(post);
            //接口返回信息
            String result = new String(post.getResponseBody(), "UTF-8");
            System.out.println(result);
            JSONObject parseObject = JSONObject.parseObject(result);
            token =(String) parseObject.get("data");
            System.out.println(token);
            
            
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }finally {
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();

        }
        return token;
        
        
    }

    

    
    public static void main(String[] args) {
//        post();
        String userinfo="{\"loginid\":\"xls0805\",\"sex\":\"1\",\"mobile\":\"15023605223\",\"id\":1659,\"pwd\":\"325A2CC052914CEEB8C19016C091D2AC\",\"email\":\"<EMAIL>\",\"lastname\":\"谢丽莎0805\"}";
        String gyString="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCPrHRZg8iILrgVhMXqb+79KqU7j/eb/L7lN6dao+i5EExpX8zOg2XUiudIczDYApj/q7/uSyLf+57ibTvm6YyR+FruLUVXNC0sQqymufC9FgrYOBc4UEEGV8Ajearv1eEcvO0DOU3snz74Fr442Q//xzRn7rBeZPt9lu9B7lx0zwIDAQAB";
        try {
            PublicKey publicKey = RSAUtils.getPublicKey(gyString);
            String encrypt = RSAUtils.encrypt(userinfo, publicKey);
            String token = getToken(encrypt);
            System.err.println(encrypt);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        
   
        
        
    }
    
    public static String doHttpPost(String xmlInfo, String URL,String tokenString) {
        System.out.println("发起的数据:" + xmlInfo);
        byte[] xmlData = xmlInfo.getBytes();
        InputStream instr = null;
        ByteArrayOutputStream out = null;
        try {
            URL url = new URL(URL);
        URLConnection urlCon = url.openConnection();
        urlCon.setDoOutput(true);
        urlCon.setDoInput(true);
        urlCon.setUseCaches(false);
        urlCon.setRequestProperty("content-Type", "application/json");
        urlCon.setRequestProperty("Authorization", tokenString);
        urlCon.setRequestProperty("charset", "utf-8");
        urlCon.setRequestProperty("Content-length",
        String.valueOf(xmlData.length));
        System.out.println(String.valueOf(xmlData.length));
        DataOutputStream printout = new DataOutputStream(
        urlCon.getOutputStream());
        printout.write(xmlData);
        printout.flush();
        printout.close();
        instr = urlCon.getInputStream();
        byte[] bis = toByteArray(instr);
        String ResponseString = new String(bis, "UTF-8");
        if ((ResponseString == null) || ("".equals(ResponseString.trim()))) {
        System.out.println("返回空");
        }
        System.out.println("返回数据为:" + ResponseString);
        return ResponseString;
        
        } catch (Exception e) {
        e.printStackTrace();
        return "0";
        } finally {
        try {
        out.close();
        instr.close();
        
        } catch (Exception ex) {
        return "0";
        }
        }
        }
    
    public static byte[] toByteArray(InputStream input)
        throws IOException
      {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        copy(input, output);
        return output.toByteArray();
      }

      public static int copy(InputStream input, OutputStream output)
        throws IOException
      {
        long count = copyLarge(input, output);
        if (count > 2147483647L) {
          return -1;
        }
        return (int)count;
      }

      public static long copyLarge(InputStream input, OutputStream output)
        throws IOException
      {
        byte[] buffer = new byte[4096];
        long count = 0L;
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
          output.write(buffer, 0, n);
          count += n;
        }
        return count;
      }
}
