package weaver.interfaces.workflow.action.cyitce.service;

import sun.misc.BASE64Encoder;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.io.*;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @Description: 图片处理工具类
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/6/28 17:52
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/6/28      HONOR          v1.0.0               修改原因
 */
public class ImageManage extends BaseBean {

    private String SavePath = this.getClass().getResource("/weaver/interfaces/workflow/action/resources/tempImage").getPath();

    private String FileAllPath;

    public void init(){
        SavePath = SavePath+"/"+ UUID.randomUUID();

        File file = new File(SavePath);

        file.mkdirs();
    }

    /**
     * @Description: 图片转换为Base64编码的字符串
     * @param fieldId 文件id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/28 17:53
     */
    public String getImageBase64String(String fieldId) {
        writeLog("开始转换图片数据");
        String imageFile = FileToBinary(fieldId);

        if ("".equals(imageFile)) {
            return "";
        }
        File file = new File(imageFile);
        if (!file.exists()) {
            return "";
        }

        BASE64Encoder encoder = null;
        InputStream is = null;
        byte[] data = null;
        String imageBase64String = "";
        try {
            is = new FileInputStream(file);
            data = new byte[is.available()];
            is.read(data);
            encoder = new BASE64Encoder();
            imageBase64String = encoder.encode(data);
            return Util.null2String(imageBase64String);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }finally {
            try {
                is.close();
            }catch (Exception e){
                e.printStackTrace();
                return "";
            }
        }
    }

    /**
     * @Description: 获取图片全称路径
     * @param fieldId 文件id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/28 17:53
     */
    public String getFileAllPath(String fieldId){
        writeLog("获取文件全称路径");
        String imageFile = FileToBinary(fieldId);

        if ("".equals(imageFile)) {
            return "";
        }
        File file = new File(imageFile);
        if (!file.exists()) {
            return "";
        }
        return imageFile;
    }

    /**
     *
     * 获取文件全称路径
     *
     * @param fieldId 文件id
     * @return
     */
    private String FileToBinary(String fieldId) {
        if(fieldId==null||"".equals(fieldId)){
            return "";
        }

        writeLog("获取图片全称路径");
        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        byte[] fileByte = null;

        File file = null;
        FileInputStream fis = null;
        ByteArrayOutputStream bos = null;

        // 获取解压后文件路径
        String unzipfilepath = "";
        String fileSql = "";
        int dex = 0;
        byte[] b = null;
        int len = 0;

        try {
            fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                    + fieldId + " order by imagefileid desc";
            rs.execute(fileSql);
            if (rs.next()) {
                filename = com.weaver.general.Util.null2String(rs.getString("imagefilename"));
                filepath = com.weaver.general.Util.null2String(rs.getString("filerealpath"));
                // 解压文件
                UnZip(filepath, filename);  // 把文件解压出来
            }
            return FileAllPath;  // 返回文件全称路径
        }catch(Exception e){
            return "";
        }
    }

    /**
     * @Description:  解压文件到新目录
     * @param path 文件路径
     * @param realname 解压后文件名称
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/30 11:03
     */
    private void UnZip(String path, String realname) {
        writeLog("开始解压图片");
        int count = -1;
        int index = -1;

        BufferedOutputStream bos = null;   // 用于接收到文件的输出流
        ZipEntry entry = null;          // zip压缩实例
        FileInputStream fis = null;
        ZipInputStream zis = null;

        File f = null;
        FileOutputStream fos = null;
        String temp = "";
        try {

            fis = new FileInputStream(path);   //  读取文件流
            zis = new ZipInputStream(new BufferedInputStream(fis));  // 该类实现了以ZIP文件格式读取文件的输入流过滤器。 包括对压缩和未压缩条目的支持(读取到缓冲流)。

            while ((entry = zis.getNextEntry()) != null) {   // 文件实例是否为空
                byte[] data = new byte[2048];
                temp = entry.getName();  // 获取文件的名
                index = path.lastIndexOf("."); // 文件的后缀名，得到文件类型
                if (index > -1){
                    temp = path.substring(0, index + 1);  // 拼接后缀名，得到文件的全名
                }

                FileAllPath = SavePath +"/"+ realname;// 获取到文件的全路径（全新的保存在系统）
                f = new File(FileAllPath);
                f.createNewFile();  // 抽象路径名命名的新的空文件
                fos = new FileOutputStream(f);   // 文件输出流
                bos = new BufferedOutputStream(fos, 2048);    //把文件的输出流写入到   文件的缓存输出流
				/*从当前ZIP条目读取到字节数组,文件的大小
				 * b - 读取数据的缓冲区
				off - 目的地阵列中的起始偏移量 b
				len - 读取的最大字节数 */
                while ((count = zis.read(data, 0, 2048)) != -1) {
                    bos.write(data, 0, count);  // 把文件的   以缓存输出流的形式写出去
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                bos.flush();   // 刷新缓冲输出流。
                bos.close();  // 关闭缓存输出流。
                zis.close();  // 关闭此输入流并释放与流相关联的任何系统资源。
            }catch (Exception e){

            }
        }
    }

    /**
     * @Description: 删除临时文件
     * @Author:  lijianpan
     * @date: 2022/6/30 14:46
     */
    public void deleteTempFile(){
        File file = new File(FileAllPath);
        file.delete();
    }

    public String getSavePath(){
        return SavePath;
    }
}
