package weaver.interfaces.workflow.action.cyitce.service.buyContract;

import org.apache.commons.io.FileUtils;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.cyitce.doc.config.DocConfig;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.PdfEditUtils;
import weaver.interfaces.workflow.action.cyitce.util.WordEditUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName: 文档操作
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-02  16:23
 * @Version: 1.0
 */
public class ContractFileMerg extends BaseBean {
    //最终合并的文档路径
    private String merg_file_path;
    //临时文档存放目录
    private String out_path;
    //临时文档名称前缀
    private String name;
    //临时文档流水
    private int count = 0;
    //未保存在数藤平台的文档路径
    private Map<Integer,String> otherFilePath;
    //未保存在数藤平台的文档路径1
    private List<String> otherFilePathLast;

    public ContractFileMerg(String m_path, String o_path) {
        UUID uuid = UUID.randomUUID();
        this.merg_file_path = pathCheck(m_path)+uuid+"/";
        this.out_path = pathCheck(o_path)+uuid+"/";
        this.name = "temp";

        File f = new File(merg_file_path);
        f.mkdirs();
        File f2 = new File(out_path);
        f2.mkdirs();

        otherFilePath = new HashMap<>();
        otherFilePathLast = new ArrayList<>();
    }

    public ContractFileMerg() {
        UUID uuid = UUID.randomUUID();
//        this.merg_file_path = pathCheck(BuyContract.outUrl+"/tempDoc/merg/")+uuid+"/";
//        this.out_path = pathCheck(BuyContract.outUrl+"/tempDoc/temp/")+uuid+"/";
        this.merg_file_path = pathCheck(DocConfig.MERG_SAVE_PATH)+uuid+"/";
        this.out_path = pathCheck(DocConfig.DOWN_SAVE_PATH)+uuid+"/";
        this.name = "temp";

        File f = new File(merg_file_path);
        f.mkdirs();
        File f2 = new File(out_path);
        f2.mkdirs();

        otherFilePath = new HashMap<>();
        otherFilePathLast = new ArrayList<>();
    }

    public String getMerg_file_path() {
        return merg_file_path;
    }

    public String getOut_path() {
        return out_path;
    }

    public String getMergPath(){
        writeLog("文档合并保存路径："+merg_file_path);
        return merg_file_path;
    }

    public String getTempPath(){
        writeLog("临时文档保存路径："+out_path);
        return out_path;
    }

    public void delete() throws IOException {
        File f = new File(merg_file_path);
        FileUtils.deleteDirectory(f);
        File f2 = new File(out_path);
        FileUtils.deleteDirectory(f2);
    }

    public boolean insertOtherFilePath(int i,String filePath){
        if(filePath!=null&&!"".equals(filePath)){
            otherFilePath.put(i,filePath);
            return true;
        }else {
            return false;
        }
    }

    public boolean insertOtherFilePath(String filePath){
        if(filePath!=null&&!"".equals(filePath)){
            otherFilePathLast.add(filePath);
            return true;
        }else {
            return false;
        }
    }

    /*
     * @description: 单个图片转word（附件文档）
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [title 附件标号, imgPath]
     * @return: java.lang.String
     **/
    public String createDocxByImage(String title,String imgPath) throws IOException {
        String dex = imgPath.substring(imgPath.lastIndexOf("."));

        if(".png;.jpg;.jpge".indexOf(dex)!=-1){
            String imgName = out_path+name+"_"+count+".docx";
            count++;
            WordEditUtils.createDocxMergImage(title,imgPath,imgName);
            return imgName;
        }
        return "";
    }

    /*
     * @description:通过附件id合并附件
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [mergFileName, file_ids]
     * @return: java.lang.String
     **/
    public String mergFileById(String mergFileName,Map<Integer,String> file_ids) throws Exception {
        List<String> file_Path = new ArrayList<>();
        String[] ids;
        String dex;
        String temp;

        Set<Integer> keySet1 = otherFilePath.keySet();
        Set<Integer> keySet2 = file_ids.keySet();
        List<Integer> list = new ArrayList<>();

        for(Integer i:keySet1){
            list.add(i);
        }
        for(Integer i:keySet2){
            list.add(i);
        }
        Collections.sort(list);

        for (int i=0;i<list.size();++i){
            if(otherFilePath.containsKey(list.get(i))){
                ids = otherFilePath.get(list.get(i)).split(",");
                for (String s:ids){
                    file_Path.add(s);
                }
                continue;
            }

            ids=file_ids.get(list.get(i)).split(",");

            List<String> appendixImgPath = new ArrayList<>();
            List<String> appendixWordPath = new ArrayList<>();
            for (String s:ids){
                temp = getFilePathById(s);
                dex = temp.substring(temp.lastIndexOf("."));
                writeLog("附件解压路径：" + temp);
                if(".png;.jpg;.jpge".indexOf(dex)!=-1){
                    writeLog("图片附件解压路径："+temp);
                    appendixImgPath.add(temp);
                }else if(".docx;.doc".indexOf(dex)!=-1) {
                    writeLog("文档附件解压路径：" + temp);
                    appendixWordPath.add(temp);
                }else if(".pdf".indexOf(dex)!=-1){
                    appendixWordPath.add(pdfToWord(temp));
                }
            }

            writeLog("附件合并");
            String tempMergPath1 = "";
            if(appendixWordPath.size()>1){
                tempMergPath1 = tempMergFileByPath(appendixWordPath);
                tempMergPath1 = WordEditUtils.wordFirstParagraphAddContent("附件"+list.get(i),tempMergPath1);
            }else if (appendixWordPath.size()==1){
                tempMergPath1 = WordEditUtils.wordFirstParagraphAddContent("附件"+list.get(i),appendixWordPath.get(0));
            }

            if(appendixImgPath.size()>0){
                if("".equals(tempMergPath1)){
                    tempMergPath1 = imagesToWord("附件"+list.get(i),appendixImgPath);
                    writeLog("图片附件合并后路径："+tempMergPath1);
                }else {
                    String imgword = imagesToWord("",appendixImgPath);
                    tempMergPath1 = tempMergFileByPath(tempMergPath1,imgword);
                }
            }

            file_Path.add(tempMergPath1);
        }

        for (String s:otherFilePathLast){
            writeLog("末尾附件路径："+s);
            file_Path.add(s);
        }


        return mergFileByPath(mergFileName,file_Path);
    }


    /**
     * @description:多个word合并（自定义文件名称）
     * @author: lijianpan
     * @date: 2023/8/8
     * @param: [mergFileName, file_Path]
     * @return: java.lang.String
     **/
    private String mergFileByPath(String mergFileName, List<String> file_Path) throws IOException {
        return WordEditUtils.MergWordList(merg_file_path+mergFileName,file_Path);
//        return merg_file_path+mergFileName;
    }

    /**
     * @description:多个word合并(临时文件名称)
     * @author: lijianpan
     * @date: 2023/8/8
     * @param: [file_Path]
     * @return: java.lang.String
     **/
    public String tempMergFileByPath(List<String> file_Path) throws IOException {
        String path = WordEditUtils.MergWordList(out_path+name+"_"+count+".docx",file_Path);
        count++;
        return path;
    }

    /**
     * @description:主文档拼接子文档
     * @author: lijianpan
     * @date: 2023/8/8
     * @param: [mainDocxPath, mxDocxPath]
     * @return: java.lang.String
     **/
    public String tempMergFileByPath(String mainDocxPath, String mxDocxPath) throws IOException {
        String path = WordEditUtils.MergeWord(mainDocxPath,mxDocxPath,out_path+name+"_"+count+".docx");
        count++;
        return path;
    }

    /**
     * @description:根据文档id下载文档并返回路径
     * @author: lijianpan
     * @date: 2023/8/8
     * @param: [id]
     * @return: java.lang.String
     **/
    public String getFilePathById(String id) throws Exception {
        String path = FileUtil.GetFileByDocid(id,out_path,name+"_"+count);
        count++;
        return path;
    }

    /**
     * @description:pdf转docx
     * @author: lijianpan
     * @date: 2023/8/8
     * @param: [formPath]
     * @return: java.lang.String
     **/
    public String pdfToWord(String formPath) throws IOException {
        writeLog("pdf转docx");
        writeLog("pdf path："+formPath);
        String path = PdfEditUtils.pdfToWord(formPath,out_path+name+"_"+count+".docx");
        count++;
        return path;
    }

    /**
     * @description:多图片转word（附件文档）
     * @author: lijianpan
     * @date: 2023/4/13
     * @param: [title 附件标号, imgPath]
     * @return: java.lang.String
     **/
    public String imagesToWord(String title,List<String> formPaths) throws IOException {
        String path = WordEditUtils.createDocxMergImage(title,formPaths,out_path+name+"_"+count+".docx");
        count++;
        return path;
    }

    private String pathCheck(String path){
        if(!"/".equals(path.substring(path.length()-1))){
            return path+"/";
        }
        return path;
    }

    public void countNext(){
        count++;
    }

    public int getCount(){
        return count;
    }

    public String getName(){
        return name;
    }
}