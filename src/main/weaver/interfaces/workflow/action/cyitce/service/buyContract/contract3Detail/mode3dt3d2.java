package weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: mode3dt3d2
 * @Description: 劳务分包附件3.2：无关联人员认证声明书
 * @Author: lijianpan
 * @CreateTime: 2022-11-24  14:49
 * @Version: 1.0
 */
public class mode3dt3d2 extends BaseBean implements Action {
    /**
     * 流程数据
     */
    public RecordSet rs = null;


    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;

    public RecordSet getRs() {
        return rs;
    }

    public void setRs(RecordSet rs) {
        this.rs = rs;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    @Override
    public String toString() {
        return "mode3dt3d2{" +
                "rs=" + rs +
                ", gysmc='" + gysmc + '\'' +
                '}';
    }

    public void data(RecordSet rs){
        this.rs = rs;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();

        try {
            if(rs==null){
                rs = new RecordSet();
                rs.execute(sql);
                if(!rs.next()){
                    writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                    throw new Exception("contract 没有查询到合同数据！");
                }
            }

            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            requestInfo.getRequestManager().setMessage("附件三生成失败！");
            writeLog("mode3dt2 error："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}