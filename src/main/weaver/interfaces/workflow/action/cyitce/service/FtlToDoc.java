package weaver.interfaces.workflow.action.cyitce.service;


import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import weaver.general.BaseBean;
import java.io.*;
import java.util.*;


public class FtlToDoc extends BaseBean {

    /**
     * @Description: 生成word
     * @param contract 合同模板实体类
     * @param ftlName ftl模板名称
     * @param ftlUrl ftl模板路径
     * @param outFileName word名称
     * @param outUrl word存放路径
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/27 15:36
     */
    public <T> String beanToWord(T contract,String ftlName,String ftlUrl,String outFileName,String outUrl) throws IOException, TemplateException {
        writeLog("开始制作word");
        //Configuration用于读取ftl文件
        Configuration configuration = null;
        File outFile = null;
        Writer out = null;
        String outFileUrl = null;
        try {
            writeLog("生成word 1111111");
            configuration = new Configuration();
            configuration.setDefaultEncoding("utf-8");
            writeLog("生成word 2222222");
            /*以下是两种指定ftl文件所在目录路径的方式, 注意这两种方式都是
             * 指定ftl文件所在目录的路径,而不是ftl文件的路径
             */
            //指定路径的第一种方式(根据某个类的相对路径指定)
//            configuration.setClassForTemplateLoading(this.getClass(), "/weaver/interfaces/workflow/action/resources/freemarker/");
            writeLog("生成word 33333  "+ftlUrl);
            //指定路径的第二种方式,我的路径是C:/a.ftl
            configuration.setDirectoryForTemplateLoading(new File(ftlUrl));
            writeLog("生成word 444444");
            outFileUrl = outUrl + UUID.randomUUID() + outFileName.substring(outFileName.indexOf("."));
            // 输出文档路径及名称
            outFile = new File(outFileUrl);
            writeLog("生成word 55555555"+ftlName);
            //以utf-8的编码读取ftl文件
            Template template = configuration.getTemplate(ftlName,"utf-8");
            writeLog("生成word 66666666666666666666");
            out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"), 10240);
            template.process(contract, out);
            out.close();
            writeLog("生成路径: "+outFileUrl);

            return outFileUrl;
        }catch (Exception e){
            writeLog("错误信息----"+e.getMessage());
            e.printStackTrace();
            return "";
        }

    }

//    public static void main(String[] args) throws IOException, TemplateException {
//        Contract1 c = new Contract1();
//        c.setHtkssj(JoinFieldManage.dateStringToString("2022-06-28"));
//        c.setHtzzsj(JoinFieldManage.dateStringToString("2022-08-28"));
//
//        String ftlName = "clzpht.ftl";
//        String ftlUrl = new FreemarkerToDoc().getClass().getResource("/weaver/interfaces/workflow/action/resources/freemarker").getPath();
//
//        String docName = "房屋租赁合同.doc";
//        String docUrl = new FreemarkerToDoc().getClass().getResource("/weaver/interfaces/workflow/action/resources/tempDoc").getPath()+"/";
//
//        try {
//            FreemarkerToDoc freemarkerToDoc = new FreemarkerToDoc();
//            freemarkerToDoc.beanToWord(c,ftlName,ftlUrl,docName,docUrl);
//        }catch (Exception e){
//            e.printStackTrace();
//            System.out.println("失败");
//        }
//        System.out.println(new FreemarkerToDoc().getClass().getResource("/weaver/interfaces/workflow").getPath());
//    }
}
