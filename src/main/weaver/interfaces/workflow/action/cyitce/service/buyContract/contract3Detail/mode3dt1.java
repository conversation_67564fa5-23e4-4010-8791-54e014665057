package weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: mode3dt2
 * @Description: 劳务分包附件1：关于规范发票行为及收款账户的不可撤销承诺函
 * @Author: lijianpan
 * @CreateTime: 2022-11-22  14:37
 * @Version: 1.0
 */
public class mode3dt1 extends BaseBean implements Action {
    /**
     * 流程数据
     */
    public RecordSet rs = null;

//    ---------------------------乙方-------------------------
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * 统一社会信用代码 creditCode
     */
    public String creditCode;
    /**
     * 住所地 lxdzyf
     */
    public String lxdzyf;
    /**
     * 电话 fzrlxdh
     */
    public String fzrlxdh;
    /**
     * zhmc	账户名称
     */
    public String zhmc;
    /**
     * yxkh	账号
     */
    public String yxkh;
    /**
     * khyx	开户行
     */
    public String khyx;
    /**
     * ywqyfzr 乙方联系人
     */
    public String ywqyfzr;

    //-------------------------甲方---------------------------------
    /**
     * 甲方（分包方） fb
     */
    public String fb;
    /**
     * 联系人 lxrjf
     */
    public String lxrjf;
    /**
     * 电话 shrdh
     */
    public String lxdhjf;


    public RecordSet getRs() {
        return rs;
    }

    public void setRs(RecordSet rs) {
        this.rs = rs;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getLxrjf() {
        return lxrjf;
    }

    public void setLxrjf(String lxrjf) {
        this.lxrjf = lxrjf;
    }

    public String getLxdhjf() {
        return lxdhjf;
    }

    public void setLxdhjf(String lxdhjf) {
        this.lxdhjf = lxdhjf;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    @Override
    public String toString() {
        return "mode3dt1{" +
                "rs=" + rs +
                ", gysmc='" + gysmc + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", fb='" + fb + '\'' +
                ", lxrjf='" + lxrjf + '\'' +
                ", lxdhjf='" + lxdhjf + '\'' +
                '}';
    }

    public void data(RecordSet rs){
        this.rs = rs;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();

        try {
            if(rs==null){
                rs = new RecordSet();
                rs.execute(sql);
                if(!rs.next()){
                    writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                    throw new Exception("contract 没有查询到合同数据！");
                }
            }

            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            creditCode = Util.null2String(rs.getString("sfzhmtyshxydmczf"));
            lxdzyf = Util.null2String(rs.getString("lxdzyf"));
            fzrlxdh = Util.null2String(rs.getString("fzrlxdh"));
            zhmc = Util.null2String(rs.getString("zhmc"));
            yxkh = Util.null2String(rs.getString("yxkh"));
            khyx = Util.null2String(rs.getString("khyx"));
            ywqyfzr = Util.null2String(rs.getString("ywqyfzr"));

            fb = JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")),"subcompanyname");
            lxrjf = JoinFieldManage.getPersonName(rs.getString("lxrjf"));
            lxdhjf = Util.null2String(rs.getString("lxdhjf"));

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            requestInfo.getRequestManager().setMessage("附件一生成失败！");
            writeLog("mode3dt1 error："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}
