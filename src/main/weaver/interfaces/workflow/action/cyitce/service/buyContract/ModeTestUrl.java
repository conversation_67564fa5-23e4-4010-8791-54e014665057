package weaver.interfaces.workflow.action.cyitce.service.buyContract;

/**
 * @ClassName: ModeUrl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-11-28  13:59
 * @Version: 1.0
 */
public class ModeTestUrl {
    public static String VERSION_URL = "/v3";

    public static String CLZPHT_URL = VERSION_URL + "/clzpht";
    public static String FWZPHT_URL = VERSION_URL + "/fwzpht";
    public static String LWFBHT_URL = VERSION_URL + "/lwfbht";
    public static String SBGXHT_URL = VERSION_URL + "/sbgxht";
    public static String CLGXHT_URL = VERSION_URL + "/clgxht";
    public static String JSXMFWKJHT_URL = VERSION_URL + "/jsxmfwkjht";
    public static String LWWX_URL = VERSION_URL + "/lwwx";

    public static String LWFBHT_FJ1_URL = LWFBHT_URL + "/fj1";
    public static String LWFBHT_FJ2_URL = LWFBHT_URL + "/fj2";
    public static String LWFBHT_FJ3_URL = LWFBHT_URL + "/fj3";
    public static String LWFBHT_FJ4_URL = LWFBHT_URL + "/fj4";
    public static String LWFBHT_FJ13_URL = LWFBHT_URL + "/fj13";
    public static String LWFBHT_FJ3d1_URL = LWFBHT_FJ3_URL + "/fj3d1";
    public static String LWFBHT_FJ3d2_URL = LWFBHT_FJ3_URL + "/fj3d2";

    public static String LWWX_FJ1_URL = LWWX_URL + "/fj1";
    public static String LWWX_FJ2_URL = LWWX_URL + "/fj2";
    public static String LWWX_FJ3_URL = LWWX_URL + "/fj3";
    public static String LWWX_FJ4_URL = LWWX_URL + "/fj4";
    public static String LWWX_FJ12_URL = LWWX_URL + "/fj12";
    public static String LWWX_FJ3d1_URL = LWWX_FJ3_URL + "/fj3d1";
    public static String LWWX_FJ3d2_URL = LWWX_FJ3_URL + "/fj3d2";

}