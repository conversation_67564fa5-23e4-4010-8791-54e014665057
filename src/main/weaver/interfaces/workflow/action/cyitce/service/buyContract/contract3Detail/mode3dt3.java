package weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: mode3dt3
 * @Description: 劳务分包附件3：廉洁诚信承诺书
 * @Author: lijianpan
 * @CreateTime: 2022-11-23  16:07
 * @Version: 1.0
 */
public class mode3dt3 extends BaseBean implements Action{
    /**
     * 流程数据
     */
    public RecordSet rs = null;


    /**
     * htmc	采购合同名称
     */
    public String htmc;
    /**
     * htbh 采购合同编号
     */
    public String htbh;


    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * 联系人（乙方） ywqyfzr
     */
    public String ywqyfzr;

    public mode3dt3d1 fj1;
    public mode3dt3d2 fj2;


    public RecordSet getRs() {
        return rs;
    }

    public void setRs(RecordSet rs) {
        this.rs = rs;
    }

    public String getHtmc() {
        return htmc;
    }

    public void setHtmc(String htmc) {
        this.htmc = htmc;
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public mode3dt3d1 getFj1() {
        return fj1;
    }

    public void setFj1(mode3dt3d1 fj1) {
        this.fj1 = fj1;
    }

    public mode3dt3d2 getFj2() {
        return fj2;
    }

    public void setFj2(mode3dt3d2 fj2) {
        this.fj2 = fj2;
    }

    public mode3dt3() {
        this.rs = rs;
        this.htmc = htmc;
        this.htbh = htbh;
        this.gysmc = gysmc;
        this.ywqyfzr = ywqyfzr;
        this.fj1 = new mode3dt3d1();
        this.fj2 = new mode3dt3d2();
    }

    @Override
    public String toString() {
        return "mode3dt3{" +
                "rs=" + rs +
                ", htmc='" + htmc + '\'' +
                ", htbh='" + htbh + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", fj1=" + fj1 +
                ", fj2=" + fj2 +
                '}';
    }

    public void data(RecordSet rs){
        this.rs = rs;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();

        try {
            if(rs==null){
                rs = new RecordSet();
                rs.execute(sql);
                if(!rs.next()){
                    writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                    throw new Exception("contract 没有查询到合同数据！");
                }
            }

            //附件
            fj1.data(rs);
            fj1.execute(requestInfo);
            fj2.data(rs);
            fj2.execute(requestInfo);

            htmc = Util.null2String(rs.getString("htmc"));
            htbh = Util.null2String(rs.getString("htbh"));
            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            ywqyfzr = Util.null2String(rs.getString("ywqyfzr"));

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            requestInfo.getRequestManager().setMessage("附件三生成失败！");
            writeLog("mode3dt2 error："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}