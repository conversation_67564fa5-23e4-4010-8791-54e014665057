package weaver.interfaces.workflow.action.cyitce.service.buyContract;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail.*;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 劳务分包合同实体
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/11 15:03
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/11      HONOR          v1.0.0               修改原因
 */
public class Contract3 extends BaseBean implements Action {
    /**
     * htbh 采购合同编号
     */
    private String htbh;
    /**
     * 甲方（分包方） fb
     */
    private String fb;
    /**
     * 统一社会信用代码 tyshxydmczf
     */
    private String tyshxydmczf;
    /**
     * 住所地 zsdjf
     */
    private String zsdjf;
    /**
     * 联系人 lxrjf
     */
    private String lxrjf;
    /**
     * 电话 shrdh
     */
    private String lxdhjf;
    /**
     * 微信号 wxhjf
     */
    private String wxhjf;
    /**
     * 电子邮箱 dzyxjf
     */
    private String dzyxjf;
    /**
     * 乙方（承包方）gysmc
     */
    private String gysmc;
    /**
     * 统一社会信用代码 sfzhmtyshxydmczf
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地 lxdzyf
     */
    private String lxdzyf;
    /**
     * 联系人 ywqyfzr
     */
    private String ywqyfzr;
    /**
     * 电话 fzrlxdh
     */
    private String fzrlxdh;
    /**
     * 微信号 wxhyf
     */
    private String wxhyf;
    /**
     * 电子邮箱（乙方）fzryxdz
     */
    private String fzryxdz;
    /**
     * 资质证书编号（安全生产许可证）zzzsbh1
     */
    private String zzzsbh1;
    /**
     * 资质专业及等级（安全生产许可证）zzzyjdj1
     */
    private String zzzyjdj1;
    /**
     * 发证机关（安全生产许可证）fzjg1
     */
    private String fzjg1;
    /**
     * 复审时间及有效期（安全生产许可证）yxqstart1，yxqend1
     */
    private String yxqstart1;
    private String yxqend1;
    /**
     * 资质证书编号（劳务资质）zzzsbh2
     */
    private String zzzsbh2;
    /**
     * 资质专业及等级（劳务资质）zzzyjdj2
     */
    private String zzzyjdj2;
    /**
     * fzjg2	发证机关
     */
    private String fzjg2;
    /**
     * 复审时间及有效期（劳务资质）yxqstart2，yxqend2
     */
    private String yxqstart2;
    private String yxqend2;
    /**
     * zsmc	证书名称（其他资质）
     */
    private String zsmc;
    /**
     * zzzsbh3	资质证书编号（其他资质）
     */
    private String zzzsbh3;
    /**
     * zzzyjdj3	资质专业及等级（其他资质）
     */
    private String zzzyjdj3;
    /**
     * fzjg3	发证机关（其他资质）
     */
    private String fzjg3;
    /**
     * yxqstart3，yxqend2	复审时间及有效期（其他资质）
     */
    private String yxqstart3;
    private String yxqend3;
    /**
     * yjxmmc	项目名称
     */
    private String yjxmmc;
    /**
     * xmszsf	项目地点
     */
    private String xmszsf;
    /**
     * fbnr	分包内容
     */
    private String fbnr;
    /**
     * htkssj	合同期限的开始日期
     */
    private String htkssj;
    /**
     * htzzsj	合同期限的结束日期
     */
    private String htzzsj;
    /**
     * bhsjg	合同不含税价预估金额
     * 虚拟字段 bhsjgDX
     */
    private String bhsjg;
    private String bhsjgDX;
    /**
     * slxz	合同签署日的法定税率,%
     */
    private String slxz;
    /**
     * hsjg	合同含税价预估总金额
     * 虚拟字段 hsjgDX
     */
    private String hsjg;
    private String hsjgDX;
    /**
     * xmjl	项目经理姓名
     */
    private String xmjl;
    /**
     * hbjdqx	汇报进度期限
     */
    private String hbjdqx;
    /**
     * nwbcjs	本工程的劳务报酬计算方式
     */
    private String nwbcjs;
    /**
     * nwbcgj	采用第（一）种方式计价的，劳务报酬共计
     */
    private String nwbcgj;
    /**
     * fkfs	付款方式
     */
    private String fkfs;
    /**
     * qtfkfs1 其他付款方式
     */
    private String qtfkfs1;
    /**
     * zfsj	支付时间（收全资料后）
     */
    private String zfsj;
    /**
     * skhtkhmc	业主方名称
     */
    private String skhtkhmc;
    /**
     * skhtbh	合同编号
     */
    private String skhtbh;
    /**
     * skhtmc	合同名称
     */
    private String skhtmc;
    /**
     * fpzzsl	发票增值税率
     */
    private String fpzzsl;
    /**
     * fplx1	发票类型
     */
    private String fplx1;
    /**
     * lybzj	履约保证金
     * 虚拟字段 lybzjDX
     */
    private String lybzj;
    private String lybzjDX;
    /**
     * zhmc	户名
     */
    private String zhmc;
    /**
     * yxkh	账号
     */
    private String yxkh;
    /**
     * khyx	开户行
     */
    private String khyx;
    /**
     * gcclsbcgf	工程材料、设备采购方
     */
    private String gcclsbcgf;
    /**
     * xmjlxm	项目经理姓名
     */
    private String xmjlxm;
    /**
     * gczlbxqx	工程质量保修期限
     */
    private String gczlbxqx;
    /**
     * qcfy	甲方代乙方清场费用
     */
    private String qcfy;
    /**
     * lkgdqx	乙方离开甲方工地期限
     */
    private String lkgdqx;
    /**
     * bfhclje	乙方使用的不符合要求材料的金额
     */
    private String bfhclje;
    /**
     * 明细表四
     */
    private List<Map<String,String>> detail;


    /*----------- 附件 ----------------------*/

    /**
     * 附件一
     */
    private mode3dt1 fj1;
    /**
     * 附件二
     */
    private mode3dt2 fj2;
    /**
     * 附件三
     */
    private mode3dt3 fj3;
    /**
     * 附件四
     */
    private mode3dt4 fj4;
    /**
     * 附件五
     */
    private List<ImageInfo> fjw;
    /**
     * 附件六
     */
    private List<ImageInfo> fjl;
    /**
     * 附件七
     */
    private List<ImageInfo> fjq;
    /**
     * 附件八
     */
    private List<ImageInfo> fjb;
    /**
     * 附件九
     */
    private List<ImageInfo> fjj;
    /**
     * 附件十
     */
    private List<ImageInfo> fjshi;
    /**
     * 附件十一
     */
    private List<ImageInfo> fjshiy;
    /**
     * 附件十二
     */
    private List<ImageInfo> fjshie;
    /**
     * 附件十三
     */
    private mode3dt13 fj13;
    /**
     * 附件十四
     */
    private List<ImageInfo> fjshisi;

    /*----------- 附件集合 ----------------------*/
    private List<ImageInfo> imageInfoList;

    public Contract3() {
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.lxrjf = "";
        this.lxdhjf = "";
        this.wxhjf = "";
        this.dzyxjf = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.ywqyfzr = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.fzryxdz = "";
        this.zzzsbh1 = "";
        this.zzzyjdj1 = "";
        this.fzjg1 = "";
        this.yxqstart1 = "";
        this.yxqend1 = "";
        this.zzzsbh2 = "";
        this.zzzyjdj2 = "";
        this.fzjg2 = "";
        this.yxqstart2 = "";
        this.yxqend2 = "";
        this.zsmc = "";
        this.zzzsbh3 = "";
        this.zzzyjdj3 = "";
        this.fzjg3 = "";
        this.yxqstart3 = "";
        this.yxqend3 = "";
        this.yjxmmc = "";
        this.xmszsf = "";
        this.fbnr = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.bhsjg = "";
        this.bhsjgDX = "";
        this.slxz = "";
        this.hsjg = "";
        this.hsjgDX = "";
        this.xmjl = "";
        this.hbjdqx = "";
        this.nwbcjs = "";
        this.nwbcgj = "";
        this.fkfs = "";
        this.qtfkfs1 = "";
        this.zfsj = "";
        this.skhtkhmc = "";
        this.skhtbh = "";
        this.skhtmc = "";
        this.fpzzsl = "";
        this.fplx1 = "";
        this.lybzj = "";
        this.lybzjDX = "";
        this.zhmc = "";
        this.yxkh = "";
        this.khyx = "";
        this.gcclsbcgf = "";
        this.xmjlxm = "";
        this.gczlbxqx = "";
        this.qcfy = "";
        this.lkgdqx = "";
        this.bfhclje = "";
        this.detail = new ArrayList<>();
        this.fjw = new ArrayList<>();
        this.fjl = new ArrayList<>();
        this.fjq = new ArrayList<>();
        this.fjb = new ArrayList<>();
        this.fjj = new ArrayList<>();
        this.fjshi = new ArrayList<>();
        this.fjshiy = new ArrayList<>();
        this.fjshie = new ArrayList<>();
        this.fjshisi = new ArrayList<>();
        this.imageInfoList = new ArrayList<>();
        this.fj1 = new mode3dt1();
        this.fj2 = new mode3dt2();
        this.fj3 = new mode3dt3();
        this.fj4 = new mode3dt4();
        this.fj13 = new mode3dt13();
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getLxrjf() {
        return lxrjf;
    }

    public void setLxrjf(String lxrjf) {
        this.lxrjf = lxrjf;
    }

    public String getLxdhjf() {
        return lxdhjf;
    }

    public void setLxdhjf(String lxdhjf) {
        this.lxdhjf = lxdhjf;
    }

    public String getWxhjf() {
        return wxhjf;
    }

    public void setWxhjf(String wxhjf) {
        this.wxhjf = wxhjf;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getZzzsbh1() {
        return zzzsbh1;
    }

    public void setZzzsbh1(String zzzsbh1) {
        this.zzzsbh1 = zzzsbh1;
    }

    public String getZzzyjdj1() {
        return zzzyjdj1;
    }

    public void setZzzyjdj1(String zzzyjdj1) {
        this.zzzyjdj1 = zzzyjdj1;
    }

    public String getFzjg1() {
        return fzjg1;
    }

    public void setFzjg1(String fzjg1) {
        this.fzjg1 = fzjg1;
    }

    public String getYxqstart1() {
        return yxqstart1;
    }

    public void setYxqstart1(String yxqstart1) {
        this.yxqstart1 = yxqstart1;
    }

    public String getYxqend1() {
        return yxqend1;
    }

    public void setYxqend1(String yxqend1) {
        this.yxqend1 = yxqend1;
    }

    public String getZzzsbh2() {
        return zzzsbh2;
    }

    public void setZzzsbh2(String zzzsbh2) {
        this.zzzsbh2 = zzzsbh2;
    }

    public String getZzzyjdj2() {
        return zzzyjdj2;
    }

    public void setZzzyjdj2(String zzzyjdj2) {
        this.zzzyjdj2 = zzzyjdj2;
    }

    public String getFzjg2() {
        return fzjg2;
    }

    public void setFzjg2(String fzjg2) {
        this.fzjg2 = fzjg2;
    }

    public String getYxqstart2() {
        return yxqstart2;
    }

    public void setYxqstart2(String yxqstart2) {
        this.yxqstart2 = yxqstart2;
    }

    public String getYxqend2() {
        return yxqend2;
    }

    public void setYxqend2(String yxqend2) {
        this.yxqend2 = yxqend2;
    }

    public String getZsmc() {
        return zsmc;
    }

    public void setZsmc(String zsmc) {
        this.zsmc = zsmc;
    }

    public String getZzzsbh3() {
        return zzzsbh3;
    }

    public void setZzzsbh3(String zzzsbh3) {
        this.zzzsbh3 = zzzsbh3;
    }

    public String getZzzyjdj3() {
        return zzzyjdj3;
    }

    public void setZzzyjdj3(String zzzyjdj3) {
        this.zzzyjdj3 = zzzyjdj3;
    }

    public String getFzjg3() {
        return fzjg3;
    }

    public void setFzjg3(String fzjg3) {
        this.fzjg3 = fzjg3;
    }

    public String getYxqstart3() {
        return yxqstart3;
    }

    public void setYxqstart3(String yxqstart3) {
        this.yxqstart3 = yxqstart3;
    }

    public String getYxqend3() {
        return yxqend3;
    }

    public void setYxqend3(String yxqend3) {
        this.yxqend3 = yxqend3;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getXmszsf() {
        return xmszsf;
    }

    public void setXmszsf(String xmszsf) {
        this.xmszsf = xmszsf;
    }

    public String getFbnr() {
        return fbnr;
    }

    public void setFbnr(String fbnr) {
        this.fbnr = fbnr;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getBhsjg() {
        return bhsjg;
    }

    public void setBhsjg(String bhsjg) {
        this.bhsjg = bhsjg;
    }

    public String getBhsjgDX() {
        return bhsjgDX;
    }

    public void setBhsjgDX(String bhsjgDX) {
        this.bhsjgDX = bhsjgDX;
    }

    public String getSlxz() {
        return slxz;
    }

    public void setSlxz(String slxz) {
        this.slxz = slxz;
    }

    public String getHsjg() {
        return hsjg;
    }

    public void setHsjg(String hsjg) {
        this.hsjg = hsjg;
    }

    public String getHsjgDX() {
        return hsjgDX;
    }

    public void setHsjgDX(String hsjgDX) {
        this.hsjgDX = hsjgDX;
    }

    public String getXmjl() {
        return xmjl;
    }

    public void setXmjl(String xmjl) {
        this.xmjl = xmjl;
    }

    public String getHbjdqx() {
        return hbjdqx;
    }

    public void setHbjdqx(String hbjdqx) {
        this.hbjdqx = hbjdqx;
    }

    public String getNwbcjs() {
        return nwbcjs;
    }

    public void setNwbcjs(String nwbcjs) {
        this.nwbcjs = nwbcjs;
    }

    public String getNwbcgj() {
        return nwbcgj;
    }

    public void setNwbcgj(String nwbcgj) {
        this.nwbcgj = nwbcgj;
    }

    public String getFkfs() {
        return fkfs;
    }

    public void setFkfs(String fkfs) {
        this.fkfs = fkfs;
    }

    public String getQtfkfs1() {
        return qtfkfs1;
    }

    public void setQtfkfs1(String qtfkfs1) {
        this.qtfkfs1 = qtfkfs1;
    }

    public String getZfsj() {
        return zfsj;
    }

    public void setZfsj(String zfsj) {
        this.zfsj = zfsj;
    }

    public String getSkhtkhmc() {
        return skhtkhmc;
    }

    public void setSkhtkhmc(String skhtkhmc) {
        this.skhtkhmc = skhtkhmc;
    }

    public String getSkhtbh() {
        return skhtbh;
    }

    public void setSkhtbh(String skhtbh) {
        this.skhtbh = skhtbh;
    }

    public String getSkhtmc() {
        return skhtmc;
    }

    public void setSkhtmc(String skhtmc) {
        this.skhtmc = skhtmc;
    }

    public String getFpzzsl() {
        return fpzzsl;
    }

    public void setFpzzsl(String fpzzsl) {
        this.fpzzsl = fpzzsl;
    }

    public String getFplx1() {
        return fplx1;
    }

    public void setFplx1(String fplx1) {
        this.fplx1 = fplx1;
    }

    public String getLybzj() {
        return lybzj;
    }

    public void setLybzj(String lybzj) {
        this.lybzj = lybzj;
    }

    public String getLybzjDX() {
        return lybzjDX;
    }

    public void setLybzjDX(String lybzjDX) {
        this.lybzjDX = lybzjDX;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getGcclsbcgf() {
        return gcclsbcgf;
    }

    public void setGcclsbcgf(String gcclsbcgf) {
        this.gcclsbcgf = gcclsbcgf;
    }

    public String getXmjlxm() {
        return xmjlxm;
    }

    public void setXmjlxm(String xmjlxm) {
        this.xmjlxm = xmjlxm;
    }

    public String getGczlbxqx() {
        return gczlbxqx;
    }

    public void setGczlbxqx(String gczlbxqx) {
        this.gczlbxqx = gczlbxqx;
    }

    public String getQcfy() {
        return qcfy;
    }

    public void setQcfy(String qcfy) {
        this.qcfy = qcfy;
    }

    public String getLkgdqx() {
        return lkgdqx;
    }

    public void setLkgdqx(String lkgdqx) {
        this.lkgdqx = lkgdqx;
    }

    public String getBfhclje() {
        return bfhclje;
    }

    public void setBfhclje(String bfhclje) {
        this.bfhclje = bfhclje;
    }

    public List<Map<String, String>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, String>> detail) {
        this.detail = detail;
    }

    public List<ImageInfo> getFjl() {
        return fjl;
    }

    public void setFjl(List<ImageInfo> fjl) {
        this.fjl = fjl;
    }

    public List<ImageInfo> getFjq() {
        return fjq;
    }

    public void setFjq(List<ImageInfo> fjq) {
        this.fjq = fjq;
    }

    public List<ImageInfo> getFjb() {
        return fjb;
    }

    public void setFjb(List<ImageInfo> fjb) {
        this.fjb = fjb;
    }

    public List<ImageInfo> getFjj() {
        return fjj;
    }

    public void setFjj(List<ImageInfo> fjj) {
        this.fjj = fjj;
    }

    public List<ImageInfo> getFjshi() {
        return fjshi;
    }

    public void setFjshi(List<ImageInfo> fjshi) {
        this.fjshi = fjshi;
    }

    public List<ImageInfo> getFjshiy() {
        return fjshiy;
    }

    public void setFjsshiy(List<ImageInfo> fjshiy) {
        this.fjshiy = fjshiy;
    }

    public List<ImageInfo> getFjshie() {
        return fjshie;
    }

    public void setFjshie(List<ImageInfo> fjshie) {
        this.fjshie = fjshie;
    }

    public mode3dt1 getFj1() {
        return fj1;
    }

    public void setFj1(mode3dt1 fj1) {
        this.fj1 = fj1;
    }

    public mode3dt2 getFj2() {
        return fj2;
    }

    public void setFj2(mode3dt2 fj2) {
        this.fj2 = fj2;
    }

    public mode3dt3 getFj3() {
        return fj3;
    }

    public void setFj3(mode3dt3 fj3) {
        this.fj3 = fj3;
    }

    public mode3dt4 getFj4() {
        return fj4;
    }

    public void setFj4(mode3dt4 fj4) {
        this.fj4 = fj4;
    }

    public List<ImageInfo> getFjw() {
        return fjw;
    }

    public void setFjw(List<ImageInfo> fjw) {
        this.fjw = fjw;
    }

    public void setFjshiy(List<ImageInfo> fjshiy) {
        this.fjshiy = fjshiy;
    }

    public mode3dt13 getFj13() {
        return fj13;
    }

    public void setFj13(mode3dt13 fj13) {
        this.fj13 = fj13;
    }

    public void setFjshisi(List<ImageInfo> fjshisi) {
        this.fjshisi = fjshisi;
    }

    public List<ImageInfo> getFjshisi() {
        return fjshisi;
    }

    public void setFjsshisi(List<ImageInfo> fjshisi) {
        this.fjshisi = fjshisi;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }


    @Override
    public String toString() {
        return "Contract3{" +
                "htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", lxrjf='" + lxrjf + '\'' +
                ", lxdhjf='" + lxdhjf + '\'' +
                ", wxhjf='" + wxhjf + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", zzzsbh1='" + zzzsbh1 + '\'' +
                ", zzzyjdj1='" + zzzyjdj1 + '\'' +
                ", fzjg1='" + fzjg1 + '\'' +
                ", yxqstart1='" + yxqstart1 + '\'' +
                ", yxqend1='" + yxqend1 + '\'' +
                ", zzzsbh2='" + zzzsbh2 + '\'' +
                ", zzzyjdj2='" + zzzyjdj2 + '\'' +
                ", fzjg2='" + fzjg2 + '\'' +
                ", yxqstart2='" + yxqstart2 + '\'' +
                ", yxqend2='" + yxqend2 + '\'' +
                ", zsmc='" + zsmc + '\'' +
                ", zzzsbh3='" + zzzsbh3 + '\'' +
                ", zzzyjdj3='" + zzzyjdj3 + '\'' +
                ", fzjg3='" + fzjg3 + '\'' +
                ", yxqstart3='" + yxqstart3 + '\'' +
                ", yxqend3='" + yxqend3 + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", xmszsf='" + xmszsf + '\'' +
                ", fbnr='" + fbnr + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", bhsjg='" + bhsjg + '\'' +
                ", bhsjgDX='" + bhsjgDX + '\'' +
                ", slxz='" + slxz + '\'' +
                ", hsjg='" + hsjg + '\'' +
                ", hsjgDX='" + hsjgDX + '\'' +
                ", xmjl='" + xmjl + '\'' +
                ", hbjdqx='" + hbjdqx + '\'' +
                ", nwbcjs='" + nwbcjs + '\'' +
                ", nwbcgj='" + nwbcgj + '\'' +
                ", fkfs='" + fkfs + '\'' +
                ", qtfkfs1='" + qtfkfs1 + '\'' +
                ", zfsj='" + zfsj + '\'' +
                ", skhtkhmc='" + skhtkhmc + '\'' +
                ", skhtbh='" + skhtbh + '\'' +
                ", skhtmc='" + skhtmc + '\'' +
                ", fpzzsl='" + fpzzsl + '\'' +
                ", fplx1='" + fplx1 + '\'' +
                ", lybzj='" + lybzj + '\'' +
                ", lybzjDX='" + lybzjDX + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", gcclsbcgf='" + gcclsbcgf + '\'' +
                ", xmjlxm='" + xmjlxm + '\'' +
                ", gczlbxqx='" + gczlbxqx + '\'' +
                ", qcfy='" + qcfy + '\'' +
                ", lkgdqx='" + lkgdqx + '\'' +
                ", bfhclje='" + bfhclje + '\'' +
                ", detail=" + detail +
                ", fj1=" + fj1 +
                ", fj2=" + fj2 +
                ", fj3=" + fj3 +
                ", fj4=" + fj4 +
                ", fjw=" + fjw +
                ", fjl=" + fjl +
                ", fjq=" + fjq +
                ", fjb=" + fjb +
                ", fjj=" + fjj +
                ", fjshi=" + fjshi +
                ", fjshiy=" + fjshiy +
                ", fjshie=" + fjshie +
                ", fj13=" + fj13 +
                ", fjshisi=" + fjshisi +
                ", imageInfoList=" + imageInfoList +
                '}';
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
        writeLog("4123412");
        ConverToChinesePart cp = null;
        writeLog("sql:    select * from "+table+" where requestid="+requestInfo.getRequestid());
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();
        RecordSet rs = null;
        Row[] r = null;
        Cell[] c = null;

        ImageInfoManage im = null;
        try {
            writeLog("896548");
            im = new ImageInfoManage();
            cp = new ConverToChinesePart();

            im.init();
            rs = new RecordSet();
            rs.execute(sql);
            if(!rs.next()){
                writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                throw new Exception("contract 没有查询到合同数据！");
            }

            //附件
            fj1.data(rs);
            fj1.execute(requestInfo);
            fj2.data(rs);
            fj2.execute(requestInfo);
            fj3.data(rs);
            fj3.execute(requestInfo);
            fj4.data(rs);
            fj4.execute(requestInfo);
            fj13.data(rs);
            fj13.execute(requestInfo);


            htbh = Util.null2String(rs.getString("htbh"));
            writeLog("合同编号 ："+htbh);
            fb = JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")),"subcompanyname");
            writeLog("===fb: "+this.fb);
            tyshxydmczf = Util.null2String(rs.getString("tyshxydmczf"));
            zsdjf = Util.null2String(rs.getString("zsdjf"));
            lxrjf = JoinFieldManage.getPersonName(rs.getString("lxrjf"));
            lxdhjf = Util.null2String(rs.getString("lxdhjf"));
            wxhjf = Util.null2String(rs.getString("wxhjf"));
            dzyxjf = Util.null2String(rs.getString("dzyxjf"));
            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            sfzhmtyshxydmczf = Util.null2String(rs.getString("sfzhmtyshxydmczf"));
            lxdzyf = Util.null2String(rs.getString("lxdzyf"));
            ywqyfzr = Util.null2String(rs.getString("ywqyfzr"));
            fzrlxdh = Util.null2String(rs.getString("fzrlxdh"));
            wxhyf = Util.null2String(rs.getString("wxhyf"));
            fzryxdz = Util.null2String(rs.getString("fzryxdz"));
            zzzsbh1 = Util.null2String(rs.getString("zzzsbh1"));
            zzzyjdj1 = Util.null2String(rs.getString("zzzyjdj1"));
            fzjg1 = Util.null2String(rs.getString("fzjg1"));
            yxqstart1 = JoinFieldManage.dateStringToString(rs.getString("yxqstart1"));
            yxqend1 = JoinFieldManage.dateStringToString(rs.getString("yxqend1"));

            zzzsbh2 = Util.null2String(rs.getString("zzzsbh2"));
            zzzyjdj2 = Util.null2String(rs.getString("zzzyjdj2"));
            fzjg2 = Util.null2String(rs.getString("fzjg2"));
            yxqstart2 = JoinFieldManage.dateStringToString(rs.getString("yxqstart2"));
            yxqend2 = JoinFieldManage.dateStringToString(rs.getString("yxqend2"));

            zsmc = Util.null2String(rs.getString("zsmc"));
            zzzsbh3 = Util.null2String(rs.getString("zzzsbh3"));
            zzzyjdj3 = Util.null2String(rs.getString("zzzyjdj3"));
            fzjg3 = Util.null2String(rs.getString("fzjg3"));
            yxqstart3 = JoinFieldManage.dateStringToString(rs.getString("yxqstart3"));
            yxqend3 = JoinFieldManage.dateStringToString(rs.getString("yxqend3"));

            yjxmmc = JoinFieldManage.getProjectName(rs.getString("yjxmmc"));
            xmszsf = JoinFieldManage.getProvinceName(rs.getString("xmszsf"));
            fbnr = Util.null2String(rs.getString("fbnr"));
            htkssj = JoinFieldManage.dateStringToString(rs.getString("htkssj"));
            htzzsj = JoinFieldManage.dateStringToString(rs.getString("htzzsj"));
            bhsjg = Util.null2String(rs.getString("bhsjg"));

            writeLog("111111");
            bhsjgDX = cp.convertToChinese(Util.null2String(rs.getString("bhsjg"),"0"));
            slxz = JoinFieldManage.getSelectName("slxz",table,rs.getString("slxz"));
            hsjg = Util.null2String(rs.getString("hsjg"));
            writeLog("2222");
            hsjgDX = cp.convertToChinese(Util.null2String(rs.getString("hsjg"),"0"));
            xmjl = JoinFieldManage.getPersonName(rs.getString("xmjl"));
            hbjdqx = JoinFieldManage.getSelectName("hbjdqx",table,rs.getString("hbjdqx"));
            writeLog("3333");
            if("0".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "一";
            }else if("1".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "二";
            }else if("2".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "三";
            }
            writeLog("4444");
            nwbcgj = Util.null2String(rs.getString("nwbcgj"));
            fkfs = Util.null2String(rs.getString("fkfs"));
            qtfkfs1 = Util.null2String(rs.getString("qtfkfs1"));
            zfsj = Util.null2String(rs.getString("zfsj"));
            skhtkhmc = JoinFieldManage.getCustomerName(rs.getString("skhtkhmc"));
            skhtbh = Util.null2String(rs.getString("skhtbh"));
            writeLog("255555");
            skhtmc = JoinFieldManage.getContractName(rs.getString("skhtmc"));
            fpzzsl = JoinFieldManage.getSelectName("fpzzsl",table,rs.getString("skhtbh"));
            writeLog("748645ds");
            fplx1 = JoinFieldManage.getSelectName("fplx1",table,rs.getString("fplx1"));
            lybzj = Util.null2String(rs.getString("lybzj"));
            writeLog("jjjjj");
            lybzjDX = cp.convertToChinese(Util.null2String(rs.getString("lybzj"),"0"));
            writeLog("sdsadas");
            zhmc = Util.null2String(rs.getString("zhmc"));
            yxkh = Util.null2String(rs.getString("yxkh"));
            writeLog("6666");
            khyx = Util.null2String(rs.getString("khyx"));
            gcclsbcgf = Util.null2String(rs.getString("gcclsbcgf"));
            xmjlxm = Util.null2String(rs.getString("xmjlxm"));
            gczlbxqx = Util.null2String(rs.getString("gczlbxqx"));
            qcfy = Util.null2String(rs.getString("qcfy"));
            lkgdqx = Util.null2String(rs.getString("lkgdqx"));
            writeLog("7777");
            bfhclje = Util.null2String(rs.getString("bfhclje"));
            writeLog("8888");

            r = detailTable[3].getRow();
            writeLog("99999  "+r.toString());
            for (int i = 0;i<r.length;++i){
                Map<String,String> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id",String.valueOf(i+1));
                for (int j = 0;j<c.length;++j){
                    writeLog(c[j].getName()+"---"+c[j].getValue());
                    map.put(c[j].getName(),c[j].getValue());
                }
                this.detail.add(map);
            }

            //附件
            String[] str = null;
            String fjValue = "";

            writeLog("获取附件五 字节码");
            fjValue = Util.null2String(rs.getString("fjw"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                writeLog("str6 "+str.length);
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjw.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件六 字节码");
            fjValue = Util.null2String(rs.getString("fjl"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                writeLog("str6 "+str.length);
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjl.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件七 字节码");
            fjValue = Util.null2String(rs.getString("fjq"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjq.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件八 字节码");
            fjValue = Util.null2String(rs.getString("fjb"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjb.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件九 字节码");
            fjValue = Util.null2String(rs.getString("fjj"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjj.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件十 字节码");
            fjValue = Util.null2String(rs.getString("fjshi"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjshi.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件十一 字节码");
            fjValue = Util.null2String(rs.getString("fjshiy"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjshiy.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件十二 字节码");
            fjValue = Util.null2String(rs.getString("fjshie"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjshie.add(imageInfo);
                imageInfoList.add(imageInfo);
            }


            writeLog("获取附件十四 字节码");
            fjValue = Util.null2String(rs.getString("fjshisi"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjshisi.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("赋值完成");

            //删除临时附件
            im.deleteFile();

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            writeLog("Contract3 报错"+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

}
