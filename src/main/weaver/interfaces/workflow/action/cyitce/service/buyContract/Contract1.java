package weaver.interfaces.workflow.action.cyitce.service.buyContract;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.*;
import java.util.stream.IntStream;

/**
 * 合同模板实体类
 * <p>
 * 车辆合同
 */
public class Contract1 extends BaseBean implements Action {

    public String getBcyd() {
        return bcyd;
    }

    public void setBcyd(String bcyd) {
        this.bcyd = bcyd;
    }

    /**
     * 补充协议
     */
    private String bcyd;

    /**
     * 包干税金
     */
    private String bgzzsj;

    /**
     * 不包干税金
     */
    private String bbgzzsj;


    /**
     * 合同编号
     */
    private String htbh;
    /**
     * 承租方（甲方）
     */
    private String fb;
    /**
     * 统一社会信用代码
     */
    private String tyshxydmczf;
    /**
     * 住所地（甲方）
     */
    private String zsdjf;
    /**
     * 电子邮箱（甲方）
     */
    private String dzyxjf;
    /**
     * 联系电话（甲方）----收货人电话（shrdh）
     */
    private String shrdh;
    /**
     * 出租方（乙方） 供应商
     */
    private String gysmc;
    /**
     * 身份证号码/统一社会信用代码
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地（乙方）
     */
    private String lxdzyf;
    /**
     * 电子邮箱（乙方）-----供应商邮箱地址
     */
    private String fzryxdz;
    /**
     * 联系电话（乙方）------供应商联系电话（）
     */
    private String fzrlxdh;
    /**
     * 微信号（乙方）
     */
    private String wxhyf;
    /**
     * 租赁期限（单位：月）
     */
    private String zlqxdwy;
    /**
     * 租赁开始日期
     * 格式：年月日
     * 对应流程字段：合同开始时间
     */
    private String htkssj;
    /**
     * 租赁结束日期
     * 格式：年月日
     * 对应流程字段：合同终止时间
     */
    private String htzzsj;
    /**
     * 租赁模式及支付方式
     */
    private String zlmsjzffs;
    /**
     * 是否带驾驶员
     */
    private String sfdjsy;
    /**
     * 包干模式
     */
    private String bgms;
    /**
     * 包干金额
     */
    private String bgje;
    /**
     * 包干是否含税
     */
    private String bgsfhs;
    /**
     * 包干价含税税率
     */
    private String bgjhssl;
    /**
     * 不包干模式
     */
    private String bbgms;
    /**
     * 不包干模式基本租金
     */
    private String bbgmsjbzj;
    /**
     * 车辆租赁不包干价模式费用类型
     **/
    private String clzlfylx;
    /**
     * 驾驶员服务费模式
     */
    private String jsyfwfms;
    /**
     * 驾驶员服务费
     */
    private String jsyrgf;
    /**
     * 不包干模式其他费用承担方
     */
    private String bbgmsqtfycdr;
    /**
     * 油费计价方式
     */
    private String bbgmsqtfyzyfjjfs;
    /**
     * 不包干是否含税
     */
    private String bbgsfhs;
    /**
     * 不包干价含税税率
     */
    private String bbgjhssl;
    /**
     * 乙方开具发票类型
     */
    private String yfkjfplx;
    /**
     * 乙方开具发票税率
     */
    private String yfkjfpsl;
    /**
     * 租车押金
     */
    private String zcyj;
    /**
     * 押金支付在【】工作日内
     */
    private String yjzfsj;
    /**
     * 甲方延长或缩短租赁，提前【】日通知乙方
     */
    private String tqtzts;
    /**
     * 账户名称（乙方）
     */
    private String zhmc;
    /**
     * 银行卡号（乙方）
     */
    private String yxkh;
    /**
     * 开户银行（乙方）
     */
    private String khyx;
    /**
     * 甲方（签章）名称前半段
     */
    private String fbLeft;
    /**
     * 甲方（签章）名称后半段
     */
    private String fbRight;
    /**
     * 乙方（签章）名称前半段
     */
    private String gysmcLeft;
    /**
     * 乙方（签章）名称后半段
     */
    private String gysmcRight;
    /**
     * 明细二
     * 租赁车辆清单
     */
    private List<Map<String, Object>> detail;

    /**
     * 明细三
     * 驾驶员信息登记表
     */
    private List<Map<String, String>> detail2;


    /*---------------附件--------------------*/
    /**
     * 附件二
     */
    private List<ImageInfo> czrsfzyyzz;
    /**
     * 附件三
     */
    private List<ImageInfo> fjs;
    /**
     * 附件五
     */
    private List<ImageInfo> fjw;


    /*----------- 附件集合 ----------------------*/
    private List<ImageInfo> imageInfoList;

    public Contract1() {
        this.bcyd = " ";
        this.bgzzsj = "";
        this.bbgzzsj = "";
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.dzyxjf = "";
        this.shrdh = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.fzryxdz = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.zlqxdwy = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.zlmsjzffs = "";
        this.sfdjsy = "";
        this.bgms = "";
        this.bgje = "";
        this.bgsfhs = "";
        this.bgjhssl = "";
        this.bbgms = "";
        this.bbgmsjbzj = "";
        this.jsyfwfms = "";
        this.jsyrgf = "";
        this.bbgmsqtfycdr = "";
        this.bbgmsqtfyzyfjjfs = "";
        this.bbgsfhs = "";
        this.bbgjhssl = "";
        this.yfkjfplx = "";
        this.yfkjfpsl = "";
        this.zcyj = "";
        this.yjzfsj = "";
        this.tqtzts = "";
        this.zhmc = "";
        this.yxkh = "";
        this.khyx = "";
        this.fbLeft = "";
        this.fbRight = "";
        this.gysmcLeft = "";
        this.gysmcRight = "";
        this.clzlfylx = "0,1,2";
        this.detail = new ArrayList<>();
        this.detail2 = new ArrayList<>();
        this.czrsfzyyzz = new ArrayList<>();
        this.fjs = new ArrayList<>();
        this.fjw = new ArrayList<>();
        this.imageInfoList = new ArrayList<>();
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getShrdh() {
        return shrdh;
    }

    public void setShrdh(String shrdh) {
        this.shrdh = shrdh;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getZlqxdwy() {
        return zlqxdwy;
    }

    public void setZlqxdwy(String zlqxdwy) {
        this.zlqxdwy = zlqxdwy;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getZlmsjzffs() {
        return zlmsjzffs;
    }

    public void setZlmsjzffs(String zlmsjzffs) {
        this.zlmsjzffs = zlmsjzffs;
    }

    public String getSfdjsy() {
        return sfdjsy;
    }

    public void setSfdjsy(String sfdjsy) {
        this.sfdjsy = sfdjsy;
    }

    public String getBgms() {
        return bgms;
    }

    public void setBgms(String bgms) {
        this.bgms = bgms;
    }

    public String getBgje() {
        return bgje;
    }

    public void setBgje(String bgje) {
        this.bgje = bgje;
    }

    public String getBgsfhs() {
        return bgsfhs;
    }

    public void setBgsfhs(String bgsfhs) {
        this.bgsfhs = bgsfhs;
    }

    public String getBgjhssl() {
        return bgjhssl;
    }

    public void setBgjhssl(String bgjhssl) {
        this.bgjhssl = bgjhssl;
    }

    public String getBbgms() {
        return bbgms;
    }

    public void setBbgms(String bbgms) {
        this.bbgms = bbgms;
    }

    public String getBbgmsjbzj() {
        return bbgmsjbzj;
    }

    public void setBbgmsjbzj(String bbgmsjbzj) {
        this.bbgmsjbzj = bbgmsjbzj;
    }

    public String getJsyfwfms() {
        return jsyfwfms;
    }

    public void setJsyfwfms(String jsyfwfms) {
        this.jsyfwfms = jsyfwfms;
    }

    public String getJsyrgf() {
        return jsyrgf;
    }

    public void setJsyrgf(String jsyrgf) {
        this.jsyrgf = jsyrgf;
    }

    public String getBbgmsqtfycdr() {
        return bbgmsqtfycdr;
    }

    public void setBbgmsqtfycdr(String bbgmsqtfycdr) {
        this.bbgmsqtfycdr = bbgmsqtfycdr;
    }

    public String getBbgmsqtfyzyfjjfs() {
        return bbgmsqtfyzyfjjfs;
    }

    public void setBbgmsqtfyzyfjjfs(String bbgmsqtfyzyfjjfs) {
        this.bbgmsqtfyzyfjjfs = bbgmsqtfyzyfjjfs;
    }

    public String getBbgsfhs() {
        return bbgsfhs;
    }

    public void setBbgsfhs(String bbgsfhs) {
        this.bbgsfhs = bbgsfhs;
    }

    public String getBbgjhssl() {
        return bbgjhssl;
    }

    public void setBbgjhssl(String bbgjhssl) {
        this.bbgjhssl = bbgjhssl;
    }

    public String getYfkjfplx() {
        return yfkjfplx;
    }

    public void setYfkjfplx(String yfkjfplx) {
        this.yfkjfplx = yfkjfplx;
    }

    public String getYfkjfpsl() {
        return yfkjfpsl;
    }

    public void setYfkjfpsl(String yfkjfpsl) {
        this.yfkjfpsl = yfkjfpsl;
    }

    public String getZcyj() {
        return zcyj;
    }

    public void setZcyj(String zcyj) {
        this.zcyj = zcyj;
    }

    public String getYjzfsj() {
        return yjzfsj;
    }

    public void setYjzfsj(String yjzfsj) {
        this.yjzfsj = yjzfsj;
    }

    public String getTqtzts() {
        return tqtzts;
    }

    public void setTqtzts(String tqtzts) {
        this.tqtzts = tqtzts;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getFbLeft() {
        return fbLeft;
    }

    public void setFbLeft(String fbLeft) {
        this.fbLeft = fbLeft;
    }

    public String getFbRight() {
        return fbRight;
    }

    public void setFbRight(String fbRight) {
        this.fbRight = fbRight;
    }

    public String getGysmcLeft() {
        return gysmcLeft;
    }

    public void setGysmcLeft(String gysmcLeft) {
        this.gysmcLeft = gysmcLeft;
    }

    public String getGysmcRight() {
        return gysmcRight;
    }

    public void setGysmcRight(String gysmcRight) {
        this.gysmcRight = gysmcRight;
    }

    public List<Map<String, Object>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, Object>> detail) {
        this.detail = detail;
    }

    public List<Map<String, String>> getDetail2() {
        return detail2;
    }

    public void setDetail2(List<Map<String, String>> detail2) {
        this.detail2 = detail2;
    }

    public List<ImageInfo> getCzrsfzyyzz() {
        return czrsfzyyzz;
    }

    public void setCzrsfzyyzz(List<ImageInfo> czrsfzyyzz) {
        this.czrsfzyyzz = czrsfzyyzz;
    }

    public List<ImageInfo> getFjs() {
        return fjs;
    }

    public void setFjs(List<ImageInfo> fjs) {
        this.fjs = fjs;
    }

    public List<ImageInfo> getFjw() {
        return fjw;
    }

    public void setFjw(List<ImageInfo> fjw) {
        this.fjw = fjw;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    public String getBgzzsj() {
        return bgzzsj;
    }

    public void setBgzzsj(String bgzzsj) {
        this.bgzzsj = bgzzsj;
    }

    public String getBbgzzsj() {
        return bbgzzsj;
    }

    public void setBbgzzsj(String bbgzzsj) {
        this.bbgzzsj = bbgzzsj;
    }

    public String getClzlfylx() {
        return clzlfylx;
    }

    public void setClzlfylx(String clzlfylx) {
        this.clzlfylx = clzlfylx;
    }

    @Override
    public String toString() {
        return "Contract1{" +
                "bgzzsj='" + bgzzsj + '\'' +
                ", bbgzzsj='" + bbgzzsj + '\'' +
                ", htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", shrdh='" + shrdh + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", zlqxdwy='" + zlqxdwy + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", zlmsjzffs='" + zlmsjzffs + '\'' +
                ", sfdjsy='" + sfdjsy + '\'' +
                ", bgms='" + bgms + '\'' +
                ", bgje='" + bgje + '\'' +
                ", bgsfhs='" + bgsfhs + '\'' +
                ", bgjhssl='" + bgjhssl + '\'' +
                ", bbgms='" + bbgms + '\'' +
                ", bbgmsjbzj='" + bbgmsjbzj + '\'' +
                ", jsyfwfms='" + jsyfwfms + '\'' +
                ", jsyrgf='" + jsyrgf + '\'' +
                ", bbgmsqtfycdr='" + bbgmsqtfycdr + '\'' +
                ", bbgmsqtfyzyfjjfs='" + bbgmsqtfyzyfjjfs + '\'' +
                ", bbgsfhs='" + bbgsfhs + '\'' +
                ", bbgjhssl='" + bbgjhssl + '\'' +
                ", yfkjfplx='" + yfkjfplx + '\'' +
                ", yfkjfpsl='" + yfkjfpsl + '\'' +
                ", zcyj='" + zcyj + '\'' +
                ", yjzfsj='" + yjzfsj + '\'' +
                ", tqtzts='" + tqtzts + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", fbLeft='" + fbLeft + '\'' +
                ", fbRight='" + fbRight + '\'' +
                ", gysmcLeft='" + gysmcLeft + '\'' +
                ", gysmcRight='" + gysmcRight + '\'' +
                ", detail=" + detail +
                ", detail2=" + detail2 +
                ", czrsfzyyzz=" + czrsfzyyzz +
                ", fjs=" + fjs +
                ", fjw=" + fjw +
                ", imageInfoList=" + imageInfoList +
                '}';
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String tableName = requestInfo.getRequestManager().getBillTableName();
        RecordSet rs = null;
        String sql = "select * from " + tableName + " where requestid=" + requestInfo.getRequestid();

        List<Map<String, String>> list = null;
        DetailTable[] detailTable = null;
        Row[] r = null;
        Cell[] c = null;

        ImageInfoManage im = null;
        try {
            im = new ImageInfoManage();
            im.init();

            rs = new RecordSet();
            rs.execute(sql);
            if (!rs.next()) {
                writeLog("sql:  select * from " + tableName + " where requestid=" + requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                throw new Exception("contract 没有查询到合同数据！");
            }

            // 合同模板字段赋值处理
            this.bcyd = Util.null2String(rs.getString("bcyd"));
            this.htbh = Util.null2String(rs.getString("htbh"));
            writeLog("合同编号 ：" + htbh);
            this.fb = JoinFieldManage.getCompanyName(rs.getString("fb"), "subcompanyname");
            writeLog("===fb: " + this.fb);
            this.tyshxydmczf = Util.null2String(rs.getString("tyshxydmczf"));
            this.zsdjf = Util.null2String(rs.getString("zsdjf"));
            this.dzyxjf = Util.null2String(rs.getString("dzyxjf"));
            this.shrdh = Util.null2String(rs.getString("shrdh"));
            this.gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            this.sfzhmtyshxydmczf = Util.null2String(rs.getString("sfzhmtyshxydmczf"));
            this.lxdzyf = Util.null2String(rs.getString("lxdzyf"));
            this.fzryxdz = Util.null2String(rs.getString("fzryxdz"));
            this.fzrlxdh = Util.null2String(rs.getString("fzrlxdh"));
            this.wxhyf = Util.null2String(rs.getString("wxhyf"));
            this.zlqxdwy = Util.null2String(rs.getString("zlqxdwy"));
            writeLog("合同开始日期：" + rs.getString("htkssj"));
            this.htkssj = JoinFieldManage.dateStringToString(rs.getString("htkssj"));
            writeLog("合同结束日期：" + rs.getString("htzzsj"));
            this.htzzsj = JoinFieldManage.dateStringToString(rs.getString("htzzsj"));
            this.zlmsjzffs = String.valueOf(Integer.parseInt(rs.getString("zlmsjzffs")) + 1);
            this.sfdjsy = JoinFieldManage.getSelectName("sfdjsy", tableName, rs.getString("sfdjsy"));
            this.bgms = String.valueOf(rs.getString("bgms"));
            this.bgje = Util.null2String(rs.getString("bgje"));
            this.bgsfhs = JoinFieldManage.getSelectName("bgsfhs", tableName, rs.getString("bgsfhs")) + "税价";
            writeLog("111111");
            this.bgjhssl = JoinFieldManage.getSelectName("bgjhssl", tableName, rs.getString("bgjhssl"));
            writeLog("22222");
            this.bbgms = Util.null2String(rs.getString("bbgms"));
            this.bbgmsjbzj = Util.null2String(rs.getString("bbgmsjbzj"));
            String clzlfylx = Util.null2String(rs.getString("clzlfylx"));
            StringBuilder strv = new StringBuilder();
            if (!"".equals(clzlfylx)) {
                if (clzlfylx.contains("0")) {
                    if (strv.length() > 0) {
                        strv.append(",");
                    }
                    strv.append("油费");
                }
                if (clzlfylx.contains("1")) {
                    if (strv.length() > 0) {
                        strv.append(",");
                    }
                    strv.append("过路费");
                }
                if (clzlfylx.contains("2")) {
                    if (strv.length() > 0) {
                        strv.append(",");
                    }
                    strv.append("停车费");
                }
            }

            String result = strv.toString().replaceAll("^,+", "");

            this.clzlfylx = result;
            this.jsyrgf = Util.null2String(rs.getString("jsyrgf"));
            this.jsyfwfms = Util.null2String(rs.getString("jsyfwfms"));
            writeLog("333333");
            this.bbgmsqtfycdr = JoinFieldManage.getSelectName("bbgmsqtfycdr", tableName, rs.getString("bbgmsqtfycdr")).replace("方", "");
            writeLog("444444");
            this.bbgmsqtfyzyfjjfs = Util.null2String(rs.getString("bbgmsqtfyzyfjjfs"));
            writeLog("55555");
            this.bbgsfhs = JoinFieldManage.getSelectName("bbgsfhs", tableName, rs.getString("bbgsfhs")) + "税价";
            writeLog("66666");
            this.bbgjhssl = JoinFieldManage.getSelectName("bbgjhssl", tableName, rs.getString("bbgjhssl"));
            this.yfkjfplx = JoinFieldManage.getSelectName("yfkjfplx", tableName, rs.getString("yfkjfplx")).replace("发票", "");
            this.yfkjfpsl = JoinFieldManage.getSelectName("yfkjfpsl", tableName, rs.getString("yfkjfpsl"));
            this.zcyj = Util.null2String(rs.getString("zcyj"));
            this.yjzfsj = Util.null2String(rs.getString("yjzfsj"));
            this.tqtzts = Util.null2String(rs.getString("tqtzts"));
            this.zhmc = Util.null2String(rs.getString("zhmc"));
            this.yxkh = Util.null2String(rs.getString("yxkh"));
            this.khyx = Util.null2String(rs.getString("khyx"));

            writeLog("获取明细表二");
            detailTable = requestInfo.getDetailTableInfo().getDetailTable();
            r = detailTable[1].getRow();
            for (int i = 0; i < r.length; ++i) {
                Map<String, Object> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id", String.valueOf(i + 1));
                for (int j = 0; j < c.length; ++j) {
                    if ("zp".equals(c[j].getName())) {
                        String[] zpId = "".equals(c[j].getValue())? new String[]{} :c[j].getValue().split(",");
                        List<ImageInfo> val1 = new ArrayList<>();
                        for (String s : zpId) {
                            ImageInfo imageInfo = im.getImageInfo(s);
                            val1.add(imageInfo);
                            imageInfoList.add(imageInfo);
                        }
                        map.put(c[j].getName(), val1);
                    } else {
                        map.put(c[j].getName(), c[j].getValue());
                    }
                }
                detail.add(map);
            }

            writeLog("获取明细表三");
            detailTable = requestInfo.getDetailTableInfo().getDetailTable();
            r = detailTable[2].getRow();
            for (int i = 0; i < r.length; ++i) {
                Map<String, String> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id", String.valueOf(i + 1));
                for (int j = 0; j < c.length; ++j) {
                    if ("sfns".equals(c[j].getName())) {
                        map.put(c[j].getName(), JoinFieldManage.getSelectName(c[j].getName(), tableName, c[j].getValue()));
                    } else {
                        map.put(c[j].getName(), c[j].getValue());
                    }
                }
                detail2.add(map);
            }


            // 附件
            String[] str = null;

            writeLog("获取附件二 字节码");
            String fjValue = Util.null2String(rs.getString("czrsfzyyzz"));
            if (!"".equals(fjValue)) {
                str = fjValue.split(",");
            } else {
                str = new String[]{};
            }
            for (int i = 0; i < str.length; ++i) {
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.czrsfzyyzz.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件三 二进制字符串");
            fjValue = Util.null2String(rs.getString("fjs"));
            if (!"".equals(fjValue)) {
                str = fjValue.split(",");
            } else {
                str = new String[]{};
            }
            for (int i = 0; i < str.length; ++i) {
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjs.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件五 二进制字符串");
            fjValue = Util.null2String(rs.getString("fjw"));
            if (!"".equals(fjValue)) {
                str = fjValue.split(",");
            } else {
                str = new String[]{};
            }
            for (int i = 0; i < str.length; ++i) {
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                this.fjw.add(imageInfo);
                imageInfoList.add(imageInfo);
            }
            writeLog("赋值完成");

            // 甲乙方 盖章
            int len = 12;

            if (this.fb.length() > len) {
                this.fbLeft = Util.null2String(this.fb.substring(0, len));
                this.fbRight = Util.null2String(this.fb.substring(len));
            } else {
                this.fbLeft = Util.null2String(this.fb);
            }

            if (this.gysmc.length() > len) {
                this.gysmcLeft = Util.null2String(this.gysmc.substring(0, len));
                this.gysmcRight = Util.null2String(this.gysmc.substring(len));
            } else {
                this.gysmcLeft = Util.null2String(this.gysmc);
            }

            // 删除临时附件
            im.deleteFile();

            return Action.SUCCESS;
        } catch (Exception e) {
            writeLog("Contract1   " + e.getMessage());
            e.printStackTrace();
            return Action.FAILURE_AND_CONTINUE;
        }
    }



}

