package weaver.interfaces.workflow.action.cyitce.service;

import com.weaver.general.BaseBean;
import com.weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/15 15:41
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/15      HONOR          v1.0.0               修改原因
 */
public class ImageInfoManage extends BaseBean {
    private int id = 1;
    private String FileSuffix = ".jpg";

//    private String SavePath = this.getClass().getResource("/weaver/interfaces/workflow/action/resources/tempImage").getPath();
    private String SavePath = AddressManagementPool.getResourceAddress("OA")+"/weaver/interfaces/workflow/action/resources/tempImage/";

    private String FileAllPath;

    private File file;

    public void init(){
        SavePath = SavePath+"/"+ UUID.randomUUID();

        File file = new File(SavePath);

        file.mkdirs();
    }


    /**
     *
     * OA平台图片转二进制
     *
     * @param docid 文件id
     * @return
     */
    private byte[] FileToBinary(String docid) {
        writeLog("==开始==");
        if("".equals(docid)||docid==null){
            return new byte[0];
        }

        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        byte[] fileByte = null;

        File file = null;
        FileInputStream fis = null;
        ByteArrayOutputStream bos = null;

        // 获取解压后文件路径
        String unzipfilepath = "";
        String fileSql = "";
        int dex = 0;
        byte[] b = null;
        int len = 0;

        try {
            writeLog("获取图片信息 sql : select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                    + docid + " order by imagefileid desc");
            fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                    + docid + " order by imagefileid desc";
            rs.execute(fileSql);
            if (rs.next()) {
                filename = Util.null2String(rs.getString("imagefilename"));
                filepath = Util.null2String(rs.getString("filerealpath"));

                // 解压文件
                UnZip(filepath, filename);// 把文件解压出来

                writeLog("filename :"+filename);
                FileSuffix = filename.substring(filename.lastIndexOf("."));
                writeLog("FileSuffix :"+FileSuffix);

                if(!".jpge".equals(FileSuffix)&&!".jpg".equals(FileSuffix)&&!".png".equals(FileSuffix)){
                    return new byte[0];
                }

                dex = filepath.lastIndexOf("/");
                writeLog("dex :"+dex);
                if (dex > -1) {
                    unzipfilepath = FileAllPath;   // 获取到解压文件的相对路径
                    writeLog("unzipfilepath :"+unzipfilepath);
                }

                file = new File(unzipfilepath);   // 读取到文件
                this.file = file;

                fis = new FileInputStream(file);  // 把文件读取到输入流中
                bos = new ByteArrayOutputStream();

                b = new byte[1024];
                len = -1;
                while ((len = fis.read(b)) != -1) {
                    bos.write(b, 0, len);   // 输出到这个字节数组输出流。
                }
                fileByte = bos.toByteArray();   // 该输出流的当前内容，作为字节数组。
                writeLog("==字节流=="+fileByte);
            }

            return fileByte;  // 返回二进制文件流
        } catch (Exception e) {
            e.printStackTrace();
            writeLog(e.getMessage());
            return fileByte;
        }
    }

    /**
     * @Description:  解压文件到新目录
     * @param path 文件路径
     * @param realname 解压后文件名称
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/30 11:03
     */
    private void UnZip(String path, String realname) {
        writeLog("开始文件");
        int count = -1;
        int index = -1;

        BufferedOutputStream bos = null;   // 用于接收到文件的输出流
        ZipEntry entry = null;          // zip压缩实例
        FileInputStream fis = null;
        ZipInputStream zis = null;

        File f = null;
        FileOutputStream fos = null;
        String temp = "";
        try {

            fis = new FileInputStream(path);   //  读取文件流
            zis = new ZipInputStream(new BufferedInputStream(fis));  // 该类实现了以ZIP文件格式读取文件的输入流过滤器。 包括对压缩和未压缩条目的支持(读取到缓冲流)。

            while ((entry = zis.getNextEntry()) != null) {   // 文件实例是否为空
                byte[] data = new byte[2048];
                temp = entry.getName();  // 获取文件的名
                index = path.lastIndexOf("."); // 文件的后缀名，得到文件类型
                if (index > -1){
                    temp = path.substring(0, index + 1);  // 拼接后缀名，得到文件的全名
                }

                FileAllPath = SavePath +"/"+ realname;// 获取到文件的全路径（全新的保存在系统）
                f = new File(FileAllPath);
                f.createNewFile();  // 抽象路径名命名的新的空文件
                fos = new FileOutputStream(f);   // 文件输出流
                bos = new BufferedOutputStream(fos, 2048);    //把文件的输出流写入到   文件的缓存输出流
				/*从当前ZIP条目读取到字节数组,文件的大小
				 * b - 读取数据的缓冲区
				off - 目的地阵列中的起始偏移量 b
				len - 读取的最大字节数 */
                while ((count = zis.read(data, 0, 2048)) != -1) {
                    bos.write(data, 0, count);  // 把文件的   以缓存输出流的形式写出去
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                bos.flush();   // 刷新缓冲输出流。
                bos.close();  // 关闭缓存输出流。
                zis.close();  // 关闭此输入流并释放与流相关联的任何系统资源。
            }catch (Exception e){

            }
        }
    }



    /**
     * @Description: 获取图片信息
     * @param docid
     * @return weaver.interfaces.workflow.action.cyitce.entity.ImageInfo
     * @Author:  lijianpan
     * @date: 2022/7/15 15:49
     */
    public ImageInfo getImageInfo(String docid) throws Exception {
        ImageInfo imageInfo = new ImageInfo();

        writeLog("获取图片信息");
        if("".equals(docid)||docid==null){
            return imageInfo;
        }
        writeLog("获取图片字节码");
        byte[] bytes = FileToBinary(docid);

        if(bytes.length<=0){
            writeLog("bytes 为 0");
            return imageInfo;
        }

        writeLog("获取图片尺寸");
        // 图片对象
        BufferedImage bufferedImage = ImageIO.read(new FileInputStream(file));
        float w = bufferedImage.getWidth();
        float h = bufferedImage.getHeight();

        writeLog("w :"+w+"------  h:"+h);

        int height = Math.round(h/w*5040000)>23*360000?23*360000:Math.round(h/w*5040000);
        writeLog("实际值 h:"+h);
        imageInfo.setWidth("5040000");
        imageInfo.setHeight(String.valueOf(height));
        imageInfo.setBytes(bytes);
        imageInfo.setId(id+"");
        imageInfo.setpId("pId"+id);
        imageInfo.setName("image"+id+FileSuffix);
        id+=1;
        writeLog("imageInfo  "+imageInfo.toString());

        return imageInfo;
    }

    public Boolean deleteFile(File file){
        if(file == null||file.exists()){
            return false;
        }

        File[] files = file.listFiles();

        for (File f:files){
            if(f.isDirectory()){
                deleteFile(f);
            }else {
                f.delete();
            }
        }

        file.delete();
        return true;
    }


    public void deleteFile(){
        deleteFile(new File(SavePath));
    }
}
