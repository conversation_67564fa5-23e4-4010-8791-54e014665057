package weaver.interfaces.workflow.action.cyitce.service;

import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import com.weaver.general.Util;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import weaver.interfaces.workflow.action.cyitce.util.CookieUtil;

import java.io.*;
import java.nio.charset.Charset;

public class HttpRequestManage extends BaseBean {

    /** 调用OA上传文件接口（将文件信息存入数据库）
     *
     * @param fileName 文件名称
     * @param fileUrl 文件路径
     * @param url 请求地址
     * @param json_entity body参数
     *
     * @return 文件id
     */
    public String raise(String fileName,String fileUrl, String url,String json_entity){
        writeLog("开始上传附件");
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        HttpPost post=null;
        InputStream content = null;
        BufferedReader in = null;
        String result = "";
        File file1 = null;

        String cookie = "";

        try {
            client = HttpClients.createDefault();
            post = new HttpPost(url);
            file1 = new File(fileUrl);
            //添加参数
            cookie = CookieUtil.getCookies("tendertest0861","Abcd1234");
//            post.addHeader("Cookie", "ecology_JSessionId="+CookieUtil.getCookies("tendertest0861","Abcd1234"));
            post.addHeader("Cookie", "ecology_JSessionId="+cookie);
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setMode(HttpMultipartMode.RFC6532);//解决文件名为中文时乱码
            builder.setCharset(Charset.forName("utf-8"));
//            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);//加上此行代码解决返回中文乱码问题
            builder.addBinaryBody("file",file1, ContentType.MULTIPART_FORM_DATA, fileName);// 文件流
            addBody(builder,json_entity);
            HttpEntity entity = builder.build();
            post.setEntity(entity);
            //执行
            response = client.execute(post);
            HttpEntity responseEntity = response.getEntity();
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                // 返回的内容都在content中
                content = responseEntity.getContent();
                // 定义BufferedReader输入流来读取URL的响应
                in = new BufferedReader(new InputStreamReader(content));
                String line;
                while ((line = in.readLine()) != null) {
                    result += line;
                }
            }
            result = Util.null2String(JSONObject.parseObject(result).getJSONObject("data").getString("fileid"));
            return result;
        }catch(Exception e) {
            e.printStackTrace();
            return "";
        }finally {//处理结束后关闭httpclient的链接
            try {
                client.close();
                content.close();
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public String raise(String fileName,byte[] bytes, String url,String json_entity){
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        HttpPost post=null;
        InputStream content = null;
        BufferedReader in = null;
        String result = "";
        File file1 = null;
        try {
            client = HttpClients.createDefault();
            post = new HttpPost(url);
            //添加参数
            post.addHeader("Cookie", "ecology_JSessionId="+CookieUtil.getCookies("ljp1212","Ljp19970226"));
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setMode(HttpMultipartMode.RFC6532);//解决文件名为中文时乱码
            builder.setCharset(Charset.forName("utf-8"));
//            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);//加上此行代码解决返回中文乱码问题
            builder.addBinaryBody("file",bytes, ContentType.MULTIPART_FORM_DATA, fileName);// 文件流
            addBody(builder,json_entity);
            HttpEntity entity = builder.build();
            post.setEntity(entity);
            //执行
            response = client.execute(post);
            HttpEntity responseEntity = response.getEntity();
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                // 返回的内容都在content中
                content = responseEntity.getContent();
                // 定义BufferedReader输入流来读取URL的响应
                in = new BufferedReader(new InputStreamReader(content));
                String line;
                while ((line = in.readLine()) != null) {
                    result += line;
                }
            }
            result = Util.null2String(JSONObject.parseObject(result).getJSONObject("data").getString("fileid"));

            return result;
        }catch(Exception e) {
            e.printStackTrace();
            return "";
        }finally {//处理结束后关闭httpclient的链接
            try {
                client.close();
                content.close();
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    private void addBody(MultipartEntityBuilder builder,String entity){
        JSONObject jsonObject = JSONObject.parseObject(entity);
        builder.addTextBody("category",jsonObject.getString("category"));
        builder.addTextBody("isFirstUploadFile",jsonObject.getString("isFirstUploadFile"));
        builder.addTextBody("listType",jsonObject.getString("listType"));
        builder.addTextBody("workflowid",jsonObject.getString("workflowid"));
        builder.addTextBody("f_weaver_belongto_userid",jsonObject.getString("f_weaver_belongto_userid"));
        builder.addTextBody("f_weaver_belongto_usertype",jsonObject.getString("f_weaver_belongto_usertype"));
    }
}
