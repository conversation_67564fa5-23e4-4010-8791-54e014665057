package weaver.interfaces.workflow.action.cyitce.service.buyContract;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail.*;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 劳务外协合同实体
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2023/2/28 18:12
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/11      HONOR          v1.0.0               修改原因
 */
public class Contract6 extends BaseBean implements Action {
    /**
     * htbh 采购合同编号
     */
    private String htbh;
    /**
     * 甲方（分包方） fb
     */
    private String fb;
    /**
     * 统一社会信用代码 tyshxydmczf
     */
    private String tyshxydmczf;
    /**
     * 住所地 zsdjf
     */
    private String zsdjf;
    /**
     * 联系人 wxjfjl
     */
    private String wxjfjl;
    /**
     * 电话 wxjfjldh
     */
    private String wxjfjldh;
    /**
     * 微信号 wxhjf
     */
    private String wxhjf;
    /**
     * 电子邮箱 wxjfjlyx
     */
    private String wxjfjlyx;
    /**
     * 乙方（承包方）gysmc
     */
    private String gysmc;
    /**
     * 统一社会信用代码 sfzhmtyshxydmczf
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地 lxdzyf
     */
    private String lxdzyf;
    /**
     * 联系人 ywqyfzr
     */
    private String ywqyfzr;
    /**
     * 电话 fzrlxdh
     */
    private String fzrlxdh;
    /**
     * 微信号 wxhyf
     */
    private String wxhyf;
    /**
     * 电子邮箱（乙方）fzryxdz
     */
    private String fzryxdz;
    /**
     * 资质证书编号（安全生产许可证）zzzsbh1
     */
    private String zzzsbh1;
    /**
     * 资质专业及等级（安全生产许可证）zzzyjdj1
     */
    private String zzzyjdj1;
    /**
     * 发证机关（安全生产许可证）fzjg1
     */
    private String fzjg1;
    /**
     * 有效期（安全生产许可证）yxqstart1，yxqend1
     */
    private String yxqstart1;
    private String yxqend1;
    /**
     * 许可范围（安全生产许可证）xkfw
     */
    private String xkfw;
    /**
     * 资质证书编号（劳务资质）zzzsbh2
     */
    private String zzzsbh2;
    /**
     * 资质专业及等级（劳务资质）zzzyjdj2
     */
    private String zzzyjdj2;
    /**
     * fzjg2	发证机关
     */
    private String fzjg2;
    /**
     * 有效期（劳务资质）yxqstart2，yxqend2
     */
    private String yxqstart2;
    private String yxqend2;
    /**
     * zsmc	证书名称（其他资质）
     */
    private String zsmc;
    /**
     * zzzsbh3	资质证书编号（其他资质）
     */
    private String zzzsbh3;
    /**
     * zzzyjdj3	资质专业及等级（其他资质）
     */
    private String zzzyjdj3;
    /**
     * fzjg3	发证机关（其他资质）
     */
    private String fzjg3;
    /**
     * yxqstart3，yxqend2	有效期（其他资质）
     */
    private String yxqstart3;
    private String yxqend3;
    /**
     * zsmc	证书名称2（其他资质）
     */
    private String zsmc2;
    /**
     * zzzsbh3	资质证书编号4（其他资质）
     */
    private String zzzsbh4;
    /**
     * zzzyjdj3	资质专业及等级4（其他资质）
     */
    private String zzzyjdj4;
    /**
     * fzjg3	发证机关4（其他资质）
     */
    private String fzjg4;
    /**
     * yxqstart4，yxqend4	有效期4（其他资质）
     */
    private String yxqstart4;
    private String yxqend4;
    /**
     * yjxmmc	项目名称
     */
    private String yjxmmc;
    /**
     * xmszsf	项目地点
     */
    private String xmszsf;
    /**
     * fbnr	劳务外协内容
     */
    private String fbnr;
    /**
     * htkssj	合同开始日期
     */
    private String htkssj;
    /**
     * htzzsj	合同终止日期
     */
    private String htzzsj;
    /**
     * bhsjg	合同不含税价预估金额
     * 虚拟字段 bhsjgDX
     */
    private String bhsjg;
    private String bhsjgDX;
    /**
     * slxz	合同签署日的法定税率,%
     */
    private String slxz;
    /**
     * hsjg	合同含税价预估总金额
     * 虚拟字段 hsjgDX
     */
    private String hsjg;
    private String hsjgDX;
    /**
     * nwbcjs	劳务报酬结算方式
     */
    private String nwbcjs;
    /**
     * nwbcgj	固定总价
     * nwbcgjDX 大写
     */
    private String nwbcgj;
    private String nwbcgjDX;
    /**
     * fkfs	付款方式
     */
    private String fkfs;
    /**
     * qtfkfs1 其他付款方式
     */
    private String qtfkfs1;
    /**
     * skhtkhmc	业主方名称
     */
    private String skhtkhmc;
    /**
     * skhtbh	合同编号
     */
    private String skhtbh;
    /**
     * skhtmc	合同名称
     */
    private String skhtmc;
    /**
     * fpzzsl	发票增值税率
     */
    private String fpzzsl;
    /**
     * fplx1	发票类型
     */
    private String fplx1;
    /**
     * lybzjjnsj	履约保证金缴纳时间
     */
    private String lybzjjnsj;
    /**
     * lybzj	履约保证金
     * 虚拟字段 lybzjDX
     */
    private String lybzj;
    private String lybzjDX;
    /**
     * zhmc	户名
     */
    private String zhmc;
    /**
     * yxkh	账号
     */
    private String yxkh;
    /**
     * khyx	开户行
     */
    private String khyx;
    /**
     * yflwwxdz	乙方劳务外协施工队长
     */
    private String yflwwxdz;
    /**
     * yfdzdh	乙方施工队长联系电话
     */
    private String yfdzdh;
    /**
     * yfdzsfz	乙方施工队长身份证号码
     */
    private String yfdzsfz;

    /**--------------九 工程质量管理及验收 start----------------
    /**
     * GCZLgcmc	业主方
     */
    private String GCZLgcmc;
    /**
     * GCZLhtbh	合同编号
     */
    private String GCZLhtbh;
    /**
     * GCZLhtmc	合同名称
     */
    private String GCZLhtmc;
    /**--------------九 工程质量管理及验收 end---------------
     *
     */

    /**
     * gczlbxqx	工程质量保修期限
     */
    private String gczlbxqx;
    /**
     * qcfy	甲方代乙方清场费用
     */
    private String qcfy;
    /**
     * lkgdqx	乙方离开甲方工地期限
     */
    private String lkgdqx;
    /**
     * wjbl	逾期未通过验收违约金比例（千分号）
     */
    private String wjbl;

    /**
     * wxsftjfj11	是否添加附件11
     */
    private String wxsftjfj11;
    /**
     * 明细表四
     */
    private List<Map<String,String>> detail;


    /*----------- 附件 ----------------------*/

    /**
     * 附件一
     */
    private mode3dt1 fj1;
    /**
     * 附件二
     */
    private mode3dt2 fj2;
    /**
     * 附件三
     */
    private mode3dt3 fj3;
    /**
     * 附件四
     */
    private mode3dt4 fj4;

    /*----------- 附件集合 ----------------------*/
    private Map<Integer,String> fileids;

    public Map<Integer,String> getFileids() {
        return fileids;
    }

    public Contract6() {
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.wxjfjl = "";
        this.wxjfjldh = "";
        this.wxhjf = "";
        this.wxjfjlyx = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.ywqyfzr = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.fzryxdz = "";
        this.zzzsbh1 = "";
        this.zzzyjdj1 = "";
        this.fzjg1 = "";
        this.yxqstart1 = "";
        this.yxqend1 = "";
        this.xkfw = "";
        this.zzzsbh2 = "";
        this.zzzyjdj2 = "";
        this.fzjg2 = "";
        this.yxqstart2 = "";
        this.yxqend2 = "";
        this.zsmc = "";
        this.zzzsbh3 = "";
        this.zzzyjdj3 = "";
        this.fzjg3 = "";
        this.yxqstart3 = "";
        this.yxqend3 = "";
        this.zsmc2 = "";
        this.zzzsbh4 = "";
        this.zzzyjdj4 = "";
        this.fzjg4 = "";
        this.yxqstart4 = "";
        this.yxqend4 = "";
        this.yjxmmc = "";
        this.xmszsf = "";
        this.fbnr = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.bhsjg = "";
        this.bhsjgDX = "";
        this.slxz = "";
        this.hsjg = "";
        this.hsjgDX = "";
        this.nwbcjs = "";
        this.nwbcgj = "";
        this.fkfs = "";
        this.qtfkfs1 = "";
        this.skhtkhmc = "";
        this.skhtbh = "";
        this.skhtmc = "";
        this.fpzzsl = "";
        this.fplx1 = "";
        this.lybzjjnsj = "";
        this.lybzj = "";
        this.lybzjDX = "";
        this.zhmc = "";
        this.yxkh = "";
        this.khyx = "";
        this.yflwwxdz = "";
        this.yfdzdh = "";
        this.yfdzsfz = "";
        this.gczlbxqx = "";
        this.qcfy = "";
        this.lkgdqx = "";
        this.wxsftjfj11 = "0";
        this.wjbl = "";
        this.detail = new ArrayList<>();
        this.fj1 = new mode3dt1();
        this.fj2 = new mode3dt2();
        this.fj3 = new mode3dt3();
        this.fj4 = new mode3dt4();
        this.fileids = new HashMap<>();
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getWxjfjl() {
        return wxjfjl;
    }

    public void setWxjfjl(String wxjfjl) {
        this.wxjfjl = wxjfjl;
    }

    public String getWxjfjldh() {
        return wxjfjldh;
    }

    public void setWxjfjldh(String wxjfjldh) {
        this.wxjfjldh = wxjfjldh;
    }

    public String getWxhjf() {
        return wxhjf;
    }

    public void setWxhjf(String wxhjf) {
        this.wxhjf = wxhjf;
    }

    public String getWxjfjlyx() {
        return wxjfjlyx;
    }

    public void setWxjfjlyx(String wxjfjlyx) {
        this.wxjfjlyx = wxjfjlyx;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getZzzsbh1() {
        return zzzsbh1;
    }

    public void setZzzsbh1(String zzzsbh1) {
        this.zzzsbh1 = zzzsbh1;
    }

    public String getZzzyjdj1() {
        return zzzyjdj1;
    }

    public void setZzzyjdj1(String zzzyjdj1) {
        this.zzzyjdj1 = zzzyjdj1;
    }

    public String getFzjg1() {
        return fzjg1;
    }

    public void setFzjg1(String fzjg1) {
        this.fzjg1 = fzjg1;
    }

    public String getYxqstart1() {
        return yxqstart1;
    }

    public void setYxqstart1(String yxqstart1) {
        this.yxqstart1 = yxqstart1;
    }

    public String getYxqend1() {
        return yxqend1;
    }

    public void setYxqend1(String yxqend1) {
        this.yxqend1 = yxqend1;
    }

    public String getXkfw() {
        return xkfw;
    }

    public void setXkfw(String xkfw) {
        this.xkfw = xkfw;
    }

    public String getZzzsbh2() {
        return zzzsbh2;
    }

    public void setZzzsbh2(String zzzsbh2) {
        this.zzzsbh2 = zzzsbh2;
    }

    public String getZzzyjdj2() {
        return zzzyjdj2;
    }

    public void setZzzyjdj2(String zzzyjdj2) {
        this.zzzyjdj2 = zzzyjdj2;
    }

    public String getFzjg2() {
        return fzjg2;
    }

    public void setFzjg2(String fzjg2) {
        this.fzjg2 = fzjg2;
    }

    public String getYxqstart2() {
        return yxqstart2;
    }

    public void setYxqstart2(String yxqstart2) {
        this.yxqstart2 = yxqstart2;
    }

    public String getYxqend2() {
        return yxqend2;
    }

    public void setYxqend2(String yxqend2) {
        this.yxqend2 = yxqend2;
    }

    public String getZsmc() {
        return zsmc;
    }

    public void setZsmc(String zsmc) {
        this.zsmc = zsmc;
    }

    public String getZzzsbh3() {
        return zzzsbh3;
    }

    public void setZzzsbh3(String zzzsbh3) {
        this.zzzsbh3 = zzzsbh3;
    }

    public String getZzzyjdj3() {
        return zzzyjdj3;
    }

    public void setZzzyjdj3(String zzzyjdj3) {
        this.zzzyjdj3 = zzzyjdj3;
    }

    public String getFzjg3() {
        return fzjg3;
    }

    public void setFzjg3(String fzjg3) {
        this.fzjg3 = fzjg3;
    }

    public String getYxqstart3() {
        return yxqstart3;
    }

    public void setYxqstart3(String yxqstart3) {
        this.yxqstart3 = yxqstart3;
    }

    public String getYxqend3() {
        return yxqend3;
    }

    public void setYxqend3(String yxqend3) {
        this.yxqend3 = yxqend3;
    }

    public String getZsmc2() {
        return zsmc2;
    }

    public void setZsmc2(String zsmc2) {
        this.zsmc2 = zsmc2;
    }

    public String getZzzsbh4() {
        return zzzsbh4;
    }

    public void setZzzsbh4(String zzzsbh4) {
        this.zzzsbh4 = zzzsbh4;
    }

    public String getZzzyjdj4() {
        return zzzyjdj4;
    }

    public void setZzzyjdj4(String zzzyjdj4) {
        this.zzzyjdj4 = zzzyjdj4;
    }

    public String getFzjg4() {
        return fzjg4;
    }

    public void setFzjg4(String fzjg4) {
        this.fzjg4 = fzjg4;
    }

    public String getYxqstart4() {
        return yxqstart4;
    }

    public void setYxqstart4(String yxqstart4) {
        this.yxqstart4 = yxqstart4;
    }

    public String getYxqend4() {
        return yxqend4;
    }

    public void setYxqend4(String yxqend4) {
        this.yxqend4 = yxqend4;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getXmszsf() {
        return xmszsf;
    }

    public void setXmszsf(String xmszsf) {
        this.xmszsf = xmszsf;
    }

    public String getFbnr() {
        return fbnr;
    }

    public void setFbnr(String fbnr) {
        this.fbnr = fbnr;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getBhsjg() {
        return bhsjg;
    }

    public void setBhsjg(String bhsjg) {
        this.bhsjg = bhsjg;
    }

    public String getBhsjgDX() {
        return bhsjgDX;
    }

    public void setBhsjgDX(String bhsjgDX) {
        this.bhsjgDX = bhsjgDX;
    }

    public String getSlxz() {
        return slxz;
    }

    public void setSlxz(String slxz) {
        this.slxz = slxz;
    }

    public String getHsjg() {
        return hsjg;
    }

    public void setHsjg(String hsjg) {
        this.hsjg = hsjg;
    }

    public String getHsjgDX() {
        return hsjgDX;
    }

    public void setHsjgDX(String hsjgDX) {
        this.hsjgDX = hsjgDX;
    }

    public String getNwbcjs() {
        return nwbcjs;
    }

    public void setNwbcjs(String nwbcjs) {
        this.nwbcjs = nwbcjs;
    }

    public String getNwbcgj() {
        return nwbcgj;
    }

    public void setNwbcgj(String nwbcgj) {
        this.nwbcgj = nwbcgj;
    }

    public String getNwbcgjDX() {
        return nwbcgjDX;
    }

    public void setNwbcgjDX(String nwbcgjDX) {
        this.nwbcgjDX = nwbcgjDX;
    }

    public String getFkfs() {
        return fkfs;
    }

    public void setFkfs(String fkfs) {
        this.fkfs = fkfs;
    }

    public String getQtfkfs1() {
        return qtfkfs1;
    }

    public void setQtfkfs1(String qtfkfs1) {
        this.qtfkfs1 = qtfkfs1;
    }

    public String getSkhtkhmc() {
        return skhtkhmc;
    }

    public void setSkhtkhmc(String skhtkhmc) {
        this.skhtkhmc = skhtkhmc;
    }

    public String getSkhtbh() {
        return skhtbh;
    }

    public void setSkhtbh(String skhtbh) {
        this.skhtbh = skhtbh;
    }

    public String getSkhtmc() {
        return skhtmc;
    }

    public void setSkhtmc(String skhtmc) {
        this.skhtmc = skhtmc;
    }

    public String getFpzzsl() {
        return fpzzsl;
    }

    public void setFpzzsl(String fpzzsl) {
        this.fpzzsl = fpzzsl;
    }

    public String getFplx1() {
        return fplx1;
    }

    public void setFplx1(String fplx1) {
        this.fplx1 = fplx1;
    }

    public String getLybzjjnsj() {
        return lybzjjnsj;
    }

    public void setLybzjjnsj(String lybzjjnsj) {
        this.lybzjjnsj = lybzjjnsj;
    }

    public String getLybzj() {
        return lybzj;
    }

    public void setLybzj(String lybzj) {
        this.lybzj = lybzj;
    }

    public String getLybzjDX() {
        return lybzjDX;
    }

    public void setLybzjDX(String lybzjDX) {
        this.lybzjDX = lybzjDX;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYflwwxdz() {
        return yflwwxdz;
    }

    public void setYflwwxdz(String yflwwxdz) {
        this.yflwwxdz = yflwwxdz;
    }

    public String getYfdzdh() {
        return yfdzdh;
    }

    public void setYfdzdh(String yfdzdh) {
        this.yfdzdh = yfdzdh;
    }

    public String getYfdzsfz() {
        return yfdzsfz;
    }

    public void setYfdzsfz(String yfdzsfz) {
        this.yfdzsfz = yfdzsfz;
    }

    public String getGCZLgcmc() {
        return GCZLgcmc;
    }

    public void setGCZLgcmc(String GCZLgcmc) {
        this.GCZLgcmc = GCZLgcmc;
    }

    public String getGCZLhtbh() {
        return GCZLhtbh;
    }

    public void setGCZLhtbh(String GCZLhtbh) {
        this.GCZLhtbh = GCZLhtbh;
    }

    public String getGCZLhtmc() {
        return GCZLhtmc;
    }

    public void setGCZLhtmc(String GCZLhtmc) {
        this.GCZLhtmc = GCZLhtmc;
    }

    public String getGczlbxqx() {
        return gczlbxqx;
    }

    public void setGczlbxqx(String gczlbxqx) {
        this.gczlbxqx = gczlbxqx;
    }

    public String getQcfy() {
        return qcfy;
    }

    public void setQcfy(String qcfy) {
        this.qcfy = qcfy;
    }

    public String getLkgdqx() {
        return lkgdqx;
    }

    public void setLkgdqx(String lkgdqx) {
        this.lkgdqx = lkgdqx;
    }

    public List<Map<String, String>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, String>> detail) {
        this.detail = detail;
    }

    public mode3dt1 getFj1() {
        return fj1;
    }

    public void setFj1(mode3dt1 fj1) {
        this.fj1 = fj1;
    }

    public mode3dt2 getFj2() {
        return fj2;
    }

    public void setFj2(mode3dt2 fj2) {
        this.fj2 = fj2;
    }

    public mode3dt3 getFj3() {
        return fj3;
    }

    public void setFj3(mode3dt3 fj3) {
        this.fj3 = fj3;
    }

    public mode3dt4 getFj4() {
        return fj4;
    }

    public void setFj4(mode3dt4 fj4) {
        this.fj4 = fj4;
    }

    public String getWxsftjfj11() {
        return wxsftjfj11;
    }

    public void setWxsftjfj11(String wxsftjfj11) {
        this.wxsftjfj11 = wxsftjfj11;
    }

    public void setFileids(Map<Integer, String> fileids) {
        this.fileids = fileids;
    }

    public String getWjbl() {
        return wjbl;
    }

    public void setWjbl(String wjbl) {
        this.wjbl = wjbl;
    }

    @Override
    public String toString() {
        return "Contract6{" +
                "htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", wxjfjl='" + wxjfjl + '\'' +
                ", wxjfjldh='" + wxjfjldh + '\'' +
                ", wxhjf='" + wxhjf + '\'' +
                ", wxjfjlyx='" + wxjfjlyx + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", zzzsbh1='" + zzzsbh1 + '\'' +
                ", zzzyjdj1='" + zzzyjdj1 + '\'' +
                ", fzjg1='" + fzjg1 + '\'' +
                ", yxqstart1='" + yxqstart1 + '\'' +
                ", yxqend1='" + yxqend1 + '\'' +
                ", xkfw='" + xkfw + '\'' +
                ", zzzsbh2='" + zzzsbh2 + '\'' +
                ", zzzyjdj2='" + zzzyjdj2 + '\'' +
                ", fzjg2='" + fzjg2 + '\'' +
                ", yxqstart2='" + yxqstart2 + '\'' +
                ", yxqend2='" + yxqend2 + '\'' +
                ", zsmc='" + zsmc + '\'' +
                ", zzzsbh3='" + zzzsbh3 + '\'' +
                ", zzzyjdj3='" + zzzyjdj3 + '\'' +
                ", fzjg3='" + fzjg3 + '\'' +
                ", yxqstart3='" + yxqstart3 + '\'' +
                ", yxqend3='" + yxqend3 + '\'' +
                ", zsmc2='" + zsmc2 + '\'' +
                ", zzzsbh4='" + zzzsbh4 + '\'' +
                ", zzzyjdj4='" + zzzyjdj4 + '\'' +
                ", fzjg4='" + fzjg4 + '\'' +
                ", yxqstart4='" + yxqstart4 + '\'' +
                ", yxqend4='" + yxqend4 + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", xmszsf='" + xmszsf + '\'' +
                ", fbnr='" + fbnr + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", bhsjg='" + bhsjg + '\'' +
                ", bhsjgDX='" + bhsjgDX + '\'' +
                ", slxz='" + slxz + '\'' +
                ", hsjg='" + hsjg + '\'' +
                ", hsjgDX='" + hsjgDX + '\'' +
                ", nwbcjs='" + nwbcjs + '\'' +
                ", nwbcgj='" + nwbcgj + '\'' +
                ", nwbcgjDX='" + nwbcgjDX + '\'' +
                ", fkfs='" + fkfs + '\'' +
                ", qtfkfs1='" + qtfkfs1 + '\'' +
                ", skhtkhmc='" + skhtkhmc + '\'' +
                ", skhtbh='" + skhtbh + '\'' +
                ", skhtmc='" + skhtmc + '\'' +
                ", fpzzsl='" + fpzzsl + '\'' +
                ", fplx1='" + fplx1 + '\'' +
                ", lybzjjnsj='" + lybzjjnsj + '\'' +
                ", lybzj='" + lybzj + '\'' +
                ", lybzjDX='" + lybzjDX + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", yflwwxdz='" + yflwwxdz + '\'' +
                ", yfdzdh='" + yfdzdh + '\'' +
                ", yfdzsfz='" + yfdzsfz + '\'' +
                ", GCZLgcmc='" + GCZLgcmc + '\'' +
                ", GCZLhtbh='" + GCZLhtbh + '\'' +
                ", GCZLhtmc='" + GCZLhtmc + '\'' +
                ", gczlbxqx='" + gczlbxqx + '\'' +
                ", qcfy='" + qcfy + '\'' +
                ", lkgdqx='" + lkgdqx + '\'' +
                ", wjbl='" + wjbl + '\'' +
                ", wxsftjfj11='" + wxsftjfj11 + '\'' +
                ", detail=" + detail +
                ", fj1=" + fj1 +
                ", fj2=" + fj2 +
                ", fj3=" + fj3 +
                ", fj4=" + fj4 +
                ", fileids=" + fileids +
                '}';
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        List<String> list = new ArrayList<>();
        String table = requestInfo.getRequestManager().getBillTableName();
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
        writeLog("4123412");
        ConverToChinesePart cp = null;
        writeLog("sql:    select * from "+table+" where requestid="+requestInfo.getRequestid());
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();
        RecordSet rs = null;
        Row[] r = null;
        Cell[] c = null;

        ImageInfoManage im = null;
        try {
            writeLog("896548");

            cp = new ConverToChinesePart();


            rs = new RecordSet();
            rs.execute(sql);
            if(!rs.next()){
                writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                throw new Exception("contract 没有查询到合同数据！");
            }

            //附件
            fj1.data(rs);
            fj1.execute(requestInfo);
            fj2.data(rs);
            fj2.execute(requestInfo);
            fj3.data(rs);
            fj3.execute(requestInfo);
            fj4.data(rs);
            fj4.execute(requestInfo);


            htbh = Util.null2String(rs.getString("htbh"));
            writeLog("合同编号 ："+htbh);
            fb = JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")),"subcompanyname");
            writeLog("===fb: "+this.fb);
            tyshxydmczf = Util.null2String(rs.getString("tyshxydmczf"));
            zsdjf = Util.null2String(rs.getString("zsdjf"));
            wxjfjl = JoinFieldManage.getPersonName(rs.getString("wxjfjl"));
            wxjfjldh = Util.null2String(rs.getString("wxjfjldh"));
            wxhjf = Util.null2String(rs.getString("wxhjf"));
            wxjfjlyx = Util.null2String(rs.getString("wxjfjlyx"));
            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            sfzhmtyshxydmczf = Util.null2String(rs.getString("sfzhmtyshxydmczf"));
            lxdzyf = Util.null2String(rs.getString("lxdzyf"));
            ywqyfzr = Util.null2String(rs.getString("ywqyfzr"));
            fzrlxdh = Util.null2String(rs.getString("fzrlxdh"));
            wxhyf = Util.null2String(rs.getString("wxhyf"));
            fzryxdz = Util.null2String(rs.getString("fzryxdz"));
            zzzsbh1 = Util.null2String(rs.getString("zzzsbh1"));
            zzzyjdj1 = Util.null2String(rs.getString("zzzyjdj1"));
            fzjg1 = Util.null2String(rs.getString("fzjg1"));
            yxqstart1 = JoinFieldManage.dateStringToString(rs.getString("yxqstart1"));
            yxqend1 = JoinFieldManage.dateStringToString(rs.getString("yxqend1"));
            xkfw = Util.null2String(rs.getString("xkfw"));

            zzzsbh2 = Util.null2String(rs.getString("zzzsbh2"));
            zzzyjdj2 = Util.null2String(rs.getString("zzzyjdj2"));
            fzjg2 = Util.null2String(rs.getString("fzjg2"));
            yxqstart2 = JoinFieldManage.dateStringToString(rs.getString("yxqstart2"));
            yxqend2 = JoinFieldManage.dateStringToString(rs.getString("yxqend2"));

            //其他资质
            zsmc = nullToStr(rs.getString("zsmc"));
            zzzsbh3 = nullToStr(rs.getString("zzzsbh3"));
            zzzyjdj3 = nullToStr(rs.getString("zzzyjdj3"));
            fzjg3 = nullToStr(rs.getString("fzjg3"));
            yxqstart3 = nullToStr(JoinFieldManage.dateStringToString(rs.getString("yxqstart3")));
            yxqend3 = nullToStr(JoinFieldManage.dateStringToString(rs.getString("yxqend3")));
            zsmc2 = nullToStr(rs.getString("zsmc2"));
            zzzsbh4 = nullToStr(rs.getString("zzzsbh4"));
            zzzyjdj4 = nullToStr(rs.getString("zzzyjdj4"));
            fzjg4 = nullToStr(rs.getString("fzjg4"));
            yxqstart4 = nullToStr(JoinFieldManage.dateStringToString(rs.getString("yxqstart4")));
            yxqend4 = nullToStr(JoinFieldManage.dateStringToString(rs.getString("yxqend4")));

            //--------------------------二 start----------------------------------------------
            yjxmmc = nullToStr(JoinFieldManage.getProjectName(rs.getString("yjxmmc")));
            xmszsf = nullToStr(JoinFieldManage.getProvinceName(rs.getString("xmszsf")));
            fbnr = nullToStr(idToStringLWWX(rs.getString("wxlwwx"),rs.getString("fbnr")));
            //--------------------------二 end----------------------------------------------

            //-----------------三 start -----------------------------
            htkssj = JoinFieldManage.dateStringToString(rs.getString("htkssj"));
            htzzsj = JoinFieldManage.dateStringToString(rs.getString("htzzsj"));
            //-----------------三 end -----------------------------

            //-------------------五 start ------------------------------------
            bhsjg = nullToStr(rs.getString("bhsjg"));
            writeLog("五 1");
            bhsjgDX = nullToStr(cp.convertToChinese(Util.null2String(rs.getString("bhsjg"),"0")));
            String slxzVals = Util.null2String(rs.getString("sldx"));
            String slxzValsStr = "";
            if(!"".equals(slxzVals)){
                String[] slxzs = slxzVals.split(",");
                for (String s:slxzs){
                    slxzValsStr += JoinFieldManage.getSelectName("sldx",table,s)+" ";
                }
            }
            slxz = slxzValsStr;

            hsjg = nullToStr(rs.getString("hsjg"));
            writeLog("五 2");
            hsjgDX = nullToStr(cp.convertToChinese(Util.null2String(rs.getString("hsjg"),"0")));
            writeLog("五 3");
            if("0".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "一";
            }else if("1".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "二";
            }else if("2".equals(Util.null2String(rs.getString("nwbcjs")))){
                nwbcjs = "三";
            }
            writeLog("五 4");
            nwbcgj = Util.null2String(rs.getString("nwbcgj"));
            nwbcgjDX = nullToStr(cp.convertToChinese(rs.getString("nwbcgj")));
            fkfs = Util.null2String(rs.getString("fkfs"));
            qtfkfs1 = Util.null2String(rs.getString("qtfkfs1"));
            //特别约定
            skhtkhmc = nullToStr(JoinFieldManage.getCustomerName(rs.getString("skhtkhmc")));
            skhtbh = nullToStr(rs.getString("skhtbh"));
            writeLog("五 5");
            skhtmc = nullToStr(JoinFieldManage.getContractName(rs.getString("skhtmc")));

            String fpzzslVals = Util.null2String(rs.getString("fpzzsl"));
            String fpzzslValsStr = "";
            if(!"".equals(fpzzslVals)){
                String[] fpzzsls = fpzzslVals.split(",");
                for (String s:fpzzsls){
                    fpzzslValsStr += JoinFieldManage.getSelectName("fpzzsl",table,s)+" ";
                }
            }
            fpzzsl = fpzzslValsStr;

            writeLog("五 6");
            fplx1 = JoinFieldManage.getSelectName("fplx1",table,rs.getString("fplx1"));
            lybzjjnsj = Util.null2String(rs.getString("lybzjjnsj"));
            lybzj = Util.null2String(rs.getString("lybzj"));
            writeLog("五 7");
            lybzjDX = cp.convertToChinese(Util.null2String(rs.getString("lybzj"),"0"));
            writeLog("五 8");
            //-------------------五 end ------------------------------------

            //------------------六 start----------------------------
            zhmc = Util.null2String(rs.getString("zhmc"));
            yxkh = Util.null2String(rs.getString("yxkh"));
            writeLog("六");
            khyx = Util.null2String(rs.getString("khyx"));
            //------------------六 end----------------------------

            //------------------八 施工与设计变更 start----------------------------
            yflwwxdz = Util.null2String(rs.getString("yflwwxdz"));
            yfdzdh = Util.null2String(rs.getString("yfdzdh"));
            yfdzsfz = Util.null2String(rs.getString("yfdzsfz"));
            //------------------八 施工与设计变更 end----------------------------

            //--------------九 工程质量管理及验收 start----------------
            GCZLgcmc = nullToStr(skhtkhmc);
            GCZLhtbh = nullToStr(skhtbh);
            GCZLhtmc = nullToStr(skhtmc);
            //--------------九 工程质量管理及验收 end---------------

            //---------------------------十一 start--------------------------------
            gczlbxqx = Util.null2String(rs.getString("gczlbxqx"));
            //---------------------------十一 end--------------------------------

            //---------------------------十二 start--------------------------------
            qcfy = Util.null2String(rs.getString("qcfy"));
            lkgdqx = Util.null2String(rs.getString("lkgdqx"));
            //---------------------------十二 end--------------------------------

            //---------------------------十三 start--------------------------------
            wjbl = saveSmallNum(Util.null2String(rs.getString("wjbl")));
            //---------------------------十三 end--------------------------------

            wxsftjfj11 = Util.null2String(rs.getString("wxsftjfj11"));
            //明细表4
            r = detailTable[3].getRow();
            if(r.length==0){
                writeLog("mx4 length == 0");
                Map<String,String> map = new HashMap<>();
                map.put("id","/");
                map.put("gzqy","/");
                map.put("bl","/");
                map.put("cnsl","/");
                map.put("bz","/");
                detail.add(map);
            }else {
                for (int i = 0;i<r.length;++i){
                    Map<String,String> map = new HashMap<>();
                    c = r[i].getCell();
                    map.put("id",String.valueOf(i+1));
                    for (int j = 0;j<c.length;++j){
                        writeLog(c[j].getName()+"---"+c[j].getValue());
                        String name = c[j].getName();
                        String value = Util.null2String(c[j].getValue());

                        try {
                            if("bl".equals(name)){
                                value = "".equals(value)?String.format("%.2f",Double.parseDouble(value)*100):value;
                            }else if("cnsl".equals(name)){
                                value = "".equals(value)?String.format("%.1f",Double.parseDouble(value)*100):value;
                            }
                        }catch (Exception e){
                            value = "";
                        }

                        map.put(name,value);
                    }
                    this.detail.add(map);
                }
            }

            writeLog("获取附件五 字节码");

            if(!isNull(rs.getString("fjw"))){
                fileids.put(5,rs.getString("fjw"));
            }
            if(!isNull(rs.getString("fjl"))){
                fileids.put(6,rs.getString("fjl"));
            }
            if(!isNull(rs.getString("fjq"))){
                fileids.put(7,rs.getString("fjq"));
            }
            if(!isNull(rs.getString("fjb"))){
                fileids.put(8,rs.getString("fjb"));
            }
            if(!isNull(rs.getString("fjj"))){
                fileids.put(9,rs.getString("fjj"));
            }
            if(!isNull(rs.getString("fjshi"))){
                fileids.put(10,rs.getString("fjshi"));
            }

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            writeLog("Contract6 报错"+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    /**
     * @description:为空时返回“/”
     * @author: lijianpan
     * @date: 2023/2/28
     * @param: [str]
     * @return: java.lang.String
     **/
    public String nullToStr(String str){
        return CommonUtil.nullToStr(str,"/");
    }


    public String idToStringLWWX(String ids,String qt){
        if("".equals(Util.null2String(ids))){
            return "";
        }
        StringBuffer content = new StringBuffer();

        String str[] = ids.split(",");
        RecordSet rs = new RecordSet();
        for (int i=0;i<str.length;++i){
            rs.execute("select nr from uf_wxlwwxnr where id="+str[i]);
            if(rs.next()){
                if("17".equals(str[i])){
                    content.append(Util.null2String(qt));
                }else {
                    content.append(Util.null2String(rs.getString(1)));
                }

                if(i!=(str.length-1)){
                    content.append("、");
                }
            }
        }
        return content.toString();
    }

    public boolean isNull(String val){
        if("".equals(Util.null2String(val))){
            return true;
        }

        return false;
    }

    public String saveSmallNum(String str){
        String dexStr = "";
        String num = str;
        if(str.lastIndexOf(".")!=-1){
            num = str.substring(0,str.lastIndexOf("."));
            dexStr = str.substring(str.lastIndexOf(".")+1);
            int len = dexStr.length()-1;

            while (len>=0&&"0".equals(dexStr.substring(len,len+1))){
                --len;
            }

            if(len>=0){
                dexStr = "."+dexStr.substring(0,len+1);
            }else {
                dexStr = "";
            }
        }

        return num+dexStr;
    }
}
