package weaver.interfaces.workflow.action.cyitce.service.buyContract;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description: 材料设备合同实体
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/13 9:25
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/13      HONOR          v1.0.0               修改原因
 */
public class Contract4 extends BaseBean implements Action {

    /**
     * 补充协议
     */
    private String bcyd;


    /**
     * 税率选择
     */
    private String slxz;


    /**
     * 增值税金
     */
    private String zzsj;

    /**
     * 大写增值税金
     */
    private String dxzzsj;
    /**
     * htbh 采购合同编号
     */
    private String htbh;
    /**
     * 甲方（购货方） fb
     */
    private String fb;
    /**
     * 统一社会信用代码 tyshxydmczf
     */
    private String tyshxydmczf;
    /**
     * 住所地 zsdjf
     */
    private String zsdjf;
    /**
     * 联系人 lxrjf
     */
    private String lxrjf;
    /**
     * 职务（甲方）
     */
    private String jflxrzw;
    /**
     * 电子邮箱 dzyxjf
     */
    private String dzyxjf;
    /**
     * 电话 lxdhjf
     */
    private String lxdhjf;
    /**
     * 微信号 wxhjf
     */
    private String wxhjf;
    /**
     * 乙方（供货方）gysmc
     */
    private String gysmc;
    /**
     * 身份证号码/统一社会信用代码 sfzhmtyshxydmczf
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地 lxdzyf
     */
    private String lxdzyf;
    /**
     * 联系人 ywqyfzr
     */
    private String ywqyfzr;
    /**
     * 职务（乙方）
     */
    private String gyslxrzw;
    /**
     * 电子邮箱（乙方）fzryxdz
     */
    private String fzryxdz;
    /**
     * 电话 fzrlxdh
     */
    private String fzrlxdh;
    /**
     * 微信号 wxhyf
     */
    private String wxhyf;
    /**
     * bhsjg 合同不含税金额
     * 虚拟字段 bhsjgDX 合同不含税金额大写
     */
    private String bhsjg;
    private String bhsjgDX;
    /**
     * sldx	合同签署日的法定税率
     */
    private String sldx;
    /**
     * hsjg	合同含税金额
     * 虚拟字段 hsjgDX 合同含税金额大写
     */
    private String hsjg;
    private String hsjgDX;
    /**
     * fkfslw	付款形式
     */
    private String fkfslw;
    /**
     * zfhtjkfs	支付合同价款方式
     */
    private String zfhtjkfs;
    /**
     * fsy	方式一
     */
    private String fsy;
    /**
     * zfdjqx	支付定金期限（合同生效之日起）
     */
    private String zfdjqx;
    /**
     * djje	定金金额
     * 虚拟字段 djjeDX	定金金额大写
     */
    private String djje;
    private String djjeDX;
    /**
     * hgyszfqx 甲方书面验收合格支付期限
     */
    private String hgyszfqx;
    /**
     * htzjkzfbl	合同总价款支付比例
     */
    private String htzjkzfbl;
    /**
     * zfje	支付金额
     * 虚拟字段 zfjeDX 支付金额大写
     */
    private String zfje;
    private String zfjeDX;
    /**
     * ylhtzjkbfb	余留合同总价款百分比
     */
    private String ylhtzjkbfb;
    /**
     * zbjje	质保金金额
     * 虚拟字段 zbjjeDX 质保金金额大写
     */
    private String zbjje;
    private String zbjjeDX;
    /**
     * zfdjqx2	支付定金期限（合同生效之日起）
     */
    private String zfdjqx2;
    /**
     * djje2	定金金额
     * 虚拟字段 djje2DX	定金金额大写
     */
    private String djje2;
    private String djje2DX;
    /**
     * hgyszfqx2	甲方初验收合格支付期限
     */
    private String hgyszfqx2;
    /**
     * htzjkzfbl2	合同总价款支付比例
     */
    private String htzjkzfbl2;
    /**
     * zfje2	支付金额
     * 虚拟字段 zfje2DX	支付金额大写
     */
    private String zfje2;
    private String zfje2DX;
    /**
     * jfysrn	本合同项下所有设备完成安装调试取得业主方出具的验收报告且经甲方验收合格之日起【  】日
     */
    private String jfysrn;
    /**
     * htzjkzfbl3	合同总价款支付比例
     */
    private String htzjkzfbl3;
    /**
     * zfje3	支付金额
     * 虚拟字段 zfje3DX 支付金额大写
     */
    private String zfje3;
    private String zfje3DX;
    /**
     * jfysrn2	本合同项下所有设备通过试运行阶段取得业主方出具的试运行报告且经甲方终验收合格之日起【  】日
     */
    private String jfysrn2;
    /**
     * htzjkzfbl4 合同总价款支付比例
     */
    private String htzjkzfbl4;
    /**
     * zfje4	支付金额
     * 虚拟字段 zfje4DX 支付金额大写
     */
    private String zfje4;
    private String zfje4DX;
    /**
     * ylhtzjkbfb2	余留合同总价款百分比
     */
    private String ylhtzjkbfb2;
    /**
     * zbjje2	质保金金额
     * 虚拟字段 zbjje2DX 质保金金额大写
     */
    private String zbjje2;
    private String zbjje2DX;
    /**
     * qtfkfs	方式四：其他付款方式
     */
    private String qtfkfs;
    /**
     * yffkfs	乙方按照以下第【X】方式向甲方开具并交付增值税发票
     */
    private String yffkfs;
    /**
     * zhmc	乙方名称
     */
    private String zhmc;
    /**
     * gsnsrsbh	国税纳税人识别号
     */
    private String gsnsrsbh;
    /**
     * khyx	开户行
     */
    private String khyx;
    /**
     * yxkh	账号
     */
    private String yxkh;
    /**
     * yfzfsj	乙方应在本合同签署后     日内完成交付
     */
    private String yfzfsj;
    /**
     * shrxxdz	甲方指定的收货地址及收货人-收货人联系电话
     */
    private String shrxxdz;
    /**
     * 收货人
     */
    private String shr;
    /**
     * 收货人联系电话
     */
    private String shrdh;
    /**
     * jhfs	交货方式
     */
    private String jhfs;
    /**
     * zthdz	自提货物地址
     */
    private String zthdz;
    /**
     * zthlxr 自提货物联系人
     */
    private String zthlxr;

    /**
     * ysrxm 验收人姓名
     */
    private String ysrxm;
    /**
     * wyfyxsyfzfwy	违约方应向守约方支付X万元
     */
    private String wyfyxsyfzfwy;

    /**
     * 明细表六
     */
    private List<Map<String, String>> detail;

    /**
     * 甲方（签章）名称前半段
     */
    private String fbLeft;
    /**
     * 甲方（签章）名称后半段
     */
    private String fbRight;
    /**
     * 乙方（签章）名称前半段
     */
    private String gysmcLeft;
    /**
     * 乙方（签章）名称后半段
     */
    private String gysmcRight;

    /*----------- 附件 ----------------------*/
    /**
     * 附件一
     */
    private List<ImageInfo> zlfwcqz;
    /**
     * 附件二
     */
    private List<ImageInfo> czrsfzyyzz;

    /*----------- 附件集合 ----------------------*/
    private List<ImageInfo> imageInfoList;


    public Contract4() {
        this.zzsj = "";
        this.dxzzsj = "";
        this.bcyd = "";
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.lxrjf = "";
        this.jflxrzw = "";
        this.dzyxjf = "";
        this.lxdhjf = "";
        this.wxhjf = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.ywqyfzr = "";
        this.gyslxrzw = "";
        this.fzryxdz = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.bhsjg = "";
        this.bhsjgDX = "";
        this.sldx = "";
        this.hsjg = "";
        this.hsjgDX = "";
        this.fkfslw = "";
        this.zfhtjkfs = "";
        this.fsy = "";
        this.zfdjqx = "";
        this.djje = "";
        this.djjeDX = "";
        this.hgyszfqx = "";
        this.htzjkzfbl = "";
        this.zfje = "";
        this.zfjeDX = "";
        this.ylhtzjkbfb = "";
        this.zbjje = "";
        this.zbjjeDX = "";
        this.zfdjqx2 = "";
        this.djje2 = "";
        this.djje2DX = "";
        this.hgyszfqx2 = "";
        this.htzjkzfbl2 = "";
        this.zfje2 = "";
        this.zfje2DX = "";
        this.jfysrn = "";
        this.htzjkzfbl3 = "";
        this.zfje3 = "";
        this.zfje3DX = "";
        this.jfysrn2 = "";
        this.htzjkzfbl4 = "";
        this.zfje4 = "";
        this.zfje4DX = "";
        this.ylhtzjkbfb2 = "";
        this.zbjje2 = "";
        this.zbjje2DX = "";
        this.qtfkfs = "";
        this.yffkfs = "";
        this.zhmc = "";
        this.gsnsrsbh = "";
        this.khyx = "";
        this.yxkh = "";
        this.yfzfsj = "";
        this.shrxxdz = "";
        this.shr = "";
        this.shrdh = "";
        this.jhfs = "";
        this.zthdz = "";
        this.zthlxr = "";
        this.ysrxm = "";
        this.wyfyxsyfzfwy = "";
        this.fbLeft = "";
        this.fbRight = "";
        this.gysmcLeft = "";
        this.gysmcRight = "";
        this.zlfwcqz = new ArrayList<>();
        this.czrsfzyyzz = new ArrayList<>();
        this.imageInfoList = new ArrayList<>();
        this.detail = new ArrayList<>();
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getLxrjf() {
        return lxrjf;
    }

    public void setLxrjf(String lxrjf) {
        this.lxrjf = lxrjf;
    }

    public String getJflxrzw() {
        return jflxrzw;
    }

    public void setJflxrzw(String jflxrzw) {
        this.jflxrzw = jflxrzw;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getLxdhjf() {
        return lxdhjf;
    }

    public void setLxdhjf(String lxdhjf) {
        this.lxdhjf = lxdhjf;
    }

    public String getWxhjf() {
        return wxhjf;
    }

    public void setWxhjf(String wxhjf) {
        this.wxhjf = wxhjf;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getGyslxrzw() {
        return gyslxrzw;
    }

    public void setGyslxrzw(String gyslxrzw) {
        this.gyslxrzw = gyslxrzw;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getBhsjg() {
        return bhsjg;
    }

    public void setBhsjg(String bhsjg) {
        this.bhsjg = bhsjg;
    }

    public String getBhsjgDX() {
        return bhsjgDX;
    }

    public void setBhsjgDX(String bhsjgDX) {
        this.bhsjgDX = bhsjgDX;
    }

    public String getSldx() {
        return sldx;
    }

    public void setSldx(String sldx) {
        this.sldx = sldx;
    }

    public String getHsjg() {
        return hsjg;
    }

    public void setHsjg(String hsjg) {
        this.hsjg = hsjg;
    }

    public String getHsjgDX() {
        return hsjgDX;
    }

    public void setHsjgDX(String hsjgDX) {
        this.hsjgDX = hsjgDX;
    }

    public String getFkfslw() {
        return fkfslw;
    }

    public void setFkfslw(String fkfslw) {
        this.fkfslw = fkfslw;
    }

    public String getZfhtjkfs() {
        return zfhtjkfs;
    }

    public void setZfhtjkfs(String zfhtjkfs) {
        this.zfhtjkfs = zfhtjkfs;
    }

    public String getFsy() {
        return fsy;
    }

    public void setFsy(String fsy) {
        this.fsy = fsy;
    }

    public String getZfdjqx() {
        return zfdjqx;
    }

    public void setZfdjqx(String zfdjqx) {
        this.zfdjqx = zfdjqx;
    }

    public String getDjje() {
        return djje;
    }

    public void setDjje(String djje) {
        this.djje = djje;
    }

    public String getDjjeDX() {
        return djjeDX;
    }

    public void setDjjeDX(String djjeDX) {
        this.djjeDX = djjeDX;
    }

    public String getHgyszfqx() {
        return hgyszfqx;
    }

    public void setHgyszfqx(String hgyszfqx) {
        this.hgyszfqx = hgyszfqx;
    }

    public String getHtzjkzfbl() {
        return htzjkzfbl;
    }

    public void setHtzjkzfbl(String htzjkzfbl) {
        this.htzjkzfbl = htzjkzfbl;
    }

    public String getZfje() {
        return zfje;
    }

    public void setZfje(String zfje) {
        this.zfje = zfje;
    }

    public String getZfjeDX() {
        return zfjeDX;
    }

    public void setZfjeDX(String zfjeDX) {
        this.zfjeDX = zfjeDX;
    }

    public String getYlhtzjkbfb() {
        return ylhtzjkbfb;
    }

    public void setYlhtzjkbfb(String ylhtzjkbfb) {
        this.ylhtzjkbfb = ylhtzjkbfb;
    }

    public String getZbjje() {
        return zbjje;
    }

    public void setZbjje(String zbjje) {
        this.zbjje = zbjje;
    }

    public String getZbjjeDX() {
        return zbjjeDX;
    }

    public void setZbjjeDX(String zbjjeDX) {
        this.zbjjeDX = zbjjeDX;
    }

    public String getZfdjqx2() {
        return zfdjqx2;
    }

    public void setZfdjqx2(String zfdjqx2) {
        this.zfdjqx2 = zfdjqx2;
    }

    public String getDjje2() {
        return djje2;
    }

    public void setDjje2(String djje2) {
        this.djje2 = djje2;
    }

    public String getDjje2DX() {
        return djje2DX;
    }

    public void setDjje2DX(String djje2DX) {
        this.djje2DX = djje2DX;
    }

    public String getHgyszfqx2() {
        return hgyszfqx2;
    }

    public void setHgyszfqx2(String hgyszfqx2) {
        this.hgyszfqx2 = hgyszfqx2;
    }

    public String getHtzjkzfbl2() {
        return htzjkzfbl2;
    }

    public void setHtzjkzfbl2(String htzjkzfbl2) {
        this.htzjkzfbl2 = htzjkzfbl2;
    }

    public String getZfje2() {
        return zfje2;
    }

    public void setZfje2(String zfje2) {
        this.zfje2 = zfje2;
    }

    public String getZfje2DX() {
        return zfje2DX;
    }

    public void setZfje2DX(String zfje2DX) {
        this.zfje2DX = zfje2DX;
    }

    public String getJfysrn() {
        return jfysrn;
    }

    public void setJfysrn(String jfysrn) {
        this.jfysrn = jfysrn;
    }

    public String getHtzjkzfbl3() {
        return htzjkzfbl3;
    }

    public void setHtzjkzfbl3(String htzjkzfbl3) {
        this.htzjkzfbl3 = htzjkzfbl3;
    }

    public String getZfje3() {
        return zfje3;
    }

    public void setZfje3(String zfje3) {
        this.zfje3 = zfje3;
    }

    public String getZfje3DX() {
        return zfje3DX;
    }

    public void setZfje3DX(String zfje3DX) {
        this.zfje3DX = zfje3DX;
    }

    public String getJfysrn2() {
        return jfysrn2;
    }

    public void setJfysrn2(String jfysrn2) {
        this.jfysrn2 = jfysrn2;
    }

    public String getHtzjkzfbl4() {
        return htzjkzfbl4;
    }

    public void setHtzjkzfbl4(String htzjkzfbl4) {
        this.htzjkzfbl4 = htzjkzfbl4;
    }

    public String getZfje4() {
        return zfje4;
    }

    public void setZfje4(String zfje4) {
        this.zfje4 = zfje4;
    }

    public String getZfje4DX() {
        return zfje4DX;
    }

    public void setZfje4DX(String zfje4DX) {
        this.zfje4DX = zfje4DX;
    }

    public String getYlhtzjkbfb2() {
        return ylhtzjkbfb2;
    }

    public void setYlhtzjkbfb2(String ylhtzjkbfb2) {
        this.ylhtzjkbfb2 = ylhtzjkbfb2;
    }

    public String getZbjje2() {
        return zbjje2;
    }

    public void setZbjje2(String zbjje2) {
        this.zbjje2 = zbjje2;
    }

    public String getZbjje2DX() {
        return zbjje2DX;
    }

    public void setZbjje2DX(String zbjje2DX) {
        this.zbjje2DX = zbjje2DX;
    }

    public String getQtfkfs() {
        return qtfkfs;
    }

    public void setQtfkfs(String qtfkfs) {
        this.qtfkfs = qtfkfs;
    }

    public String getYffkfs() {
        return yffkfs;
    }

    public void setYffkfs(String yffkfs) {
        this.yffkfs = yffkfs;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getGsnsrsbh() {
        return gsnsrsbh;
    }

    public void setGsnsrsbh(String gsnsrsbh) {
        this.gsnsrsbh = gsnsrsbh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getYfzfsj() {
        return yfzfsj;
    }

    public void setYfzfsj(String yfzfsj) {
        this.yfzfsj = yfzfsj;
    }

    public String getShrxxdz() {
        return shrxxdz;
    }

    public void setShrxxdz(String shrxxdz) {
        this.shrxxdz = shrxxdz;
    }

    public String getShr() {
        return shr;
    }

    public void setShr(String shr) {
        this.shr = shr;
    }

    public String getShrdh() {
        return shrdh;
    }

    public void setShrdh(String shrdh) {
        this.shrdh = shrdh;
    }

    public String getJhfs() {
        return jhfs;
    }

    public void setJhfs(String jhfs) {
        this.jhfs = jhfs;
    }

    public String getZthdz() {
        return zthdz;
    }

    public void setZthdz(String zthdz) {
        this.zthdz = zthdz;
    }

    public String getZthlxr() {
        return zthlxr;
    }

    public void setZthlxr(String zthlxr) {
        this.zthlxr = zthlxr;
    }

    public String getYsrxm() {
        return ysrxm;
    }

    public void setYsrxm(String ysrxm) {
        this.ysrxm = ysrxm;
    }

    public String getWyfyxsyfzfwy() {
        return wyfyxsyfzfwy;
    }

    public void setWyfyxsyfzfwy(String wyfyxsyfzfwy) {
        this.wyfyxsyfzfwy = wyfyxsyfzfwy;
    }

    public String getFbLeft() {
        return fbLeft;
    }

    public void setFbLeft(String fbLeft) {
        this.fbLeft = fbLeft;
    }

    public String getFbRight() {
        return fbRight;
    }

    public void setFbRight(String fbRight) {
        this.fbRight = fbRight;
    }

    public String getGysmcLeft() {
        return gysmcLeft;
    }

    public void setGysmcLeft(String gysmcLeft) {
        this.gysmcLeft = gysmcLeft;
    }

    public String getGysmcRight() {
        return gysmcRight;
    }

    public void setGysmcRight(String gysmcRight) {
        this.gysmcRight = gysmcRight;
    }

    public List<ImageInfo> getZlfwcqz() {
        return zlfwcqz;
    }

    public void setZlfwcqz(List<ImageInfo> zlfwcqz) {
        this.zlfwcqz = zlfwcqz;
    }

    public List<Map<String, String>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, String>> detail) {
        this.detail = detail;
    }

    public List<ImageInfo> getCzrsfzyyzz() {
        return czrsfzyyzz;
    }

    public void setCzrsfzyyzz(List<ImageInfo> czrsfzyyzz) {
        this.czrsfzyyzz = czrsfzyyzz;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    public String getBcyd() {
        return bcyd;
    }

    public void setBcyd(String bcyd) {
        this.bcyd = bcyd;
    }

    public String getSlxz() {
        return slxz;
    }

    public void setSlxz(String slxz) {
        this.slxz = slxz;
    }

    public String getZzsj() {
        return zzsj;
    }

    public void setZzsj(String zzsj) {
        this.zzsj = zzsj;
    }

    public String getDxzzsj() {
        return dxzzsj;
    }

    public void setDxzzsj(String dxzzsj) {
        this.dxzzsj = dxzzsj;
    }

    @Override
    public String toString() {
        return "Contract4{" +
                "htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", lxrjf='" + lxrjf + '\'' +
                ", jflxrzw='" + jflxrzw + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", lxdhjf='" + lxdhjf + '\'' +
                ", wxhjf='" + wxhjf + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", gyslxrzw='" + gyslxrzw + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", bhsjg='" + bhsjg + '\'' +
                ", bhsjgDX='" + bhsjgDX + '\'' +
                ", sldx='" + sldx + '\'' +
                ", hsjg='" + hsjg + '\'' +
                ", hsjgDX='" + hsjgDX + '\'' +
                ", fkfslw='" + fkfslw + '\'' +
                ", zfhtjkfs='" + zfhtjkfs + '\'' +
                ", fsy='" + fsy + '\'' +
                ", zfdjqx='" + zfdjqx + '\'' +
                ", djje='" + djje + '\'' +
                ", djjeDX='" + djjeDX + '\'' +
                ", hgyszfqx='" + hgyszfqx + '\'' +
                ", htzjkzfbl='" + htzjkzfbl + '\'' +
                ", zfje='" + zfje + '\'' +
                ", zfjeDX='" + zfjeDX + '\'' +
                ", ylhtzjkbfb='" + ylhtzjkbfb + '\'' +
                ", zbjje='" + zbjje + '\'' +
                ", zbjjeDX='" + zbjjeDX + '\'' +
                ", zfdjqx2='" + zfdjqx2 + '\'' +
                ", djje2='" + djje2 + '\'' +
                ", djje2DX='" + djje2DX + '\'' +
                ", hgyszfqx2='" + hgyszfqx2 + '\'' +
                ", htzjkzfbl2='" + htzjkzfbl2 + '\'' +
                ", zfje2='" + zfje2 + '\'' +
                ", zfje2DX='" + zfje2DX + '\'' +
                ", jfysrn='" + jfysrn + '\'' +
                ", htzjkzfbl3='" + htzjkzfbl3 + '\'' +
                ", zfje3='" + zfje3 + '\'' +
                ", zfje3DX='" + zfje3DX + '\'' +
                ", jfysrn2='" + jfysrn2 + '\'' +
                ", htzjkzfbl4='" + htzjkzfbl4 + '\'' +
                ", zfje4='" + zfje4 + '\'' +
                ", zfje4DX='" + zfje4DX + '\'' +
                ", ylhtzjkbfb2='" + ylhtzjkbfb2 + '\'' +
                ", zbjje2='" + zbjje2 + '\'' +
                ", zbjje2DX='" + zbjje2DX + '\'' +
                ", qtfkfs='" + qtfkfs + '\'' +
                ", yffkfs='" + yffkfs + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", gsnsrsbh='" + gsnsrsbh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", yfzfsj='" + yfzfsj + '\'' +
                ", shrxxdz='" + shrxxdz + '\'' +
                ", shr='" + shr + '\'' +
                ", shrdh='" + shrdh + '\'' +
                ", jhfs='" + jhfs + '\'' +
                ", zthdz='" + zthdz + '\'' +
                ", zthlxr='" + zthlxr + '\'' +
                ", ysrxm='" + ysrxm + '\'' +
                ", wyfyxsyfzfwy='" + wyfyxsyfzfwy + '\'' +
                ", detail='" + detail + '\'' +
                ", fbLeft='" + fbLeft + '\'' +
                ", fbRight='" + fbRight + '\'' +
                ", gysmcLeft='" + gysmcLeft + '\'' +
                ", gysmcRight='" + gysmcRight + '\'' +
                ", zlfwcqz=" + zlfwcqz +
                ", czrsfzyyzz=" + czrsfzyyzz +
                ", imageInfoList=" + imageInfoList +
                '}';
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();

        ConverToChinesePart cp = null;

        String sql = "select * from " + table + " where requestid=" + requestInfo.getRequestid();
        RecordSet rs = null;


        ImageInfoManage im = null;
        Row[] r = null;
        Cell[] c = null;
        try {
            im = new ImageInfoManage();
            cp = new ConverToChinesePart();

            im.init();
            rs = new RecordSet();
            rs.execute(sql);
            if (!rs.next()) {
                writeLog("sql:  select * from " + table + " where requestid=" + requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                throw new Exception("contract 没有查询到合同数据！");
            }
            bcyd = Util.null2String(rs.getString("bcyd"));
            slxz = Util.null2String(rs.getString("slsjz"));
            htbh = Util.null2String(rs.getString("htbh"));
            writeLog("合同编号 ：" + htbh);
            fb = JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")), "subcompanyname");
            writeLog("===fb: " + this.fb);
            tyshxydmczf = Util.null2String(rs.getString("tyshxydmczf"));
            zsdjf = Util.null2String(rs.getString("zsdjf"));
            lxrjf = JoinFieldManage.getPersonName(rs.getString("lxrjf"));
            jflxrzw = Util.null2String(rs.getString("jflxrzw"));
            lxdhjf = Util.null2String(rs.getString("lxdhjf"));
            wxhjf = Util.null2String(rs.getString("wxhjf"));
            dzyxjf = Util.null2String(rs.getString("dzyxjf"));
            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            sfzhmtyshxydmczf = Util.null2String(rs.getString("sfzhmtyshxydmczf"));
            lxdzyf = Util.null2String(rs.getString("lxdzyf"));
            ywqyfzr = Util.null2String(rs.getString("ywqyfzr"));
            gyslxrzw = Util.null2String(rs.getString("gyslxrzw"));
            fzrlxdh = Util.null2String(rs.getString("fzrlxdh"));
            wxhyf = Util.null2String(rs.getString("wxhyf"));
            fzryxdz = Util.null2String(rs.getString("fzryxdz"));

            bhsjg = Util.null2String(rs.getString("bhsjg"));
            bhsjgDX = cp.convertToChinese(Util.null2String(rs.getString("bhsjg"), "0"));
            sldx = JoinFieldManage.getSelectName("sldx", table, rs.getString("sldx"));
            hsjg = Util.null2String(rs.getString("hsjg"));
            hsjgDX = cp.convertToChinese(Util.null2String(rs.getString("hsjg"), "0"));
            fkfslw = Util.null2String(rs.getString("fkfslw"));
            // 付款方式
            zfhtjkfs = "".equals(Util.null2String(rs.getString("zfhtjkfs"))) ? "" : String.valueOf(rs.getInt("zfhtjkfs") + 1);
            // 方式一
            fsy = Util.null2String(rs.getString("fsy"));
            // 方式二
            zfdjqx = Util.null2String(rs.getString("zfdjqx"));
            djje = Util.null2String(rs.getString("djje"));
            djjeDX = cp.convertToChinese(Util.null2String(rs.getString("djje"), "0"));
            hgyszfqx = Util.null2String(rs.getString("hgyszfqx"));
            htzjkzfbl = CommonUtil.IntToPercentage(rs.getString("htzjkzfbl"));
            zfje = Util.null2String(rs.getString("zfje"));
            zfjeDX = cp.convertToChinese(Util.null2String(rs.getString("zfje"), "0"));
            ylhtzjkbfb = CommonUtil.IntToPercentage(rs.getString("ylhtzjkbfb"));
            zbjje = Util.null2String(rs.getString("zbjje"));
            zbjjeDX = cp.convertToChinese(Util.null2String(rs.getString("zbjje"), "0"));
            // 方式三
            zfdjqx2 = Util.null2String(rs.getString("zfdjqx2"));
            djje2 = Util.null2String(rs.getString("djje2"));
            djje2DX = cp.convertToChinese(Util.null2String(rs.getString("djje2"), "0"));
            hgyszfqx2 = Util.null2String(rs.getString("hgyszfqx2"));
            htzjkzfbl2 = CommonUtil.IntToPercentage(rs.getString("htzjkzfbl2"));
            zfje2 = Util.null2String(rs.getString("zfje2"));
            zfje2DX = cp.convertToChinese(Util.null2String(rs.getString("zfje2"), "0"));
            jfysrn = Util.null2String(rs.getString("jfysrn"));
            htzjkzfbl3 = CommonUtil.IntToPercentage(rs.getString("htzjkzfbl3"));
            zfje3 = Util.null2String(rs.getString("zfje3"));
            zfje3DX = cp.convertToChinese(Util.null2String(rs.getString("zfje3"), "0"));
            jfysrn2 = Util.null2String(rs.getString("jfysrn2"));
            htzjkzfbl4 = CommonUtil.IntToPercentage(rs.getString("htzjkzfbl4"));
            zfje4 = Util.null2String(rs.getString("zfje4"));
            zfje4DX = cp.convertToChinese(Util.null2String(rs.getString("zfje4"), "0"));

            ylhtzjkbfb2 = CommonUtil.IntToPercentage(rs.getString("ylhtzjkbfb2"));
            zbjje2 = Util.null2String(rs.getString("zbjje2"));
            zbjje2DX = cp.convertToChinese(Util.null2String(rs.getString("zbjje2"), "0"));
            // 方式四
            qtfkfs = CommonUtil.ReplaceSpeciaiToBlank(rs.getString("qtfkfs"));
            yffkfs = "".equals(Util.null2String(rs.getString("yffkfs"))) ? "" : String.valueOf(rs.getInt("yffkfs") + 1);

            zhmc = Util.null2String(rs.getString("zhmc"));
            gsnsrsbh = Util.null2String(rs.getString("gsnsrsbh"));
            yxkh = Util.null2String(rs.getString("yxkh"));
            khyx = Util.null2String(rs.getString("khyx"));

            yfzfsj = Util.null2String(rs.getString("yfzfsj"));
            shrxxdz = Util.null2String(rs.getString("shrxxdz"));
            shr = JoinFieldManage.getPersonName(rs.getString("shr"));
            shrdh = Util.null2String(rs.getString("shrdh"));
            jhfs = "".equals(Util.null2String(rs.getString("jhfs"))) ? "" : String.valueOf(rs.getInt("jhfs") + 1);
            ysrxm = Util.null2String(rs.getString("ysrxm"));
            wyfyxsyfzfwy = Util.null2String(rs.getString("wyfyxsyfzfwy"));

            r = detailTable[5].getRow();
            for (int i = 0; i < r.length; ++i) {
                Map<String, String> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id", String.valueOf(i + 1));
                for (int j = 0; j < c.length; ++j) {
                    writeLog(c[j].getName() + "---" + c[j].getValue());
                    map.put(c[j].getName(), c[j].getValue());
                }
                this.detail.add(map);
            }

            // 附件处理
            String[] str = null;

            writeLog("获取附件一 字节码");
            String fjValue = Util.null2String(rs.getString("zlfwcqz"));
            if (!"".equals(fjValue)) {
                str = fjValue.split(",");
            } else {
                str = new String[]{};
            }
            for (int i = 0; i < str.length; ++i) {
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                zlfwcqz.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            writeLog("获取附件二");
            fjValue = Util.null2String(rs.getString("czrsfzyyzz"));
            if (!"".equals(fjValue)) {
                str = fjValue.split(",");
            } else {
                str = new String[]{};
            }
            for (int i = 0; i < str.length; ++i) {
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                czrsfzyyzz.add(imageInfo);
                imageInfoList.add(imageInfo);
            }

            // 删除临时附件
            im.deleteFile();

            // 甲乙方盖章
            int len = 14;

            if (this.fb.length() > len) {
                this.fbLeft = Util.null2String(this.fb.substring(0, len));
                this.fbRight = Util.null2String(this.fb.substring(len));
            } else {
                this.fbLeft = Util.null2String(this.fb);
            }

            if (this.gysmc.length() > len) {
                this.gysmcLeft = Util.null2String(this.gysmc.substring(0, len));
                this.gysmcRight = Util.null2String(this.gysmc.substring(len));
            } else {
                this.gysmcLeft = Util.null2String(this.gysmc);
            }


            return Action.SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            writeLog("Contract4 报错" + e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}
