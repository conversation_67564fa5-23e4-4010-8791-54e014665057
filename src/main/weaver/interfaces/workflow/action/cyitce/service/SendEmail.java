package weaver.interfaces.workflow.action.cyitce.service;

import weaver.interfaces.workflow.action.cyitce.util.emailUntilss;

import javax.mail.*;
import javax.mail.internet.*;

import java.io.UnsupportedEncodingException;


/**
 * @ClassName: 发送邮件类
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-04  13:49
 * @Version: 1.0
 */
public class SendEmail {

    private Session session;
    private MimeMessage msg;
    private StringBuilder buf;
    private BodyPart textpart;
    private Multipart multipart;

    public SendEmail() {
        this.session = emailUntilss.createSession();
    }

    public void setInternetAddress(String sender,String subject,String sendAddress,String ReceiveAddress) throws MessagingException, UnsupportedEncodingException {
        this.msg = new MimeMessage(this.session);
        msg.setFrom(new InternetAddress(sendAddress,sender));
        msg.setRecipient(Message.RecipientType.TO,new InternetAddress(ReceiveAddress));
        msg.setSubject(subject,"utf-8");
    }

    public void setContent(String contentHtml) throws MessagingException {
        this.textpart = new MimeBodyPart();
        this.buf = new StringBuilder();
        this.buf.append(contentHtml);
        this.textpart.setContent(this.buf.toString(),"text/html;charset=utf-8");
        this.multipart = new MimeMultipart();
        this.multipart.addBodyPart(this.textpart);
    }

    public void send() throws MessagingException {
        this.msg.setContent(this.multipart);
        Transport.send(this.msg);
    }
}