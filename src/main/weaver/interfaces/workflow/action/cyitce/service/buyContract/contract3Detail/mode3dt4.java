package weaver.interfaces.workflow.action.cyitce.service.buyContract.contract3Detail;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: mode3dt4
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-11-24  14:56
 * @Version: 1.0
 */
public class mode3dt4 extends BaseBean implements Action{
    /**
     * 流程数据
     */
    public RecordSet rs = null;


    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * yjxmmc	项目名称
     */
    public String yjxmmc;

    /**
     * fkrq 农民工工资付款日期
     */
    public String fkrq;

    public RecordSet getRs() {
        return rs;
    }

    public void setRs(RecordSet rs) {
        this.rs = rs;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getFkrq() {
        return fkrq;
    }

    public void setFkrq(String fkrq) {
        this.fkrq = fkrq;
    }

    @Override
    public String toString() {
        return "mode3dt4{" +
                "rs=" + rs +
                ", gysmc='" + gysmc + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", fkrq='" + fkrq + '\'' +
                '}';
    }

    public void data(RecordSet rs){
        this.rs = rs;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();

        try {
            if(rs==null){
                rs = new RecordSet();
                rs.execute(sql);
                if(!rs.next()){
                    writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                    throw new Exception("contract 没有查询到合同数据！");
                }
            }

            gysmc = JoinFieldManage.getVendorAccountName(rs.getString("gysmc"));
            yjxmmc = JoinFieldManage.getProjectName(rs.getString("yjxmmc"));
            fkrq = Util.null2String(rs.getString("fkrq"));

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            requestInfo.getRequestManager().setMessage("附件三生成失败！");
            writeLog("mode3dt2 error："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}