package weaver.interfaces.workflow.action.cyitce.manager.filesystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;
import weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem.Token;

/**
 * @ClassName: FileInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-23  15:23
 * @Version: 1.0
 */
public class FileInfo extends BaseBean {
    private String api = AddressManagementPool.getIpAddress("FILESYS");

    /**
     * @description:获取文件名称
     * @author: lijianpan
     * @date: 2023/11/23
     * @param: [telephone]
     * @return: java.lang.String
     **/
    public String getFileName(String ids, User user) {
        HttpClient client = null;
        HttpGet get = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;

        try {
            client = HttpClients.createDefault();
            builder = new URIBuilder(api+"/file-system/file_center/getFileName");
            builder.addParameter("ids",ids);
            get = new HttpGet(builder.build());
            get.setHeader("Blade-Auth", Token.getToken(user.getLoginid(),user.getLastname()));
            get.setHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            get.setHeader("Tenant-Id", "000000");
            response = client.execute(get);

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }
            httpEntity = response.getEntity();
            JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(httpEntity));
            if(!"200".equals(jsonObject.getString("code"))){
                return "";
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            JSONObject obj = jsonArray.getJSONObject(0);

            return obj.getString("sourceName")+obj.getString("suffix");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}