package weaver.interfaces.workflow.action.cyitce.manager.paasspace;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;

/**
 * @ClassName: PassSpaceToken
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-23  10:53
 * @Version: 1.0
 */
public class PaasSpaceToken extends BaseBean {
    private String api = AddressManagementPool.getIpAddress("PAAS");

    public String token(String telephone) {
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;

        try {
            client = HttpClients.createDefault();
            post = new HttpPost();
            post.addHeader("Content-Type", "application/x-www-form-urlencoded");
            post.addHeader("Authorization", "Basic Y3lpdGNlX2Vjb2xvZ3lfZWVmNTQ6Q3AkM2hkc05ZREgjU040cHBUclc=");
            builder = new URIBuilder(api+"/oauth2/token");
            builder.addParameter("grant_type","client_credentials");
            builder.addParameter("client_id","cyitce_ecology_eef54");
            builder.addParameter("client_secret","Cp$3hdsNYDH#SN4ppTrW");
            builder.addParameter("scope","all");
            builder.addParameter("phone",telephone);
            post = new HttpPost(builder.build());
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }

            httpEntity = response.getEntity();
            JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(httpEntity));
            String toke = jsonObject.getString("token_type") +" "+jsonObject.getString("access_token");
            return toke;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}