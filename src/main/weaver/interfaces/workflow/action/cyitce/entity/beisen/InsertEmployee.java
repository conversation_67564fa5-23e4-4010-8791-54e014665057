package weaver.interfaces.workflow.action.cyitce.entity.beisen;

/**
 * 该类用于表示待插入的员工信息，对应数据库中的员工表插入操作。
 */
public class InsertEmployee {
    /**
     * 员工的唯一标识符，使用 int 类型存储。
     */
    private int id;
    /**
     * 员工的姓名，使用 varchar(255) 类型存储。
     */
    private String name;
    /**
     * 员工的工号，使用 varchar(255) 类型存储。
     */
    private String code;
    /**
     * 员工离职前所在的机构，使用 varchar(255) 类型存储。
     */
    private String gs;
    /**
     * 员工离职前所在的部门，使用 varchar(255) 类型存储。
     */
    private String dept;
    /**
     * 员工离职前的职位，使用 varchar(255) 类型存储。
     */
    private String zw;
    /**
     * 员工的最后工作日，使用 varchar(255) 类型存储。
     */
    private String zhgzr;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    private String userId;

    /**
     * 无参构造方法，用于创建一个空的 InsertEmployee 对象。
     */
    public InsertEmployee() {
    }

    /**
     * 有参构造方法，用于创建一个具有完整信息的 InsertEmployee 对象。
     *
     * @param id     员工的唯一标识符
     * @param name   员工的姓名
     * @param code   员工的工号
     * @param gs     员工离职前所在的机构
     * @param dept   员工离职前所在的部门
     * @param zw     员工离职前的职位
     * @param zhgzr  员工的最后工作日
     */
    public InsertEmployee(int id, String name, String code, String gs, String dept, String zw, String zhgzr) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.gs = gs;
        this.dept = dept;
        this.zw = zw;
        this.zhgzr = zhgzr;
    }

    /**
     * 获取员工的唯一标识符。
     *
     * @return 员工的唯一标识符
     */
    public int getId() {
        return id;
    }

    /**
     * 设置员工的唯一标识符。
     *
     * @param id 员工的唯一标识符
     */
    public void setId(int id) {
        this.id = id;
    }

    /**
     * 获取员工的姓名。
     *
     * @return 员工的姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置员工的姓名。
     *
     * @param name 员工的姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取员工的工号。
     *
     * @return 员工的工号
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置员工的工号。
     *
     * @param code 员工的工号
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取员工离职前所在的机构。
     *
     * @return 员工离职前所在的机构
     */
    public String getGs() {
        return gs;
    }

    /**
     * 设置员工离职前所在的机构。
     *
     * @param gs 员工离职前所在的机构
     */
    public void setGs(String gs) {
        this.gs = gs;
    }

    /**
     * 获取员工离职前所在的部门。
     *
     * @return 员工离职前所在的部门
     */
    public String getDept() {
        return dept;
    }

    /**
     * 设置员工离职前所在的部门。
     *
     * @param dept 员工离职前所在的部门
     */
    public void setDept(String dept) {
        this.dept = dept;
    }

    /**
     * 获取员工离职前的职位。
     *
     * @return 员工离职前的职位
     */
    public String getZw() {
        return zw;
    }

    /**
     * 设置员工离职前的职位。
     *
     * @param zw 员工离职前的职位
     */
    public void setZw(String zw) {
        this.zw = zw;
    }

    /**
     * 获取员工的最后工作日。
     *
     * @return 员工的最后工作日
     */
    public String getZhgzr() {
        return zhgzr;
    }

    /**
     * 设置员工的最后工作日。
     *
     * @param zhgzr 员工的最后工作日
     */
    public void setZhgzr(String zhgzr) {
        this.zhgzr = zhgzr;
    }

    /**
     * 将 InsertEmployee 对象转换为字符串形式，方便打印和调试。
     *
     * @return 包含员工信息的字符串
     */
    @Override
    public String toString() {
        return "InsertEmployee{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", gs='" + gs + '\'' +
                ", dept='" + dept + '\'' +
                ", zw='" + zw + '\'' +
                ", zhgzr='" + zhgzr + '\'' +
                '}';
    }
}    