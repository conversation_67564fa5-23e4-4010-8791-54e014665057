package weaver.interfaces.workflow.action.cyitce.entity;

import java.util.Arrays;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/7/15 14:36
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/7/15      HONOR          v1.0.0               修改原因
 */
public class ImageInfo {
    private String id;
    private String pId;
    private String name;
    private String width;
    private String height;
    private byte[] bytes;

    public ImageInfo() {
        this.id = "";
        this.pId = "";
        this.name = "";
        this.width = "0";
        this.height = "0";
        this.bytes = new byte[0];
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    @Override
    public String toString() {
        return "ImageInfo{" +
                "id='" + id + '\'' +
                ", pId='" + pId + '\'' +
                ", name='" + name + '\'' +
                ", width='" + width + '\'' +
                ", height='" + height + '\'' +
                '}';
    }
}
