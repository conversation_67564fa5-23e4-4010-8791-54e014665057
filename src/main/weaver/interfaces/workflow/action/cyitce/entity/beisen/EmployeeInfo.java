package weaver.interfaces.workflow.action.cyitce.entity.beisen;

import java.util.Map;
import java.util.Objects;

public class EmployeeInfo {
    private Long userID;
    private String name;
    private String _Name;
    private int gender;
    private String email;
    private String iDType;
    private String iDNumber;
    private Object birthday;
    private Object workDate;
    private Object homeAddress;
    private String mobilePhone;
    private Object weiXin;
    private Object iDPhoto;
    private Object iDPortraitSide;
    private Object iDCountryEmblemSide;
    private Object personalHomepage;
    private Object speciality;
    private Object major;
    private Object postalCode;
    private Object passportNumber;
    private Object constellation;
    private Object bloodType;
    private Object residenceAddress;
    private Object joinPartyDate;
    private Object domicileType;
    private Object emergencyContact;
    private Object emergencyContactRelationship;
    private Object emergencyContactPhone;
    private Object qQ;
    private Object aboutMe;
    private Object homePhone;
    private Object graduateDate;
    private Object marryCategory;
    private Object politicalStatus;
    private int nationality;
    private Object nation;
    private Object birthplace;
    private Object registAddress;
    private Object educationLevel;
    private Object lastSchool;
    private Object engName;
    private Object officeTel;
    private Object businessAddress;
    private Object backupMail;
    private Object applicantId;
    private Object applyIdV6;
    private int age;
    private long businessModifiedBy;
    private String businessModifiedTime;
    private Object sourceType;
    private String objectId;
    private Map<String, String> customProperties;
    private Map<String, String> translateProperties;
    private long createdBy;
    private String createdTime;
    private long modifiedBy;
    private String modifiedTime;
    private boolean stdIsDeleted;

    public Long getUserID() {
        return userID;
    }

    public void setUserID(Long userID) {
        this.userID = userID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String get_Name() {
        return _Name;
    }

    public void set_Name(String _Name) {
        this._Name = _Name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getiDType() {
        return iDType;
    }

    public void setiDType(String iDType) {
        this.iDType = iDType;
    }

    public String getiDNumber() {
        return iDNumber;
    }

    public void setiDNumber(String iDNumber) {
        this.iDNumber = iDNumber;
    }

    public Object getBirthday() {
        return birthday;
    }

    public void setBirthday(Object birthday) {
        this.birthday = birthday;
    }

    public Object getWorkDate() {
        return workDate;
    }

    public void setWorkDate(Object workDate) {
        this.workDate = workDate;
    }

    public Object getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(Object homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Object getWeiXin() {
        return weiXin;
    }

    public void setWeiXin(Object weiXin) {
        this.weiXin = weiXin;
    }

    public Object getiDPhoto() {
        return iDPhoto;
    }

    public void setiDPhoto(Object iDPhoto) {
        this.iDPhoto = iDPhoto;
    }

    public Object getiDPortraitSide() {
        return iDPortraitSide;
    }

    public void setiDPortraitSide(Object iDPortraitSide) {
        this.iDPortraitSide = iDPortraitSide;
    }

    public Object getiDCountryEmblemSide() {
        return iDCountryEmblemSide;
    }

    public void setiDCountryEmblemSide(Object iDCountryEmblemSide) {
        this.iDCountryEmblemSide = iDCountryEmblemSide;
    }

    public Object getPersonalHomepage() {
        return personalHomepage;
    }

    public void setPersonalHomepage(Object personalHomepage) {
        this.personalHomepage = personalHomepage;
    }

    public Object getSpeciality() {
        return speciality;
    }

    public void setSpeciality(Object speciality) {
        this.speciality = speciality;
    }

    public Object getMajor() {
        return major;
    }

    public void setMajor(Object major) {
        this.major = major;
    }

    public Object getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(Object postalCode) {
        this.postalCode = postalCode;
    }

    public Object getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(Object passportNumber) {
        this.passportNumber = passportNumber;
    }

    public Object getConstellation() {
        return constellation;
    }

    public void setConstellation(Object constellation) {
        this.constellation = constellation;
    }

    public Object getBloodType() {
        return bloodType;
    }

    public void setBloodType(Object bloodType) {
        this.bloodType = bloodType;
    }

    public Object getResidenceAddress() {
        return residenceAddress;
    }

    public void setResidenceAddress(Object residenceAddress) {
        this.residenceAddress = residenceAddress;
    }

    public Object getJoinPartyDate() {
        return joinPartyDate;
    }

    public void setJoinPartyDate(Object joinPartyDate) {
        this.joinPartyDate = joinPartyDate;
    }

    public Object getDomicileType() {
        return domicileType;
    }

    public void setDomicileType(Object domicileType) {
        this.domicileType = domicileType;
    }

    public Object getEmergencyContact() {
        return emergencyContact;
    }

    public void setEmergencyContact(Object emergencyContact) {
        this.emergencyContact = emergencyContact;
    }

    public Object getEmergencyContactRelationship() {
        return emergencyContactRelationship;
    }

    public void setEmergencyContactRelationship(Object emergencyContactRelationship) {
        this.emergencyContactRelationship = emergencyContactRelationship;
    }

    public Object getEmergencyContactPhone() {
        return emergencyContactPhone;
    }

    public void setEmergencyContactPhone(Object emergencyContactPhone) {
        this.emergencyContactPhone = emergencyContactPhone;
    }

    public Object getqQ() {
        return qQ;
    }

    public void setqQ(Object qQ) {
        this.qQ = qQ;
    }

    public Object getAboutMe() {
        return aboutMe;
    }

    public void setAboutMe(Object aboutMe) {
        this.aboutMe = aboutMe;
    }

    public Object getHomePhone() {
        return homePhone;
    }

    public void setHomePhone(Object homePhone) {
        this.homePhone = homePhone;
    }

    public Object getGraduateDate() {
        return graduateDate;
    }

    public void setGraduateDate(Object graduateDate) {
        this.graduateDate = graduateDate;
    }

    public Object getMarryCategory() {
        return marryCategory;
    }

    public void setMarryCategory(Object marryCategory) {
        this.marryCategory = marryCategory;
    }

    public Object getPoliticalStatus() {
        return politicalStatus;
    }

    public void setPoliticalStatus(Object politicalStatus) {
        this.politicalStatus = politicalStatus;
    }

    public int getNationality() {
        return nationality;
    }

    public void setNationality(int nationality) {
        this.nationality = nationality;
    }

    public Object getNation() {
        return nation;
    }

    public void setNation(Object nation) {
        this.nation = nation;
    }

    public Object getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(Object birthplace) {
        this.birthplace = birthplace;
    }

    public Object getRegistAddress() {
        return registAddress;
    }

    public void setRegistAddress(Object registAddress) {
        this.registAddress = registAddress;
    }

    public Object getEducationLevel() {
        return educationLevel;
    }

    public void setEducationLevel(Object educationLevel) {
        this.educationLevel = educationLevel;
    }

    public Object getLastSchool() {
        return lastSchool;
    }

    public void setLastSchool(Object lastSchool) {
        this.lastSchool = lastSchool;
    }

    public Object getEngName() {
        return engName;
    }

    public void setEngName(Object engName) {
        this.engName = engName;
    }

    public Object getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(Object officeTel) {
        this.officeTel = officeTel;
    }

    public Object getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(Object businessAddress) {
        this.businessAddress = businessAddress;
    }

    public Object getBackupMail() {
        return backupMail;
    }

    public void setBackupMail(Object backupMail) {
        this.backupMail = backupMail;
    }

    public Object getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(Object applicantId) {
        this.applicantId = applicantId;
    }

    public Object getApplyIdV6() {
        return applyIdV6;
    }

    public void setApplyIdV6(Object applyIdV6) {
        this.applyIdV6 = applyIdV6;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public long getBusinessModifiedBy() {
        return businessModifiedBy;
    }

    public void setBusinessModifiedBy(long businessModifiedBy) {
        this.businessModifiedBy = businessModifiedBy;
    }

    public String getBusinessModifiedTime() {
        return businessModifiedTime;
    }

    public void setBusinessModifiedTime(String businessModifiedTime) {
        this.businessModifiedTime = businessModifiedTime;
    }

    public Object getSourceType() {
        return sourceType;
    }

    public void setSourceType(Object sourceType) {
        this.sourceType = sourceType;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public Map<String, String> getCustomProperties() {
        return customProperties;
    }

    public void setCustomProperties(Map<String, String> customProperties) {
        this.customProperties = customProperties;
    }

    public Map<String, String> getTranslateProperties() {
        return translateProperties;
    }

    public void setTranslateProperties(Map<String, String> translateProperties) {
        this.translateProperties = translateProperties;
    }

    public long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public long getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(long modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public String getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(String modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public boolean isStdIsDeleted() {
        return stdIsDeleted;
    }

    public void setStdIsDeleted(boolean stdIsDeleted) {
        this.stdIsDeleted = stdIsDeleted;
    }


}
