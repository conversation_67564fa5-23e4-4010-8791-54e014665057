package weaver.interfaces.workflow.action.cyitce.entity.beisen;

import java.util.Map;
import java.util.Objects;

public class RecordInfo {
    private Long userID;
    private String pObjectDataID;
    private Long oIdDepartment;
    private String startDate;
    private String stopDate;
    private String jobNumber;
    private String entryDate;
    private String lastWorkDate;
    private Object regularizationDate;
    private int probation;
    private Object order;
    private int employType;
    private int serviceType;
    private int serviceStatus;
    private int approvalStatus;
    private Object employmentSource;
    private Object employmentForm;
    private String isCharge;
    private String oIdJobPost;
    private Object oIdJobSequence;
    private Object oIdProfessionalLine;
    private Object oIdJobPosition;
    private Object oIdJobLevel;
    private Object oidJobGrade;
    private Object place;
    private String employeeStatus;
    private Object employmentType;
    private String employmentChangeID;
    private String changedStatus;
    private int pOIdEmpAdmin;
    private Object pOIdEmpReserve2;
    private String businessTypeOID;
    private Object changeTypeOID;
    private Object entryStatus;
    private boolean isCurrentRecord;
    private Object workYearBefore;
    private Object workYearGroupBefore;
    private int workYearCompanyBefore;
    private Object workYearTotal;
    private Object workYearGroupTotal;
    private double workYearCompanyTotal;
    private Long oIdOrganization;
    private Object whereabouts;
    private Object blackStaffDesc;
    private Object blackListAddReason;
    private Object transitionTypeOID;
    private Object changeReason;
    private Object probationResult;
    private String probationActualStopDate;
    private String probationStartDate;
    private String probationStopDate;
    private String isHaveProbation;
    private Object remarks;
    private boolean addOrNotBlackList;
    private long businessModifiedBy;
    private String businessModifiedTime;
    private Object traineeStartDate;
    private String objectId;
    private Object customProperties;
    private Map<String, String> translateProperties;
    private long createdBy;
    private String createdTime;
    private long modifiedBy;
    private String modifiedTime;
    private boolean stdIsDeleted;

    public Long getUserID() {
        return userID;
    }

    public void setUserID(long userID) {
        this.userID = userID;
    }

    public String getpObjectDataID() {
        return pObjectDataID;
    }

    public void setpObjectDataID(String pObjectDataID) {
        this.pObjectDataID = pObjectDataID;
    }

    public Long getoIdDepartment() {
        return oIdDepartment;
    }

    public void setoIdDepartment(Long oIdDepartment) {
        this.oIdDepartment = oIdDepartment;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStopDate() {
        return stopDate;
    }

    public void setStopDate(String stopDate) {
        this.stopDate = stopDate;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(String entryDate) {
        this.entryDate = entryDate;
    }

    public String getLastWorkDate() {
        return lastWorkDate;
    }

    public void setLastWorkDate(String lastWorkDate) {
        this.lastWorkDate = lastWorkDate;
    }

    public Object getRegularizationDate() {
        return regularizationDate;
    }

    public void setRegularizationDate(Object regularizationDate) {
        this.regularizationDate = regularizationDate;
    }

    public int getProbation() {
        return probation;
    }

    public void setProbation(int probation) {
        this.probation = probation;
    }

    public Object getOrder() {
        return order;
    }

    public void setOrder(Object order) {
        this.order = order;
    }

    public int getEmployType() {
        return employType;
    }

    public void setEmployType(int employType) {
        this.employType = employType;
    }

    public int getServiceType() {
        return serviceType;
    }

    public void setServiceType(int serviceType) {
        this.serviceType = serviceType;
    }

    public int getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(int serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public int getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(int approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Object getEmploymentSource() {
        return employmentSource;
    }

    public void setEmploymentSource(Object employmentSource) {
        this.employmentSource = employmentSource;
    }

    public Object getEmploymentForm() {
        return employmentForm;
    }

    public void setEmploymentForm(Object employmentForm) {
        this.employmentForm = employmentForm;
    }

    public String getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(String isCharge) {
        this.isCharge = isCharge;
    }

    public String getoIdJobPost() {
        return oIdJobPost;
    }

    public void setoIdJobPost(String oIdJobPost) {
        this.oIdJobPost = oIdJobPost;
    }

    public Object getoIdJobSequence() {
        return oIdJobSequence;
    }

    public void setoIdJobSequence(Object oIdJobSequence) {
        this.oIdJobSequence = oIdJobSequence;
    }

    public Object getoIdProfessionalLine() {
        return oIdProfessionalLine;
    }

    public void setoIdProfessionalLine(Object oIdProfessionalLine) {
        this.oIdProfessionalLine = oIdProfessionalLine;
    }

    public Object getoIdJobPosition() {
        return oIdJobPosition;
    }

    public void setoIdJobPosition(Object oIdJobPosition) {
        this.oIdJobPosition = oIdJobPosition;
    }

    public Object getoIdJobLevel() {
        return oIdJobLevel;
    }

    public void setoIdJobLevel(Object oIdJobLevel) {
        this.oIdJobLevel = oIdJobLevel;
    }

    public Object getOidJobGrade() {
        return oidJobGrade;
    }

    public void setOidJobGrade(Object oidJobGrade) {
        this.oidJobGrade = oidJobGrade;
    }

    public Object getPlace() {
        return place;
    }

    public void setPlace(Object place) {
        this.place = place;
    }

    public String getEmployeeStatus() {
        return employeeStatus;
    }

    public void setEmployeeStatus(String employeeStatus) {
        this.employeeStatus = employeeStatus;
    }

    public Object getEmploymentType() {
        return employmentType;
    }

    public void setEmploymentType(Object employmentType) {
        this.employmentType = employmentType;
    }

    public String getEmploymentChangeID() {
        return employmentChangeID;
    }

    public void setEmploymentChangeID(String employmentChangeID) {
        this.employmentChangeID = employmentChangeID;
    }

    public String getChangedStatus() {
        return changedStatus;
    }

    public void setChangedStatus(String changedStatus) {
        this.changedStatus = changedStatus;
    }

    public int getpOIdEmpAdmin() {
        return pOIdEmpAdmin;
    }

    public void setpOIdEmpAdmin(int pOIdEmpAdmin) {
        this.pOIdEmpAdmin = pOIdEmpAdmin;
    }

    public Object getpOIdEmpReserve2() {
        return pOIdEmpReserve2;
    }

    public void setpOIdEmpReserve2(Object pOIdEmpReserve2) {
        this.pOIdEmpReserve2 = pOIdEmpReserve2;
    }

    public String getBusinessTypeOID() {
        return businessTypeOID;
    }

    public void setBusinessTypeOID(String businessTypeOID) {
        this.businessTypeOID = businessTypeOID;
    }

    public Object getChangeTypeOID() {
        return changeTypeOID;
    }

    public void setChangeTypeOID(Object changeTypeOID) {
        this.changeTypeOID = changeTypeOID;
    }

    public Object getEntryStatus() {
        return entryStatus;
    }

    public void setEntryStatus(Object entryStatus) {
        this.entryStatus = entryStatus;
    }

    public boolean isCurrentRecord() {
        return isCurrentRecord;
    }

    public void setCurrentRecord(boolean currentRecord) {
        isCurrentRecord = currentRecord;
    }

    public Object getWorkYearBefore() {
        return workYearBefore;
    }

    public void setWorkYearBefore(Object workYearBefore) {
        this.workYearBefore = workYearBefore;
    }

    public Object getWorkYearGroupBefore() {
        return workYearGroupBefore;
    }

    public void setWorkYearGroupBefore(Object workYearGroupBefore) {
        this.workYearGroupBefore = workYearGroupBefore;
    }

    public int getWorkYearCompanyBefore() {
        return workYearCompanyBefore;
    }

    public void setWorkYearCompanyBefore(int workYearCompanyBefore) {
        this.workYearCompanyBefore = workYearCompanyBefore;
    }

    public Object getWorkYearTotal() {
        return workYearTotal;
    }

    public void setWorkYearTotal(Object workYearTotal) {
        this.workYearTotal = workYearTotal;
    }

    public Object getWorkYearGroupTotal() {
        return workYearGroupTotal;
    }

    public void setWorkYearGroupTotal(Object workYearGroupTotal) {
        this.workYearGroupTotal = workYearGroupTotal;
    }

    public double getWorkYearCompanyTotal() {
        return workYearCompanyTotal;
    }

    public void setWorkYearCompanyTotal(double workYearCompanyTotal) {
        this.workYearCompanyTotal = workYearCompanyTotal;
    }

    public Long getoIdOrganization() {
        return oIdOrganization;
    }

    public void setoIdOrganization(Long oIdOrganization) {
        this.oIdOrganization = oIdOrganization;
    }

    public Object getWhereabouts() {
        return whereabouts;
    }

    public void setWhereabouts(Object whereabouts) {
        this.whereabouts = whereabouts;
    }

    public Object getBlackStaffDesc() {
        return blackStaffDesc;
    }

    public void setBlackStaffDesc(Object blackStaffDesc) {
        this.blackStaffDesc = blackStaffDesc;
    }

    public Object getBlackListAddReason() {
        return blackListAddReason;
    }

    public void setBlackListAddReason(Object blackListAddReason) {
        this.blackListAddReason = blackListAddReason;
    }

    public Object getTransitionTypeOID() {
        return transitionTypeOID;
    }

    public void setTransitionTypeOID(Object transitionTypeOID) {
        this.transitionTypeOID = transitionTypeOID;
    }

    public Object getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(Object changeReason) {
        this.changeReason = changeReason;
    }

    public Object getProbationResult() {
        return probationResult;
    }

    public void setProbationResult(Object probationResult) {
        this.probationResult = probationResult;
    }

    public String getProbationActualStopDate() {
        return probationActualStopDate;
    }

    public void setProbationActualStopDate(String probationActualStopDate) {
        this.probationActualStopDate = probationActualStopDate;
    }

    public String getProbationStartDate() {
        return probationStartDate;
    }

    public void setProbationStartDate(String probationStartDate) {
        this.probationStartDate = probationStartDate;
    }

    public String getProbationStopDate() {
        return probationStopDate;
    }

    public void setProbationStopDate(String probationStopDate) {
        this.probationStopDate = probationStopDate;
    }

    public String getIsHaveProbation() {
        return isHaveProbation;
    }

    public void setIsHaveProbation(String isHaveProbation) {
        this.isHaveProbation = isHaveProbation;
    }

    public Object getRemarks() {
        return remarks;
    }

    public void setRemarks(Object remarks) {
        this.remarks = remarks;
    }

    public boolean isAddOrNotBlackList() {
        return addOrNotBlackList;
    }

    public void setAddOrNotBlackList(boolean addOrNotBlackList) {
        this.addOrNotBlackList = addOrNotBlackList;
    }

    public long getBusinessModifiedBy() {
        return businessModifiedBy;
    }

    public void setBusinessModifiedBy(long businessModifiedBy) {
        this.businessModifiedBy = businessModifiedBy;
    }

    public String getBusinessModifiedTime() {
        return businessModifiedTime;
    }

    public void setBusinessModifiedTime(String businessModifiedTime) {
        this.businessModifiedTime = businessModifiedTime;
    }

    public Object getTraineeStartDate() {
        return traineeStartDate;
    }

    public void setTraineeStartDate(Object traineeStartDate) {
        this.traineeStartDate = traineeStartDate;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public Object getCustomProperties() {
        return customProperties;
    }

    public void setCustomProperties(Object customProperties) {
        this.customProperties = customProperties;
    }

    public Map<String, String> getTranslateProperties() {
        return translateProperties;
    }

    public void setTranslateProperties(Map<String, String> translateProperties) {
        this.translateProperties = translateProperties;
    }

    public long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public long getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(long modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public String getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(String modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public boolean isStdIsDeleted() {
        return stdIsDeleted;
    }

    public void setStdIsDeleted(boolean stdIsDeleted) {
        this.stdIsDeleted = stdIsDeleted;
    }


}
