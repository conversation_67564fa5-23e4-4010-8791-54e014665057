package weaver.interfaces.workflow.action.cyitce.entity.beisen;

import java.util.Objects;

public class Employee {
    private Object originalId;
    private EmployeeInfo employeeInfo;
    private RecordInfo recordInfo;

    // Getters and Setters
    public Object getOriginalId() {
        return originalId;
    }

    public void setOriginalId(Object originalId) {
        this.originalId = originalId;
    }

    public EmployeeInfo getEmployeeInfo() {
        return employeeInfo;
    }

    public void setEmployeeInfo(EmployeeInfo employeeInfo) {
        this.employeeInfo = employeeInfo;
    }

    public RecordInfo getRecordInfo() {
        return recordInfo;
    }

    public void setRecordInfo(RecordInfo recordInfo) {
        this.recordInfo = recordInfo;
    }


}
