# 分贝通项目信息查询功能使用说明

## 功能简介

分贝通项目信息查询功能提供了基于原生JDBC的数据查询方法，可以根据批量ID获取项目信息，返回结果以`List<Map<String, String>>`形式存储，便于后续处理和展示。

## 提供的方法

### 1. 批量查询项目信息

```java
/**
 * 根据批量ID获取项目信息
 * 使用原生JDBC查询，返回List<Map<String, String>>结构
 * 
 * @param idList 数据库表主键ID列表
 * @return List<Map<String, String>>结构的项目信息，key为字段名，value为字段值（为空时返回""）
 */
public List<Map<String, String>> getProjectInfoByIds(List<Integer> idList)
```

### 2. 查询单个项目信息

```java
/**
 * 根据单个ID获取项目信息
 * 
 * @param id 数据库表主键ID
 * @return 项目信息Map，key为字段名，value为字段值（为空时返回""）
 */
public Map<String, String> getProjectInfoById(Integer id)
```

## 使用示例

### 批量查询示例

```java
// 创建FBTJob实例
FBTJob job = new FBTJob();

// 准备ID列表
List<Integer> idsToQuery = new ArrayList<>();
idsToQuery.add(1);
idsToQuery.add(2);

// 执行批量查询
List<Map<String, String>> projectInfoList = job.getProjectInfoByIds(idsToQuery);

// 处理查询结果
for (Map<String, String> projectInfo : projectInfoList) {
    System.out.println("ID: " + projectInfo.get("id"));
    System.out.println("项目代码: " + projectInfo.get("project_code"));
    System.out.println("项目名称: " + projectInfo.get("project_name"));
    System.out.println("公司ID: " + projectInfo.get("company_id"));
    System.out.println("公司名称: " + projectInfo.get("company_name"));
    // 处理其他字段...
}
```

### 单个项目查询示例

```java
// 创建FBTJob实例
FBTJob job = new FBTJob();

// 执行单个项目查询
Integer id = 1;
Map<String, String> projectInfo = job.getProjectInfoById(id);

// 检查结果是否为空
if (!projectInfo.isEmpty()) {
    System.out.println("ID: " + projectInfo.get("id"));
    System.out.println("项目代码: " + projectInfo.get("project_code"));
    System.out.println("项目名称: " + projectInfo.get("project_name"));
    // 处理其他字段...
} else {
    System.out.println("未找到项目信息");
}
```

## 返回结果说明

查询返回的Map中包含uf_fbt_project_info表的所有字段，字段名与数据库字段名一致。所有值都会被转换为字符串类型。主要字段包括：

- `id` - 数据库主键ID
- `project_code` - 项目代码
- `project_name` - 项目名称
- `company_id` - 主要关联公司ID
- `company_name` - 主要关联公司名称
- `related_companies` - 所有关联公司ID字符串
- `bill_code` - 分贝通账单编号
- `bill_start_time` - 账单开始时间
- `bill_end_time` - 账单结束时间
- `bill_status` - 账单状态
- `include_tax_amount` - 参考含税总金额
- `exclude_tax_amount` - 参考不含税总金额
- 其他各类金额和费用字段

## 字段缩写说明

为了优化数据库表结构，部分较长的字段名已采用缩写形式存储。在查询结果中，这些字段既可以通过缩写名访问，也可以通过原始长名称访问。主要缩写对应关系如下：

| 原始字段名 | 缩写形式 | 字段描述 |
|------------|----------|----------|
| air_ticket_domestic_include_tax | atdit | 国内机票含税金额 |
| air_ticket_domestic_exclude_tax | atdet | 国内机票不含税金额 |
| air_ticket_international_include_tax | atiit | 国际机票含税金额 |
| air_ticket_international_exclude_tax | atiet | 国际机票不含税金额 |
| corporate_payment_include_tax | cpit | 对公付款含税金额 |
| corporate_payment_exclude_tax | cpet | 对公付款不含税金额 |
| value_added_service_include_tax | vasit | 增值服务含税金额 |
| value_added_service_exclude_tax | vaset | 增值服务不含税金额 |
| hotel_overseas_include_tax | hoit | 海外酒店含税金额 |
| accommodation_fee_special_include_tax | afsit | 住宿费专票含税金额 |
| accommodation_fee_special_exclude_tax | afset | 住宿费专票不含税金额 |
| hotel_overseas_special_include_tax | hosit | 海外酒店专票含税金额 |
| hotel_overseas_special_exclude_tax | hoset | 海外酒店专票不含税金额 |
| accommodation_fee_ordinary_include_tax | afoit | 住宿费普票含税金额 |
| accommodation_fee_ordinary_exclude_tax | afoet | 住宿费普票不含税金额 |
| hotel_overseas_ordinary_include_tax | hooit | 海外酒店普票含税金额 |
| hotel_overseas_ordinary_exclude_tax | hooet | 海外酒店普票不含税金额 |
| total_accommodation_fee_include_tax | tafit | 住宿费含税总金额（酒店+海外酒店） |
| total_accommodation_fee_exclude_tax | tafet | 住宿费不含税总金额（酒店+海外酒店） |
| total_accommodation_fee_special | tafs | 住宿费专票总金额（酒店+海外酒店） |
| total_accommodation_fee_special_include_tax | tafsit | 住宿费专票含税金额（酒店+海外酒店） |
| total_accommodation_fee_special_exclude_tax | tafset | 住宿费专票不含税金额（酒店+海外酒店） |
| total_accommodation_fee_ordinary | tafo | 住宿费普票总金额（酒店+海外酒店） |
| total_accommodation_fee_ordinary_include_tax | tafoit | 住宿费普票含税金额（酒店+海外酒店） |
| total_accommodation_fee_ordinary_exclude_tax | tafoet | 住宿费普票不含税金额（酒店+海外酒店） |

示例：
```java
// 两种访问方式等效
String value1 = projectInfo.get("air_ticket_domestic_include_tax");
String value2 = projectInfo.get("atdit");
```

## 注意事项

1. 查询结果中，对于数据库中为NULL的值，会转换为空字符串`""`
2. 所有字段值都会被转换为字符串类型，包括数字和日期等
3. 批量查询时，如果提供的ID列表为空，将返回空列表
4. 单个查询时，如果ID小于等于0或未找到对应的信息，将返回空Map
5. 数据库连接信息(URL、用户名、密码)在方法内部硬编码，如需更改请修改方法中的相关常量