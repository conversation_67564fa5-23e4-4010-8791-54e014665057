package weaver.interfaces.workflow.action.cyitce.job;

import com.cloudstore.dev.api.bean.MessageBean;
import com.cloudstore.dev.api.bean.MessageType;
import com.cloudstore.dev.api.util.Util_Message;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.mobile.webservices.workflow.*;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashSet;
import java.util.Set;

/**
 * @ClassName: 用车归还流程触发
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-14  09:23
 * @Version: 1.0
 */
public class CarReturnJob extends BaseCronJob {
    RecordSet con = new RecordSet();

    private void writeLog(String var){
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(),var);
    }

    @Override
    public void execute() {
        Calendar now = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("SELECT id,workflowname FROM workflow_base where formid=? and isvalid=1",new Object[]{-1881});
            if(!rs.next()){
                throw new Exception("流程不存在");
            }

            String workflowid = rs.getString(1);
            String workflowname = rs.getString(2);

            rs.executeQuery("select id,sqr,cph,sqbm,cphwb from uf_ycsqb " +
                    "where sfgh=0 " +
                    "and ((ycjh=0 and yjfcsj<=cast(GETDATE() as DATE)) or (ycjh=1 and cqsyyjjssj<=cast(GETDATE() as DATE))) " +
                    "and id not in (select cph from formtable_main_1881)");
            while (rs.next()){
                //主字段
                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[4];
                // 字段信息
                wrti[0] = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqr");
                wrti[0].setFieldValue(rs.getString("sqr"));
                wrti[0].setView(true);// 字段是否可见
                wrti[0].setEdit(false);// 字段是否可编辑

                wrti[1] = new WorkflowRequestTableField();
                wrti[1].setFieldName("sqrq");
                wrti[1].setFieldValue(format.format(now.getTime()));
                wrti[1].setView(true);// 字段是否可见
                wrti[1].setEdit(false);// 字段是否可编辑

                wrti[2] = new WorkflowRequestTableField();
                wrti[2].setFieldName("cph");
                wrti[2].setFieldValue(rs.getString("id"));
                wrti[2].setView(true);// 字段是否可见
                wrti[2].setEdit(false);// 字段是否可编辑

                wrti[3] = new WorkflowRequestTableField();
                wrti[3].setFieldName("sqbm");
                wrti[3].setFieldValue(rs.getString("sqbm"));
                wrti[3].setView(true);// 字段是否可见
                wrti[3].setEdit(false);// 字段是否可编辑

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //添加工作流id
                WorkflowExtInfo  wbi = new WorkflowExtInfo();
                wbi.setWorkflowId(workflowid);
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(rs.getString("sqr"));//创建人id
                wri.setRequestLevel("0");//0 正常，1重要，2紧急
                wri.setRequestName(workflowname+" "+format.format(now.getTime())+" "+userid(rs.getString("sqr"))+"，车牌号："+rs.getString("cphwb"));//流程标题
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl wsi = new WorkflowServiceImpl();
                String requestid =wsi.saveWorkflowRequest(wri, -1,Util.getIntValue(rs.getString("sqr"),1),"","","");
//                String requestid = wsi.doCreateWorkflowRequest(wri, Util.getIntValue(rs.getString("sqr"),1),"","");

                MessageType messageType = MessageType.newInstance(20); // 消息来源
                Set<String> userIdList = new HashSet<>(); // 接收人id 必填
                userIdList.add(rs.getString("sqr"));
                String title = "车辆(车牌号："+rs.getString("cphwb")+") 归还提醒"; // 标题
                String context = "申请的车辆(车牌号："+rs.getString("cphwb")+")返程日期到了，请进行车辆归还流程"; // 内容
                String linkUrl = "/spa/workflow/static4form/index.html?_rdm=1726304752124#/main/workflow/req?ismonitor=1&requestid="+requestid; // PC端链接
                String linkMobileUrl = "/spa/coms/static4mobileMessage/index.html#/?typeId=2&flag=1"; // 移动端链接

                try {
                    MessageBean messageBean = Util_Message.createMessage(messageType, userIdList, title, context, linkUrl, linkMobileUrl);
                    messageBean.setCreater(1);// 创建人id
                    Util_Message.store(messageBean);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new Exception("消息推送失败，e:"+e.getMessage());
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            writeLog("============="+e.getMessage());
        }
    }

    public String userid(String id){
        con.executeQuery("SELECT lastname FROM HrmResource where id=?",new Object[]{id});
        if (con.next()){
            return con.getString(1);
        }

        return "";
    }
}