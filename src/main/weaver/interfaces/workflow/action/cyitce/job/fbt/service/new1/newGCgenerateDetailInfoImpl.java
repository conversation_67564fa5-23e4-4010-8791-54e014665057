package weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1;

import cn.hutool.json.JSONUtil;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.FlowUtil;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYProjectDetailInfo;
import weaver.mobile.webservices.workflow.WorkflowRequestInfo;
import weaver.mobile.webservices.workflow.WorkflowServiceImpl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 12:00
 * @describe 工程公司项目详情生成器实现类
 */
public class newGCgenerateDetailInfoImpl extends newAbstractDetailInfoGenerator {

    @Override
    protected String getFirstSql() {
        return "select id,ejxmnbbh,yjbmfq,ejbmfq,sjbmfq,yjxmmc,yjxmbh,jsms from uf_ejxmlx where ejxmnbbh=?";
    }

    @Override
    protected String getSecondSql() {
        return "select bmcx,sfsggxm,smdj from uf_yjxmlxjm where id=?";
    }

    @Override
    protected String getThirdSql() {
        return "select yjbm,ejbm,sjbm from view_bmjz_cw where sqbm=?";
    }

    @Override
    protected String getFirstQueryParam(Map<String, String> map) {
        return map.get("project_code");
    }

    @Override
    protected String getSecondQueryParam(Map<String, String> resultMap) {
        return resultMap.get("1_yjxmmc");
    }

    @Override
    protected String getThirdQueryParam(Map<String, String> resultMap) {
        return resultMap.get("2_bmcx");
    }

    @Override
    protected STYProjectDetailInfo convertToProjectDetailInfo(Map<String, String> resultMap) {
        STYProjectDetailInfo detailInfo = new STYProjectDetailInfo();

        // 设置id字段
        detailInfo.setId(resultMap.get("1_id"));

        // 设置第一个查询的字段 (uf_ejxmlx表)
        detailInfo.setEjxmnbbh(resultMap.get("1_ejxmnbbh"));
        detailInfo.setYjbmfq(resultMap.get("1_yjbmfq"));
        detailInfo.setEjbmfq(resultMap.get("1_ejbmfq"));
        detailInfo.setSjbmfq(resultMap.get("1_sjbmfq"));
        detailInfo.setYjxmmc(resultMap.get("1_yjxmmc"));
        detailInfo.setYjxmbh(resultMap.get("1_yjxmbh"));
        detailInfo.setJsms(resultMap.get("1_jsms"));

        // 设置第二个查询的字段 (uf_yjxmlxjm表)
        detailInfo.setBmcx(resultMap.get("2_bmcx"));
        detailInfo.setSfsggxm(resultMap.get("2_sfsggxm"));
        detailInfo.setXmlx(resultMap.get("2_smdj"));

        // 设置第三个查询的字段 (view_bmjz_cw表)
        detailInfo.setCwYjbm(resultMap.get("3_yjbm"));
        detailInfo.setCwEjbm(resultMap.get("3_ejbm"));
        detailInfo.setCwSjbm(resultMap.get("3_sjbm"));

        return detailInfo;
    }

    /**
     * 生成流程数据
     *
     * @param list       项目详情列表
     * @param userId     用户ID
     * @param gszt       公司主体
     * @param tableIndex 表索引
     */
    public String generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex) {
        log.writeLog("进入构建方法");
        String requestName = FlowUtil.generateExpenseTitle(userId);
        String userName = FlowUtil.userid(userId);
        Map<String, String> fieldMap = new HashMap<>();
        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());
        fieldMap.put("sqr", userId);// 申请人
        fieldMap.put("sqrq", currentDate);// 申请日期
        fieldMap.put("zdrq", currentDate);// 制单日期
        fieldMap.put("zdr", userId);// 制单人
        fieldMap.put("gszt", gszt);// 公司主体
        String deptId = FlowUtil.queryDepartmentIdByEmployeeId(Integer.parseInt(userId));
        fieldMap.put("szbm", StringUtils.hasText(deptId) ? deptId : "");// 申请部门
        Map<String, Boolean[]> viewEditMap = new HashMap<>();
        viewEditMap.put("gszt", new Boolean[]{true, true});
        List<Map<String, String>> detailTableData = new ArrayList<>();

        // 遍历项目列表，将每个项目的属性添加到明细表中
        for (STYProjectDetailInfo project : list) {
            Map<String, String> rowData = new HashMap<>();
            log.writeLog("project: " + project.toString());
            // 添加项目基本信息gcyjxmmc
            rowData.put("gcejxmbh", project.getEjxmnbbh());  // 二级项目编号
            rowData.put("gcejxmmc", project.getId());  // 二级项目名称
            rowData.put("gcyjxmmc", project.getYjxmmc());  // 一级项目名称
            rowData.put("gcyjxmbh", project.getYjxmbh());  // 一级项目编号

            // 添加部门信息
            rowData.put("yjbm", project.getYjbmfq());      // 一级部门
            rowData.put("ejbm", project.getEjbmfq());      // 二级部门
            rowData.put("sjbm", project.getSjbmfq());      // 三级部门

            // 添加财务部门信息
            rowData.put("cwyjbm", project.getCwYjbm());  // 财务一级部门
            rowData.put("cwejbm", project.getCwEjbm());  // 财务二级部门
            rowData.put("cwsjbm", project.getCwSjbm());  // 财务三级部门

            // 添加项目类型信息
            // rowData.put("xmlx", project.getXmlx());      // 项目类型
            // rowData.put("xmlxDesc", project.getXmlxDesc()); // 项目类型描述

            // 添加其他信息
            rowData.put("fycdbm", project.getBmcx());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            // rowData.put("sfsggxmDesc", project.getSfsggxmDesc()); // 是否是公共项目描述
            rowData.put("jsms", project.getJsms()); // 计税模式 0 增值税 1 简易计税
            // rowData.put("jsmsDesc", project.getJsmsDesc()); // 计税模式描述

            // 计算项目性质（xmxz）
            String xmxz = "2"; // 默认为业务项目
            String sfsggxm = project.getSfsggxm();
            Map<String, String> map = project.getMap();
            String jglx = project.getXmlx(); // 获取监管类型

            // 如果是公共项目(sfsggxm=0)，则项目性质=0
            if ("0".equals(sfsggxm)) {
                xmxz = "0";
            }
            // 如果不是公共项目，且监管类型=4(研发项目)，则项目性质=1(研发)
            else if (jglx != null && "4".equals(jglx)) {
                xmxz = "1";
            }
            // 否则为业务项目(2)，这是默认值，不需要额外设置

            // 添加项目性质字段
            rowData.put("xmxz", xmxz);
            rowData.put("sxmcv2", map.get("sxmc"));      // 事项名称
            rowData.put("dcfhsje", map.get("car_include_tax")); // 打车 不含税
            rowData.put("mhfzjj", map.get("airport_fee")); // 民航发展基金
            rowData.put("xcdpmje", map.get("air_ticket_total_include_tax")); // 票面金额
            rowData.put("xcdse", FlowUtil.calculateAmount(map.get("air_ticket_total_include_tax"), map.get("airport_fee")).toPlainString()); // 火车票税额
            rowData.put("xcdsl", "9%"); // 机票税率
            rowData.put("hcphsje", map.get("train_include_tax")); // 火车票票价
            rowData.put("hcpbhsje", map.get("train_exclude_tax")); // 火车票不含税
            rowData.put("hcpse", map.get("train_tax")); // 火车票税额

            rowData.put("zpzspbhsje", map.get("tafset")); // 住宿费专用发票不含税
            // 获取两个金额字符串
            String tafsitStr = map.get("tafsit");
            String tafsetStr = map.get("tafset");

            // 初始化默认值为 0
            BigDecimal tafsit = BigDecimal.ZERO;
            BigDecimal tafset = BigDecimal.ZERO;

            try {
                // 尝试将字符串转换为 BigDecimal 类型
                if (tafsitStr != null) {
                    tafsit = new BigDecimal(tafsitStr);
                }
                if (tafsetStr != null) {
                    tafset = new BigDecimal(tafsetStr);
                }
            } catch (NumberFormatException e) {
                // 处理转换失败的情况
                log.writeLog("金额转换失败: " + e.getMessage());
            }

            // 计算差值
            BigDecimal difference = tafsit.subtract(tafset);
            // 将差值添加到 rowData 中
            rowData.put("zspse", difference.toPlainString()); // // 住宿费税额

            rowData.put("zspsl", "6%"); // 住宿费税率
            rowData.put("ppzsfhsje", map.get("tafoit")); // 住宿费普通发票（含税金额）

            rowData.put("ddhcpfwfbhsje", map.get("manual_price")); // 代打火车票服务费（不含税金额）
            rowData.put("ddhcpfwfse", map.get("manual_price_tax")); // 代打火车票服务费（税额）
            rowData.put("zjzclx", "0"); // 资金支出类型
            rowData.put("mxid", map.get("id")); // mxid
            rowData.put("clbt", map.get("clbt")); // 差旅补贴
            // rowData.put("ppzsfhsje", map.get("tafoit")); // 住宿费普通发票（含税金额）

            log.writeLog("构建完成");
            // 添加到明细表数据列表
            detailTableData.add(rowData);
        }
        Map<String, Boolean[]> detailViewEditMap = new HashMap<>();
        // 创建工作流
        newSTYgenerateDetailInfoImpl newSTYgenerateDetailInfo = new newSTYgenerateDetailInfoImpl();
        WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
                fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        log.writeLog("创建工作流对象 ：" + JSONUtil.toJsonStr(workflowRequestInfo));
        WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
        String requestId = workflowService.saveWorkflowRequest(workflowRequestInfo, -1, Integer.parseInt(userId), "", "", "");
        log.writeLog("创建工作流对象 requestId：" + requestId);
        String id = newSTYgenerateDetailInfo.getIdByRequestId("formtable_main_1981", requestId);

        log.writeLog("开始添加明细 : 明细表：formtable_main_1981_dt1  id: " + id + "   数据 ： " + JSONUtil.toJsonStr(detailTableData));
        int i = newSTYgenerateDetailInfo.batchInsertData(detailTableData, "formtable_main_1981_dt1", id);
        log.writeLog("添加明细成功");
        return
        requestId;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试代码
        List<Map<String, String>> testDataList = new ArrayList<>();

        Map<String, String> map1 = new HashMap<>();
        map1.put("code", "GC-EJXM20240029");
        map1.put("userId", "2947");
        map1.put("gszt", "6");
        testDataList.add(map1);

        List<STYProjectDetailInfo> results = new newGCgenerateDetailInfoImpl().generateDetailInfo(testDataList);
    }
} 