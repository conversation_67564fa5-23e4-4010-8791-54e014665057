-- 添加整合住宿费数据字段（酒店+海外酒店）
ALTER TABLE fbt_project_info ADD total_accommodation_fee DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费总金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_include_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费含税总金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_exclude_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费不含税总金额（酒店+海外酒店）';

-- 添加整合住宿费专票数据字段（酒店专票+海外酒店专票）
ALTER TABLE fbt_project_info ADD total_accommodation_fee_special DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费专票总金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_special_include_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费专票含税金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_special_exclude_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费专票不含税金额（酒店+海外酒店）';

-- 添加整合住宿费普票数据字段（酒店普票+海外酒店普票）
ALTER TABLE fbt_project_info ADD total_accommodation_fee_ordinary DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费普票总金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_ordinary_include_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费普票含税金额（酒店+海外酒店）';
ALTER TABLE fbt_project_info ADD total_accommodation_fee_ordinary_exclude_tax DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '住宿费普票不含税金额（酒店+海外酒店）';

-- 添加索引以提高查询性能
CREATE INDEX idx_fbt_project_info_total_accommodation ON fbt_project_info (total_accommodation_fee);
CREATE INDEX idx_fbt_project_info_total_accommodation_special ON fbt_project_info (total_accommodation_fee_special);
CREATE INDEX idx_fbt_project_info_total_accommodation_ordinary ON fbt_project_info (total_accommodation_fee_ordinary);

-- 添加代打火车服务费、机建费和燃油费字段
ALTER TABLE fbt_project_info ADD manual_price DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '代打火车服务费';
ALTER TABLE fbt_project_info ADD airport_fee DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '机建费';
ALTER TABLE fbt_project_info ADD fuel_fee DECIMAL(18,2) DEFAULT 0 NOT NULL COMMENT '燃油费'; 