package weaver.interfaces.workflow.action.cyitce.job.fbt;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.util.StringUtils;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29 09:07
 * @describe 分贝通差补结算单统计任务 - 按项目维度统计补贴金额
 */
public class Bill<PERSON>ob extends BaseCronJob {

    private BaseBean log = new BaseBean();

    // 固定的操作员信息
    private static final String OPERATOR_ID = "681b56234809cb6616295316";
    private static final int ID_TYPE = 2;

    // API基础URL
    private static final String BASE_URL = "https://openapi.fenbeitong.com";

    @Override
    public void execute() {
        try {
            log.writeLog("开始执行差补结算单统计任务");

            // 第一步：查询上个月已结算的差补结算单列表
            List<String> settlementIds = querySettlementList();
            log.writeLog("获取到结算单数量: " + settlementIds.size());

            if (settlementIds.isEmpty()) {
                log.writeLog("未找到上个月的已结算账单，任务结束");
                return;
            }

            // 第二步：查询结算单详情，提取项目代码和补贴金额
            // Map<String, BigDecimal> projectAllowanceMap = querySettlementDetails(settlementIds);
            Map<String, Map<String, BigDecimal>> projectAllowanceMap = querySettlementDetailsByPeriod(settlementIds);

            log.writeLog("统计到项目数量: " + projectAllowanceMap.size());

            if (projectAllowanceMap.isEmpty()) {
                log.writeLog("未找到项目相关的补贴数据，任务结束");
                return;
            }
            Set<String> allProjectCodes = new HashSet<>();
            for (Map<String, BigDecimal> projectMap : projectAllowanceMap.values()) {
                allProjectCodes.addAll(projectMap.keySet());
            }
            // 第三步：查询项目对应的companyId
            // Map<String, String> projectCompanyMap = queryProjectCompanyIds(projectAllowanceMap.keySet());
            Map<String, String> projectCompanyMap = queryProjectCompanyIds(allProjectCodes);

            log.writeLog("获取到项目公司信息数量: " + projectCompanyMap.size());

            // 输出统计结果
            // outputStatistics(projectAllowanceMap, projectCompanyMap);
            String[] lastMonthRange = getLastMonthRange();
            billSave(projectAllowanceMap, projectCompanyMap, lastMonthRange[0], lastMonthRange[1]);

            log.writeLog("差补结算单统计任务执行完成");

        } catch (Exception e) {
            log.writeLog("执行差补结算单统计任务时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取分贝通API访问令牌
     * 参考FBTJob2的实现
     *
     * @return 访问令牌
     * @throws IOException 网络请求异常
     */
    public String getToken() throws IOException {
        String apiUrl = BASE_URL + "/openapi/auth/getToken";
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        String jsonInputString = "{\"app_id\": \"67d7f3243d83426b2c5cc4b0\",\"app_key\": \"6801e92efe12446af1d556ad\"}";

        try (OutputStream os = connection.getOutputStream()) {
            os.write(jsonInputString.getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                JSONObject jsonObj = JSONUtil.parseObj(response);
                if (jsonObj.getInt("code", -1) == 0) {
                    return jsonObj.getStr("data");
                } else {
                    throw new IOException("获取Token失败: " + jsonObj.getStr("msg", "未知错误"));
                }
            }
        } else {
            throw new IOException("获取Token失败，HTTP响应码: " + connection.getResponseCode());
        }
    }

    /**
     * 查询差补结算单列表
     * 获取上个月已结算的结算单编号
     *
     * @return 结算单ID列表
     * @throws IOException 网络请求异常
     */
    private List<String> querySettlementList() throws IOException {
        String token = getToken();
        String apiUrl = BASE_URL + "/openapi/reimbursement/settlement/v2/list";

        // 构建请求参数 - 查询上个月已结算的账单
        JSONObject requestBody = new JSONObject();
        requestBody.put("page_index", 1);
        requestBody.put("page_size", 100); // 先设置较大的页面大小
        requestBody.put("status", 3); // 3-确认结算
        // 设置操作员信息
        JSONObject operatorInfo = new JSONObject();
        operatorInfo.put("id", OPERATOR_ID);
        operatorInfo.put("id_type", ID_TYPE);
        requestBody.put("operator_id_info", operatorInfo);

        // 设置查询时间范围为上个月
        String[] lastMonthRange = getLastMonthRange();
        requestBody.put("confirm_time_from", lastMonthRange[0]);
        // requestBody.put("confirm_time_from", "2025-07-01 00:00:00");
        requestBody.put("confirm_time_to", lastMonthRange[1]);
        // requestBody.put("confirm_time_to", "2025-08-31 23:59:59");

        log.writeLog("查询差补结算单列表请求参数: " + requestBody.toString());

        List<String> settlementIds = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        // 分页查询所有结算单
        do {
            requestBody.put("page_index", currentPage);

            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("access-token", token);
            connection.setDoOutput(true);

            try (OutputStream os = connection.getOutputStream()) {
                os.write(requestBody.toString().getBytes("utf-8"));
            }

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                try (InputStream is = connection.getInputStream()) {
                    String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                    JSONObject jsonObj = JSONUtil.parseObj(response);

                    if (jsonObj.getInt("code", -1) == 0) {
                        JSONObject data = jsonObj.getJSONObject("data");
                        totalPages = data.getInt("total_pages", 1);

                        JSONArray orders = data.getJSONArray("orders");
                        if (orders != null) {
                            for (int i = 0; i < orders.size(); i++) {
                                JSONObject order = orders.getJSONObject(i);
                                String settlementId = order.getStr("id");
                                if (settlementId != null) {
                                    settlementIds.add(settlementId);
                                }
                            }
                        }

                        log.writeLog("第" + currentPage + "页查询到结算单数量: " + (orders != null ? orders.size() : 0));
                    } else {
                        log.writeLog("查询结算单列表失败: " + jsonObj.getStr("msg"));
                        break;
                    }
                }
            } else {
                log.writeLog("查询结算单列表HTTP请求失败，响应码: " + connection.getResponseCode());
                break;
            }

            currentPage++;
        } while (currentPage <= totalPages);

        return settlementIds;
    }

    /**
     * 查询结算单详情
     * 根据结算单ID列表，获取详情并按项目统计补贴金额
     *
     * @param settlementIds 结算单ID列表
     * @return 项目代码 -> 补贴金额的映射
     * @throws IOException 网络请求异常
     */
    private Map<String, BigDecimal> querySettlementDetails(List<String> settlementIds) throws IOException {
        String token = getToken();
        String apiUrl = BASE_URL + "/openapi/reimbursement/settlement/v1/list";

        Map<String, BigDecimal> projectAllowanceMap = new HashMap<>();

        // 构建请求参数 - 使用settlement_ids数组
        JSONObject requestBody = new JSONObject();
        requestBody.put("settlement_ids", settlementIds);

        log.writeLog("查询结算单详情，IDs: " + settlementIds.toString());

        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);
        connection.setDoOutput(true);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(requestBody.toString().getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                log.writeLog("结算单详情响应: " + response);

                JSONObject jsonObj = JSONUtil.parseObj(response);

                if (jsonObj.getInt("code", -1) == 0) {
                    // data字段直接是数组，不是包含settlements的对象
                    JSONArray settlements = jsonObj.getJSONArray("data");

                    if (settlements != null) {
                        for (int s = 0; s < settlements.size(); s++) {
                            JSONObject settlement = settlements.getJSONObject(s);

                            String settlementId = settlement.getStr("id");
                            BigDecimal totalAllowanceMoney = settlement.getBigDecimal("total_allowance_money", BigDecimal.ZERO);

                            log.writeLog("处理结算单: " + settlementId + ", 总补贴金额: " + totalAllowanceMoney);


                            // // 解析details数组中的员工详情
                            // JSONArray details = settlement.getJSONArray("details");
                            // if (details != null) {
                            //     for (int d = 0; d < details.size(); d++) {
                            //         JSONObject detail = details.getJSONObject(d);
                            //
                            //         // 解析employee_details数组
                            //         JSONArray employeeDetails = detail.getJSONArray("employee_details");
                            //         if (employeeDetails != null) {
                            //             String lastProjectCode = null; // 保存上一个遍历到的项目代码
                            //             List<BigDecimal> pendingAmounts = new ArrayList<>(); // 暂存没有项目代码的金额
                            //
                            //             for (int i = 0; i < employeeDetails.size(); i++) {
                            //                 JSONObject employeeDetail = employeeDetails.getJSONObject(i);
                            //                 BigDecimal employeeAllowanceMoney = employeeDetail.getBigDecimal("allowance_money", BigDecimal.ZERO);
                            //
                            //                 String currentProjectCode = null;
                            //
                            //                 // 尝试从当前元素获取项目代码
                            //                 JSONArray costAttributions = employeeDetail.getJSONArray("cost_attributions");
                            //                 if (costAttributions != null) {
                            //                     for (int j = 0; j < costAttributions.size(); j++) {
                            //                         JSONObject costAttribution = costAttributions.getJSONObject(j);
                            //
                            //                         // 查找type为2的项目归属
                            //                         if (costAttribution.getInt("type", -1) == 2) {
                            //                             JSONArray projectDetails = costAttribution.getJSONArray("details");
                            //                             if (projectDetails != null && projectDetails.size() > 0) {
                            //                                 JSONObject projectDetail = projectDetails.getJSONObject(0);
                            //                                 String projectCode = projectDetail.getStr("code");
                            //
                            //                                 if (projectCode != null && !projectCode.trim().isEmpty()) {
                            //                                     currentProjectCode = projectCode;
                            //                                     break;
                            //                                 }
                            //                             }
                            //                         }
                            //                     }
                            //                 }
                            //
                            //                 if (currentProjectCode != null) {
                            //                     // 找到了项目代码，先处理之前暂存的金额
                            //                     if (lastProjectCode == null && !pendingAmounts.isEmpty()) {
                            //                         // 第一次找到项目代码，处理之前暂存的金额
                            //                         for (BigDecimal pendingAmount : pendingAmounts) {
                            //                             BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                            //                             projectAllowanceMap.put(currentProjectCode, currentAmount.add(pendingAmount));
                            //                             log.writeLog("结算单: " + settlementId + ", 项目: " + currentProjectCode +
                            //                                     ", 补贴金额: " + pendingAmount + " (处理暂存金额)");
                            //                         }
                            //                         pendingAmounts.clear();
                            //                     }
                            //
                            //                     // 处理当前金额
                            //                     BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                            //                     projectAllowanceMap.put(currentProjectCode, currentAmount.add(employeeAllowanceMoney));
                            //                     log.writeLog("结算单: " + settlementId + ", 项目: " + currentProjectCode +
                            //                             ", 员工补贴金额: " + employeeAllowanceMoney);
                            //
                            //                     lastProjectCode = currentProjectCode;
                            //                 } else if (lastProjectCode != null) {
                            //                     // 没有项目代码但有上一个项目代码，直接累加
                            //                     BigDecimal currentAmount = projectAllowanceMap.getOrDefault(lastProjectCode, BigDecimal.ZERO);
                            //                     projectAllowanceMap.put(lastProjectCode, currentAmount.add(employeeAllowanceMoney));
                            //                     log.writeLog("结算单: " + settlementId + ", 项目: " + lastProjectCode +
                            //                             ", 员工补贴金额: " + employeeAllowanceMoney + " (使用上一个项目代码)");
                            //                 } else {
                            //                     // 既没有当前项目代码也没有上一个项目代码，暂存金额
                            //                     pendingAmounts.add(employeeAllowanceMoney);
                            //                 }
                            //             }
                            //         }
                            //     }
                            // }
                            // 解析details数组中的员工详情
                            JSONArray details = settlement.getJSONArray("details");
                            if (details != null) {
                                String globalLastProjectCode = null; // 全局保存上一个子数组的项目代码

                                for (int d = 0; d < details.size(); d++) {
                                    JSONObject detail = details.getJSONObject(d);

                                    // 解析employee_details数组
                                    JSONArray employeeDetails = detail.getJSONArray("employee_details");
                                    if (employeeDetails != null) {
                                        String lastProjectCode = null; // 保存当前子数组中上一个遍历到的项目代码
                                        List<BigDecimal> pendingAmounts = new ArrayList<>(); // 暂存没有项目代码的金额
                                        boolean foundProjectInCurrentDetail = false; // 标记当前子数组是否找到了项目代码

                                        for (int i = 0; i < employeeDetails.size(); i++) {
                                            JSONObject employeeDetail = employeeDetails.getJSONObject(i);
                                            BigDecimal employeeAllowanceMoney = employeeDetail.getBigDecimal("allowance_money", BigDecimal.ZERO);

                                            String currentProjectCode = null;

                                            // 尝试从当前元素获取项目代码
                                            JSONArray costAttributions = employeeDetail.getJSONArray("cost_attributions");
                                            if (costAttributions != null) {
                                                for (int j = 0; j < costAttributions.size(); j++) {
                                                    JSONObject costAttribution = costAttributions.getJSONObject(j);

                                                    // 查找type为2的项目归属
                                                    if (costAttribution.getInt("type", -1) == 2) {
                                                        JSONArray projectDetails = costAttribution.getJSONArray("details");
                                                        if (projectDetails != null && projectDetails.size() > 0) {
                                                            JSONObject projectDetail = projectDetails.getJSONObject(0);
                                                            String projectCode = projectDetail.getStr("code");

                                                            if (projectCode != null && !projectCode.trim().isEmpty()) {
                                                                currentProjectCode = projectCode;
                                                                foundProjectInCurrentDetail = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (currentProjectCode != null) {
                                                // 找到了项目代码，先处理之前暂存的金额
                                                if (lastProjectCode == null && !pendingAmounts.isEmpty()) {
                                                    // 第一次找到项目代码，处理之前暂存的金额
                                                    for (BigDecimal pendingAmount : pendingAmounts) {
                                                        BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                                                        projectAllowanceMap.put(currentProjectCode, currentAmount.add(pendingAmount));
                                                        log.writeLog("结算单: " + settlementId + ", 项目: " + currentProjectCode +
                                                                ", 补贴金额: " + pendingAmount + " (处理暂存金额)");
                                                    }
                                                    pendingAmounts.clear();
                                                }

                                                // 处理当前金额
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(currentProjectCode, currentAmount.add(employeeAllowanceMoney));
                                                log.writeLog("结算单: " + settlementId + ", 项目: " + currentProjectCode +
                                                        ", 员工补贴金额: " + employeeAllowanceMoney);

                                                lastProjectCode = currentProjectCode;
                                            } else if (lastProjectCode != null) {
                                                // 没有项目代码但有当前子数组的上一个项目代码，直接累加
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(lastProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(lastProjectCode, currentAmount.add(employeeAllowanceMoney));
                                                log.writeLog("结算单: " + settlementId + ", 项目: " + lastProjectCode +
                                                        ", 员工补贴金额: " + employeeAllowanceMoney + " (使用当前子数组上一个项目代码)");
                                            } else {
                                                // 既没有当前项目代码也没有当前子数组的上一个项目代码，暂存金额
                                                pendingAmounts.add(employeeAllowanceMoney);
                                            }
                                        }

                                        // 处理完当前子数组后，如果整个子数组都没有找到项目代码，使用全局上一个项目代码
                                        if (!foundProjectInCurrentDetail && globalLastProjectCode != null && !pendingAmounts.isEmpty()) {
                                            for (BigDecimal pendingAmount : pendingAmounts) {
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(globalLastProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(globalLastProjectCode, currentAmount.add(pendingAmount));
                                                log.writeLog("结算单: " + settlementId + ", 项目: " + globalLastProjectCode +
                                                        ", 补贴金额: " + pendingAmount + " (使用上一个子数组项目代码)");
                                            }
                                        }

                                        // 更新全局项目代码
                                        if (foundProjectInCurrentDetail && lastProjectCode != null) {
                                            globalLastProjectCode = lastProjectCode;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        log.writeLog("查询结算单详情失败，错误: " + jsonObj.getStr("msg"));
                    }
                }
            }
        } else {
            log.writeLog("查询结算单详情HTTP请求失败，响应码: " + connection.getResponseCode());
        }

        return projectAllowanceMap;
    }


    /**
     * 查询结算单详情
     * 根据结算单ID列表，获取详情并按项目统计补贴金额
     *
     * @param settlementIds 结算单ID列表
     * @return 项目代码 -> 补贴金额的映射
     * @throws IOException 网络请求异常
     */
    private Map<String, BigDecimal> querySettlementDetails1(List<String> settlementIds) throws IOException {
        String token = getToken();
        String apiUrl = BASE_URL + "/openapi/reimbursement/settlement/v1/list";

        Map<String, BigDecimal> projectAllowanceMap = new HashMap<>();

        // 构建请求参数 - 使用settlement_ids数组
        JSONObject requestBody = new JSONObject();
        requestBody.put("settlement_ids", settlementIds);

        log.writeLog("查询结算单详情，IDs: " + settlementIds.toString());

        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);
        connection.setDoOutput(true);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(requestBody.toString().getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                log.writeLog("结算单详情响应: " + response);

                JSONObject jsonObj = JSONUtil.parseObj(response);

                if (jsonObj.getInt("code", -1) == 0) {
                    // data字段直接是数组，不是包含settlements的对象
                    JSONArray settlements = jsonObj.getJSONArray("data");

                    if (settlements != null) {
                        for (int s = 0; s < settlements.size(); s++) {
                            JSONObject settlement = settlements.getJSONObject(s);

                            String settlementId = settlement.getStr("id");
                            BigDecimal totalAllowanceMoney = settlement.getBigDecimal("total_allowance_money", BigDecimal.ZERO);

                            log.writeLog("处理结算单: " + settlementId + ", 总补贴金额: " + totalAllowanceMoney);

                            // 解析details数组中的员工详情
                            JSONArray details = settlement.getJSONArray("details");
                            if (details != null) {
                                for (int d = 0; d < details.size(); d++) {
                                    JSONObject detail = details.getJSONObject(d);
                                    // detail.getStr()
                                    // 获取该员工的补贴金额
                                    BigDecimal employeeAllowanceMoney = detail.getBigDecimal("allowance_money", BigDecimal.ZERO);

                                    // 解析employee_details数组
                                    JSONArray employeeDetails = detail.getJSONArray("employee_details");
                                    if (employeeDetails != null) {
                                        boolean foundProject = false; // 标记是否已找到项目代码

                                        // for (int i = 0; i < employeeDetails.size() && !foundProject; i++) {
                                        for (int i = 0; i < employeeDetails.size(); i++) {
                                            JSONObject employeeDetail = employeeDetails.getJSONObject(i);

                                            // cost_attributions在employee_details的每个元素中
                                            JSONArray costAttributions = employeeDetail.getJSONArray("cost_attributions");
                                            if (costAttributions != null) {
                                                for (int j = 0; j < costAttributions.size(); j++) {
                                                    // for (int j = 0; j < costAttributions.size() && !foundProject; j++) {
                                                    JSONObject costAttribution = costAttributions.getJSONObject(j);

                                                    // 查找type为2的项目归属
                                                    if (costAttribution.getInt("type", -1) == 2) {
                                                        JSONArray projectDetails = costAttribution.getJSONArray("details");
                                                        if (projectDetails != null && projectDetails.size() > 0) {
                                                            JSONObject projectDetail = projectDetails.getJSONObject(0);
                                                            String projectCode = projectDetail.getStr("code");

                                                            if (projectCode != null && !projectCode.trim().isEmpty()) {
                                                                // 累加项目的补贴金额
                                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(projectCode, BigDecimal.ZERO);
                                                                projectAllowanceMap.put(projectCode, currentAmount.add(employeeAllowanceMoney));

                                                                log.writeLog("结算单: " + settlementId + ", 项目: " + projectCode + ", 员工补贴金额: " + employeeAllowanceMoney);

                                                                foundProject = true; // 找到项目代码，设置标记
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        log.writeLog("查询结算单详情失败，错误: " + jsonObj.getStr("msg"));
                    }
                }
            }
        } else {
            log.writeLog("查询结算单详情HTTP请求失败，响应码: " + connection.getResponseCode());
        }

        return projectAllowanceMap;
    }

    /**
     * 查询项目对应的公司ID
     * 根据项目代码查询部门信息，获取companyId
     *
     * @param projectCodes 项目代码集合
     * @return 项目代码 -> 公司ID的映射
     * @throws IOException 网络请求异常
     */
    private Map<String, String> queryProjectCompanyIds(Set<String> projectCodes) throws IOException {
        String token = getToken();
        String apiUrl = BASE_URL + "/openapi/org/project/v1/detail";

        Map<String, String> projectCompanyMap = new HashMap<>();
        // Map<String, String> projectNameMap = new HashMap<>();
        for (String projectCode : projectCodes) {
            try {
                // 构建请求参数
                JSONObject requestBody = new JSONObject();
                requestBody.put("third_project_id", projectCode);

                // // 设置操作员信息
                // JSONObject operatorInfo = new JSONObject();
                // operatorInfo.put("id", OPERATOR_ID);
                // operatorInfo.put("id_type", ID_TYPE);
                // requestBody.put("operator_id_info", operatorInfo);

                log.writeLog("查询项目详情，项目代码: " + projectCode);

                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("access-token", token);
                connection.setDoOutput(true);

                try (OutputStream os = connection.getOutputStream()) {
                    os.write(requestBody.toString().getBytes("utf-8"));
                }

                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    try (InputStream is = connection.getInputStream()) {
                        String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                        JSONObject jsonObj = JSONUtil.parseObj(response);

                        if (jsonObj.getInt("code", -1) == 0) {
                            JSONObject data = jsonObj.getJSONObject("data");
                            JSONObject project = data.getJSONObject("project");

                            if (project != null) {

                                // 获取项目名称，使用固定后缀_NAME作为key
                                String projectName = project.getStr("name");
                                if (projectName != null && !projectName.trim().isEmpty()) {
                                    projectCompanyMap.put(projectCode + "_NAME", projectName);
                                    log.writeLog("项目: " + projectCode + ", 名称: " + projectName);
                                }

                                JSONArray customFields = project.getJSONArray("custom_fields");
                                if (customFields != null) {
                                    for (int i = 0; i < customFields.size(); i++) {
                                        JSONObject field = customFields.getJSONObject(i);
                                        String title = field.getStr("title");

                                        // 查找title为companyId的字段
                                        if ("companyId".equals(title)) {
                                            String companyId = field.getStr("detail");
                                            if (companyId != null && !companyId.trim().isEmpty()) {
                                                projectCompanyMap.put(projectCode, companyId);
                                                log.writeLog("项目: " + projectCode + ", 公司ID: " + companyId);
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                        } else {
                            log.writeLog("查询项目详情失败，项目代码: " + projectCode + ", 错误: " + jsonObj.getStr("msg"));
                        }
                    }
                } else {
                    log.writeLog("查询项目详情HTTP请求失败，项目代码: " + projectCode + ", 响应码: " + connection.getResponseCode());
                }

                // 避免请求过于频繁
                Thread.sleep(100);

            } catch (Exception e) {
                log.writeLog("查询项目详情异常，项目代码: " + projectCode + ", 异常: " + e.getMessage());
            }
        }

        return projectCompanyMap;
    }

    /**
     * 获取上个月的时间范围
     *
     * @return [开始时间, 结束时间]
     */
    private String[] getLastMonthRange() {
        Calendar calendar = Calendar.getInstance();

        // 设置为上个月
        calendar.add(Calendar.MONTH, -1);

        // 上个月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.getTime());

        // 上个月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.getTime());

        log.writeLog("查询时间范围: " + startTime + " 至 " + endTime);

        return new String[]{startTime, endTime};
    }

    /**
     * 输出统计结果
     *
     * @param projectAllowanceMap 项目补贴金额映射
     * @param projectCompanyMap   项目公司ID映射
     */
    private void outputStatistics
    (Map<String, BigDecimal> projectAllowanceMap, Map<String, String> projectCompanyMap) {
        log.writeLog("===== 项目维度补贴金额统计结果 =====");

        BigDecimal totalAmount = BigDecimal.ZERO;
        int projectCount = 0;

        for (Map.Entry<String, BigDecimal> entry : projectAllowanceMap.entrySet()) {
            String projectCode = entry.getKey();
            BigDecimal allowanceAmount = entry.getValue();
            String companyId = projectCompanyMap.getOrDefault(projectCode, "未知");

            log.writeLog(String.format("项目: %s, 补贴金额: %s, 公司ID: %s",
                    projectCode, allowanceAmount.toPlainString(), companyId));

            totalAmount = totalAmount.add(allowanceAmount);
            projectCount++;
        }

        log.writeLog("===== 统计汇总 =====");
        log.writeLog("项目总数: " + projectCount);
        log.writeLog("补贴总金额: " + totalAmount.toPlainString());
        log.writeLog("========================");
    }

    public void saveProjectInfoToDatabase1(Map<String, Map<String, FBTJob2.ProjectInfo>> projectEventMap) {
        if (projectEventMap == null || projectEventMap.isEmpty()) {
            log.writeLog("没有项目信息可保存。");
            return;
        }

        String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String URL = "*********************************************************";
        String USERNAME = "lijunhong";
        String PASSWORD = "Cyitce@0106";
        Connection conn = null;
        PreparedStatement pstmt = null;

        String insertSql = "INSERT INTO uf_fbt_project_info (" +
                "formmodeid, modedatacreater, modedatacreatertype, modedatacreatedate, modedatacreatetime, MODEUUID, " +
                "project_code, project_name, sxmc,  bill_code, bill_start_time, bill_end_time, bill_status, " +
                "company_id, company_name, related_companies, " +
                "air_ticket_domestic, air_ticket_international, hotel, train, bus, car, " +
                "meal, takeaway, purchase, flash, express, freight, other, " +
                "virtual_card, corporate_payment, value_added_service, hotel_overseas, " +
                "include_tax_amount, exclude_tax_amount, " +
                "hotel_include_tax, hotel_exclude_tax, " +
                "train_include_tax, train_exclude_tax, " +
                "bus_include_tax, bus_exclude_tax, " +
                "car_include_tax, car_exclude_tax, " +
                "meal_include_tax, meal_exclude_tax, " +
                "takeaway_include_tax, takeaway_exclude_tax, " +
                "purchase_include_tax, purchase_exclude_tax, " +
                "flash_include_tax, flash_exclude_tax, " +
                "express_include_tax, express_exclude_tax, " +
                "freight_include_tax, freight_exclude_tax, " +
                "other_include_tax, other_exclude_tax, " +
                "virtual_card_include_tax, virtual_card_exclude_tax, " +
                "accommodation_fee, accommodation_fee_include_tax, accommodation_fee_exclude_tax, " +
                "accommodation_fee_special, accommodation_fee_ordinary, " +
                "hotel_special, hotel_special_include_tax, hotel_special_exclude_tax, " +
                "hotel_ordinary, hotel_ordinary_include_tax, hotel_ordinary_exclude_tax, " +
                "hotel_overseas_special, hotel_overseas_ordinary, " +
                "total_accommodation_fee, " +
                "atdit, atdet, atiit, atiet, cpit, cpet, vasit, vaset, hoit, " +
                "afsit, afset, hosit, hoset, afoit, afoet, hooit, hooet, " +
                "tafit, tafet, tafs, tafsit, tafset, tafo, tafoit, tafoet, " +
                "manual_price, manual_price_tax, train_tax, airport_fee, fuel_fee, " +
                "air_ticket_total_include_tax, air_ticket_tax, " +
                "create_date, create_time) " +
                "VALUES (" +
                "?,?,?,?,?,?, ?,?,?,?,?,?,?, ?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?, ?,?,?,?)";

        try {
            Class.forName(DRIVER);
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            conn.setAutoCommit(false);
            pstmt = conn.prepareStatement(insertSql);

            int totalCount = 0;
            final int BATCH_SIZE = 100;
            String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            for (Map<String, FBTJob2.ProjectInfo> eventMap : projectEventMap.values()) {
                for (FBTJob2.ProjectInfo project : eventMap.values()) {
                    if (project.getCompanyId().startsWith("no_company_id")) {
                        continue;
                    }
                    int paramIndex = 1;
                    pstmt.setInt(paramIndex++, 1077);
                    pstmt.setInt(paramIndex++, 3810);
                    pstmt.setInt(paramIndex++, 0);
                    pstmt.setString(paramIndex++, currentDate);
                    pstmt.setString(paramIndex++, currentTime);
                    pstmt.setString(paramIndex++, UUID.randomUUID().toString());
                    pstmt.setString(paramIndex++, project.getProjectCode());
                    pstmt.setString(paramIndex++, project.getProjectName());
                    String eventNameStr = project.getEventName();
                    int eventId = ProjectEventEnum.safeGetId(eventNameStr, 16);
                    pstmt.setInt(paramIndex++, eventId);
                    // pstmt.setInt(paramIndex++, Integer.parseInt(project.getEventName()));
                    // pstmt.setString(paramIndex++, project.getType());
                    pstmt.setString(paramIndex++, project.getBillCode());
                    pstmt.setString(paramIndex++, project.getBillStartTime());
                    pstmt.setString(paramIndex++, project.getBillEndTime());
                    pstmt.setInt(paramIndex++, project.getBillStatus());
                    pstmt.setString(paramIndex++, project.getCompanyId());
                    pstmt.setString(paramIndex++, project.getCompanyName());
                    pstmt.setString(paramIndex++, project.getRelatedCompaniesStr());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomestic());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternational());
                    pstmt.setBigDecimal(paramIndex++, project.getHotel());
                    pstmt.setBigDecimal(paramIndex++, project.getTrain());
                    pstmt.setBigDecimal(paramIndex++, project.getBus());
                    pstmt.setBigDecimal(paramIndex++, project.getCar());
                    pstmt.setBigDecimal(paramIndex++, project.getMeal());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeaway());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchase());
                    pstmt.setBigDecimal(paramIndex++, project.getFlash());
                    pstmt.setBigDecimal(paramIndex++, project.getExpress());
                    pstmt.setBigDecimal(paramIndex++, project.getFreight());
                    pstmt.setBigDecimal(paramIndex++, project.getOther());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCard());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePayment());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedService());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseas());
                    pstmt.setBigDecimal(paramIndex++, project.getIncludeTaxAmount());
                    pstmt.setBigDecimal(paramIndex++, project.getExcludeTaxAmount());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getBusIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getBusExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCarIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCarExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getMealIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getMealExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeawayIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeawayExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchaseIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchaseExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFlashIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFlashExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getExpressIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getExpressExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFreightIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFreightExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getOtherIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getOtherExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCardIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCardExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getManualPrice());
                    pstmt.setBigDecimal(paramIndex++, project.getManualPriceTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirportFee());
                    pstmt.setBigDecimal(paramIndex++, project.getFuelFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketTotalIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketTax());
                    pstmt.setString(paramIndex++, currentDate);
                    pstmt.setString(paramIndex++, currentTime);

                    pstmt.addBatch();
                    totalCount++;

                    if (totalCount % BATCH_SIZE == 0) {
                        pstmt.executeBatch();
                        conn.commit();
                        log.writeLog("已提交一批(" + BATCH_SIZE + ")数据到数据库。");
                    }
                }
            }

            if (totalCount > 0 && totalCount % BATCH_SIZE != 0) {
                pstmt.executeBatch();
                conn.commit();
            }

            log.writeLog("成功保存 " + totalCount + " 条项目信息到数据库。");

        } catch (Exception e) {
            log.writeLog("保存项目信息到数据库时出错: " + e.getMessage());
            e.printStackTrace();
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            try {
                if (pstmt != null) pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            try {
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 查询结算单详情并按期间和项目统计补贴金额
     * 根据结算单ID列表，获取详情并按期间->项目代码统计补贴金额
     *
     * @param settlementIds 结算单ID列表
     * @return 期间 -> (项目代码 -> 补贴金额)的映射
     * @throws IOException 网络请求异常
     */
    private Map<String, Map<String, BigDecimal>> querySettlementDetailsByPeriod(List<String> settlementIds) throws IOException {
        String token = getToken();
        String apiUrl = BASE_URL + "/openapi/reimbursement/settlement/v1/list";

        Map<String, Map<String, BigDecimal>> periodProjectAllowanceMap = new HashMap<>();

        // 构建请求参数 - 使用settlement_ids数组
        JSONObject requestBody = new JSONObject();
        requestBody.put("settlement_ids", settlementIds);

        log.writeLog("查询结算单详情，IDs: " + settlementIds.toString());

        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);
        connection.setDoOutput(true);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(requestBody.toString().getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new Scanner(is, "UTF-8").useDelimiter("\\A").next();
                log.writeLog("结算单详情响应: " + response);

                JSONObject jsonObj = JSONUtil.parseObj(response);

                if (jsonObj.getInt("code", -1) == 0) {
                    // data字段直接是数组，不是包含settlements的对象
                    JSONArray settlements = jsonObj.getJSONArray("data");

                    if (settlements != null) {
                        for (int s = 0; s < settlements.size(); s++) {
                            JSONObject settlement = settlements.getJSONObject(s);

                            String settlementId = settlement.getStr("id");
                            String period = settlement.getStr("period"); // 获取期间
                            BigDecimal totalAllowanceMoney = settlement.getBigDecimal("total_allowance_money", BigDecimal.ZERO);

                            log.writeLog("处理结算单: " + settlementId + ", 期间: " + period + ", 总补贴金额: " + totalAllowanceMoney);

                            // 确保期间对应的Map存在
                            periodProjectAllowanceMap.putIfAbsent(period, new HashMap<>());
                            Map<String, BigDecimal> projectAllowanceMap = periodProjectAllowanceMap.get(period);

                            // 解析details数组中的员工详情
                            JSONArray details = settlement.getJSONArray("details");
                            if (details != null) {
                                String globalLastProjectCode = null; // 全局保存上一个子数组的项目代码

                                for (int d = 0; d < details.size(); d++) {
                                    JSONObject detail = details.getJSONObject(d);

                                    // 解析employee_details数组
                                    JSONArray employeeDetails = detail.getJSONArray("employee_details");
                                    if (employeeDetails != null) {
                                        String lastProjectCode = null; // 保存当前子数组中上一个遍历到的项目代码
                                        List<BigDecimal> pendingAmounts = new ArrayList<>(); // 暂存没有项目代码的金额
                                        boolean foundProjectInCurrentDetail = false; // 标记当前子数组是否找到了项目代码

                                        for (int i = 0; i < employeeDetails.size(); i++) {
                                            JSONObject employeeDetail = employeeDetails.getJSONObject(i);
                                            BigDecimal employeeAllowanceMoney = employeeDetail.getBigDecimal("allowance_money", BigDecimal.ZERO);

                                            String currentProjectCode = null;

                                            // 尝试从当前元素获取项目代码
                                            JSONArray costAttributions = employeeDetail.getJSONArray("cost_attributions");
                                            if (costAttributions != null) {
                                                for (int j = 0; j < costAttributions.size(); j++) {
                                                    JSONObject costAttribution = costAttributions.getJSONObject(j);

                                                    // 查找type为2的项目归属
                                                    if (costAttribution.getInt("type", -1) == 2) {
                                                        JSONArray projectDetails = costAttribution.getJSONArray("details");
                                                        if (projectDetails != null && projectDetails.size() > 0) {
                                                            JSONObject projectDetail = projectDetails.getJSONObject(0);
                                                            String projectCode = projectDetail.getStr("code");

                                                            if (projectCode != null && !projectCode.trim().isEmpty()) {
                                                                currentProjectCode = projectCode;
                                                                foundProjectInCurrentDetail = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (currentProjectCode != null) {
                                                // 找到了项目代码，先处理之前暂存的金额
                                                if (lastProjectCode == null && !pendingAmounts.isEmpty()) {
                                                    // 第一次找到项目代码，处理之前暂存的金额
                                                    for (BigDecimal pendingAmount : pendingAmounts) {
                                                        BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                                                        projectAllowanceMap.put(currentProjectCode, currentAmount.add(pendingAmount));
                                                        log.writeLog("期间: " + period + ", 结算单: " + settlementId + ", 项目: " + currentProjectCode +
                                                                ", 补贴金额: " + pendingAmount + " (处理暂存金额)");
                                                    }
                                                    pendingAmounts.clear();
                                                }

                                                // 处理当前金额
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(currentProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(currentProjectCode, currentAmount.add(employeeAllowanceMoney));
                                                log.writeLog("期间: " + period + ", 结算单: " + settlementId + ", 项目: " + currentProjectCode +
                                                        ", 员工补贴金额: " + employeeAllowanceMoney);

                                                lastProjectCode = currentProjectCode;
                                            } else if (lastProjectCode != null) {
                                                // 没有项目代码但有当前子数组的上一个项目代码，直接累加
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(lastProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(lastProjectCode, currentAmount.add(employeeAllowanceMoney));
                                                log.writeLog("期间: " + period + ", 结算单: " + settlementId + ", 项目: " + lastProjectCode +
                                                        ", 员工补贴金额: " + employeeAllowanceMoney + " (使用当前子数组上一个项目代码)");
                                            } else {
                                                // 既没有当前项目代码也没有当前子数组的上一个项目代码，暂存金额
                                                pendingAmounts.add(employeeAllowanceMoney);
                                            }
                                        }

                                        // 处理完当前子数组后，如果整个子数组都没有找到项目代码，使用全局上一个项目代码
                                        if (!foundProjectInCurrentDetail && globalLastProjectCode != null && !pendingAmounts.isEmpty()) {
                                            for (BigDecimal pendingAmount : pendingAmounts) {
                                                BigDecimal currentAmount = projectAllowanceMap.getOrDefault(globalLastProjectCode, BigDecimal.ZERO);
                                                projectAllowanceMap.put(globalLastProjectCode, currentAmount.add(pendingAmount));
                                                log.writeLog("期间: " + period + ", 结算单: " + settlementId + ", 项目: " + globalLastProjectCode +
                                                        ", 补贴金额: " + pendingAmount + " (使用上一个子数组项目代码)");
                                            }
                                        }

                                        // 更新全局项目代码
                                        if (foundProjectInCurrentDetail && lastProjectCode != null) {
                                            globalLastProjectCode = lastProjectCode;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        log.writeLog("查询结算单详情失败，错误: " + jsonObj.getStr("msg"));
                    }
                }
            }
        } else {
            log.writeLog("查询结算单详情HTTP请求失败，响应码: " + connection.getResponseCode());
        }

        return periodProjectAllowanceMap;
    }

    /**
     * 输出按期间和项目维度的统计结果
     *
     * @param periodProjectAllowanceMap 期间->项目补贴金额映射
     * @param projectCompanyMap         项目公司ID映射
     */
    private void outputPeriodProjectStatistics(Map<String, Map<String, BigDecimal>> periodProjectAllowanceMap,
                                               Map<String, String> projectCompanyMap) {
        log.writeLog("===== 按期间和项目维度补贴金额统计结果 =====");

        BigDecimal grandTotal = BigDecimal.ZERO;
        int totalProjectCount = 0;

        for (Map.Entry<String, Map<String, BigDecimal>> periodEntry : periodProjectAllowanceMap.entrySet()) {
            String period = periodEntry.getKey();
            Map<String, BigDecimal> projectAllowanceMap = periodEntry.getValue();

            log.writeLog("\n===== 期间: " + period + " =====");

            BigDecimal periodTotal = BigDecimal.ZERO;
            int periodProjectCount = 0;

            for (Map.Entry<String, BigDecimal> projectEntry : projectAllowanceMap.entrySet()) {
                String projectCode = projectEntry.getKey();
                BigDecimal allowanceAmount = projectEntry.getValue();
                String companyId = projectCompanyMap.getOrDefault(projectCode, "未知");

                log.writeLog(String.format("项目: %s, 补贴金额: %s, 公司ID: %s",
                        projectCode, allowanceAmount.toPlainString(), companyId));

                periodTotal = periodTotal.add(allowanceAmount);
                periodProjectCount++;
            }

            log.writeLog(String.format("期间 %s 小计: 项目数 %d, 补贴总金额: %s",
                    period, periodProjectCount, periodTotal.toPlainString()));

            grandTotal = grandTotal.add(periodTotal);
            totalProjectCount += periodProjectCount;
        }

        log.writeLog("\n===== 总计 =====");
        log.writeLog("总项目数: " + totalProjectCount);
        log.writeLog("总补贴金额: " + grandTotal.toPlainString());
        log.writeLog("========================");
    }

    /**
     * 保存差补结算单统计数据到数据库
     *
     * @param projectAllowanceMap 项目代码 -> 补贴金额的映射
     * @param projectInfoMap      项目信息映射 (项目代码->公司ID, 项目代码_NAME->项目名称)
     * @param billStartTime       账单开始时间
     * @param billEndTime         账单结束时间
     */
    public void billSave(Map<String, Map<String, BigDecimal>> projectAllowanceMap, Map<String, String> projectInfoMap,
                         String billStartTime, String billEndTime) {
        if (projectAllowanceMap == null || projectAllowanceMap.isEmpty()) {
            log.writeLog("没有项目补贴信息可保存。");
            return;
        }

        String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String URL = "*********************************************************";
        String USERNAME = "lijunhong";
        String PASSWORD = "Cyitce@0106";
        Connection conn = null;
        PreparedStatement pstmt = null;

        String insertSql = "INSERT INTO uf_fbt_project_info (" +
                "formmodeid, modedatacreater, modedatacreatertype, modedatacreatedate, modedatacreatetime, MODEUUID, " +
                "project_code, project_name, sxmc, bill_start_time,status,clbt,company_id) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Class.forName(DRIVER);
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            conn.setAutoCommit(false);
            pstmt = conn.prepareStatement(insertSql);

            int totalCount = 0;
            final int BATCH_SIZE = 100;
            String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            for (String key : projectAllowanceMap.keySet()) {
                Map<String, BigDecimal> map = projectAllowanceMap.get(key);
                for (String projectCode : map.keySet()) {
                    BigDecimal allowanceAmount = map.get(projectCode);
                    String projectName = projectInfoMap.getOrDefault(projectCode + "_NAME", "未知项目");
                    int paramIndex = 1;
                    pstmt.setInt(paramIndex++, 1077);
                    pstmt.setInt(paramIndex++, 3810);
                    pstmt.setInt(paramIndex++, 0);
                    pstmt.setString(paramIndex++, currentDate);
                    pstmt.setString(paramIndex++, currentTime);
                    pstmt.setString(paramIndex++, UUID.randomUUID().toString());

                    pstmt.setString(paramIndex++, projectCode);
                    pstmt.setString(paramIndex++, projectName);
                    pstmt.setInt(paramIndex++, 16);
                    pstmt.setString(paramIndex++, key);
                    pstmt.setInt(paramIndex++, 0);
                    pstmt.setBigDecimal(paramIndex++, allowanceAmount);
                    pstmt.setInt(paramIndex++, StringUtils.hasText(projectInfoMap.get(projectCode)) ? Integer.parseInt(projectInfoMap.get(projectCode)) : -1);
                    pstmt.addBatch();
                    totalCount++;
                    if (totalCount % BATCH_SIZE == 0) {
                        pstmt.executeBatch();
                        conn.commit();
                        log.writeLog("已提交一批(" + BATCH_SIZE + ")数据到数据库。");
                    }
                }
            }


            if (totalCount > 0 && totalCount % BATCH_SIZE != 0) {
                pstmt.executeBatch();
                conn.commit();
            }

            log.writeLog("成功保存 " + totalCount + " 条差补结算单统计信息到数据库。");

        } catch (Exception e) {
            log.writeLog("保存差补结算单统计信息到数据库时出错: " + e.getMessage());
            e.printStackTrace();
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            try {
                if (pstmt != null) pstmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 参考 FBTJob2中数据库保存的方法 现在需要保存进数据库的数据是formmodeid, modedatacreater,
     * modedatacreatertype, modedatacreatedate, modedatacreatetime, MODEUUID  固定字段，
     * project_code 对应项目代码 project_name对应事项名称
     */

    public static void main(String[] args) {
        BillJob job = new BillJob();
        job.execute();
        // job.outputPeriodProjectStatistics();
    }

    /**
     * 测试方法
     * 用于本地测试整个流程
     */
    public static void main1(String[] args) {
        BillJob job = new BillJob();
        try {
            System.out.println("======= 开始执行本地测试 =======");

            System.out.println("\n[步骤 1/4] 正在获取访问令牌...");
            String token = job.getToken();
            System.out.println("Token获取成功: " + token.substring(0, Math.min(20, token.length())) + "...");

            System.out.println("\n[步骤 2/4] 正在查询上个月已结算的差补结算单列表...");
            List<String> settlementIds = job.querySettlementList();


            System.out.println("获取到结算单数量: " + settlementIds.size());
            if (!settlementIds.isEmpty()) {
                System.out.println("前5个结算单ID: " + settlementIds.subList(0, Math.min(5, settlementIds.size())));
            }

            if (settlementIds.isEmpty()) {
                System.out.println("未找到上个月的已结算账单，测试结束");
                return;
            }

            System.out.println("\n[步骤 3/4] 正在查询结算单详情并统计项目补贴金额...");
            // Map<String, BigDecimal> projectAllowanceMap = job.querySettlementDetails(settlementIds);
            Map<String, Map<String, BigDecimal>> projectAllowanceMap = job.querySettlementDetailsByPeriod(settlementIds);

            System.out.println("统计到项目数量: " + projectAllowanceMap.size());

            if (projectAllowanceMap.isEmpty()) {
                System.out.println("未找到项目相关的补贴数据，测试结束");
                return;
            }

            Set<String> allProjectCodes = new HashSet<>();
            for (Map<String, BigDecimal> projectMap : projectAllowanceMap.values()) {
                allProjectCodes.addAll(projectMap.keySet());
            }

            System.out.println("统计到项目数量: " + allProjectCodes.size());

            System.out.println("\n[步骤 4/4] 正在查询项目对应的公司ID...");
            Map<String, String> projectCompanyMap = job.queryProjectCompanyIds(allProjectCodes);
            System.out.println("获取到项目公司信息数量: " + projectCompanyMap.size());

            job.outputPeriodProjectStatistics(projectAllowanceMap, projectCompanyMap);
            // System.out.println("\n======= 最终统计结果 =======");
            // BigDecimal totalAmount = BigDecimal.ZERO;
            // int projectCount = 0;
            //
            // for (Map.Entry<String, BigDecimal> entry : projectAllowanceMap.entrySet()) {
            //     String projectCode = entry.getKey();
            //     BigDecimal allowanceAmount = entry.getValue();
            //     String companyId = projectCompanyMap.getOrDefault(projectCode, "未知");
            //     System.out.println(String.format("项目: %s, 补贴金额: %s, 公司ID: %s",
            //             projectCode, allowanceAmount.toPlainString(), companyId));
            //     totalAmount = totalAmount.add(allowanceAmount);
            //     projectCount++;
            // }
            //
            // System.out.println("\n===== 统计汇总 =====");
            // System.out.println("项目总数: " + projectCount);
            // System.out.println("补贴总金额: " + totalAmount.toPlainString());
            // System.out.println("平均每项目补贴: " + (projectCount > 0 ? totalAmount.divide(BigDecimal.valueOf(projectCount), 2, BigDecimal.ROUND_HALF_UP).toPlainString() : "0"));
            // System.out.println("========================");
            // System.out.println("\n[步骤 5/5] 正在保存数据到数据库...");
            String[] lastMonthRange = job.getLastMonthRange();
            job.billSave(projectAllowanceMap, projectCompanyMap, lastMonthRange[0], lastMonthRange[1]);
            // System.out.println("数据保存完成");
            // System.out.println("\n======= 测试完成 =======");

        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }


}
