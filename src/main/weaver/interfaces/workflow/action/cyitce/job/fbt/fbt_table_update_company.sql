-- 添加公司相关字段
ALTER TABLE fbt_project_info ADD company_id VARCHAR(50) DEFAULT '' NOT NULL COMMENT '主要关联公司ID（第一个添加的公司）';
ALTER TABLE fbt_project_info ADD company_name VARCHAR(200) DEFAULT '' NOT NULL COMMENT '主要关联公司名称';
ALTER TABLE fbt_project_info ADD related_companies TEXT COMMENT '所有关联公司ID的字符串，以逗号分隔';

-- 添加索引以提高查询性能
CREATE INDEX idx_fbt_project_info_company_id ON fbt_project_info (company_id);
CREATE INDEX idx_fbt_project_info_company_name ON fbt_project_info (company_name);

-- 创建项目公司关联表
CREATE TABLE IF NOT EXISTS fbt_project_company (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_code VARCHAR(50) NOT NULL COMMENT '项目代码',
    company_id VARCHAR(50) NOT NULL COMMENT '公司ID',
    bill_code VARCHAR(50) DEFAULT '' COMMENT '分贝通账单编号',
    bill_start_time VARCHAR(20) DEFAULT '' COMMENT '账单开始时间（格式YYYYMM，如202505）',
    bill_end_time VARCHAR(20) DEFAULT '' COMMENT '账单结束时间（格式YYYYMM，如202506）',
    bill_status INT DEFAULT 0 COMMENT '账单状态 (1:已出账单 2:已核对 3:结算中 4:已结算 5:未出账单)',
    create_date VARCHAR(10) COMMENT '创建日期',
    create_time VARCHAR(8) COMMENT '创建时间',
    update_date VARCHAR(10) COMMENT '更新日期',
    update_time VARCHAR(8) COMMENT '更新时间',
    UNIQUE KEY uk_project_company (project_code, company_id, bill_code)
) COMMENT='分贝通项目公司关联表';

-- 添加索引以提高查询性能
CREATE INDEX idx_fbt_project_company_project_code ON fbt_project_company (project_code);
CREATE INDEX idx_fbt_project_company_company_id ON fbt_project_company (company_id);
CREATE INDEX idx_fbt_project_company_bill_code ON fbt_project_company (bill_code); 