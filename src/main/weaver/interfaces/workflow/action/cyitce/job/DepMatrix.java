package weaver.interfaces.workflow.action.cyitce.job;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;


/**
 * @ClassName: 部门矩阵表数据导入
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-12-13  20:20
 * @Version: 1.0
 */
public class DepMatrix extends BaseCronJob {
    BaseBean log = new BaseBean();


    public void execute() {
        String sql = "select id from HrmDepartment where id not in (select sqbm from uf_bmjzbdjm)";
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        String[] ins = new String[5];

        for(int i=0;i<ins.length;++i){
            ins[i]="";
        }

        try {
            log.writeLog("-----11----");
            rs.execute(sql);
            while (rs.next()){
                log.writeLog("insert----");
                insCallback(ins,rs.getString(1),0);
                sql = "insert into uf_bmjzbdjm(sqbm,yjbm,ejbm,sjbm,xmz) values("+ins[0]+","+ins[1]+","+ins[2]+","+ins[3]+","+ins[4]+")";
                log.writeLog("---11 sql----"+sql);
                rs2.execute(sql);
                log.writeLog("---11 over----");
            }

            log.writeLog("---------22---------");
            sql = "select sqbm from uf_bmjzbdjm";
            rs.execute(sql);
            while (rs.next()){
                log.writeLog("update----");
                insCallback(ins,rs.getString(1),0);
                sql = "update uf_bmjzbdjm set yjbm="+ins[1]+",ejbm="+ins[2]+",sjbm="+ins[3]+",xmz="+ins[4]+" where sqbm="+ins[0];
                log.writeLog("---22 sql----"+sql);
                rs2.execute(sql);
                log.writeLog("---22 over----");
            }
        }catch (Exception e){
            e.printStackTrace();
            log.writeLog(e.getMessage());
        }

    }


    public int insCallback(String[] ins,String id,int i){
        RecordSet rs = new RecordSet();
        String depid  = "";
        String sql = "select supdepid from HrmDepartment where id = "+id;
        rs.execute(sql);
        ins[0] = id;

        log.writeLog(sql);
        if (rs.next()){
            if("0".equals(rs.getString(1))){
                int n = i+1;

                while (n<=4){
                    ins[n]="null";
                    n++;
                }

                i = 1;
                depid = id;
            }else {
                i = insCallback(ins,rs.getString(1),i);
                depid = rs.getString(1);
            }

            ins[i]=depid;
            i++;
        }
        log.writeLog(ins[0]+"--"+ins[1]+"--"+ins[2]+"--"+ins[3]);
        return i;
    }
}