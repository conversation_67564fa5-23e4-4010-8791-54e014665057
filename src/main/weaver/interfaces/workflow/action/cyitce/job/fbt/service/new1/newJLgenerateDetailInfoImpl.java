package weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1;

import cn.hutool.json.JSONUtil;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.FlowUtil;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYProjectDetailInfo;
import weaver.mobile.webservices.workflow.WorkflowRequestInfo;
import weaver.mobile.webservices.workflow.WorkflowServiceImpl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 11:30
 * @describe 监理公司项目详情生成器实现类
 */
public class newJLgenerateDetailInfoImpl extends newAbstractDetailInfoGenerator {

    @Override
    protected String getFirstSql() {
        return "select id,ejxmnbbh,yjbm,ejbm,sjbm,yjxmmc,yjxmbh from uf_jl_ejxmlxsqjmbd where ejxmnbbh=?";
    }

    @Override
    protected String getSecondSql() {
        return "select sqrbm,sfsggxm,gclx from uf_jl_yjxmlxsqjmbd where id=?";
    }

    @Override
    protected String getThirdSql() {
        return "select yjbm,ejbm,sjbm from view_bmjz_cw where sqbm=?";
    }

    @Override
    protected String getFirstQueryParam(Map<String, String> map) {
        return map.get("project_code");
    }

    @Override
    protected String getSecondQueryParam(Map<String, String> resultMap) {
        return resultMap.get("1_yjxmmc");
    }

    @Override
    protected String getThirdQueryParam(Map<String, String> resultMap) {
        return resultMap.get("2_sqrbm");
    }

    @Override
    protected STYProjectDetailInfo convertToProjectDetailInfo(Map<String, String> resultMap) {
        STYProjectDetailInfo detailInfo = new STYProjectDetailInfo();

        // 设置id字段
        detailInfo.setId(resultMap.get("1_id"));

        // 设置第一个查询的字段 (uf_jl_ejxmlxsqjmbd表)
        detailInfo.setEjxmnbbh(resultMap.get("1_ejxmnbbh"));
        detailInfo.setYjbm(resultMap.get("1_yjbm"));
        detailInfo.setEjbm(resultMap.get("1_ejbm"));
        detailInfo.setSjbm(resultMap.get("1_sjbm"));
        detailInfo.setYjxmmc(resultMap.get("1_yjxmmc"));
        detailInfo.setYjxmbh(resultMap.get("1_yjxmbh"));

        // 设置第二个查询的字段 (uf_jl_yjxmlxsqjmbd表)
        detailInfo.setSqrbm(resultMap.get("2_sqrbm"));
        detailInfo.setSfsggxm(resultMap.get("2_sfsggxm"));
        // 使用监理公司特有的项目类型描述
        detailInfo.setJLGclx(resultMap.get("2_gclx"));

        // 设置第三个查询的字段 (view_bmjz_cw表)
        detailInfo.setCwYjbm(resultMap.get("3_yjbm"));
        detailInfo.setCwEjbm(resultMap.get("3_ejbm"));
        detailInfo.setCwSjbm(resultMap.get("3_sjbm"));

        return detailInfo;
    }

    /**
     * 生成流程数据
     *
     * @param list       项目详情列表
     * @param userId     用户ID
     * @param gszt       公司主体
     * @param tableIndex 表索引
     */
    public String generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex) {
        String requestName = FlowUtil.generateExpenseTitle(userId);
        String userName = FlowUtil.userid(userId);
        Map<String, String> fieldMap = new HashMap<>();
        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());
        fieldMap.put("sqr", userId);// 申请人
        fieldMap.put("sqrq", currentDate);// 申请日期
        fieldMap.put("zdrq", currentDate);// 制单日期
        fieldMap.put("zdr", userId);// 制单人
        fieldMap.put("gszt", gszt);// 公司主体
        String deptId = FlowUtil.queryDepartmentIdByEmployeeId(Integer.parseInt(userId));
        fieldMap.put("szbm", StringUtils.hasText(deptId) ? deptId : "");// 申请部门
        Map<String, Boolean[]> viewEditMap = new HashMap<>();
        viewEditMap.put("gszt", new Boolean[]{true, true});
        List<Map<String, String>> detailTableData = new ArrayList<>();

        // 遍历项目列表，将每个项目的属性添加到明细表中
        for (STYProjectDetailInfo project : list) {
            Map<String, String> rowData = new HashMap<>();
            // 添加项目基本信息
            rowData.put("jlejxmbh", project.getEjxmnbbh());  // 二级项目编号
            rowData.put("jlejxmmc", project.getId());  // 二级项目名称
            rowData.put("jlyjxmmc", project.getYjxmmc());  // 一级项目名称
            rowData.put("jlyjxmbh", project.getYjxmbh());  // 一级项目编号

            // 添加部门信息
            rowData.put("yjbm", project.getYjbm());      // 一级部门
            rowData.put("ejbm", project.getEjbm());      // 二级部门
            rowData.put("sjbm", project.getSjbm());      // 三级部门

            // 添加财务部门信息
            rowData.put("cwyjbm", project.getCwYjbm());  // 财务一级部门
            rowData.put("cwejbm", project.getCwEjbm());  // 财务二级部门
            rowData.put("cwsjbm", project.getCwSjbm());  // 财务三级部门

            rowData.put("xmlx", project.getXmlx());      // 项目类型
            // rowData.put("xmlxDesc", project.getXmlxDesc()); // 项目类型描述

            // 添加其他信息
            rowData.put("fycdbm", project.getBmcx());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            rowData.put("jsms", project.getJsms()); // 计税模式 0 增值税 1 简易计税
            // rowData.put("jsmsDesc", project.getJsmsDesc()); // 计税模式描述

            // 计算项目性质（xmxz）
            String xmxz = "2"; // 默认为业务项目
            String sfsggxm = project.getSfsggxm();
            String xmlx = project.getXmlx(); // 获取项目类型

            // 如果是公共项目(sfsggxm=0)，则项目性质=0
            if ("0".equals(sfsggxm)) {
                xmxz = "0";
            }
            // 如果不是公共项目，且项目类型=5(研发项目)，则项目性质=1(研发)
            else if (xmlx != null && "5".equals(xmlx)) {
                xmxz = "1";
            }
            // 否则为业务项目(2)，这是默认值，不需要额外设置

            // 添加项目性质字段
            rowData.put("xmxz", xmxz);

            Map<String, String> map = project.getMap();
            rowData.put("sxmcv2", map.get("sxmc"));      // 事项名称
            rowData.put("dcfhsje", map.get("car_include_tax")); // 打车 不含税
            rowData.put("mhfzjj", map.get("airport_fee")); // 民航发展基金
            rowData.put("pmje", map.get("air_ticket_total_include_tax")); // 票面金额
            rowData.put("jpse", FlowUtil.calculateAmount(map.get("air_ticket_total_include_tax"), map.get("airport_fee")).toPlainString()); // 火车票税额

            rowData.put("jpsl", "9%"); // 机票税率
            rowData.put("hcppj", map.get("train_include_tax")); // 火车票票价
            rowData.put("hcpbhsje", map.get("train_exclude_tax")); // 火车票不含税
            rowData.put("hcpse", map.get("train_tax")); // 火车票税额
            rowData.put("zsfzyfpbhsje", map.get("tafset")); // 住宿费专用发票不含税
            // 获取两个金额字符串
            String tafsitStr = map.get("tafsit");
            String tafsetStr = map.get("tafset");

            // 初始化默认值为 0
            BigDecimal tafsit = BigDecimal.ZERO;
            BigDecimal tafset = BigDecimal.ZERO;

            try {
                // 尝试将字符串转换为 BigDecimal 类型
                if (tafsitStr != null) {
                    tafsit = new BigDecimal(tafsitStr);
                }
                if (tafsetStr != null) {
                    tafset = new BigDecimal(tafsetStr);
                }
            } catch (NumberFormatException e) {
                // 处理转换失败的情况
                log.writeLog("金额转换失败: " + e.getMessage());
            }

            // 计算差值
            BigDecimal difference = tafsit.subtract(tafset);
            // 将差值添加到 rowData 中
            rowData.put("zsfse", difference.toPlainString()); // // 住宿费税额

            rowData.put("zsfsl", "6%"); // 住宿费税率
            rowData.put("zsfptfphsje", map.get("tafoit")); // 住宿费普通发票（含税金额）

            rowData.put("ddhcpfwfbhsje", map.get("manual_price")); // 代打火车票服务费（不含税金额）
            rowData.put("ddhcpfwfse", map.get("manual_price_tax")); // 代打火车票服务费（税额）
            rowData.put("zjzclx", "0"); // 资金支出类型
            rowData.put("mxid", map.get("id")); // mxid
            rowData.put("clbt", map.get("clbt")); // 差旅补贴
            // 添加到明细表数据列表
            detailTableData.add(rowData);
        }
        Map<String, Boolean[]> detailViewEditMap = new HashMap<>();
        newSTYgenerateDetailInfoImpl newSTYgenerateDetailInfo = new newSTYgenerateDetailInfoImpl();
        // // 创建工作流
        // newSTYgenerateDetailInfoImpl newSTYgenerateDetailInfo = new newSTYgenerateDetailInfoImpl();
        // WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
        //         fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        // log.writeLog("创建工作流对象 ：" + JSONUtil.toJsonStr(workflowRequestInfo));
        // WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
        // // log.writeLog("创建工作流对象 workflowService ：" + workflowService);
        //
        // //
        // //
        // //
        // // // String requestid =wsi.saveWorkflowRequest(wri, -1,Util.getIntValue(rs.getString("927"),1),"","","");
        // //
        // //
        // String requestId = workflowService.saveWorkflowRequest(workflowRequestInfo, -1, Integer.parseInt(userId), "", "", "");
        // log.writeLog("创建工作流对象 requestId：" + requestId);
        // String id = newSTYgenerateDetailInfo.getIdByRequestId("formtable_main_1981", requestId);

        // // Util.getIntValue(new RequestService().createRequest(requestInfo));
        // // Util.getIntValue(new RequestService().createRequest(requestInfo));

        // 创建工作流
        WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
                fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        log.writeLog("创建工作流对象 ：" + JSONUtil.toJsonStr(workflowRequestInfo));
        WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
        String requestId = workflowService.saveWorkflowRequest(workflowRequestInfo, -1, Integer.parseInt(userId), "", "", "");
        log.writeLog("创建工作流对象 requestId：" + requestId);
        String id = newSTYgenerateDetailInfo.getIdByRequestId("formtable_main_1981", requestId);
        log.writeLog("开始添加明细 : 明细表：formtable_main_1981_dt4  id: " + id + "   数据 ： " + JSONUtil.toJsonStr(detailTableData));
        int i = newSTYgenerateDetailInfo.batchInsertData(detailTableData, "formtable_main_1981_dt4", id);
        log.writeLog("添加明细成功");
        return requestId;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试代码
        List<Map<String, String>> testDataList = new ArrayList<>();

        Map<String, String> map1 = new HashMap<>();
        map1.put("code", "JL-EJXM20240029");
        map1.put("userId", "2947");
        map1.put("gszt", "15");
        testDataList.add(map1);

        List<STYProjectDetailInfo> results = new newJLgenerateDetailInfoImpl().generateDetailInfo(testDataList);
    }
} 