package weaver.interfaces.workflow.action.cyitce.job.fbt;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> (根据用户需求创建)
 * @date 2025/6/5
 * @describe 项目事项枚举类，用于将事项名称映射到数据ID。
 * 数据源于用户提供的表格，已排除黄色高亮部分。
 */
public enum ProjectEventEnum {

    // --- 根据表格非黄色部分定义枚举常量 ---
    DANG_TUAN_JIAN_SHE(24, "党、团建设", "团队建设活动和党团建设活动过程中发生的差旅费、办公费、资料费、会务费、水果、补品等活动费用。"),
    SHI_GU_CHU_LI(23, "事故处理", "列费用。包括律师服务费；诉讼费；工伤事故医疗费；政府部门，行业主管部门罚款；保险费；差旅费等。"),
    REN_YUAN_PEI_XUN(22, "人员培训", "人员继续教育、外送培训、职业资格认证等培训活动而发生的费用。包含生产部门人员或职能部门人员。"),
    ZHAO_JIE_PIN_FEI(21, "招、解聘费", "以及为招聘人才所发生的差旅费等费用，也包含与员工解除劳动合同产生的解除劳动合同补偿金。"),
    YAN_FA_ZHI_CHU(20, "研发支出", "产的折旧、消耗的原材料、直接参与开发人员的工资及福利费、开发过程中发生的租金以及借款费用等。"),
    SHUI_FEI(17, "税费", "比如增值税相关的进项税、销项税及增值税附加税，印花税，土地增值税、企业所得税，个税等。"),
    ZU_ZHI_GUAN_LI(16, "组织管理", "含项目管理）产生的费用。在项目交付过程中发生的因项目竣工、验收发生的资料制作费用也在此核算。"),
    CANG_CHU_WU_LIU(15, "仓储物流", "库至施工现场转运设备、材料发生的各项与运输相关的费用，如搬运费、装卸费、我方承担的货运费等。"),
    CAI_LIAO_GOU_MAI(12, "材料购买", "为采购项目主材、辅材产生的成本费用，如材料费、运杂费、运输保险费、入库前保管费。"),
    ZI_YOU_JIAO_FU(11, "自有交付", "公司自有人员因项目交付产生的成本费用，具体指自有交付人员的工资薪酬、差旅费和福利费等。"),
    SHI_CHANG_KAI_FA(10, "市场开发", "拜访客户、联络关系、宣传等产生的差旅费、业务招待费、会务费、广告宣传费、资料费、办公费等。"),
    WAI_XIE_LAO_WU(9, "外协劳务", "时技术承揽费（签订临时技术承揽合同，向临时短期个人技术人员在完成承揽内容后支付的技术服务费）。");


    private final int id;
    private final String eventName;
    private final String definition;

    // --- 静态Map用于高效查找 ---
    private static final Map<String, ProjectEventEnum> NAME_TO_ENUM_MAP = new HashMap<>();

    static {
        for (ProjectEventEnum event : values()) {
            NAME_TO_ENUM_MAP.put(event.getEventName(), event);
        }
    }

    /**
     * 私有构造函数
     *
     * @param id         数据ID
     * @param eventName  事项名称
     * @param definition 事项定义
     */
    ProjectEventEnum(int id, String eventName, String definition) {
        this.id = id;
        this.eventName = eventName;
        this.definition = definition;
    }

    // --- Getters ---
    public int getId() {
        return id;
    }

    public String getEventName() {
        return eventName;
    }

    public String getDefinition() {
        return definition;
    }

    /**
     * 【核心方法】通过事项名称获取对应的数据ID。
     * 如果找不到匹配的名称，则返回指定的默认ID。
     *
     * @param eventName 要查找的事项名称 (String)
     * @param defaultId 如果未找到，返回的默认数据ID
     * @return 匹配的数据ID或默认ID (int)
     */
    public static int getIdByEventName(String eventName, int defaultId) {
        if (eventName == null || eventName.isEmpty()) {
            return defaultId;
        }
        ProjectEventEnum event = NAME_TO_ENUM_MAP.get(eventName);
        return (event != null) ? event.getId() : defaultId;
    }

    /**
     * 【备用方法】尝试将输入字符串直接解析为整数，如果失败，则按名称查找。
     * 这种方法能同时兼容 "16" 和 "组织管理" 这样的输入。
     *
     * @param eventNameOrIdStr 可能为事项名称或ID字符串的输入
     * @param defaultId 如果所有尝试都失败，返回的默认ID
     * @return 最终解析出的ID
     */
    public static int safeGetId(String eventNameOrIdStr, int defaultId) {
        if (eventNameOrIdStr == null || eventNameOrIdStr.isEmpty()) {
            return defaultId;
        }

        // 1. 尝试直接将字符串解析为整数
        try {
            return Integer.parseInt(eventNameOrIdStr);
        } catch (NumberFormatException e) {
            // 解析失败，说明不是纯数字，继续按名称查找
        }

        // 2. 按名称在枚举中查找
        ProjectEventEnum event = NAME_TO_ENUM_MAP.get(eventNameOrIdStr);

        // 3. 返回结果或默认值
        return (event != null) ? event.getId() : defaultId;
    }
}