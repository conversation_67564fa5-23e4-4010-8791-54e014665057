package weaver.interfaces.workflow.action.cyitce.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.interfaces.workflow.action.cyitce.entity.ProjectTag;
import weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem.Token;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ProjectTagUpdateJob extends BaseCronJob {
    BaseBean log = new BaseBean();
    String url = OutServiceUrl.FILE_Sys_Url+"/file-system/tags/list";

    public ProjectTagUpdateJob() {
    }

    public void execute() {
        List<ProjectTag> pts = null;
        RecordSet rs = null;

        try {
            String sqlSelect = "select pid from uf_xmwjbq where pid=?";
            String sqlUpdate = "update uf_xmwjbq set pid=?,bqmz=?,parent=?,level1=? where pid=?";
            String sqlInsert = "insert into uf_xmwjbq(pid,bqmz,parent,level1) values(?,?,?,?)";
            rs = new RecordSet();

            pts = getProjectTagList();
            for(ProjectTag pt:pts){
                rs.execute(CommonUtil.getSqlByWhere(sqlSelect,new Object[]{pt.getId()}));
                if(rs.next()){
                    rs.execute(CommonUtil.getSqlByWhere(sqlUpdate,new Object[]{pt.getId(),pt.getTagName(),pt.getParent(),pt.getLevel(),pt.getId()}));
                }else {
                    rs.execute(CommonUtil.getSqlByWhere(sqlInsert,new Object[]{pt.getId(),pt.getTagName(),pt.getParent(),pt.getLevel()}));
                }
            }
        }catch (Exception e){
            log.writeLog("项目标签同步数据失败 ："+e.getMessage());
            e.printStackTrace();
        }
    }

    private List<ProjectTag> getProjectTagList(){
        HttpClient client = null;
        HttpGet get = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        User user = null;
        JSONArray objs = null;
        List<ProjectTag> pts = null;

        try {
//            user = new User(3420);

            client = HttpClients.createDefault();
            get = new HttpGet(url);
            get.setHeader("Tenant-Id","000000");
            get.setHeader("Authorization","Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            get.setHeader("Blade-Auth", Token.getToken("oacs2564","对接mypage"));

//            get.setHeader("Blade-Auth", Token.getToken(user.getLoginid(),user.getLastname()));
            response = client.execute(get);

            pts = new ArrayList<>();
            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return pts;
            }
            entity = response.getEntity();
            JSONObject obj = JSONObject.parseObject(EntityUtils.toString(entity));
            objs = obj.getJSONObject("data").getJSONArray("records");
            if(objs.size()<=0){
                log.writeLog("项目标签数据为空："+objs.toJSONString());
                return pts;
            }

            //封装数据项目标签数据
            enProTag(objs,"0",pts,0);

            return pts;
        }catch (Exception e){
            e.printStackTrace();
            return pts;
        }
    }

    private void enProTag(JSONArray jsonArray,String parent,List<ProjectTag> pts,int level){
        Iterator var5 = jsonArray.iterator();

        while (var5.hasNext()){
            Object o = var5.next();
            ProjectTag pt = new ProjectTag();
            JSONObject jo = (JSONObject)o;
            pt.setId(String.valueOf(jo.get("id")));
            pt.setTagName(String.valueOf(jo.get("tagName")));
            pt.setParent(parent);
            pt.setLevel(level);
            pts.add(pt);
            if (jo.getJSONArray("children").size() > 0) {
                enProTag(jo.getJSONArray("children"), String.valueOf(jo.get("id")), pts, level + 1);
            }
        }
    }
}
