package weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1;

import cn.hutool.json.JSONUtil;
import com.weaver.general.Util;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.FlowUtil;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYProjectDetailInfo;
import weaver.mobile.webservices.workflow.WorkflowRequestInfo;
import weaver.mobile.webservices.workflow.WorkflowServiceImpl;
import weaver.mobile.webservices.workflow.soa.RequestService;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 11:15
 * @describe 数藤云公司项目详情生成器实现类
 */
public class newSTYgenerateDetailInfoImpl extends newAbstractDetailInfoGenerator {

    @Override
    protected String getFirstSql() {
        return "select id,ejxmbh,yjbm,ejbm,sjbm,yjxmmc,yjxmbh from uf_styjsejxmlx where ejxmbh=?";
    }

    @Override
    protected String getSecondSql() {
        return "select sqrbm,sfsggxm,xmlx from uf_styjs_xmlxsq where id=?";
    }

    @Override
    protected String getThirdSql() {
        return "select yjbm,ejbm,sjbm from view_bmjz_cw where sqbm=?";
    }

    @Override
    protected String getFirstQueryParam(Map<String, String> map) {
        return map.get("project_code");
    }

    @Override
    protected String getSecondQueryParam(Map<String, String> resultMap) {
        return resultMap.get("1_yjxmmc");
    }

    @Override
    protected String getThirdQueryParam(Map<String, String> resultMap) {
        return resultMap.get("2_sqrbm");
    }

    @Override
    protected STYProjectDetailInfo convertToProjectDetailInfo(Map<String, String> resultMap) {
        STYProjectDetailInfo detailInfo = new STYProjectDetailInfo();

        // 设置id字段
        detailInfo.setId(resultMap.get("1_id"));

        // 设置第一个查询的字段
        detailInfo.setEjxmbh(resultMap.get("1_ejxmbh"));
        detailInfo.setYjbm(resultMap.get("1_yjbm"));
        detailInfo.setEjbm(resultMap.get("1_ejbm"));
        detailInfo.setSjbm(resultMap.get("1_sjbm"));
        detailInfo.setYjxmmc(resultMap.get("1_yjxmmc"));
        detailInfo.setYjxmbh(resultMap.get("1_yjxmbh"));

        // 设置第二个查询的字段
        detailInfo.setSqrbm(resultMap.get("2_sqrbm"));
        detailInfo.setSfsggxm(resultMap.get("2_sfsggxm"));
        detailInfo.setXmlx(resultMap.get("2_xmlx"));

        // 设置第三个查询的字段
        detailInfo.setCwYjbm(resultMap.get("3_yjbm"));
        detailInfo.setCwEjbm(resultMap.get("3_ejbm"));
        detailInfo.setCwSjbm(resultMap.get("3_sjbm"));

        return detailInfo;
    }

    /**
     * 批量插入数据到指定表
     *
     * @param dataList  数据列表，每个Map代表一条记录，键为列名，值为对应数据
     * @param tablename 要插入的表名
     * @param mainid    主ID，会在每条记录中添加mainid字段
     * @return 成功插入的记录数
     */
    public int batchInsertData(List<Map<String, String>> dataList, String tablename, String mainid) {
        if (dataList == null || dataList.isEmpty() || tablename == null || tablename.isEmpty()) {
            log.writeLog("批量插入数据失败：数据列表为空或表名为空");
            return 0;
        }

        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;

        try {
            // 获取数据库连接
            conn = getConnection();
            if (conn == null) {
                log.writeLog("批量插入数据失败：无法获取数据库连接");
                return 0;
            }

            // 禁用自动提交，以便进行批处理
            conn.setAutoCommit(false);

            // 获取表结构信息（如果可能）
            Map<String, Integer> columnTypes = getTableColumnTypes(conn, tablename);
            log.writeLog("获取表 " + tablename + " 的列类型信息: " + columnTypes);

            // 遍历数据列表
            for (Map<String, String> dataMap : dataList) {
                // 在每条记录中添加mainid字段
                dataMap.put("mainid", mainid);

                // 构建SQL语句
                StringBuilder sqlBuilder = new StringBuilder();
                StringBuilder columnsBuilder = new StringBuilder();
                StringBuilder valuesBuilder = new StringBuilder();
                List<Object> paramValues = new ArrayList<>();
                Map<String, Integer> paramTypes = new HashMap<>();

                sqlBuilder.append("INSERT INTO ").append(tablename).append(" (");

                // 处理列名和值
                boolean isFirst = true;
                for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                    String columnName = entry.getKey();
                    String value = entry.getValue();

                    // 跳过空值
                    if (value == null || value.trim().isEmpty()) {
                        continue;
                    }

                    if (!isFirst) {
                        columnsBuilder.append(", ");
                        valuesBuilder.append(", ");
                    }

                    columnsBuilder.append(columnName);
                    valuesBuilder.append("?");

                    // 确定参数类型
                    int sqlType = determineParameterType(columnName, value, columnTypes);
                    paramTypes.put(columnName, sqlType);

                    // 根据类型转换参数值
                    Object convertedValue = convertValueByType(value, sqlType);
                    paramValues.add(convertedValue);

                    isFirst = false;
                }

                // 如果没有有效字段，跳过此记录
                if (isFirst) {
                    continue;
                }

                sqlBuilder.append(columnsBuilder).append(") VALUES (").append(valuesBuilder).append(")");

                log.writeLog("SQL语句 = " + sqlBuilder.toString());
                // 准备SQL语句
                ps = conn.prepareStatement(sqlBuilder.toString());

                // 设置参数值
                int paramIndex = 1;
                for (Object paramValue : paramValues) {
                    setParameterValue(ps, paramIndex++, paramValue);
                }

                // 执行插入
                int result = ps.executeUpdate();
                successCount += result;

                // 关闭当前PreparedStatement
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            }

            // 提交事务
            conn.commit();
            log.writeLog("批量插入数据成功，共插入 " + successCount + " 条记录到表 " + tablename);

        } catch (SQLException e) {
            // 发生错误时回滚事务
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException ex) {
                log.writeLog("回滚事务失败: " + ex.getMessage());
            }

            e.printStackTrace();
            log.writeLog("批量插入数据失败: " + e.getMessage());
        } finally {
            // 恢复自动提交
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                }
            } catch (SQLException e) {
                log.writeLog("恢复自动提交失败: " + e.getMessage());
            }

            // 关闭资源
            closeResources(null, ps, conn);
        }

        return successCount;
    }

    /**
     * 获取表的列类型信息
     *
     * @param conn      数据库连接
     * @param tableName 表名
     * @return 列名到SQL类型的映射
     */
    public static Map<String, Integer> getTableColumnTypes(Connection conn, String tableName) {
        Map<String, Integer> columnTypes = new HashMap<>();

        try {
            // 查询表结构
            java.sql.DatabaseMetaData metaData = conn.getMetaData();
            java.sql.ResultSet columns = metaData.getColumns(null, null, tableName, null);

            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME").toLowerCase();
                int dataType = columns.getInt("DATA_TYPE");
                columnTypes.put(columnName, dataType);
            }

            columns.close();
        } catch (SQLException e) {
            // 如果获取表结构失败，返回空映射，后续将使用智能类型推断
        }

        return columnTypes;
    }

    /**
     * 智能确定参数类型
     *
     * @param columnName  列名
     * @param value       值
     * @param columnTypes 表的列类型信息
     * @return SQL类型常量
     */
    public static int determineParameterType(String columnName, String value, Map<String, Integer> columnTypes) {
        // 首先检查是否有表结构信息
        if (columnTypes.containsKey(columnName.toLowerCase())) {
            return columnTypes.get(columnName.toLowerCase());
        }

        // 根据列名进行猜测
        String lowerColumnName = columnName.toLowerCase();
        if (lowerColumnName.contains("id") || lowerColumnName.equals("mainid")) {
            return java.sql.Types.INTEGER;
        }

        if (lowerColumnName.contains("je") || lowerColumnName.contains("jg") ||
                lowerColumnName.contains("fee") || lowerColumnName.contains("price") ||
                lowerColumnName.contains("tax") || lowerColumnName.contains("amount") ||
                lowerColumnName.contains("money") || lowerColumnName.contains("cost") ||
                lowerColumnName.endsWith("sl") || lowerColumnName.endsWith("se")) {
            return java.sql.Types.DECIMAL;
        }

        // 根据值进行猜测
        if (value == null || value.trim().isEmpty()) {
            return java.sql.Types.VARCHAR;
        }

        // 移除百分号等特殊字符
        String testValue = value;
        if (testValue.endsWith("%")) {
            testValue = testValue.substring(0, testValue.length() - 1);
        }

        // 尝试解析为数字
        try {
            if (testValue.contains(".")) {
                new BigDecimal(testValue);
                return java.sql.Types.DECIMAL;
            } else if (testValue.matches("-?\\d+")) {
                Long.parseLong(testValue);
                return java.sql.Types.INTEGER;
            }
        } catch (NumberFormatException e) {
            // 不是数字，使用字符串类型
        }

        // 默认为字符串类型
        return java.sql.Types.VARCHAR;
    }

    /**
     * 根据SQL类型转换值
     *
     * @param value   原始字符串值
     * @param sqlType SQL类型
     * @return 转换后的对象
     */
    public static Object convertValueByType(String value, int sqlType) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        // 处理百分号
        if (value.endsWith("%") && (sqlType == java.sql.Types.DECIMAL || sqlType == java.sql.Types.INTEGER)) {
            value = value.substring(0, value.length() - 1);
        }

        try {
            switch (sqlType) {
                case java.sql.Types.INTEGER:
                case java.sql.Types.BIGINT:
                case java.sql.Types.SMALLINT:
                case java.sql.Types.TINYINT:
                    return Long.parseLong(value);

                case java.sql.Types.DECIMAL:
                case java.sql.Types.NUMERIC:
                case java.sql.Types.DOUBLE:
                case java.sql.Types.FLOAT:
                case java.sql.Types.REAL:
                    return new BigDecimal(value);

                case java.sql.Types.DATE:
                    return java.sql.Date.valueOf(value);

                case java.sql.Types.TIMESTAMP:
                    return java.sql.Timestamp.valueOf(value);

                default:
                    return value;
            }
        } catch (Exception e) {
            // throw new RuntimeException("值转换失败，将使用原始字符串: " + value + ", 错误: " + e.getMessage());
            return value;
        }
    }

    /**
     * 设置PreparedStatement参数值
     *
     * @param ps    PreparedStatement
     * @param index 参数索引
     * @param value 参数值
     * @throws SQLException SQL异常
     */
    public static void setParameterValue(PreparedStatement ps, int index, Object value) throws SQLException {
        if (value == null) {
            ps.setNull(index, java.sql.Types.NULL);
        } else if (value instanceof Long) {
            ps.setLong(index, (Long) value);
        } else if (value instanceof Integer) {
            ps.setInt(index, (Integer) value);
        } else if (value instanceof BigDecimal) {
            ps.setBigDecimal(index, (BigDecimal) value);
        } else if (value instanceof java.sql.Date) {
            ps.setDate(index, (java.sql.Date) value);
        } else if (value instanceof java.sql.Timestamp) {
            ps.setTimestamp(index, (java.sql.Timestamp) value);
        } else {
            ps.setString(index, value.toString());
        }
    }

    /**
     * 生成流程数据
     *
     * @param list       项目详情列表
     * @param userId     用户ID
     * @param gszt       公司主体
     * @param tableIndex 表索引
     */
    public String generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex) {
        String requestName = FlowUtil.generateExpenseTitle(userId);
        String userName = FlowUtil.userid(userId);
        Map<String, String> fieldMap = new HashMap<>();

        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());
        fieldMap.put("sqr", userId);// 申请人
        fieldMap.put("sqrq", currentDate);// 申请日期
        fieldMap.put("zdrq", currentDate);// 申请日期
        fieldMap.put("zdr", userId);// 申请日期
        fieldMap.put("gszt", gszt);// 公司主体
        String deptId = FlowUtil.queryDepartmentIdByEmployeeId(Integer.parseInt(userId));
        fieldMap.put("szbm", StringUtils.hasText(deptId) ? deptId : "");// 申请部门
        Map<String, Boolean[]> viewEditMap = new HashMap<>();
        viewEditMap.put("gszt", new Boolean[]{true, true});
        List<Map<String, String>> detailTableData = new ArrayList<>();

        // 遍历项目列表，将每个项目的属性添加到明细表中
        for (STYProjectDetailInfo project : list) {
            Map<String, String> rowData = new HashMap<>();

            // 添加项目基本信息
            rowData.put("stejxmbh", project.getEjxmbh());  // 二级项目编号
            rowData.put("stejxmmc", project.getId());  // 二级项目名称
            rowData.put("styjxmmc", project.getYjxmmc());  // 一级项目名称
            rowData.put("styjxmbh", project.getYjxmbh());  // 一级项目编号

            // 添加部门信息
            rowData.put("yjbm", project.getYjbm());      // 一级部门
            rowData.put("ejbm", project.getEjbm());      // 二级部门
            rowData.put("sjbm", project.getSjbm());      // 三级部门

            // 添加财务部门信息
            rowData.put("cwyjbm", project.getCwYjbm());  // 财务一级部门
            rowData.put("cwejbm", project.getCwEjbm());  // 财务二级部门
            rowData.put("cwsjbm", project.getCwSjbm());  // 财务三级部门


            // 添加项目类型信息
            rowData.put("xmlx", project.getXmlx());      // 项目类型
            // rowData.put("xmlxDesc", project.getXmlxDesc()); // 项目类型描述


            // 计算项目性质（xmxz）
            String xmxz = "2"; // 默认为业务项目
            String sfsggxm = project.getSfsggxm();
            String xmlx = project.getXmlx(); // 获取项目类型

            // 如果是公共项目(sfsggxm=0)，则项目性质=0(公共项目)
            if ("0".equals(sfsggxm)) {
                xmxz = "0";
            }
            // 如果不是公共项目，且项目类型=6(研发项目)，则项目性质=1(研发)
            else if (xmlx != null && "6".equals(xmlx)) {
                xmxz = "1";
            }
            // 否则为业务项目(2)，这是默认值，不需要额外设置

            // 添加项目性质字段
            rowData.put("xmxz", xmxz);

            // 添加其他信息
            rowData.put("fycdbm", project.getSqrbm());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            // rowData.put("sfsggxmDesc", project.getSfsggxmDesc()); // 是否是公共项目描述

            // 添加其他信息
            rowData.put("fycdbm", project.getBmcx());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            // rowData.put("sfsggxmDesc", project.getSfsggxmDesc()); // 是否是公共项目描述
            rowData.put("jsms", project.getJsms()); // 计税模式 0 增值税 1 简易计税
            // rowData.put("jsmsDesc", project.getJsmsDesc()); // 计税模式描述
            Map<String, String> map = project.getMap();
            rowData.put("sxmcv2", map.get("sxmc"));      // 事项名称
            rowData.put("dcfhsje", map.get("car_include_tax")); // 打车
            rowData.put("mhfzjj", map.get("airport_fee")); // 民航发展基金
            rowData.put("pmje", map.get("air_ticket_total_include_tax")); // 票面金额
            rowData.put("jpse", FlowUtil.calculateAmount(map.get("air_ticket_total_include_tax"), map.get("airport_fee")).toPlainString());
            rowData.put("jpsl", "9%"); // 机票税率
            rowData.put("hcppj", map.get("train_include_tax")); // 火车票票价
            rowData.put("hcpbhsje", map.get("train_exclude_tax")); // 火车票不含税
            rowData.put("hcpse", map.get("train_tax")); // 火车票税额

            rowData.put("zsfzyfpbhsje", map.get("tafset")); // 住宿费专用发票不含税
            // 获取两个金额字符串
            String tafsitStr = map.get("tafsit");
            String tafsetStr = map.get("tafset");

            // 初始化默认值为 0
            BigDecimal tafsit = BigDecimal.ZERO;
            BigDecimal tafset = BigDecimal.ZERO;

            try {
                // 尝试将字符串转换为 BigDecimal 类型
                if (tafsitStr != null) {
                    tafsit = new BigDecimal(tafsitStr);
                }
                if (tafsetStr != null) {
                    tafset = new BigDecimal(tafsetStr);
                }
            } catch (NumberFormatException e) {
                // 处理转换失败的情况
                log.writeLog("金额转换失败: " + e.getMessage());
            }

            // 计算差值
            BigDecimal difference = tafsit.subtract(tafset);
            // 将差值添加到 rowData 中
            rowData.put("zsfse", difference.toPlainString()); // // 住宿费税额

            rowData.put("zsfsl", "6%"); // 住宿费税率
            rowData.put("zsfptfphsje", map.get("tafoit")); // 住宿费普通发票（含税金额）

            rowData.put("ddhcpfwfbhsje", map.get("manual_price")); // 代打火车票服务费（不含税金额）
            rowData.put("ddhcpfwfse", map.get("manual_price_tax")); // 代打火车票服务费（税额）
            rowData.put("zjzclx", "0"); // 资金支出类型
            rowData.put("mxid", map.get("id")); // mxid
            rowData.put("clbt", map.get("clbt")); // 差旅补贴
            // 添加到明细表数据列表
            detailTableData.add(rowData);
        }
        Map<String, Boolean[]> detailViewEditMap = new HashMap<>();
        // 创建工作流
        // 创建工作流
        newSTYgenerateDetailInfoImpl newSTYgenerateDetailInfo = new newSTYgenerateDetailInfoImpl();
        WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
                fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        log.writeLog("创建工作流对象 ：" + JSONUtil.toJsonStr(workflowRequestInfo));
        WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
        String requestId = workflowService.saveWorkflowRequest(workflowRequestInfo, -1, Integer.parseInt(userId), "", "", "");
        log.writeLog("创建工作流对象 requestId：" + requestId);
        String id = newSTYgenerateDetailInfo.getIdByRequestId("formtable_main_1981", requestId);

        log.writeLog("开始添加明细 : 明细表：formtable_main_1981_dt3  id: " + id + "   数据 ： " + JSONUtil.toJsonStr(detailTableData));
        int i = newSTYgenerateDetailInfo.batchInsertData(detailTableData, "formtable_main_1981_dt3", id);
        log.writeLog("添加明细成功");
        return requestId;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // // 测试代码
        // List<Map<String, String>> testDataList = new ArrayList<>();
        //
        // Map<String, String> map1 = new HashMap<>();
        // map1.put("code", "STYHS-EJXM20240029");
        // map1.put("userId", "2947");
        // map1.put("gszt", "1");
        // testDataList.add(map1);
        //
        // Map<String, String> map2 = new HashMap<>();
        // map2.put("code", "STYHS-EJXM20240030");
        // map2.put("userId", "2947");
        // map2.put("gszt", "1");
        // testDataList.add(map2);

        // List<STYProjectDetailInfo> results = new newSTYgenerateDetailInfoImpl().batchInsertData(testDataList);
        // log.writeLog("查询结果数量: " + results.size());
    }

    /**
     * 根据requestId从指定表中获取id
     *
     * @param tableName 表名
     * @param requestId 请求ID
     * @return 对应的id，如果没有找到则返回null
     */
    public String getIdByRequestId(String tableName, String requestId) {
        Connection conn = null;
        PreparedStatement ps = null;
        java.sql.ResultSet rs = null;
        String id = null;

        try {
            // 获取数据库连接
            conn = getConnection();
            if (conn == null) {
                System.out.println("获取数据库连接失败");
                return null;
            }

            // 构建SQL查询语句
            String sql = "SELECT id FROM " + tableName + " WHERE requestid = ?";
            ps = conn.prepareStatement(sql);
            ps.setInt(1, Integer.valueOf(requestId));

            // 执行查询
            rs = ps.executeQuery();

            // 获取结果
            if (rs.next()) {
                id = rs.getString("id");
            }

        } catch (SQLException e) {
            e.printStackTrace();
            System.out.println("查询id失败: " + e.getMessage());
        } finally {
            // 关闭资源
            closeResources(rs, ps, conn);
        }

        return id;
    }

    /**
     * 根据requestId从workflow_requestbase表中获取id
     *
     * @param requestId 请求ID
     * @return 对应的id，如果没有找到则返回null
     */
    public static String getWorkflowIdByRequestId(String requestId) {
        return new newSTYgenerateDetailInfoImpl().getIdByRequestId("workflow_requestbase", requestId);
    }

    /**
     * 根据requestId从指定表中获取主表记录ID
     *
     * @param tableName 表名前缀，如"formtable_main_"
     * @param tableId   表ID，如"1981"
     * @param requestId 请求ID
     * @return 对应的主表记录ID，如果没有找到则返回null
     */
    public String getFormMainIdByRequestId(String tableName, String tableId, String requestId) {
        Connection conn = null;
        PreparedStatement ps = null;
        java.sql.ResultSet rs = null;
        String id = null;

        try {
            // 获取数据库连接
            conn = getConnection();
            if (conn == null) {
                System.out.println("获取数据库连接失败");
                return null;
            }

            // 构建完整表名
            String fullTableName = tableName + tableId;

            // 构建SQL查询语句
            String sql = "SELECT id FROM " + fullTableName + " WHERE requestid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, requestId);

            // 执行查询
            rs = ps.executeQuery();

            // 获取结果
            if (rs.next()) {
                id = rs.getString("id");
            }

        } catch (SQLException e) {
            e.printStackTrace();
            System.out.println("查询表单主表ID失败: " + e.getMessage());
        } finally {
            // 关闭资源
            closeResources(rs, ps, conn);
        }

        return id;
    }
} 