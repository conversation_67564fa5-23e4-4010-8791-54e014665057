package weaver.interfaces.workflow.action.cyitce.job.fbt.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:42
 * @describe
 */
public interface generateDetailInfo {

    /**
     * 生成详细信息
     * @param dataList 包含查询参数的Map列表
     * @return 项目详细信息列表
     */
    List<STYProjectDetailInfo> generateDetailInfo(List<Map<String, String>> dataList);
}
