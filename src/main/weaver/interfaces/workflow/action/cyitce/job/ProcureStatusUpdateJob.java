package weaver.interfaces.workflow.action.cyitce.job;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @ClassName: 数藤云计算-采购需求状态控制
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-28  15:11
 * @Version: 1.0
 */
public class ProcureStatusUpdateJob extends BaseCronJob {
    private void writeLog(String var){
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(),var);
    }

    @Override
    public void execute() {
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        try {
            //采购需求截止时间检测
            if(rst.executeUpdate("update uf_styjs_cgxqspbd set cgzt=2 where cgzt=0 and CAST(bmjzrq+''+bmjzsj AS DATETIME) < CAST(GETDATE() AS DATETIME)")){
                writeLog("======采购需求截止时间检测失败");
            }
            rst.commit();
        }catch (Exception e){
            writeLog("========== "+e.getMessage());
            rst.rollback();
        }
    }
}