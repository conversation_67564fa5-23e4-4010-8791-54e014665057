## 查询差补结算单列表

### 请求

*   **URL：**/openapi/reimbursement/settlement/v2/list
*   **Method：**POST
*   **Content-Type：**application/json
*   **需要鉴权：**是

### 请求头

| 字段           | 类型     | 必填 | 名称    | 备注                   |
| ------------ | ------ | -- | ----- | -------------------- |
|              |        |    |       |                      |
| access-token | String | 是  | token | 通过获取token的接口获得token  |

### 请求体

| 字段                    | 类型         | 必填 | 名称          | 备注                                  |
| --------------------- | ---------- | -- | ----------- | ----------------------------------- |
|                       |            |    |             |                                     |
| page_index            | Integer    | 是  | 页数          |                                     |
| page_size             | Integer    | 是  | 每页条数        | 最大100条                              |
| operator_id_info      | Object     | 是  | 操作人ID信息     |                                     |
| id                    | Integer    | 否  | 结算编号        |                                     |
| period                | String     | 否  | 结算期间        | 格式：yyyy-MM                          |
| total_allowance_money | BigDecimal | 否  | 结算总金额       |                                     |
| status                | Integer    | 否  | 结算状态        | 0-结算中，1-结算待确认，2-结算失败，3-确认结算，4-取消结算  |
| reimburse_meaning_no  | String     | 否  | 关联报销单号      |                                     |
| confirm_time_from     | String     | 否  | 确认结算时间-开始时间 | 格式：yyyy-MM-dd HH:mm:ss              |
| confirm_time_to       | String     | 否  | 确认结算时间-结束时间 | 格式：yyyy-MM-dd HH:mm:ss              |
| start_time_from       | String     | 否  | 结算开始时间-开始范围 | 格式：yyyy-MM-dd HH:mm:ss              |
| start_time_to         | String     | 否  | 结算开始时间-结束范围 | 格式：yyyy-MM-dd HH:mm:ss              |

### 响应体

| 字段         | 类型     | 名称     | 备注                            |
| ---------- | ------ | ------ | ----------------------------- |
|            |        |        |                               |
| trace_id   | String | 跟踪ID   |                               |
| code       | String | 响应码枚举值 | 0：全部成功 -9999：全部失败             |
| msg        | String | 响应码描述  | （code）为-9999时，显示错误信息为0时，显示成功  |
| request_id | String | 请求ID   |                               |
| data       | Object | 数据     |                               |
    | page_index | Integer | 起始页   |  |
    | page_size  | Integer | 每页显示的条数 |  |
    | total_count | Integer | 总记录数 |  |
    | total_pages | Integer | 总页数 |  |
    orders List<Object> 结算单列表
        | id | String | 结算任务ID |
        | no | String | 结算单编号 |
        | company_id | String | 公司ID |
        | period | String | 结算期间 结算月份（yyyy-MM） |
        | settlement_date | String | 结算日期 |
        | total_allowance_money | String | 结算单总金额 |
        | settlement_operator_id | String | 结算操作人ID |
        | confirm_time | String | 确认结算时间 |
        | confirm_operator_id | String | 确认结算操作人ID |
        | cancel_time | String | 取消结算时间 |
        | cancel_operator_id | String | 取消结算操作人ID |
        | status | String | 结算状态 枚举值 | 0 结算中 1 结算完成 2 结算失败 3 确认结算 4 取消结算 |
        | reimburse_apply_id | String | 关联报销单id |
        | reimburse_meaning_no | String | 关联报销单号 |
        | push_confirm_num | String | 推送员工确认次数 |
        | push_confirm_time | String | 推送员工确认时间 格式：yyyy-MM-dd HH:mm:ss |
        | push_confirm_end_time | String | 推送员工确认截止时间 格式：yyyy-MM-dd HH:mm:ss |





查询差补结算单详情
 请求 • URL：/openapi/reimbursement/settlement/v1/list • Method：POST • Content-Type：application/json • 需要鉴权：是
 请求头   字段    类型    必填    名称    备注
 access-token String 是 token 通过获取token的接口获得token
请求示例:
{
  "settlement_ids": [
    "1926883169225326593"
  ]
}

响应示例:
{
  "trace_id": "d07fffce2bbb4eca9d88131b746654cc.114.17506638628630015@1.2.60-SNAPSHOT",
  "code": 0,
  "msg": "成功",
  "data": [
    {
      "details": [
        {
          "id": "1926883170160656386",
          "settlement_id": 1926883169225326600,
          "employee_id": "6247bd62eb25157ec220d772",
          "third_employee_id": "520888",
          "employee_name": "测试yhm",
          "employee_num": "yhm001",
          "employee_phone": "18999999876",
          "department_name": "FAT测试-yhm测试企业一二三",
          "department_id": "6247bd62eb25157ec220d770",
          "third_department_id": "e4b83070-9fc9-4e30-9a14-506514a5cd79",
          "days": 1.5,
          "allowance_money": 1532,
          "employee_details": [
            {
              "id": "1926883170173239298",
              "settlement_detail_id": "1926883170160656386",
              "date": "2025-05-14",
              "city_name": "上海市",
              "allowance_type_name": "1",
              "scheme_name": "123",
              "day_type": 0,
              "allowance_money": 500,
              "deduct_amount": 0,
              "days_type": 3,
              "add_amount": 0,
              "modify_reason": "",
              "cost_attributions": [
                {
                  "type": 1,
                  "archive_id": "department",
                  "archive_name": "部门",
                  "details": [
                    {
                      "name": "yhm测试部门1",
                      "weight": "100.00",
                      "code": "2446686359078",
                      "third_id": "DJ-ThirdID-1",
                      "id": "6294402be84b543ddabcc33e",
                      "full_name": "yhm测试部门1"
                    }
                  ]
                },
                {
                  "type": 4,
                  "archive_id": "user",
                  "archive_name": "员工"
                },
                {
                  "type": 3,
                  "third_archive_id": "third_aqwerwer",
                  "archive_id": "629432e1e84b543ddabcc250",
                  "archive_name": "aqwerwer",
                  "details": [
                    {
                      "name": "测试1",
                      "weight": "100.00",
                      "code": "DA000012022061046603",
                      "third_id": "third_archive_project_id001",
                      "id": "62a2f6bd8263ce4ebc2c48f5"
                    }
                  ]
                },
                {
                  "type": 2,
                  "archive_id": "project",
                  "archive_name": "项目",
                  "details": [
                    {
                      "name": "凭证测试用项目2",
                      "weight": "100.00",
                      "code": "2025010951379",
                      "id": "677f41a30c94721b6ee71e4e"
                    }
                  ]
                }
              ]
            },
            {
              "id": "1926883170181627905",
              "settlement_detail_id": "1926883170160656386",
              "date": "2025-05-14",
              "city_name": "上海市",
              "allowance_type_name": "009",
              "scheme_name": "009",
              "day_type": 0,
              "allowance_money": 40,
              "deduct_amount": 0,
              "days_type": 3,
              "add_amount": 0,
              "modify_reason": "",
              "cost_attributions": [
                {
                  "type": 1,
                  "archive_id": "department",
                  "archive_name": "部门",
                  "details": [
                    {
                      "name": "yhm测试部门1",
                      "weight": "100.00",
                      "code": "2446686359078",
                      "third_id": "DJ-ThirdID-1",
                      "id": "6294402be84b543ddabcc33e",
                      "full_name": "yhm测试部门1"
                    }
                  ]
                },
                {
                  "type": 4,
                  "archive_id": "user",
                  "archive_name": "员工"
                },
                {
                  "type": 3,
                  "third_archive_id": "third_aqwerwer",
                  "archive_id": "629432e1e84b543ddabcc250",
                  "archive_name": "aqwerwer",
                  "details": [
                    {
                      "name": "测试1",
                      "weight": "100.00",
                      "code": "DA000012022061046603",
                      "third_id": "third_archive_project_id001",
                      "id": "62a2f6bd8263ce4ebc2c48f5"
                    }
                  ]
                },
                {
                  "type": 2,
                  "archive_id": "project",
                  "archive_name": "项目",
                  "details": [
                    {
                      "name": "凭证测试用项目2",
                      "weight": "100.00",
                      "code": "2025010951379",
                      "id": "677f41a30c94721b6ee71e4e"
                    }
                  ]
                }
              ]
            },
            {
              "id": "1926883170185822210",
              "settlement_detail_id": "1926883170160656386",
              "date": "2025-05-15",
              "city_name": "源城区",
              "allowance_type_name": "1",
              "scheme_name": "123",
              "day_type": 0,
              "allowance_money": 992,
              "deduct_amount": 8,
              "days_type": 1,
              "deduct_items": [
                {
                  "id": "1699246316378079234",
                  "type": "hotel_breakfast",
                  "name": "酒店含早",
                  "item_amount": 8,
                  "count": 1,
                  "amount": 8
                }
              ],
              "add_amount": 0,
              "add_items": [
                {
                  "id": "1916788352214511618",
                  "type": "sys_add_1",
                  "name": "系统预设补贴1",
                  "item_amount": 0,
                  "count": 1,
                  "amount": 0
                }
              ],
              "modify_reason": "",
              "cost_attributions": [
                {
                  "type": 1,
                  "archive_id": "department",
                  "archive_name": "部门",
                  "details": [
                    {
                      "name": "yhm测试部门1",
                      "weight": "100.00",
                      "code": "2446686359078",
                      "third_id": "DJ-ThirdID-1",
                      "id": "6294402be84b543ddabcc33e",
                      "full_name": "yhm测试部门1"
                    }
                  ]
                },
                {
                  "type": 4,
                  "archive_id": "user",
                  "archive_name": "员工"
                },
                {
                  "type": 3,
                  "third_archive_id": "third_aqwerwer",
                  "archive_id": "629432e1e84b543ddabcc250",
                  "archive_name": "aqwerwer",
                  "details": [
                    {
                      "name": "测试1",
                      "weight": "100.00",
                      "code": "DA000012022061046603",
                      "third_id": "third_archive_project_id001",
                      "id": "62a2f6bd8263ce4ebc2c48f5"
                    }
                  ]
                },
                {
                  "type": 2,
                  "archive_id": "project",
                  "archive_name": "项目",
                  "details": [
                    {
                      "name": "凭证测试用项目2",
                      "weight": "100.00",
                      "code": "2025010951379",
                      "id": "677f41a30c94721b6ee71e4e"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "id": "1926883169225326593",
      "company_id": "6247bd62eb25157ec220d770",
      "period": "2025-05",
      "settlement_date": "2025-05-26",
      "total_allowance_money": 1532,
      "settlement_operator_id": "6247bd62eb25157ec220d772",
      "confirm_time": "2025-05-26 14:13:57",
      "confirm_operator_id": "6247bd62eb25157ec220d772",
      "status": 3
    }
  ],
  "request_id": "2HJAdqXapm69cvh3"
}


## 查询部门信息详情
### 请求

*   **URL：**/openapi/org/department/v1/detail
*   **Method：**POST
*   **Content-Type：**application/json
*
请求体
{
  "third_project_id": "JTLX-EJXM20250051"
}
响应参数
{
    "trace_id": "<EMAIL>",
    "code": 0,
    "msg": "成功",
    "data": {
        "project": {
            "id": "6849d91761e7300f7430719d",
            "code": "JTLX-EJXM20250051",
            "name": "2025财务经营部公共项目",
            "state": 1,
            "third_id": "JTLX-EJXM20250051",
            "expired_type": 1,
            "start_time": "2025-01-01 00:00:00",
            "end_time": "2025-12-31 00:00:00",
            "use_range": 2,
            "member_depts": [{
                "id": "6805a4aedc294c6c78b60711",
                "name": "财务经营部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "299"
            }, {
                "id": "6805a4ae8539403c6027f994",
                "name": "市场部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "300"
            }, {
                "id": "6805a4aeb3a7547add14733d",
                "name": "信息系统部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "301"
            }, {
                "id": "6805a4aefe80754fb285c32b",
                "name": "综合部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "302"
            }, {
                "id": "6805a4ae8d38510db1e36e80",
                "name": "投资管理部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "305"
            }, {
                "id": "6805a4afb3a7547add147340",
                "name": "集团党委",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "461"
            }, {
                "id": "6805a4af8539403c6027f998",
                "name": "集团董事会",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "473"
            }, {
                "id": "6805a4af8539403c6027f99b",
                "name": "海外BG",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "574"
            }, {
                "id": "6805a4af8539403c6027f99e",
                "name": "运营商BG",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "575"
            }, {
                "id": "6805a4afdc294c6c78b60714",
                "name": "设备商BG",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "577"
            }, {
                "id": "6805a4afdc294c6c78b60717",
                "name": "创新BG",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "578"
            }, {
                "id": "6805a4affe80754fb285c32f",
                "name": "解决方案部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "579"
            }, {
                "id": "6805a4afb3a7547add147344",
                "name": "政企BG",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "580"
            }, {
                "id": "6805a4afdc294c6c78b6071a",
                "name": "创新与研发部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "588"
            }, {
                "id": "6805a4b08d38510db1e36e86",
                "name": "供应链管理部",
                "company_id": "67d7f3243d83426b2c5cc4b0",
                "third_id": "589"
            }],
            "custom_fields": [{
                "id": "6805b9871521683389cf1bdf",
                "title": "EPM",
                "detail": "[6805adb3dc294c6c78b6253e]",
                "type": 2
            }, {
                "id": "6805b9910c575a0b17181211",
                "title": "SPM",
                "detail": "[6805adb3dc294c6c78b6253e]",
                "type": 2
            }, {
                "id": "6805b99913c5c41cc8d569a3",
                "title": "PD",
                "type": 2
            }, {
                "id": "6805ba150c575a0b17181323",
                "title": "parentId",
                "type": 1
            }, {
                "id": "6805ba150c575a0b17181326",
                "title": "isPublic",
                "detail": "0",
                "type": 1
            }, {
                "id": "6805ba150c575a0b17181324",
                "title": "projectId",
                "detail": "50",
                "type": 1
            }, {
                "id": "6805ba150c575a0b17181322",
                "title": "parentNum",
                "type": 1
            }, {
                "id": "6805ba150c575a0b17181325",
                "title": "companyId",
                "detail": "11",
                "type": 1
            }]
        }
    },
    "request_id": "6mz9UVeYoeFZMs3f"
}