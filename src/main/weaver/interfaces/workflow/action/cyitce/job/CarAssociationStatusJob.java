package weaver.interfaces.workflow.action.cyitce.job;

import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @ClassName: 信科工程-车辆相关状态控制
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-11  15:52
 * @Version: 1.0
 */
public class CarAssociationStatusJob extends BaseCronJob {

    private void writeLog(String var){
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(),var);
    }

    @Override
    public void execute() {
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        try {
            //驾驶员证件有效期检测
            if(!rst.executeUpdate("update uf_jsydjb set jszzt=1 where CAST(zjyxqjssj AS DATE) < CAST(GETDATE() AS DATE)")){
                writeLog("==========驾驶员证件有效期检测失败");
            }

            //车辆交强险有效期检测
            if(!rst.executeUpdate("update uf_catregister set jqxzt=1 where CAST(jqxsixrq AS DATE) < CAST(GETDATE() AS DATE)")){
                writeLog("==========驾驶员证件有效期检测失败");
            }
            //车辆商业险有效期检测
            if(!rst.executeUpdate("update uf_catregister set syxzt=1 where CAST(syxsixrq AS DATE) < CAST(GETDATE() AS DATE)")){
                writeLog("==========驾驶员证件有效期检测失败");
            }
            //车辆租赁合同有效期检测
            if(!rst.executeUpdate("UPDATE a SET zlhtsxrq=b.htkssj from uf_catregister a inner join uf_cghtspbd b on a.cght=b.id where a.zlhtsxrq<>b.htkssj;" +
                    "UPDATE a SET zlhtsixrq=b.htzzsj from uf_catregister a inner join uf_cghtspbd b on a.cght=b.id where b.zlhtsixrq<>b.htzzsj;")){
                writeLog("==========车辆租赁合同有效期更新失败");
            }
            if(!rst.executeUpdate("update uf_catregister set zlhtzt=1 where CAST(zlhtsixrq AS DATE) < CAST(GETDATE() AS DATE)")){
                writeLog("==========车辆租赁合同有效期检测失败");
            }

            rst.commit();
            //用车申请返程日期检测
        }catch (Exception e){
            writeLog("========== "+e.getMessage());
            rst.rollback();
        }
    }
}