package weaver.interfaces.workflow.action.cyitce.job.fbt.service;

import cn.hutool.json.JSONUtil;
import org.hsqldb.lib.ValidatingResourceBundle;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1.*;
import weaver.mobile.webservices.workflow.*;

import java.math.BigDecimal;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9 9:44
 * @describe 流程工具类，用于生成流程相关数据
 */

public class FlowUtil {

    BaseBean log = new BaseBean();

    /**
     * 创建流程主表数据并返回流程请求信息对象
     *
     * @param userId       创建人ID
     * @param requestLevel 流程紧急程度(0正常，1重要，2紧急)
     * @param requestName  流程标题
     * @param workflowId   流程ID
     * @param workflowName 流程名称
     * @param fieldMap     主表字段映射(字段名->字段值)
     * @param viewEditMap  字段可见性和可编辑性映射(字段名->[是否可见,是否可编辑])，可为null表示全部可见不可编辑
     * @return 流程请求信息对象
     */
    public static WorkflowRequestInfo createMainTableData(String userId, String requestLevel, String requestName,
                                                          String workflowId, String workflowName,
                                                          Map<String, String> fieldMap,
                                                          Map<String, Boolean[]> viewEditMap) {
        // 创建流程请求信息对象
        WorkflowRequestInfo wri = new WorkflowRequestInfo();
        BaseBean log = new BaseBean();
        log.writeLog("创建流程请求信息对象");
        // 设置基本信息
        wri.setCreatorId(userId);
        wri.setRequestLevel(requestLevel);
        wri.setRequestName(requestName);

        // 设置流程类型信息
        WorkflowExtInfo wbi = new WorkflowExtInfo();
        wbi.setWorkflowId(workflowId);
        wbi.setWorkflowName(workflowName);
        wri.setWorkflowBaseInfo(wbi);

        // 创建主表信息
        WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();
        WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1]; // 主表字段只有一条记录

        // 创建字段数组
        WorkflowRequestTableField[] fields = new WorkflowRequestTableField[fieldMap.size()];

        // 填充字段数据
        int index = 0;
        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String fieldName = entry.getKey();
            String fieldValue = entry.getValue();

            fields[index] = new WorkflowRequestTableField();
            fields[index].setFieldName(fieldName);
            fields[index].setFieldValue(fieldValue);

            // 设置字段可见性和可编辑性
            if (viewEditMap != null && viewEditMap.containsKey(fieldName)) {
                Boolean[] viewEdit = viewEditMap.get(fieldName);
                fields[index].setView(viewEdit[0]);    // 是否可见
                fields[index].setEdit(viewEdit[1]);    // 是否可编辑
            } else {
                // 默认可见不可编辑
                fields[index].setView(true);
                fields[index].setEdit(false);
            }

            index++;
        }

        // 设置主表记录
        wrtri[0] = new WorkflowRequestTableRecord();
        wrtri[0].setWorkflowRequestTableFields(fields);
        workflowMainTableInfo.setRequestRecords(wrtri);

        // 将主表信息添加到流程请求中
        wri.setWorkflowMainTableInfo(workflowMainTableInfo);
        log.writeLog("创建流程请求信息对象完成");
        return wri;
    }

    // /**
    //  * 简化版创建流程主表数据方法，所有字段默认可见不可编辑
    //  * @param userId 创建人ID
    //  * @param requestLevel 流程紧急程度(0正常，1重要，2紧急)
    //  * @param requestName 流程标题
    //  * @param workflowId 流程ID
    //  * @param workflowName 流程名称
    //  * @param fieldMap 主表字段映射(字段名->字段值)
    //  * @return 流程请求信息对象
    //  */
    // public static WorkflowRequestInfo createMainTableData(String userId, String requestLevel, String requestName,
    //                                                String workflowId, String workflowName,
    //                                                Map<String, String> fieldMap) {
    //     return createMainTableData(userId, requestLevel, requestName, workflowId, workflowName, fieldMap, null);
    // }

    /**
     * 初始化明细表数组
     *
     * @param wri              流程请求信息对象
     * @param detailTableCount 明细表数量
     */
    public static void initDetailTables(WorkflowRequestInfo wri, int detailTableCount) {
        WorkflowDetailTableInfo[] detailTables = new WorkflowDetailTableInfo[detailTableCount];
        for (int i = 0; i < detailTableCount; i++) {
            detailTables[i] = new WorkflowDetailTableInfo();
        }
        wri.setWorkflowDetailTableInfos(detailTables);
    }

    /**
     * 为指定索引的明细表添加数据
     *
     * @param wri             流程请求信息对象
     * @param tableIndex      明细表索引（从0开始）
     * @param detailTableData 明细表数据，每个元素代表一行数据
     * @param viewEditMap     字段可见性和可编辑性映射，可为null表示全部可见可编辑
     */
    public static void setDetailTableData(WorkflowRequestInfo wri, int tableIndex,
                                          List<Map<String, String>> detailTableData,
                                          Map<String, Boolean[]> viewEditMap) {
        if (wri == null || detailTableData == null || detailTableData.isEmpty()) {
            return;
        }
        BaseBean log = new BaseBean();
        log.writeLog("进入明细表创建:初始化完成,进入方法");
        // 获取明细表数组
        WorkflowDetailTableInfo[] detailTables = wri.getWorkflowDetailTableInfos();
        if (detailTables == null || tableIndex >= detailTables.length) {
            // 如果明细表数组未初始化或索引超出范围，则初始化或扩展数组
            int newSize = Math.max(tableIndex + 1, detailTables == null ? 1 : detailTables.length);
            WorkflowDetailTableInfo[] newDetailTables = new WorkflowDetailTableInfo[newSize];

            // 复制原有数据
            if (detailTables != null) {
                for (int i = 0; i < detailTables.length; i++) {
                    newDetailTables[i] = detailTables[i];
                }
            }

            // 初始化新增的明细表
            for (int i = (detailTables == null ? 0 : detailTables.length); i < newSize; i++) {
                newDetailTables[i] = new WorkflowDetailTableInfo();
            }

            detailTables = newDetailTables;
            wri.setWorkflowDetailTableInfos(detailTables);
        }
        // 创建明细表记录数组
        WorkflowRequestTableRecord[] detailRecords = new WorkflowRequestTableRecord[detailTableData.size()];

        // 处理明细表的每一行
        for (int rowIndex = 0; rowIndex < detailTableData.size(); rowIndex++) {
            Map<String, String> rowData = detailTableData.get(rowIndex);
            if (rowData == null || rowData.isEmpty()) {
                continue;
            }

            // 创建字段数组
            WorkflowRequestTableField[] fields = new WorkflowRequestTableField[rowData.size()];

            // 填充字段数据
            int fieldIndex = 0;
            for (Map.Entry<String, String> entry : rowData.entrySet()) {
                String fieldName = entry.getKey();
                String fieldValue = entry.getValue();

                fields[fieldIndex] = new WorkflowRequestTableField();
                fields[fieldIndex].setFieldName(fieldName);
                fields[fieldIndex].setFieldValue(fieldValue);

                // 设置字段可见性和可编辑性
                if (viewEditMap != null && viewEditMap.containsKey(fieldName)) {
                    Boolean[] viewEdit = viewEditMap.get(fieldName);
                    fields[fieldIndex].setView(viewEdit[0]);    // 是否可见
                    fields[fieldIndex].setEdit(viewEdit[1]);    // 是否可编辑
                } else {
                    // 默认可见可编辑
                    fields[fieldIndex].setView(true);
                    fields[fieldIndex].setEdit(true);
                }

                fieldIndex++;
            }

            // 设置行记录
            detailRecords[rowIndex] = new WorkflowRequestTableRecord();
            detailRecords[rowIndex].setWorkflowRequestTableFields(fields);
        }
        // 可以设置明细表名称，如果需要的话
        log.writeLog("进入明细表创建:初始化完成,设置值");
        log.writeLog("进入明细表创建:初始化完成 detailRecords:" + Arrays.toString(detailRecords));
        // 设置明细表
        // 检查指定索引的明细表是否为 null，如果为 null 则初始化
        if (detailTables[tableIndex] == null) {
            detailTables[tableIndex] = new WorkflowDetailTableInfo();
        }
        detailTables[tableIndex].setWorkflowRequestTableRecords(detailRecords);
        detailTables[0].setWorkflowRequestTableRecords(detailRecords);
        detailTables[1].setWorkflowRequestTableRecords(detailRecords);
        detailTables[2].setWorkflowRequestTableRecords(detailRecords);
        detailTables[3].setWorkflowRequestTableRecords(detailRecords);
        detailTables[4].setWorkflowRequestTableRecords(detailRecords);
        detailTables[5].setWorkflowRequestTableRecords(detailRecords);
        // detailTables[tableIndex].setTableName("明细表" + (tableIndex + 1));
        log.writeLog("进入明细表创建:初始化完成,返回");
    }

    /**
     * 为第一个明细表添加数据（简化版）
     *
     * @param wri             流程请求信息对象
     * @param detailTableData 明细表数据，每个元素代表一行数据
     */
    public static void setFirstDetailTable(WorkflowRequestInfo wri, List<Map<String, String>> detailTableData) {
        setDetailTableData(wri, 0, detailTableData, null);
    }

    /**
     * 创建完整的流程请求信息对象，包含主表和明细表数据
     *
     * @param userId          创建人ID
     * @param requestLevel    流程紧急程度(0正常，1重要，2紧急)
     * @param requestName     流程标题
     * @param workflowId      流程ID
     * @param workflowName    流程名称
     * @param mainTableData   主表字段映射(字段名->字段值)
     * @param detailTableData 第一个明细表数据
     * @return 完整的流程请求信息对象
     */
    public static WorkflowRequestInfo createWorkflowWithDetail(
            String userId, String requestLevel, String requestName,
            String workflowId, String workflowName,
            Map<String, String> mainTableData,
            Map<String, Boolean[]> mainViewEditMap,
            List<Map<String, String>> detailTableData,
            Map<String, Boolean[]> detailViewEditMap,
            int tableIndex) {
        BaseBean log = new BaseBean();
        log.writeLog("userId: " + userId);
        log.writeLog("requestLevel: " + requestLevel);
        log.writeLog("requestName: " + requestName);
        log.writeLog("workflowId: " + workflowId);
        log.writeLog("workflowName: " + workflowName);
        log.writeLog("mainTableData: " + mainTableData.toString());
        log.writeLog("mainViewEditMap: " + mainViewEditMap.toString());
        log.writeLog("detailTableData: " + detailTableData.toString());
        log.writeLog("detailViewEditMap: " + detailViewEditMap.toString());
        log.writeLog("tableIndex: " + tableIndex);
        // 创建主表数据
        WorkflowRequestInfo wri = createMainTableData(userId, requestLevel, requestName,
                workflowId, workflowName,
                mainTableData, mainViewEditMap);

        // // 添加明细表数据
        // if (detailTableData != null && !detailTableData.isEmpty()) {
        //     log.writeLog("进入明细表创建");
        //     initDetailTables(wri, 6);
        //     log.writeLog("进入明细表创建:初始化完成");
        //     // setFirstDetailTable(wri, detailTableData);
        //     setDetailTableData(wri, tableIndex, detailTableData, detailViewEditMap);
        // }
        log.writeLog("进入明细表创建：返回wri");
        return wri;
    }

    /**
     * 根据用户ID获取用户姓名
     *
     * @param id 用户ID
     * @return 用户姓名
     */
    public static String userid(String id) {
        // return "李俊鸿2947";
        RecordSet con = new RecordSet();
        con.executeQuery("SELECT lastname FROM HrmResource where id=?", new Object[]{id});
        new BaseBean().writeLog("userid--------------> id: ");
        if (con.next()) {
            String string = con.getString(1);
            new BaseBean().writeLog("userid--------------> username: " + string);
            return string;
        }

        return "";
    }

    /**
     * 生成消费账单月度报销流程标题
     * 格式：消费账单月度报销流程-用户名ID-日期
     * 例如：消费账单月度报销流程-李俊鸿2947-2025-06-09
     *
     * @param userId 用户ID
     * @return 格式化的流程标题
     */
    public static String generateExpenseTitle(String userId) {
        // 固定前缀
        String prefix = "消费账单月度报销流程";

        // 获取用户姓名
        String userName = userid(userId);

        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        // 组合标题：消费账单月度报销流程-李俊鸿2947-2025-06-09
        return prefix + "-" + userName + "-" + currentDate;
    }

    /**
     * 生成指定流程类型的标题
     *
     * @param flowPrefix 流程前缀名称
     * @param userId     用户ID
     * @return 格式化的流程标题
     */
    public static String generateFlowTitle(String flowPrefix, String userId) {
        // 获取用户姓名
        String userName = userid(userId);

        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        // 组合标题：流程前缀-用户名ID-日期
        return flowPrefix + "-" + userName + userId + "-" + currentDate;
    }


    /**
     * 示例方法
     */
    public void mainTable() {
        WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();// 主表
        WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];// 主表字段只有一条记录
        WorkflowRequestInfo wri = new WorkflowRequestInfo();// 流程基本信息
    }

    public static void main(String[] args) {
        int i = parseBigDecimal("0").compareTo(BigDecimal.ZERO);
        System.out.println("i = " + i);
    }

    public static void main1(String[] args) {


        WorkflowRequestInfo workflowRequestInfo = new WorkflowRequestInfo();// 工作流程请求信息

        int userid = 115;// 用户ID-赵晓燕
//		workflowRequestInfo.setRequestId(String.valueOf(1918557));//流程请求ID-创建流程时自动产生
        workflowRequestInfo.setCanView(true);// 显示
        workflowRequestInfo.setCanEdit(true);// 可编辑
        workflowRequestInfo.setRequestName("流程请求标题-webservice-test");// 请求标题
        workflowRequestInfo.setRequestLevel("0");// 请求重要级别
        workflowRequestInfo.setCreatorId("115");

        WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();// 工作流信息
        workflowBaseInfo.setWorkflowId("14679");// 流程ID
        workflowBaseInfo.setWorkflowName("webservice-test");// 流程名称
//		workflowBaseInfo.setWorkflowTypeId("1951");//流程类型id
        workflowBaseInfo.setWorkflowTypeName("webservice-test");// 流程类型名称
        // workflowRequestInfo.setWorkflowBaseInfo(workflowBaseInfo);//工作流信息


        /****************main table start*************/
        WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();// 主表
        WorkflowRequestTableRecord[] workflowRequestTableRecord = new WorkflowRequestTableRecord[1];// 主表字段只有一条记录
        WorkflowRequestTableField[] WorkflowRequestTableField = new WorkflowRequestTableField[6];// 主的4个字段

        WorkflowRequestTableField[0] = new WorkflowRequestTableField();
        WorkflowRequestTableField[0].setFieldName("name");// 姓名
        WorkflowRequestTableField[0].setFieldValue("115");// 被留言人字段的值，111为被留言人id
        WorkflowRequestTableField[0].setView(true);// 字段是否可见
        WorkflowRequestTableField[0].setEdit(true);// 字段是否可编辑

        WorkflowRequestTableField[1] = new WorkflowRequestTableField();
        WorkflowRequestTableField[1].setFieldName("department");// 部门
        WorkflowRequestTableField[1].setFieldValue("3");
        WorkflowRequestTableField[1].setView(true);
        WorkflowRequestTableField[1].setEdit(true);

        WorkflowRequestTableField[2] = new WorkflowRequestTableField();
        WorkflowRequestTableField[2].setFieldName("amt");// 部门
        WorkflowRequestTableField[2].setFieldValue("23.00");
        WorkflowRequestTableField[2].setView(true);
        WorkflowRequestTableField[2].setEdit(true);

        WorkflowRequestTableField[3] = new WorkflowRequestTableField();
        WorkflowRequestTableField[3].setFieldName("srm");// 文档
        WorkflowRequestTableField[3].setFieldValue("");
        WorkflowRequestTableField[3].setView(true);
        WorkflowRequestTableField[3].setEdit(true);

        WorkflowRequestTableField[4] = new WorkflowRequestTableField();
        WorkflowRequestTableField[4].setFieldName("textare");// 备注
        WorkflowRequestTableField[4].setFieldValue("测试");
        WorkflowRequestTableField[4].setView(true);
        WorkflowRequestTableField[4].setEdit(true);

        WorkflowRequestTableField[5] = new WorkflowRequestTableField();
        WorkflowRequestTableField[5].setFieldName("fujian");// 附件
        WorkflowRequestTableField[5].setFieldType("http:baidu_sylogo1.gif");// http:开头代表该字段为附件字段		wrti[5].setFieldValue("http://www.baidu.com/img/baidu_sylogo1.gif");//附件地址
        WorkflowRequestTableField[5].setView(true);
        WorkflowRequestTableField[5].setEdit(true);

        workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[0].setWorkflowRequestTableFields(WorkflowRequestTableField);
        workflowMainTableInfo.setRequestRecords(workflowRequestTableRecord);

        workflowRequestInfo.setWorkflowMainTableInfo(workflowMainTableInfo);
        /****************main table end*************/


        /****************detail table start*************/
        WorkflowDetailTableInfo[] workflowDetailTableInfo = new WorkflowDetailTableInfo[2];// 两个明细表
        /**********第一张明细表开始**********/
        workflowRequestTableRecord = new WorkflowRequestTableRecord[2];// 两行数据（两条记录）
        WorkflowRequestTableField = new WorkflowRequestTableField[2];// 每行2个字段
        /****第一行开始****/
        WorkflowRequestTableField[0] = new WorkflowRequestTableField();
        WorkflowRequestTableField[0].setFieldName("type");// select框
        WorkflowRequestTableField[0].setFieldValue("测试1");
        WorkflowRequestTableField[0].setView(true);
        WorkflowRequestTableField[0].setEdit(true);

        WorkflowRequestTableField[1] = new WorkflowRequestTableField();
        WorkflowRequestTableField[1].setFieldName("checking");// check框
        WorkflowRequestTableField[1].setFieldValue("true");
        WorkflowRequestTableField[1].setView(true);
        WorkflowRequestTableField[1].setEdit(true);
        workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[0].setWorkflowRequestTableFields(WorkflowRequestTableField);
        /****第一行结束****/

        /****第二行开始****/
        WorkflowRequestTableField[0] = new WorkflowRequestTableField();
        WorkflowRequestTableField[0].setFieldName("type");// select框
        WorkflowRequestTableField[0].setFieldValue("测试2");
        WorkflowRequestTableField[0].setView(true);
        WorkflowRequestTableField[0].setEdit(true);

        WorkflowRequestTableField[1] = new WorkflowRequestTableField();
        WorkflowRequestTableField[1].setFieldName("checking");// check框
        WorkflowRequestTableField[1].setFieldValue("false");
        WorkflowRequestTableField[1].setView(true);
        WorkflowRequestTableField[1].setEdit(true);
        workflowRequestTableRecord[1] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[1].setWorkflowRequestTableFields(WorkflowRequestTableField);
        /****第二行结束****/
        workflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
        workflowDetailTableInfo[0].setWorkflowRequestTableRecords(workflowRequestTableRecord);
        /**********第一张明细表结束**********/

        /**********第二张明细表开始**********/
        workflowRequestTableRecord = new WorkflowRequestTableRecord[1];// 一行数据（一条记录）
        WorkflowRequestTableField = new WorkflowRequestTableField[3];// 每行3个字段
        /****第一行开始****/
        WorkflowRequestTableField[0] = new WorkflowRequestTableField();
        WorkflowRequestTableField[0].setFieldName("test1");// 测试
        WorkflowRequestTableField[0].setFieldValue("test1");
        WorkflowRequestTableField[0].setView(true);
        WorkflowRequestTableField[0].setEdit(true);

        WorkflowRequestTableField[1] = new WorkflowRequestTableField();
        WorkflowRequestTableField[1].setFieldName("test2");// 测试
        WorkflowRequestTableField[1].setFieldValue("test1");
        WorkflowRequestTableField[1].setView(true);
        WorkflowRequestTableField[1].setEdit(true);
        workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[0].setWorkflowRequestTableFields(WorkflowRequestTableField);

        WorkflowRequestTableField[1] = new WorkflowRequestTableField();
        WorkflowRequestTableField[1].setFieldName("test3");// 测试
        WorkflowRequestTableField[1].setFieldValue("test1");
        WorkflowRequestTableField[1].setView(true);
        WorkflowRequestTableField[1].setEdit(true);
        workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[0].setWorkflowRequestTableFields(WorkflowRequestTableField);
        /****第一行结束****/

        workflowDetailTableInfo[1] = new WorkflowDetailTableInfo();
        workflowDetailTableInfo[1].setWorkflowRequestTableRecords(workflowRequestTableRecord);
        /**********第二张明细表结束**********/

        workflowRequestInfo.setWorkflowDetailTableInfos(workflowDetailTableInfo);
        /****************detail table end*************/

//		String response = ClientUtil.getClient().submitWorkflowRequest(workflowRequestInfo, requestid, userid, type, remark);

//		if(!"".equals(response)&&response!=null)
//		System.out.println("返回结果："+response);
//		else
//		System.out.println("返回结果为空");
//             String response = ClientUtil.getClient().doCreateWorkflowRequest(workflowRequestInfo, userid);
        System.out.println("requestid:" + JSONUtil.toJsonStr(workflowRequestInfo));
        System.out.println("requestid:" + JSONUtil.toJsonStr(workflowRequestInfo));
        // System.out.println("requestid:" + response);
    }

    public static BigDecimal calculateAmount(String airTicketTotalIncludeTaxStr, String airportFeeStr) {
        BigDecimal airTicketTotalIncludeTax = parseBigDecimal(airTicketTotalIncludeTaxStr);
        // 判断含税金额转换为 BigDecimal 后是否为 0
        if (airTicketTotalIncludeTax.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal airportFee = parseBigDecimal(airportFeeStr);

        // 计算 (airTicketTotalIncludeTax - airportFee) / 1.09 * 0.09
        BigDecimal divisor = new BigDecimal("1.09");
        BigDecimal multiplier = new BigDecimal("0.09");
        return airTicketTotalIncludeTax.subtract(airportFee).divide(divisor, 10, BigDecimal.ROUND_HALF_UP).multiply(multiplier);
    }

    public static BigDecimal parseBigDecimal(String valueStr) {
        try {
            if (valueStr != null) {
                return new BigDecimal(valueStr);
            }
        } catch (NumberFormatException e) {
            // 处理转换失败的情况
            // 这里可以添加日志记录，例如：log.writeLog("金额转换失败: " + e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据项目ID列表查询对应的公司ID列表
     *
     * @param projectIds 项目ID列表
     * @return 公司ID列表
     */
    public static List<String> queryCompanyIdsByProjectIds(List<Integer> projectIds) {
        List<String> companyIds = new ArrayList<>();
        BaseBean log = new BaseBean();

        if (projectIds == null || projectIds.isEmpty()) {
            log.writeLog("项目ID列表为空，无法查询公司ID");
            return companyIds;
        }

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            // 获取数据库连接
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            conn = DriverManager.getConnection("*********************************************************", "lijunhong", "Cyitce@0106");

            // 构建SQL语句，使用IN子句
            StringBuilder sql = new StringBuilder();
            sql.append("select DISTINCT company_id from uf_fbt_project_info where id in (");

            // 添加参数占位符
            for (int i = 0; i < projectIds.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
            }
            sql.append(")");

            log.writeLog("执行SQL: " + sql.toString());
            log.writeLog("参数: " + projectIds);

            // 准备语句
            ps = conn.prepareStatement(sql.toString());

            // 设置参数
            for (int i = 0; i < projectIds.size(); i++) {
                ps.setInt(i + 1, projectIds.get(i));
            }

            // 执行查询
            rs = ps.executeQuery();

            // 处理结果
            while (rs.next()) {
                String companyId = rs.getString("company_id");
                if (companyId != null && !companyId.trim().isEmpty()) {
                    companyIds.add(companyId);
                }
            }

            log.writeLog("查询到的公司ID: " + companyIds);

        } catch (Exception e) {
            log.writeLog("查询公司ID时发生异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                log.writeLog("关闭数据库资源时发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return companyIds;
    }

    /**
     * 根据项目ID数组查询对应的公司ID列表
     *
     * @param projectIds 项目ID数组
     * @return 公司ID列表
     */
    public static List<String> queryCompanyIdsByProjectIds(Integer... projectIds) {
        return queryCompanyIdsByProjectIds(Arrays.asList(projectIds));
    }

    /**
     * 根据员工ID查询部门ID
     *
     * @param employeeId 员工ID
     * @return 部门ID，如果未找到则返回空字符串
     */
    public static String queryDepartmentIdByEmployeeId(int employeeId) {
        String departmentId = "";
        BaseBean log = new BaseBean();
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            // 获取数据库连接
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            conn = DriverManager.getConnection("*********************************************************", "lijunhong", "Cyitce@0106");
            
            // 构建SQL语句
            String sql = "select departmentid from hrmresource where id=?";
            
            log.writeLog("执行SQL: " + sql);
            log.writeLog("参数: " + employeeId);
            
            // 准备语句
            ps = conn.prepareStatement(sql);
            
            // 设置参数
            ps.setInt(1, employeeId);
            
            // 执行查询
            rs = ps.executeQuery();
            
            // 处理结果
            if (rs.next()) {
                departmentId = rs.getString("departmentid");
                log.writeLog("查询到的部门ID: " + departmentId);
            } else {
                log.writeLog("未找到员工ID为" + employeeId + "的部门信息");
            }
            
        } catch (Exception e) {
            log.writeLog("查询部门ID时发生异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                log.writeLog("关闭数据库资源时发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        return departmentId;
    }
}




