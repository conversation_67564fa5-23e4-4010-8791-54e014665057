package weaver.interfaces.workflow.action.cyitce.job;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.Calendar;

public class BKMonthNumberJob extends BaseCronJob {
    BaseBean log = new BaseBean();

    public BKMonthNumberJob() {
    }

    public void execute() {
        Calendar cal = Calendar.getInstance();
        int month = cal.get(Calendar.MONTH);
        int bkNumber = 3;//每月补卡次数
        boolean ret=true;

        if(month==0){
            month=12;
        }

        String sql;
        RecordSet rs;
        RecordSet rs2;
        try {
            log.writeLog("人员补卡次数开始初始化...");
            rs = new RecordSet();
            rs2 = new RecordSet();

            sql = "select id from HrmResource where subcompanyid1=11 and id not in (SELECT ry FROM uf_rysybkcs_jt)";
            rs.execute(sql);
            while (rs.next()){
                rs2.execute("insert uf_rysybkcs_jt(ry,sybkcs,djcs,yf) values("+rs.getInt(1)+","+bkNumber+",0,"+month+")");
            }

            sql = "update uf_rysybkcs_jt set sybkcs=?,djcs=?,yf=?";
            log.writeLog(sql);
            ret = rs.executeUpdate(sql,new Object[]{bkNumber,0,month});
            if(ret){
                log.writeLog("...完成初始化");
            }
        }catch (Exception e){
            log.writeLog(e);
        }
    }
}
