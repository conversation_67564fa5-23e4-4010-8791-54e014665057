-- 更新uf_fbt_project_info表结构，将长字段名修改为缩写形式
-- 此脚本适用于SQL Server数据库

-- 1. 添加新的缩写字段
ALTER TABLE uf_fbt_project_info ADD atdit DECIMAL(18,2) NULL;  -- air_ticket_domestic_include_tax
ALTER TABLE uf_fbt_project_info ADD atiit DECIMAL(18,2) NULL;  -- air_ticket_international_include_tax
ALTER TABLE uf_fbt_project_info ADD cpit DECIMAL(18,2) NULL;   -- corporate_payment_include_tax
ALTER TABLE uf_fbt_project_info ADD vasit DECIMAL(18,2) NULL;  -- value_added_service_include_tax
ALTER TABLE uf_fbt_project_info ADD hoit DECIMAL(18,2) NULL;   -- hotel_overseas_include_tax

ALTER TABLE uf_fbt_project_info ADD atdet DECIMAL(18,2) NULL;  -- air_ticket_domestic_exclude_tax
ALTER TABLE uf_fbt_project_info ADD atiet DECIMAL(18,2) NULL;  -- air_ticket_international_exclude_tax
ALTER TABLE uf_fbt_project_info ADD cpet DECIMAL(18,2) NULL;   -- corporate_payment_exclude_tax
ALTER TABLE uf_fbt_project_info ADD vaset DECIMAL(18,2) NULL;  -- value_added_service_exclude_tax

ALTER TABLE uf_fbt_project_info ADD afsit DECIMAL(18,2) NULL;  -- accommodation_fee_special_include_tax
ALTER TABLE uf_fbt_project_info ADD afset DECIMAL(18,2) NULL;  -- accommodation_fee_special_exclude_tax
ALTER TABLE uf_fbt_project_info ADD hosit DECIMAL(18,2) NULL;  -- hotel_overseas_special_include_tax
ALTER TABLE uf_fbt_project_info ADD hoset DECIMAL(18,2) NULL;  -- hotel_overseas_special_exclude_tax

ALTER TABLE uf_fbt_project_info ADD afoit DECIMAL(18,2) NULL;  -- accommodation_fee_ordinary_include_tax
ALTER TABLE uf_fbt_project_info ADD afoet DECIMAL(18,2) NULL;  -- accommodation_fee_ordinary_exclude_tax
ALTER TABLE uf_fbt_project_info ADD hooit DECIMAL(18,2) NULL;  -- hotel_overseas_ordinary_include_tax
ALTER TABLE uf_fbt_project_info ADD hooet DECIMAL(18,2) NULL;  -- hotel_overseas_ordinary_exclude_tax

ALTER TABLE uf_fbt_project_info ADD tafit DECIMAL(18,2) NULL;  -- total_accommodation_fee_include_tax
ALTER TABLE uf_fbt_project_info ADD tafet DECIMAL(18,2) NULL;  -- total_accommodation_fee_exclude_tax
ALTER TABLE uf_fbt_project_info ADD tafs DECIMAL(18,2) NULL;   -- total_accommodation_fee_special
ALTER TABLE uf_fbt_project_info ADD tafsit DECIMAL(18,2) NULL; -- total_accommodation_fee_special_include_tax
ALTER TABLE uf_fbt_project_info ADD tafset DECIMAL(18,2) NULL; -- total_accommodation_fee_special_exclude_tax
ALTER TABLE uf_fbt_project_info ADD tafo DECIMAL(18,2) NULL;   -- total_accommodation_fee_ordinary
ALTER TABLE uf_fbt_project_info ADD tafoit DECIMAL(18,2) NULL; -- total_accommodation_fee_ordinary_include_tax
ALTER TABLE uf_fbt_project_info ADD tafoet DECIMAL(18,2) NULL; -- total_accommodation_fee_ordinary_exclude_tax

-- 2. 迁移现有数据到新字段
UPDATE uf_fbt_project_info SET 
  atdit = air_ticket_domestic_include_tax,
  atiit = air_ticket_international_include_tax,
  cpit = corporate_payment_include_tax,
  vasit = value_added_service_include_tax,
  hoit = hotel_overseas_include_tax,
  
  atdet = air_ticket_domestic_exclude_tax,
  atiet = air_ticket_international_exclude_tax,
  cpet = corporate_payment_exclude_tax,
  vaset = value_added_service_exclude_tax,
  
  afsit = accommodation_fee_special_include_tax,
  afset = accommodation_fee_special_exclude_tax,
  hosit = hotel_overseas_special_include_tax,
  hoset = hotel_overseas_special_exclude_tax,
  
  afoit = accommodation_fee_ordinary_include_tax,
  afoet = accommodation_fee_ordinary_exclude_tax,
  hooit = hotel_overseas_ordinary_include_tax,
  hooet = hotel_overseas_ordinary_exclude_tax,
  
  tafit = total_accommodation_fee_include_tax,
  tafet = total_accommodation_fee_exclude_tax,
  tafs = total_accommodation_fee_special,
  tafsit = total_accommodation_fee_special_include_tax,
  tafset = total_accommodation_fee_special_exclude_tax,
  tafo = total_accommodation_fee_ordinary,
  tafoit = total_accommodation_fee_ordinary_include_tax,
  tafoet = total_accommodation_fee_ordinary_exclude_tax;

-- 3. 删除原有长字段（数据已迁移到缩写字段）
ALTER TABLE uf_fbt_project_info DROP COLUMN air_ticket_domestic_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN air_ticket_international_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN corporate_payment_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN value_added_service_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN hotel_overseas_include_tax;

ALTER TABLE uf_fbt_project_info DROP COLUMN air_ticket_domestic_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN air_ticket_international_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN corporate_payment_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN value_added_service_exclude_tax;

ALTER TABLE uf_fbt_project_info DROP COLUMN accommodation_fee_special_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN accommodation_fee_special_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN hotel_overseas_special_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN hotel_overseas_special_exclude_tax;

ALTER TABLE uf_fbt_project_info DROP COLUMN accommodation_fee_ordinary_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN accommodation_fee_ordinary_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN hotel_overseas_ordinary_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN hotel_overseas_ordinary_exclude_tax;

ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_special;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_special_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_special_exclude_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_ordinary;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_ordinary_include_tax;
ALTER TABLE uf_fbt_project_info DROP COLUMN total_accommodation_fee_ordinary_exclude_tax; 