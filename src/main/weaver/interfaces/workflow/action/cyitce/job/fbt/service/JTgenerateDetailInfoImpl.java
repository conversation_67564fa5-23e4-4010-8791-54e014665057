package weaver.interfaces.workflow.action.cyitce.job.fbt.service;

import org.springframework.util.StringUtils;
import weaver.general.BaseBean;
import weaver.mobile.webservices.workflow.WorkflowDetailTableInfo;
import weaver.mobile.webservices.workflow.WorkflowRequestInfo;
import weaver.mobile.webservices.workflow.WorkflowServiceImpl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

import static weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYgenerateDetailInfoImpl.convertToProjectDetailInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:53
 * @describe 集团公司报账
 */
public class JTgenerateDetailInfoImpl implements generateDetailInfo {
    BaseBean log = new BaseBean();
    // 数据库连接参数
    private static final String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
    private static final String URL = "*********************************************************";
    private static final String USERNAME = "lijunhong";
    private static final String PASSWORD = "Cyitce@0106";

    /**
     * 执行SQL查询并返回结果Map
     *
     * @param sql    SQL查询语句
     * @param prefix 结果Map的key前缀
     * @param params 查询参数数组
     * @return 查询结果Map
     */
    private Map<String, String> executeQueryWithParams(String sql, String prefix, Object... params) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, String> resultMap = new HashMap<>();

        try {
            // 获取数据库连接
            conn = getConnection();
            if (conn == null) {
                log.writeLog("获取数据库连接失败");
                return resultMap;
            }

            // 准备查询语句
            ps = conn.prepareStatement(sql);

            // 设置参数
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    ps.setObject(i + 1, params[i]);
                }
            }

            // 执行查询
            rs = ps.executeQuery();

            // 处理结果集
            if (rs.next()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // 获取所有列的值，添加前缀
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    String columnValue = rs.getString(i);
                    // 使用前缀区分不同查询的结果
                    resultMap.put(prefix + columnName, columnValue != null ? columnValue : "");
                }
                log.writeLog("查询成功，结果已存入map，前缀为: " + prefix);
            } else {
                log.writeLog("未找到匹配记录");
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.writeLog("查询出错: " + e.getMessage());
        } finally {
            // 关闭资源
            closeResources(rs, ps, conn);
        }

        return resultMap;
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     */
    private Connection getConnection() {
        try {
            // 加载驱动
            Class.forName(DRIVER);
            // 建立连接
            return DriverManager.getConnection(URL, USERNAME, PASSWORD);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.writeLog("数据库驱动加载失败: " + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            log.writeLog("数据库连接失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 关闭数据库资源
     *
     * @param rs   结果集
     * @param ps   预编译语句
     * @param conn 数据库连接
     */
    private void closeResources(ResultSet rs, PreparedStatement ps, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    public STYProjectDetailInfo processProjectDetail(Map<String, String> map) {
        Map<String, String> finalResult = new HashMap<>();

        // 第一个查询：根据code参数查询uf_styjsejxmlx表
        String firstSql = "select ejxmbh,yjbm,ejbm,sjbm,yjxmmc,yjxmbh from uf_styjsejxmlx where ejxmbh=?";
        String ejxmbh = map.get("project_code");

        if (ejxmbh == null || ejxmbh.isEmpty()) {
            log.writeLog("参数code为空，无法执行第一个查询");
            return null;
        }

        Map<String, String> firstResult = executeQueryWithParams(firstSql, "1_", ejxmbh);
        finalResult.putAll(firstResult);

        // 打印第一个查询结果
        log.writeLog("第一个查询结果:");
        for (Map.Entry<String, String> entry : firstResult.entrySet()) {
            log.writeLog(entry.getKey() + ": " + entry.getValue());
        }

        // 第二个查询：使用第一个查询结果中的yjxmmc作为id参数查询uf_styjs_xmlxsq表
        String yjxmmc = firstResult.get("1_yjxmmc");
        if (yjxmmc == null || yjxmmc.isEmpty()) {
            log.writeLog("第一个查询未返回yjxmmc字段值，无法执行第二个查询");
            return null;
        }

        String secondSql = "select sqrbm,sfsggxm,xmlx from uf_styjs_xmlxsq where id=?";
        Map<String, String> secondResult = executeQueryWithParams(secondSql, "2_", yjxmmc);
        finalResult.putAll(secondResult);

        // 打印第二个查询结果
        log.writeLog("第二个查询结果:");
        for (Map.Entry<String, String> entry : secondResult.entrySet()) {
            log.writeLog(entry.getKey() + ": " + entry.getValue());
        }

        // 第三个查询：使用第二个查询结果中的sqrbm作为sqbm参数查询view_bmjz_cw表
        String sqrbm = secondResult.get("2_sqrbm");
        if (sqrbm == null || sqrbm.isEmpty()) {
            log.writeLog("第二个查询未返回sqrbm字段值，无法执行第三个查询");
            return null;
        }

        String thirdSql = "select yjbm,ejbm,sjbm from view_bmjz_cw where sqbm=?";
        Map<String, String> thirdResult = executeQueryWithParams(thirdSql, "3_", sqrbm);
        finalResult.putAll(thirdResult);

        // 将查询结果转换为STYProjectDetailInfo对象
        STYProjectDetailInfo projectDetailInfo = convertToProjectDetailInfo(finalResult);

        // 打印项目详细信息
        log.writeLog("项目详细信息: " + projectDetailInfo);


        return projectDetailInfo;
    }

    @Override
    public List<STYProjectDetailInfo> generateDetailInfo(List<Map<String, String>> dataList) {
        List<STYProjectDetailInfo> resultList = new ArrayList<>();

        if (dataList == null || dataList.isEmpty()) {
            log.writeLog("输入数据列表为空，无法执行查询");
            return resultList;
        }

        // 遍历处理每个项目
        for (Map<String, String> map : dataList) {
            STYProjectDetailInfo projectDetail = processProjectDetail(map);
            if (projectDetail != null) {
                resultList.add(projectDetail);
            }
        }

        log.writeLog("共处理 " + resultList.size() + " 个项目详细信息");
        return resultList;
    }

    public void generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex) {
        String requestName = FlowUtil.generateExpenseTitle(userId);
        String userName = FlowUtil.userid(userId);
        Map<String, String> fieldMap = new HashMap<>();
        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());
        fieldMap.put("sqr", userName);// 申请人
        fieldMap.put("sqrq", currentDate);// 申请日期
        fieldMap.put("zdrq", currentDate);// 申请日期
        fieldMap.put("zdr", userName);// 申请日期
        fieldMap.put("gszt", gszt);// 公司主体
        String deptId = FlowUtil.queryDepartmentIdByEmployeeId(Integer.parseInt(userId));
        fieldMap.put("szbm", StringUtils.hasText(deptId) ? deptId : "");// 申请部门
        Map<String, Boolean[]> viewEditMap = new HashMap<>();
        viewEditMap.put("gszt", new Boolean[]{true, true});
        List<Map<String, String>> detailTableData = new ArrayList<>();

        // 遍历项目列表，将每个项目的属性添加到明细表中
        for (STYProjectDetailInfo project : list) {
            Map<String, String> rowData = new HashMap<>();

            // 添加项目基本信息
            rowData.put("stejxmbh", project.getEjxmbh());  // 二级项目编号
            rowData.put("stejxmmc", project.getId());  // 二级项目名称
            rowData.put("styjxmmc", project.getYjxmmc());  // 一级项目名称
            rowData.put("styjxmbh", project.getYjxmbh());  // 一级项目编号

            // 添加部门信息
            rowData.put("yjbm", project.getYjbm());      // 一级部门
            rowData.put("ejbm", project.getEjbm());      // 二级部门
            rowData.put("sjbm", project.getSjbm());      // 三级部门

            // 添加财务部门信息
            rowData.put("cwyjbm", project.getCwYjbm());  // 财务一级部门
            rowData.put("cwejbm", project.getCwEjbm());  // 财务二级部门
            rowData.put("cwsjbm", project.getCwSjbm());  // 财务三级部门

            // 添加项目类型信息
            rowData.put("xmlx", project.getXmlx());      // 项目类型
            rowData.put("xmlxDesc", project.getXmlxDesc()); // 项目类型描述

            // 添加其他信息
            rowData.put("fycdbm", project.getSqrbm());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            rowData.put("sfsggxmDesc", project.getSfsggxmDesc()); // 是否是公共项目描述

            // 添加到明细表数据列表
            detailTableData.add(rowData);
        }
        Map<String, Boolean[]> detailViewEditMap = new HashMap<>();
        // 创建工作流
        WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
                fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        String requestId = new WorkflowServiceImpl().doCreateWorkflowRequest(workflowRequestInfo, Integer.parseInt(userId), "", "");
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("code", "STYHS-EJXM20240029");

        // new JTgenerateDetailInfoImpl().generateDetailInfo(map);
    }

}
