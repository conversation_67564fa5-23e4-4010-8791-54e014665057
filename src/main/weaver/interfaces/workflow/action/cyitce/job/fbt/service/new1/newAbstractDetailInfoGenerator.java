package weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1;

import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYProjectDetailInfo;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.generateDetailInfo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 11:00
 * @describe 项目详情生成器抽象基类，提取各实现类的公共逻辑
 */
public abstract class newAbstractDetailInfoGenerator implements generateDetailInfo {

    protected BaseBean log = new BaseBean();

    // 共用的数据库连接参数
    private static final String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
    private static final String URL = "*********************************************************";
    private static final String USERNAME = "lijunhong";
    private static final String PASSWORD = "Cyitce@0106";

    /**
     * 模板方法：处理详细信息生成的主流程
     *
     * @param dataList 包含查询参数的Map列表
     * @return 项目详细信息列表
     */
    @Override
    public List<STYProjectDetailInfo> generateDetailInfo(List<Map<String, String>> dataList) {
        List<STYProjectDetailInfo> resultList = new ArrayList<>();

        if (dataList == null || dataList.isEmpty()) {
            log.writeLog("输入数据列表为空，无法执行查询");
            return resultList;
        }

        // 遍历处理每个项目
        for (Map<String, String> map : dataList) {
            STYProjectDetailInfo projectDetail = processProjectDetail(map);
            if (projectDetail != null) {
                log.writeLog("生成的数据: " + projectDetail.toString());
                resultList.add(projectDetail);
            }
        }

        log.writeLog("共处理 " + resultList.size() + " 个项目详细信息");
        return resultList;
    }

    /**
     * 处理单个项目详细信息的模板方法
     *
     * @param map 包含查询参数的Map
     * @return 项目详细信息对象，如果查询失败则返回null
     */
    protected STYProjectDetailInfo processProjectDetail(Map<String, String> map) {
        Map<String, String> finalResult = new HashMap<>();

        // 第一个查询
        String firstSql = getFirstSql();
        String firstParam = getFirstQueryParam(map);

        if (firstParam == null || firstParam.isEmpty()) {
            log.writeLog("第一个查询参数为空，无法执行查询");
            return null;
        }

        Map<String, String> firstResult = executeQueryWithParams(firstSql, "1_", firstParam);
        finalResult.putAll(firstResult);

        if (firstResult.isEmpty()) {
            log.writeLog("第一个查询未返回结果，无法继续执行");
            return null;
        }

        // 第二个查询
        String secondSql = getSecondSql();
        String secondParam = getSecondQueryParam(finalResult);

        if (secondParam == null || secondParam.isEmpty()) {
            log.writeLog("第二个查询参数为空，无法执行查询");
            return null;
        }

        Map<String, String> secondResult = executeQueryWithParams(secondSql, "2_", secondParam);
        finalResult.putAll(secondResult);

        if (secondResult.isEmpty()) {
            log.writeLog("第二个查询未返回结果，无法继续执行");
            return null;
        }

        // 第三个查询
        String thirdSql = getThirdSql();
        String thirdParam = getThirdQueryParam(finalResult);

        if (thirdParam == null || thirdParam.isEmpty()) {
            log.writeLog("第三个查询参数为空，无法执行查询");
            return null;
        }

        Map<String, String> thirdResult = executeQueryWithParams(thirdSql, "3_", thirdParam);
        finalResult.putAll(thirdResult);

        // 将查询结果转换为STYProjectDetailInfo对象
        STYProjectDetailInfo projectDetailInfo = convertToProjectDetailInfo(finalResult);
        projectDetailInfo.setMap(map);
        log.writeLog("项目详细信息: " + projectDetailInfo);
        return projectDetailInfo;
    }

    /**
     * 执行SQL查询并返回结果Map
     *
     * @param sql    SQL查询语句
     * @param prefix 结果Map的key前缀
     * @param params 查询参数数组
     * @return 查询结果Map
     */
    protected Map<String, String> executeQueryWithParams(String sql, String prefix, Object... params) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, String> resultMap = new HashMap<>();

        try {
            conn = getConnection();
            if (conn == null) {
                log.writeLog("获取数据库连接失败");
                return resultMap;
            }

            ps = conn.prepareStatement(sql);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    ps.setObject(i + 1, params[i]);
                }
            }

            rs = ps.executeQuery();
            if (rs.next()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    String columnValue = rs.getString(i);
                    resultMap.put(prefix + columnName, columnValue != null ? columnValue : "");
                }
                log.writeLog("查询成功，结果已存入map，前缀为: " + prefix);
            } else {
                log.writeLog("未找到匹配记录");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.writeLog("查询出错: " + e.getMessage());
        } finally {
            closeResources(rs, ps, conn);
        }
        return resultMap;
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     */
    public Connection getConnection() {
        try {
            Class.forName(DRIVER);
            return DriverManager.getConnection(URL, USERNAME, PASSWORD);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.writeLog("数据库驱动加载失败: " + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            log.writeLog("数据库连接失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 关闭数据库资源
     *
     * @param rs   结果集
     * @param ps   预编译语句
     * @param conn 数据库连接
     */
    protected void closeResources(ResultSet rs, PreparedStatement ps, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // 子类必须实现的抽象方法

    /**
     * 获取第一个SQL查询语句
     *
     * @return 第一个SQL查询语句
     */
    protected abstract String getFirstSql();

    /**
     * 获取第二个SQL查询语句
     *
     * @return 第二个SQL查询语句
     */
    protected abstract String getSecondSql();

    /**
     * 获取第三个SQL查询语句
     *
     * @return 第三个SQL查询语句
     */
    protected abstract String getThirdSql();

    /**
     * 从输入Map中获取第一个查询的参数
     *
     * @param map 输入参数Map
     * @return 第一个查询的参数
     */
    protected abstract String getFirstQueryParam(Map<String, String> map);

    /**
     * 从第一个查询结果中获取第二个查询的参数
     *
     * @param resultMap 包含第一个查询结果的Map
     * @return 第二个查询的参数
     */
    protected abstract String getSecondQueryParam(Map<String, String> resultMap);

    /**
     * 从第二个查询结果中获取第三个查询的参数
     *
     * @param resultMap 包含第二个查询结果的Map
     * @return 第三个查询的参数
     */
    protected abstract String getThirdQueryParam(Map<String, String> resultMap);

    /**
     * 将查询结果Map转换为STYProjectDetailInfo对象
     *
     * @param resultMap 查询结果Map
     * @return STYProjectDetailInfo对象
     */
    protected abstract STYProjectDetailInfo convertToProjectDetailInfo(Map<String, String> resultMap);

    public abstract String generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex);
} 