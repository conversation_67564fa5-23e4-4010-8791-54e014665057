package weaver.interfaces.workflow.action.cyitce.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.directwebremoting.dwrp.SimpleOutboundVariable;
import org.owasp.esapi.util.CollectionsUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.interfaces.workflow.action.cyitce.entity.beisen.Employee;
import weaver.interfaces.workflow.action.cyitce.entity.beisen.EmployeeInfo;
import weaver.interfaces.workflow.action.cyitce.entity.beisen.EmployeeResponse;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;

/**
 * 每天零时同步离职列表 (离职人员  是 状态为已离职 且 已超过最后工作日期)
 */
public class LZZMJob extends BaseCronJob {

    private static final String API_URL = "https://openapi.italent.cn/TenantBaseExternal/api/v5/Employee/GetByTimeWindow";
    private static final String DEPT_URL = "https://openapi.italent.cn/TenantBaseExternal/api/v5/Organization/GetByIds";
    private static final String POSITION_URL = "https://openapi.italent.cn/TenantBaseExternal/api/v5/JobPost/GetByOIds";

    private static final String AUTH_HEADER = "Bearer ";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public static Map<Long, Employee> mainMap = new HashMap();
    BaseBean log = new BaseBean();

    public LZZMJob() {
    }

    public void execute() {
        log.writeLog("LZZMJob----------------->  开始执行离职同步");
        // 定期同步 离职列表
        LocalDate currentDate = LocalDate.now();
        // 获取前一天的日期
        LocalDate previousDate = currentDate.minusDays(7);
        // 将前一天的日期与零点时间组合成 LocalDateTime 对象
        LocalDateTime previousDayStartTime = LocalDateTime.of(previousDate, LocalTime.MIDNIGHT);
        LocalDateTime startTime = LocalDateTime.of(2025, 4, 11, 0, 0, 0);
        // start(startTime);

        start(previousDayStartTime);
    }


    public static List<EmployeeResponse> retrieveEmployeeData(LocalDateTime startTime, String token) throws Exception {
        List<EmployeeResponse> allResponses = new ArrayList<>();
        LocalDateTime currentTime = LocalDateTime.now();
        // LocalDateTime currentTime = LocalDateTime.now();
        while (startTime.isBefore(currentTime)) {
            LocalDateTime stopTime = startTime.plusMonths(3).isBefore(currentTime) ? startTime.plusMonths(3) : currentTime;
            String scrollId = "";
            boolean isLastPage = false;
            List<Employee> allDataInTimeWindow = new ArrayList<>();
            while (!isLastPage) {
                JSONObject requestBody = createRequestBody(startTime, stopTime, scrollId);
                System.out.println(JSONUtil.toJsonStr(requestBody));
                EmployeeResponse response = sendRequest(requestBody, token);
                // 检查时间间隔，确保不超过10秒
                // if (allResponses.size() > 0) {
                //     LocalDateTime lastRequestTime = allResponses.get(allResponses.size() - 1).getResponseTime();
                //     Duration duration = Duration.between(lastRequestTime, LocalDateTime.now());
                //     if (duration.getSeconds() > 10) {
                //         Thread.sleep(1000); // 等待1秒
                //     }
                // }
                // response.setResponseTime(LocalDateTime.now());
                // allResponses.add(response);
                if (response.getData() != null && !response.getData().isEmpty()) {
                    allDataInTimeWindow.addAll(response.getData());
                    scrollId = response.getScrollId();
                } else {
                    isLastPage = true;
                }
            }
            // 处理当前时间窗内的重复数据
            allDataInTimeWindow = removeDuplicates(allDataInTimeWindow);

            // 这里可以添加对当前时间窗内数据的业务处理逻辑
            startTime = stopTime;
            // System.out.println("allResponses.size() = " + allResponses.size());
            // // 检查是否已经达到了当前时间，如果达到了则跳出循环
            // if (startTime.isAfter(currentTime) || startTime.isEqual(currentTime)) {
            //     System.out.println("aaaaa " + allResponses.size());
            //     break;
            // }
            EmployeeResponse employeeResponse = new EmployeeResponse();
            employeeResponse.setData(allDataInTimeWindow);
            allResponses.add(employeeResponse);

        }

        return allResponses;
    }

    private static JSONObject createRequestBody(LocalDateTime startTime, LocalDateTime stopTime, String scrollId) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("empStatus", new Integer[]{8});
        requestBody.put("employType", new Integer[]{0});
        requestBody.put("serviceType", new Integer[]{0});
        requestBody.put("approvalStatuses", new Integer[]{4});
        requestBody.put("timeWindowQueryType", 1);
        requestBody.put("startTime", startTime.format(DATE_TIME_FORMATTER).concat("Z"));
        requestBody.put("stopTime", stopTime.format(DATE_TIME_FORMATTER).concat("Z"));
        requestBody.put("capacity", 300);
        requestBody.put("columns", null);
        requestBody.put("isWithDeleted", false);
        requestBody.put("withDisabled", true);

        JSONObject sort = new JSONObject();
        sort.put("lastWorkDate", 2);
        requestBody.put("sort", sort);

        requestBody.put("isGetLatestRecord", false);
        requestBody.put("scrollId", scrollId);

        return requestBody;
    }

    private static EmployeeResponse sendRequest(JSONObject requestBody, String token) throws Exception {
        URL url = new URL(API_URL);
        HttpURLConnection con = (HttpURLConnection) url.openConnection();

        con.setRequestMethod("POST");
        con.setRequestProperty("Content-Type", "application/json");
        con.setRequestProperty("Authorization", AUTH_HEADER + token);
        con.setDoOutput(true);

        try (OutputStream os = con.getOutputStream()) {
            byte[] input = requestBody.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        int responseCode = con.getResponseCode();
        System.out.println("Response Code : " + responseCode);

        try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            return JSONUtil.toBean(response.toString(), EmployeeResponse.class);

        }
    }

    private static List<Employee> removeDuplicates(List<Employee> data) {
        // Map<Long, Employee> uniqueEmployeeMap = new HashMap<>();
        for (Employee employee : data) {
            EmployeeInfo employeeInfo = employee.getEmployeeInfo();
            if (employeeInfo != null) {
                Long userId = employeeInfo.getUserID();
                mainMap.put(userId, employee);
            }
        }
        // System.out.println("123   "+uniqueEmployeeMap.values().size());
        // return new ArrayList<>(uniqueEmployeeMap.values());
        return new ArrayList<>();
    }

    public static JSONObject getOrganizationByIds(List<Long> oIds, boolean isWithDeleted, String token, String url) {
        if (CollectionUtils.isEmpty(oIds)) {
            return new JSONObject();
        }
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("oIds", oIds);
        requestBody.put("isWithDeleted", isWithDeleted);

        // 发送 POST 请求
        HttpResponse response = HttpRequest.post(url)
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", "application/json")
                .body(requestBody.toString())
                .execute();

        // 处理响应
        if (response.isOk()) {
            String body = response.body();
            return JSONUtil.parseObj(body);
        } else {
            System.err.println("请求失败，状态码: " + response.getStatus());
            return null;
        }
    }

    public static JSONObject getOrganizationByIds1(List<String> oIds, boolean isWithDeleted, String token, String url) {
        if (CollectionUtils.isEmpty(oIds)) {
            return new JSONObject();
        }
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("oIds", oIds);
        requestBody.put("isWithDeleted", isWithDeleted);
        System.out.println(" = " + JSONUtil.toJsonStr(requestBody));
        // 发送 POST 请求
        HttpResponse response = HttpRequest.post(url)
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", "application/json")
                .body(requestBody.toString())
                .execute();

        // 处理响应
        if (response.isOk()) {
            String body = response.body();
            return JSONUtil.parseObj(body);
        } else {
            System.err.println("请求失败，状态码: " + response.getStatus());
            return null;
        }
    }

    public static JSONObject getToken() throws Exception {
        String requestBody = "{\"grant_type\":\"client_credentials\",\"app_key\":\"B83EFCE7F9A24D6EA8EA17CD930664DE\",\"app_secret\":\"C4D7437DD6E7470D989807EF738EF6385F2B508972E64EFAA6C25CA845367FE5\"}";
        URL targetUrl = new URL("https://openapi.italent.cn/token");
        HttpURLConnection connection = (HttpURLConnection) targetUrl.openConnection();

        // 设置请求方法为POST
        connection.setRequestMethod("POST");
        // 设置请求头
        connection.setRequestProperty("Content-Type", "application/json");
        // 允许输出
        connection.setDoOutput(true);

        // 发送请求
        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
            wr.writeBytes(requestBody);
            wr.flush();
        }

        // 获取响应码
        int responseCode = connection.getResponseCode();

        // 读取响应内容
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            // 将响应内容存入JSONObject
            return new JSONObject(response.toString());
        } finally {
            // 断开连接
            connection.disconnect();
        }
    }

    public static List<Employee> sortEmployeesFromMap(Map<Long, Employee> employeeMap) {
        // 将 Map 中的 Employee 对象提取到 List 中
        List<Employee> employeeList = new ArrayList<>(employeeMap.values());

        // 创建自定义比较器
        Comparator<Employee> comparator = (e1, e2) -> {
            // 获取两个 Employee 对象的 lastWorkDate
            String dateStr1 = e1.getRecordInfo() != null ? e1.getRecordInfo().getLastWorkDate() : null;
            String dateStr2 = e2.getRecordInfo() != null ? e2.getRecordInfo().getLastWorkDate() : null;

            // 处理 lastWorkDate 为 null 的情况
            if (dateStr1 == null && dateStr2 == null) {
                return 0;
            }
            if (dateStr1 == null) {
                return 1;
            }
            if (dateStr2 == null) {
                return -1;
            }

            // 将字符串日期转换为 LocalDateTime 对象
            LocalDateTime dateTime1 = LocalDateTime.parse(dateStr1, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
            LocalDateTime dateTime2 = LocalDateTime.parse(dateStr2, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));

            // 升序排序
            return dateTime1.compareTo(dateTime2);
        };

        // 使用自定义的比较器对列表进行排序
        Collections.sort(employeeList, comparator);
        return employeeList;
    }

    public static List<Map<String, Object>> extractNameAndSecondLevelOrg(JSONObject responseJson, boolean flag) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (responseJson == null || !responseJson.containsKey("data")) {
            return result;
        }
        JSONArray dataArray = responseJson.getJSONArray("data");
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            // 提取部门 id
            Long departmentId = validateAndReturn(item.getLong("oId"));
            String name = item.getStr("name");
            Long secondLevelOrganization = validateAndReturn(item.getLong("secondLevelOrganization"));
            Long firstLevelOrganization = validateAndReturn(item.getLong("firstLevelOrganization"));
            Long oIdOrganization = validateAndReturn(item.getLong("oIdOrganization"));
            Long oIdDepartment = validateAndReturn(item.getLong("oIdDepartment"));
            String deptCode = StringUtils.hasText(item.getStr("code")) ? item.getStr("code") : "";

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("oId", departmentId);
            dataMap.put("name", name);
            dataMap.put("secondLevelOrganization", oIdDepartment);
            dataMap.put("oIdOrganization", oIdOrganization);
            dataMap.put("deptCode", deptCode);

            result.add(dataMap);
        }
        return result;
    }

    public static Map<Long, String> convertListToMap(List<Map<String, Object>> list) {
        Map<Long, String> resultMap = new HashMap<>();
        if (list == null) {
            return resultMap;
        }
        for (Map<String, Object> map : list) {
            if (map.containsKey("oId") && map.containsKey("name")) {
                Object idObj = map.get("oId");
                Object nameObj = map.get("name");
                if (idObj instanceof Long && nameObj instanceof String) {
                    Long id = (Long) idObj;
                    String name = (String) nameObj;
                    resultMap.put(id, name);
                }
            }
        }
        return resultMap;
    }

    public static Map<Long, String> convertListToMap1(List<Map<String, Object>> list) {
        Map<Long, String> resultMap = new HashMap<>();
        if (list == null) {
            return resultMap;
        }
        for (Map<String, Object> map : list) {
            if (map.containsKey("oId") && map.containsKey("deptCode")) {
                Object idObj = map.get("oId");
                Object nameObj = map.get("deptCode");
                if (idObj instanceof Long && nameObj instanceof String) {
                    Long id = (Long) idObj;
                    String name = (String) nameObj;
                    resultMap.put(id, name);
                }
            }
        }
        return resultMap;
    }

    public static Long validateAndReturn(Long input) {
        return input != null ? input : 0L;
    }

    // 使用keySet遍历Map的方法
    public static void traverseMapUsingKeySet(Map<Long, String> map) {
        System.out.println("使用keySet遍历Map:");
        for (Long key : map.keySet()) {
            String value = map.get(key);
            System.out.println("Key: " + key + ", Value: " + value);
        }
    }

    public static String getValueOrDefault(Map<Long, String> map, Long key) {
        if (map == null || key == null) {
            return "";
        }
        if (map.containsKey(key)) {
            String value = map.get(key);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    public void start(LocalDateTime startTime) {
        Connection con = null;
        log.writeLog("LZZMJob----------------->  执行start： ");
        PreparedStatement pstmt = null;
        Statement statement = null;
        try {
            String token = (String) getToken().get("access_token");
            // jobNumber
            retrieveEmployeeData(startTime, token);
            log.writeLog("LZZMJob----------------->  mainMap size： " + mainMap.values().size());
            List<Employee> employees = sortEmployeesFromMap(mainMap);
            log.writeLog("LZZMJob----------------->  employees size： " + employees.size());
            List<Long> l = new ArrayList<>();
            List<Long> l1 = new ArrayList<>();
            List<String> l2 = new ArrayList<>();
            List<Map<String, Object>> deptMap = new ArrayList<>();
            log.writeLog("LZZMJob----------------->  deptMap size： " + deptMap.size());
            List<Map<String, Object>> gsMap = new ArrayList<>();
            log.writeLog("LZZMJob----------------->  gsMap size： " + gsMap.size());
            List<Map<String, Object>> positionMap = new ArrayList<>();
            log.writeLog("LZZMJob----------------->  positionMap size： " + positionMap.size());
            for (int i = 0; i < employees.size(); i++) {
                Employee employee = employees.get(i);
                Long l3 = validateAndReturn(employee.getRecordInfo().getoIdDepartment());
                l.add(l3);
                l1.add(employee.getRecordInfo().getoIdOrganization());
                l2.add(employee.getRecordInfo().getoIdJobPost());
                // 每 290 个元素执行一次查询并清空列表
                if ((i + 1) % 290 == 0) {
                    JSONObject accessToken = getOrganizationByIds(l, false, token, DEPT_URL);
                    JSONObject accessToken1 = getOrganizationByIds(l1, false, token, DEPT_URL);
                    JSONObject accessToken2 = getOrganizationByIds1(l2, false, token, POSITION_URL);
                    deptMap.addAll(extractNameAndSecondLevelOrg(accessToken, false));
                    gsMap.addAll(extractNameAndSecondLevelOrg(accessToken1, false));
                    positionMap.addAll(extractNameAndSecondLevelOrg(accessToken2, false));
                    l.clear();
                    l1.clear();
                    l2.clear();
                }
            }

            // 处理剩余的元素
            if (!l.isEmpty()) {
                JSONObject accessToken = getOrganizationByIds(l, false, token, DEPT_URL);
                JSONObject accessToken1 = getOrganizationByIds(l1, false, token, DEPT_URL);
                JSONObject accessToken2 = getOrganizationByIds1(l2, false, token, POSITION_URL);
                deptMap.addAll(extractNameAndSecondLevelOrg(accessToken, false));
                gsMap.addAll(extractNameAndSecondLevelOrg(accessToken1, false));
                positionMap.addAll(extractNameAndSecondLevelOrg(accessToken2, false));
            }

            Map<Long, String> newDeptmap = convertListToMap(deptMap);
            Map<Long, String> newgsMap = convertListToMap(gsMap);
            Map<Long, String> newDeptCodeMap = convertListToMap1(deptMap);
            Map<Long, String> newpositionMap = convertListToMap(positionMap);
            traverseMapUsingKeySet(newDeptmap);
            traverseMapUsingKeySet(newgsMap);
            traverseMapUsingKeySet(newpositionMap);

            // 加载SQL Server JDBC驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            // 建立连接
            // String connectionUrl = "jdbc:sqlserver://localhost:1433;databaseName=test;user=sa;password=***********;";
            String connectionUrl1 = "*********************************************************************************************;";
            String connectionUrl = "*********************************************************************************************;";
            // 建立连接
            con = DriverManager.getConnection(connectionUrl);
            Connection con1 = DriverManager.getConnection(connectionUrl1);
            String sql = "INSERT INTO uf_lzlb (mz,gh,gs,dept,zw,zhgzr,bsid,mobile,xb,sfz,rzrq,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,MODEUUID,gsid,deptid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            pstmt = con.prepareStatement(sql);
            statement = con.createStatement();
            log.writeLog("LZZMJob----------------->  查询到所有的雇员 ： " + employees.size());
            for (Employee value : employees) {
                // 判断是否存在
                ResultSet resultSet = statement.executeQuery("select * from uf_lzlb where bsid='" + String.valueOf(value.getEmployeeInfo().getUserID()) + "'");
                if (resultSet.next()) {
                    log.writeLog("LZZMJob----------------->  员工已添加  ： " + value.getEmployeeInfo().getName());
                    continue;
                }
                pstmt.setString(1, value.getEmployeeInfo().getName());
                // String result = newpositionMap.getOrDefault(jobNumber, "");
                pstmt.setString(2, StringUtils.hasText(value.getRecordInfo().getJobNumber()) ? value.getRecordInfo().getJobNumber() : "");
                pstmt.setString(3, getValueOrDefault(newgsMap, value.getRecordInfo().getoIdOrganization()));
                pstmt.setString(4, getValueOrDefault(newDeptmap, value.getRecordInfo().getoIdDepartment()));
                String oIdJobPost = value.getRecordInfo().getoIdJobPost();
                Long jobPostId = StringUtils.hasText(oIdJobPost) ? Long.parseLong(oIdJobPost) : -1L;
                // String result = newpositionMap.getOrDefault(jobPostId, "");
                pstmt.setString(5, newpositionMap.getOrDefault(jobPostId, ""));
                pstmt.setString(6, convertDateFormat(value.getRecordInfo().getLastWorkDate()));
                pstmt.setString(7, String.valueOf(value.getEmployeeInfo().getUserID()));
                pstmt.setString(8, StringUtils.hasText(value.getEmployeeInfo().getMobilePhone()) ? value.getEmployeeInfo().getMobilePhone() : "");
                pstmt.setString(9, value.getEmployeeInfo().getGender() == 0 ? "男" : "女");
                pstmt.setString(10, String.valueOf(value.getEmployeeInfo().getiDNumber()));
                String rzrq = "";
                if (StringUtils.hasText(value.getEmployeeInfo().getMobilePhone())) {
                    ResultSet resultSet1 = statement.executeQuery("select companystartdate from hrmresource where id in  (select id from cus_fielddata where field49='" + value.getEmployeeInfo().getMobilePhone() + "' and scopeid=1)");
                    if (resultSet1.next()) {
                        rzrq = resultSet1.getString("companystartdate");
                    }
                }
                // ResultSet resultSet = statement.executeQuery("select companystartdate from hrmresource where id= (select id from cus_fielddata where field49='"+value.getEmployeeInfo().getMobilePhone()+"' and scopeid=1)");
                //
                // if (resultSet.next()) {
                //     rzrq = resultSet.getString("companystartdate");
                // }
                pstmt.setString(11, rzrq);
                pstmt.setInt(12, 1068); // 固定值
                pstmt.setInt(13, 3947); // 固定值
                pstmt.setInt(14, 0); // 固定值
                pstmt.setString(15, getCurrentDateTimeMap().get("day")); // 创建日期
                pstmt.setString(16, getCurrentDateTimeMap().get("time")); // 创建时间
                pstmt.setString(17, UUID.randomUUID().toString()); // 创建时间
                int gsid = 0;
                String org = getValueOrDefault(newgsMap, value.getRecordInfo().getoIdOrganization());
                if (StringUtils.hasText(org)) {
                    ResultSet resultSet2 = statement.executeQuery("select id from HrmSubCompany where  subcompanyname='" + org + "'");
                    if (resultSet2.next()) {
                        gsid = resultSet2.getInt("id");
                    }
                }
                pstmt.setInt(18, gsid); // 创建时间

                int deptid = 0;
                String dept = getValueOrDefault(newDeptCodeMap, value.getRecordInfo().getoIdDepartment());
                if (StringUtils.hasText(dept)) {
                    ResultSet resultSet3 = statement.executeQuery("select id from hrmdepartment where  departmentcode='" + dept + "'");
                    if (resultSet3.next()) {
                        deptid = resultSet3.getInt("id");
                    }
                }
                pstmt.setInt(19, deptid); // 创建时间
                // pstmt.setString(2, "value4");
                pstmt.addBatch();
            }
            con.setAutoCommit(false);
            pstmt.executeBatch(); // 此方法将执行所有添加到批处理的插入操作
            con.commit(); // 提交事务
            log.writeLog("LZZMJob----------------->  离职同步完成  time ： " + DateUtil.now());
        } catch (Exception e) {
            e.printStackTrace();
            if (con != null) {
                log.writeLog("LZZMJob----------------->  con离职同步失败  time ： " + DateUtil.now());
                try {
                    // 发生异常时回滚事务
                    con.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            try {
                if (pstmt != null) {
                    pstmt.close();
                }
                if (statement != null) {
                    statement.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    public static String convertDateFormat(String inputDate) {
        if (inputDate == null || inputDate.length() < 10) {
            return "";
        }
        // 截取日期部分
        String datePart = inputDate.substring(0, 10);
        // 将字符串解析为 LocalDate 对象
        LocalDate localDate = LocalDate.parse(datePart);
        // 定义目标日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        return localDate.format(formatter);
    }

    public static Map<String, String> getCurrentDateTimeMap() {
        // 创建一个 HashMap 用于存储日期和时间
        Map<String, String> dateTimeMap = new HashMap<>();

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义日期格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期并存储到 map 中
        dateTimeMap.put("day", currentDate.format(dateFormatter));

        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 定义时间格式
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        // 格式化时间并存储到 map 中
        dateTimeMap.put("time", currentTime.format(timeFormatter));

        return dateTimeMap;
    }

    public static void main1(String[] args) {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 10, 0, 0, 0);
        try {
            String token = (String) getToken().get("access_token");
            // jobNumber
            retrieveEmployeeData(startTime, token);
            System.out.println(mainMap.values().size());
            List<Employee> employees = sortEmployeesFromMap(mainMap);
            List<Long> l = new ArrayList<>();
            List<Long> l1 = new ArrayList<>();
            List<String> l2 = new ArrayList<>();
            List<Map<String, Object>> deptMap = new ArrayList<>();
            List<Map<String, Object>> gsMap = new ArrayList<>();
            List<Map<String, Object>> positionMap = new ArrayList<>();
            for (int i = 0; i < employees.size(); i++) {
                Employee employee = employees.get(i);
                Long l3 = validateAndReturn(employee.getRecordInfo().getoIdDepartment());
                if (l3.equals(3649914L) || l3.equals(3649872L)) {
                    System.out.println("l3 = " + JSONUtil.toJsonStr(employee));
                    l.add(l3);
                } else {
                    l.add(l3);
                }

                l1.add(employee.getRecordInfo().getoIdOrganization());
                if (StringUtils.hasText(employee.getRecordInfo().getoIdJobPost())) {
                    l2.add(employee.getRecordInfo().getoIdJobPost());
                } else {
                    System.out.println("employee = " + JSONUtil.toJsonStr(employee));
                }
                // 每 290 个元素执行一次查询并清空列表
                if ((i + 1) % 290 == 0) {
                    JSONObject accessToken = getOrganizationByIds(l, false, token, DEPT_URL);
                    JSONObject accessToken1 = getOrganizationByIds(l1, false, token, DEPT_URL);
                    JSONObject accessToken2 = getOrganizationByIds1(l2, false, token, POSITION_URL);
                    deptMap.addAll(extractNameAndSecondLevelOrg(accessToken, false));
                    gsMap.addAll(extractNameAndSecondLevelOrg(accessToken1, false));
                    positionMap.addAll(extractNameAndSecondLevelOrg(accessToken2, false));
                    System.out.println(" deptMap= " + deptMap.size());
                    l.clear();
                    l1.clear();
                    l2.clear();
                }
            }

            // 处理剩余的元素
            if (!l.isEmpty()) {
                JSONObject accessToken = getOrganizationByIds(l, false, token, DEPT_URL);
                JSONObject accessToken1 = getOrganizationByIds(l1, false, token, DEPT_URL);
                JSONObject accessToken2 = getOrganizationByIds1(l2, false, token, POSITION_URL);
                deptMap.addAll(extractNameAndSecondLevelOrg(accessToken, false));
                gsMap.addAll(extractNameAndSecondLevelOrg(accessToken1, false));
                positionMap.addAll(extractNameAndSecondLevelOrg(accessToken2, false));
                System.out.println(" deptMap= " + deptMap.size());
                System.out.println(" positionMap= " + deptMap.size());
            }

            Map<Long, String> newDeptmap = convertListToMap(deptMap);
            Map<Long, String> newDeptCodeMap = convertListToMap1(deptMap);
            Map<Long, String> newgsMap = convertListToMap(gsMap);
            Map<Long, String> newpositionMap = convertListToMap(positionMap);
            traverseMapUsingKeySet(newDeptmap);
            traverseMapUsingKeySet(newgsMap);
            traverseMapUsingKeySet(newpositionMap);

            // 加载SQL Server JDBC驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            // 建立连接
            // String connectionUrl = "jdbc:sqlserver://localhost:1433;databaseName=test;user=sa;password=***********;";
            String connectionUrl1 = "*********************************************************************************************;";
            String connectionUrl = "*********************************************************************************************;";
            // 建立连接
            Connection con = DriverManager.getConnection(connectionUrl);
            Connection con1 = DriverManager.getConnection(connectionUrl1);
            String sql = "INSERT INTO uf_lzlb (mz,gh,gs,dept,zw,zhgzr,bsid,mobile,xb,sfz,rzrq,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,MODEUUID,gsid,deptid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            PreparedStatement pstmt = con.prepareStatement(sql);

            for (Employee value : employees) {
                pstmt.setString(1, value.getEmployeeInfo().getName());
                // String result = newpositionMap.getOrDefault(jobNumber, "");
                pstmt.setString(2, StringUtils.hasText(value.getRecordInfo().getJobNumber()) ? value.getRecordInfo().getJobNumber() : "");
                pstmt.setString(3, getValueOrDefault(newgsMap, value.getRecordInfo().getoIdOrganization()));
                pstmt.setString(4, getValueOrDefault(newDeptmap, value.getRecordInfo().getoIdDepartment()));
                String oIdJobPost = value.getRecordInfo().getoIdJobPost();
                Long jobPostId = StringUtils.hasText(oIdJobPost) ? Long.parseLong(oIdJobPost) : -1L;
                // String result = newpositionMap.getOrDefault(jobPostId, "");
                pstmt.setString(5, newpositionMap.getOrDefault(jobPostId, ""));
                pstmt.setString(6, convertDateFormat(value.getRecordInfo().getLastWorkDate()));
                pstmt.setString(7, String.valueOf(value.getEmployeeInfo().getUserID()));
                pstmt.setString(8, StringUtils.hasText(value.getEmployeeInfo().getMobilePhone()) ? value.getEmployeeInfo().getMobilePhone() : "");
                pstmt.setString(9, value.getEmployeeInfo().getGender() == 0 ? "男" : "女");
                pstmt.setString(10, String.valueOf(value.getEmployeeInfo().getiDNumber()));
                Statement statement = con1.createStatement();
                String rzrq = "";
                if (StringUtils.hasText(value.getEmployeeInfo().getMobilePhone())) {
                    ResultSet resultSet = statement.executeQuery("select companystartdate from hrmresource where id in  (select id from cus_fielddata where field49='" + value.getEmployeeInfo().getMobilePhone() + "' and scopeid=1)");
                    if (resultSet.next()) {
                        rzrq = resultSet.getString("companystartdate");
                    }
                }
                // ResultSet resultSet = statement.executeQuery("select companystartdate from hrmresource where id= (select id from cus_fielddata where field49='"+value.getEmployeeInfo().getMobilePhone()+"' and scopeid=1)");
                //
                // if (resultSet.next()) {
                //     rzrq = resultSet.getString("companystartdate");
                // }
                pstmt.setString(11, rzrq);
                pstmt.setInt(12, 1068); // 固定值
                pstmt.setInt(13, 3947); // 固定值
                pstmt.setInt(14, 0); // 固定值
                pstmt.setString(15, getCurrentDateTimeMap().get("day")); // 创建日期
                pstmt.setString(16, getCurrentDateTimeMap().get("time")); // 创建时间
                pstmt.setString(17, UUID.randomUUID().toString()); // 创建时间
                int gsid = 0;
                String org = getValueOrDefault(newgsMap, value.getRecordInfo().getoIdOrganization());
                if (StringUtils.hasText(org)) {
                    ResultSet resultSet = statement.executeQuery("select id from HrmSubCompany where  subcompanyname='" + org + "'");
                    if (resultSet.next()) {
                        gsid = resultSet.getInt("id");
                    }
                }
                pstmt.setInt(18, gsid); // 创建时间

                int deptid = 0;
                String dept = getValueOrDefault(newDeptCodeMap, value.getRecordInfo().getoIdDepartment());
                if (StringUtils.hasText(dept)) {
                    ResultSet resultSet = statement.executeQuery("select id from hrmdepartment where  departmentcode='" + dept + "'");
                    if (resultSet.next()) {
                        deptid = resultSet.getInt("id");
                    }
                }
                pstmt.setInt(19, deptid); // 创建时间
                // pstmt.setString(2, "value4");
                pstmt.addBatch();
            }
            con.setAutoCommit(false);
            pstmt.executeBatch(); // 此方法将执行所有添加到批处理的插入操作
            con.commit(); // 提交事务
            // Statement statement = con.createStatement();
            // ResultSet resultSet = statement.executeQuery("select * from ss");

            // while (resultSet.next()){
            //     String userID=resultSet.getString("id");
            //     String password=resultSet.getString("name");
            //     // String name=resultSet.getString("userName");
            //     System.out.println(userID+"	"+password+"	");
            // }

            // 定义命令对象
            // Statement stmt = null;


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        new LZZMJob().execute();
        // System.out.println(convertDateFormat("2025-03-11T00:00:00"));
    }
}
