package weaver.interfaces.workflow.action.cyitce.job.fbt;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/4
 * @describe 分贝通账单同步任务。
 */
public class FBTJob2 extends BaseCronJob {
    BaseBean log = new BaseBean();

    @Override
    public void execute() {
        try {
            Map<String, List<Map<String, Object>>> settledBillDetails = processSettledBills();
            log.writeLog("已结算账单处理完成，获取到的companyId数量: " + settledBillDetails.size());

            Map<String, Map<String, ProjectInfo>> projectEventMap = organizeDataByProjectAndEvent(settledBillDetails);
            log.writeLog("数据按(项目,事项)聚合完成。");

            saveProjectInfoToDatabase(projectEventMap);
            log.writeLog("已结算账单数据成功存入数据库。");

        } catch (IOException e) {
            log.writeLog("处理已结算账单时发生IO异常: " + e.getMessage());
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.writeLog("执行任务时发生未知异常: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public String getToken() throws IOException {
        String apiUrl = "https://openapi.fenbeitong.com/openapi/auth/getToken";
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        String jsonInputString = "{\"app_id\": \"67d7f3243d83426b2c5cc4b0\",\"app_key\": \"6801e92efe12446af1d556ad\"}";

        try (OutputStream os = connection.getOutputStream()) {
            os.write(jsonInputString.getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new java.util.Scanner(is, "UTF-8").useDelimiter("\\A").next();
                JSONObject jsonObj = JSONUtil.parseObj(response);
                if (jsonObj.getInt("code", -1) == 0) {
                    return jsonObj.getStr("data");
                } else {
                    throw new IOException("获取Token失败: " + jsonObj.getStr("msg", "未知错误"));
                }
            }
        } else {
            throw new IOException("获取Token失败，HTTP响应码: " + connection.getResponseCode());
        }
    }

    public Map<String, List<Map<String, Object>>> getBillBusinessDetails(String billCode) throws IOException {
        String token = getToken();
        Map<String, List<Map<String, Object>>> allDetailsMap = new HashMap<>();
        int pageIndex = 1;
        boolean hasMorePages = true;

        while (hasMorePages) {
            String responseStr = requestBillBusinessDetails(token, billCode, pageIndex, 100);
            JSONObject jsonObj = JSONUtil.parseObj(responseStr);

            if (jsonObj.getInt("code", -1) != 0) {
                throw new IOException("查询账单详情失败: " + jsonObj.getStr("msg", "未知错误"));
            }

            JSONObject dataObj = jsonObj.getJSONObject("data");
            if (dataObj == null) break;

            if (dataObj.containsKey("details")) {
                JSONArray detailsArray = dataObj.getJSONArray("details");
                for (int i = 0; i < detailsArray.size(); i++) {
                    // Object o = detailsArray.getJSONObject(i).get("order_category");
                    // System.out.println(i + " = " + String.valueOf(o));
                    // if (String.valueOf(o).equals("913")) {
                    //     System.out.println("o = " + o);
                    // }
                    JSONObject detailObj = detailsArray.getJSONObject(i);
                    Map<String, Object> detailMap = detailObj;
                    String companyId = extractCompanyId(detailObj);
                    String key = (companyId != null && !companyId.isEmpty()) ? companyId : "no_company_id_" + UUID.randomUUID();
                    if (key.startsWith("no_company_id_")) {
                        System.out.println("detailObj = " + detailObj);
                    }
                    allDetailsMap.computeIfAbsent(key, k -> new ArrayList<>()).add(detailMap);
                }
            }
            hasMorePages = pageIndex < dataObj.getInt("total_pages", 1);
            pageIndex++;
        }
        return allDetailsMap;
    }

    private String extractCompanyId(JSONObject detailObj) {
        if (!detailObj.containsKey("cost_attributions")) return null;
        try {
            JSONArray costAttributions = detailObj.getJSONArray("cost_attributions");
            for (int i = 0; i < costAttributions.size(); i++) {
                JSONObject attribution = costAttributions.getJSONObject(i);
                if (attribution.getInt("type", 0) == 2 && attribution.containsKey("details")) {
                    JSONArray details = attribution.getJSONArray("details");
                    for (int j = 0; j < details.size(); j++) {
                        JSONObject detail = details.getJSONObject(j);
                        if (detail.containsKey("custom_fields")) {
                            JSONArray customFields = detail.getJSONArray("custom_fields");
                            for (int k = 0; k < customFields.size(); k++) {
                                JSONObject field = customFields.getJSONObject(k);
                                if ("companyId".equals(field.getStr("title"))) {
                                    return field.getStr("detail");
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.writeLog("提取companyId时出错: " + e.getMessage());
        }
        return null;
    }

    private String extractEventName(JSONObject detailObj) {
        // 直接从JSON对象顶层获取 "reason" 字段的字符串值。
        // Hutool的getStr方法在字段不存在时会安全地返回null，无需try-catch。
        return detailObj.getStr("reason");
        // if (!detailObj.containsKey("cost_attributions")) return null;
        // try {
        //     JSONArray costAttributions = detailObj.getJSONArray("cost_attributions");
        //     for (int i = 0; i < costAttributions.size(); i++) {
        //         JSONObject attribution = costAttributions.getJSONObject(i);
        //         if (attribution.getInt("type", 0) == 2 && attribution.containsKey("details")) {
        //             JSONArray details = attribution.getJSONArray("details");
        //             for (int j = 0; j < details.size(); j++) {
        //                 JSONObject detail = details.getJSONObject(j);
        //                 if (detail.containsKey("custom_fields")) {
        //                     JSONArray customFields = detail.getJSONArray("custom_fields");
        //                     for (int k = 0; k < customFields.size(); k++) {
        //                         JSONObject field = customFields.getJSONObject(k);
        //                         if ("xmmc".equals(field.getStr("title"))) {
        //                             return field.getStr("detail");
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // } catch (Exception e) {
        //     log.writeLog("提取事项名称(xmmc)时出错: " + e.getMessage());
        // }
        // return null;
    }

    private String requestBillBusinessDetails(String token, String billCode, int pageIndex, int pageSize) throws IOException {
        String apiUrl = "https://openapi.fenbeitong.com/openapi/bill/business/v1/detail";
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);
        connection.setDoOutput(true);

        String jsonInputString = String.format("{\"bill_code\": \"%s\",\"page_index\": %d,\"page_size\": %d}", billCode, pageIndex, pageSize);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(jsonInputString.getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                return new java.util.Scanner(is, "UTF-8").useDelimiter("\\A").next();
            }
        } else {
            throw new IOException("查询账单详情失败，HTTP响应码: " + connection.getResponseCode());
        }
    }

    public JSONObject getBillBusinessList(int state, int pageIndex, int pageSize) throws IOException {
        Calendar calendar = Calendar.getInstance();
        String endTimeStr = String.format("%04d%02d", calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1);
        calendar.add(Calendar.MONTH, -1);
        String startTimeStr = String.format("%04d%02d", calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1);
        // startTimeStr = "202506";
        // endTimeStr = "202507";
        return getBillBusinessList(startTimeStr, endTimeStr, state, pageIndex, pageSize);
    }

    public JSONObject getBillBusinessList(String startTime, String endTime, int state, int pageIndex, int pageSize) throws IOException {
        String token = getToken();
        String apiUrl = "https://openapi.fenbeitong.com/openapi/bill/business/v1/list";
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);
        connection.setDoOutput(true);

        String jsonInputString = String.format("{\"start_time\": \"%s\",\"end_time\": \"%s\",\"state\": %d,\"page_index\": %d,\"page_size\": %d}",
                startTime, endTime, state, pageIndex, pageSize);

        try (OutputStream os = connection.getOutputStream()) {
            os.write(jsonInputString.getBytes("utf-8"));
        }

        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
            try (InputStream is = connection.getInputStream()) {
                String response = new java.util.Scanner(is, "UTF-8").useDelimiter("\\A").next();
                JSONObject jsonObj = JSONUtil.parseObj(response);
                if (jsonObj.getInt("code", -1) == 0) {
                    return jsonObj;
                } else {
                    throw new IOException("查询账单列表失败: " + jsonObj.getStr("msg", "未知错误"));
                }
            }
        } else {
            throw new IOException("查询账单列表失败，HTTP响应码: " + connection.getResponseCode());
        }
    }

    public Map<String, List<Map<String, Object>>> processSettledBills() throws IOException {
        Map<String, List<Map<String, Object>>> allSettledBillDetails = new HashMap<>();
        JSONObject billListResult = getBillBusinessList(4, 1, 100);
        JSONObject dataObj = billListResult.getJSONObject("data");
        if (dataObj != null && dataObj.containsKey("bills")) {
            JSONArray billsArray = dataObj.getJSONArray("bills");
            log.writeLog("发现 " + billsArray.size() + " 个已结算账单。");
            for (int i = 0; i < billsArray.size(); i++) {
                JSONObject bill = billsArray.getJSONObject(i);
                String code = bill.getStr("code", "");
                // if(code.equals("0013576520250725")) continue;

                if (code.isEmpty()) continue;

                log.writeLog("正在处理账单: " + code);
                try {
                    Map<String, List<Map<String, Object>>> billDetails = getBillBusinessDetails(code);
                    for (List<Map<String, Object>> detailsList : billDetails.values()) {
                        for (Map<String, Object> detail : detailsList) {
                            detail.put("bill_code", code);
                            detail.put("bill_start_time", bill.getStr("start_time", ""));
                            detail.put("bill_end_time", bill.getStr("end_time", ""));
                            detail.put("bill_status", bill.getInt("state", 0));
                        }
                    }
                    billDetails.forEach((companyId, details) ->
                            allSettledBillDetails.computeIfAbsent(companyId, k -> new ArrayList<>()).addAll(details)
                    );
                } catch (Exception e) {
                    log.writeLog("获取账单 " + code + " 详细信息时出错: " + e.getMessage());
                }
            }
        } else {
            log.writeLog("未获取到已结算的账单数据。");
        }
        return allSettledBillDetails;
    }

    public Map<String, Map<String, ProjectInfo>> organizeDataByProjectAndEvent(Map<String, List<Map<String, Object>>> billDetails) {
        Map<String, Map<String, ProjectInfo>> projectEventMap = new HashMap<>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
            String companyId = entry.getKey();
            for (Map<String, Object> detail : entry.getValue()) {
                JSONObject detailJson = new JSONObject(detail);

                String projectCode = detailJson.getStr("project_code");
                if (projectCode == null || projectCode.isEmpty()) continue;

                String eventName = extractEventName(detailJson);
                if (eventName == null || eventName.isEmpty()) {
                    eventName = "组织管理";
                }

                Map<String, ProjectInfo> eventMap = projectEventMap.computeIfAbsent(projectCode, k -> new HashMap<>());
                ProjectInfo projectInfo = eventMap.computeIfAbsent(eventName, k -> new ProjectInfo(projectCode, k));

                if (projectInfo.getProjectName() == null || projectInfo.getProjectName().equals(projectCode)) {
                    projectInfo.setProjectName(detailJson.getStr("project_name"));
                }

                if (projectInfo.getBillCode() == null) {
                    projectInfo.setBillCode(detailJson.getStr("bill_code"));
                    projectInfo.setBillStartTime(detailJson.getStr("bill_start_time"));
                    projectInfo.setBillEndTime(detailJson.getStr("bill_end_time"));
                    projectInfo.setBillStatus(detailJson.getInt("bill_status", 0));
                }

                projectInfo.addRelatedCompany(companyId);
                if (projectInfo.getCompanyName() == null) {
                    projectInfo.setCompanyName(detailJson.getStr("company_name"));
                }

                Integer orderCategory = detailJson.getInt("order_category");
                if (orderCategory == null) continue;

                BigDecimal amount = getBigDecimalFromDetail(detail, "amount");
                BigDecimal includeTaxAmount = getBigDecimalFromDetail(detail, "include_tax_amount");
                BigDecimal excludeTaxAmount = getBigDecimalFromDetail(detail, "exclude_tax_amount");

                projectInfo.addCategoryAmount(orderCategory, amount);
                projectInfo.addCategoryIncludeTaxAmount(orderCategory, includeTaxAmount);
                projectInfo.addCategoryExcludeTaxAmount(orderCategory, excludeTaxAmount);

                if (orderCategory == 7 || orderCategory == 40) {
                    projectInfo.updateAirTicketTaxes(includeTaxAmount, excludeTaxAmount);
                }

                if (orderCategory == 11 || orderCategory == 110) {
                    String invoiceType = detailJson.getStr("invoice_type");
                    if ("专票".equals(invoiceType)) {
                        projectInfo.addAccommodationFeeSpecial(orderCategory, amount, includeTaxAmount, excludeTaxAmount);
                    } else if ("普票".equals(invoiceType)) {
                        projectInfo.addAccommodationFeeOrdinary(orderCategory, amount, includeTaxAmount, excludeTaxAmount);
                    }
                }

                projectInfo.addManualPrice(getBigDecimalFromDetail(detail, "manual_price"));
                projectInfo.addAirportFee(getBigDecimalFromDetail(detail, "airport_fee"));
                projectInfo.addFuelFee(getBigDecimalFromDetail(detail, "fuel_fee"));
            }
        }
        return projectEventMap;
    }

    private BigDecimal getBigDecimalFromDetail(Map<String, Object> detail, String key) {
        Object value = detail.get(key);
        if (value == null) return BigDecimal.ZERO;
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    public void saveProjectInfoToDatabase(Map<String, Map<String, ProjectInfo>> projectEventMap) {
        if (projectEventMap == null || projectEventMap.isEmpty()) {
            log.writeLog("没有项目信息可保存。");
            return;
        }

        String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String URL = "*********************************************************";
        String USERNAME = "lijunhong";
        String PASSWORD = "Cyitce@0106";
        Connection conn = null;
        PreparedStatement pstmt = null;

        String insertSql = "INSERT INTO uf_fbt_project_info (" +
                "formmodeid, modedatacreater, modedatacreatertype, modedatacreatedate, modedatacreatetime, MODEUUID, " +
                "project_code, project_name, sxmc,  bill_code, bill_start_time, bill_end_time, bill_status, " +
                "company_id, company_name, related_companies, " +
                "air_ticket_domestic, air_ticket_international, hotel, train, bus, car, " +
                "meal, takeaway, purchase, flash, express, freight, other, " +
                "virtual_card, corporate_payment, value_added_service, hotel_overseas, " +
                "include_tax_amount, exclude_tax_amount, " +
                "hotel_include_tax, hotel_exclude_tax, " +
                "train_include_tax, train_exclude_tax, " +
                "bus_include_tax, bus_exclude_tax, " +
                "car_include_tax, car_exclude_tax, " +
                "meal_include_tax, meal_exclude_tax, " +
                "takeaway_include_tax, takeaway_exclude_tax, " +
                "purchase_include_tax, purchase_exclude_tax, " +
                "flash_include_tax, flash_exclude_tax, " +
                "express_include_tax, express_exclude_tax, " +
                "freight_include_tax, freight_exclude_tax, " +
                "other_include_tax, other_exclude_tax, " +
                "virtual_card_include_tax, virtual_card_exclude_tax, " +
                "accommodation_fee, accommodation_fee_include_tax, accommodation_fee_exclude_tax, " +
                "accommodation_fee_special, accommodation_fee_ordinary, " +
                "hotel_special, hotel_special_include_tax, hotel_special_exclude_tax, " +
                "hotel_ordinary, hotel_ordinary_include_tax, hotel_ordinary_exclude_tax, " +
                "hotel_overseas_special, hotel_overseas_ordinary, " +
                "total_accommodation_fee, " +
                "atdit, atdet, atiit, atiet, cpit, cpet, vasit, vaset, hoit, " +
                "afsit, afset, hosit, hoset, afoit, afoet, hooit, hooet, " +
                "tafit, tafet, tafs, tafsit, tafset, tafo, tafoit, tafoet, " +
                "manual_price, manual_price_tax, train_tax, airport_fee, fuel_fee, " +
                "air_ticket_total_include_tax, air_ticket_tax, " +
                "create_date, create_time) " +
                "VALUES (" +
                "?,?,?,?,?,?, ?,?,?,?,?,?,?, ?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?, ?,?,?,?)";

        try {
            Class.forName(DRIVER);
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            conn.setAutoCommit(false);
            pstmt = conn.prepareStatement(insertSql);

            int totalCount = 0;
            final int BATCH_SIZE = 100;
            String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            for (Map<String, ProjectInfo> eventMap : projectEventMap.values()) {
                for (ProjectInfo project : eventMap.values()) {
                    if (project.getCompanyId().startsWith("no_company_id")) {
                        continue;
                    }
                    int paramIndex = 1;
                    pstmt.setInt(paramIndex++, 1077);
                    pstmt.setInt(paramIndex++, 3810);
                    pstmt.setInt(paramIndex++, 0);
                    pstmt.setString(paramIndex++, currentDate);
                    pstmt.setString(paramIndex++, currentTime);
                    pstmt.setString(paramIndex++, UUID.randomUUID().toString());
                    pstmt.setString(paramIndex++, project.getProjectCode());
                    pstmt.setString(paramIndex++, project.getProjectName());
                    String eventNameStr = project.getEventName();
                    int eventId = ProjectEventEnum.safeGetId(eventNameStr, 16);
                    pstmt.setInt(paramIndex++, eventId);
                    // pstmt.setInt(paramIndex++, Integer.parseInt(project.getEventName()));
                    // pstmt.setString(paramIndex++, project.getType());
                    pstmt.setString(paramIndex++, project.getBillCode());
                    pstmt.setString(paramIndex++, project.getBillStartTime());
                    pstmt.setString(paramIndex++, project.getBillEndTime());
                    pstmt.setInt(paramIndex++, project.getBillStatus());
                    pstmt.setString(paramIndex++, project.getCompanyId());
                    pstmt.setString(paramIndex++, project.getCompanyName());
                    pstmt.setString(paramIndex++, project.getRelatedCompaniesStr());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomestic());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternational());
                    pstmt.setBigDecimal(paramIndex++, project.getHotel());
                    pstmt.setBigDecimal(paramIndex++, project.getTrain());
                    pstmt.setBigDecimal(paramIndex++, project.getBus());
                    pstmt.setBigDecimal(paramIndex++, project.getCar());
                    pstmt.setBigDecimal(paramIndex++, project.getMeal());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeaway());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchase());
                    pstmt.setBigDecimal(paramIndex++, project.getFlash());
                    pstmt.setBigDecimal(paramIndex++, project.getExpress());
                    pstmt.setBigDecimal(paramIndex++, project.getFreight());
                    pstmt.setBigDecimal(paramIndex++, project.getOther());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCard());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePayment());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedService());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseas());
                    pstmt.setBigDecimal(paramIndex++, project.getIncludeTaxAmount());
                    pstmt.setBigDecimal(paramIndex++, project.getExcludeTaxAmount());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getBusIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getBusExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCarIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCarExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getMealIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getMealExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeawayIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTakeawayExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchaseIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getPurchaseExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFlashIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFlashExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getExpressIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getExpressExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFreightIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getFreightExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getOtherIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getOtherExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCardIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getVirtualCardExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecial());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinary());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryExcludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getManualPrice());
                    pstmt.setBigDecimal(paramIndex++, project.getManualPriceTax());
                    pstmt.setBigDecimal(paramIndex++, project.getTrainTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirportFee());
                    pstmt.setBigDecimal(paramIndex++, project.getFuelFee());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketTotalIncludeTax());
                    pstmt.setBigDecimal(paramIndex++, project.getAirTicketTax());
                    pstmt.setString(paramIndex++, currentDate);
                    pstmt.setString(paramIndex++, currentTime);

                    pstmt.addBatch();
                    totalCount++;

                    if (totalCount % BATCH_SIZE == 0) {
                        pstmt.executeBatch();
                        conn.commit();
                        log.writeLog("已提交一批(" + BATCH_SIZE + ")数据到数据库。");
                    }
                }
            }

            if (totalCount > 0 && totalCount % BATCH_SIZE != 0) {
                pstmt.executeBatch();
                conn.commit();
            }

            log.writeLog("成功保存 " + totalCount + " 条项目信息到数据库。");

        } catch (Exception e) {
            log.writeLog("保存项目信息到数据库时出错: " + e.getMessage());
            e.printStackTrace();
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            try {
                if (pstmt != null) pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            try {
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static class ProjectInfo {
        //<editor-fold desc="Fields">
        private String projectCode;
        private String projectName;
        private String eventName;
        private String type = "default";
        private String billCode;
        private String billStartTime;
        private String billEndTime;
        private int billStatus;
        private Set<String> relatedCompanies = new HashSet<>();
        private String companyId;
        private String companyName;
        private BigDecimal airTicketDomestic = BigDecimal.ZERO;
        private BigDecimal airTicketInternational = BigDecimal.ZERO;
        private BigDecimal hotel = BigDecimal.ZERO;
        private BigDecimal train = BigDecimal.ZERO;
        private BigDecimal bus = BigDecimal.ZERO;
        private BigDecimal car = BigDecimal.ZERO;
        private BigDecimal meal = BigDecimal.ZERO;
        private BigDecimal takeaway = BigDecimal.ZERO;
        private BigDecimal purchase = BigDecimal.ZERO;
        private BigDecimal flash = BigDecimal.ZERO;
        private BigDecimal express = BigDecimal.ZERO;
        private BigDecimal freight = BigDecimal.ZERO;
        private BigDecimal other = BigDecimal.ZERO;
        private BigDecimal virtualCard = BigDecimal.ZERO;
        private BigDecimal corporatePayment = BigDecimal.ZERO;
        private BigDecimal valueAddedService = BigDecimal.ZERO;
        private BigDecimal hotelOverseas = BigDecimal.ZERO;
        private BigDecimal includeTaxAmount = BigDecimal.ZERO;
        private BigDecimal excludeTaxAmount = BigDecimal.ZERO;
        private BigDecimal airTicketDomesticIncludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketDomesticExcludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketInternationalIncludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketInternationalExcludeTax = BigDecimal.ZERO;
        private BigDecimal hotelIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelExcludeTax = BigDecimal.ZERO;
        private BigDecimal trainIncludeTax = BigDecimal.ZERO;
        private BigDecimal trainExcludeTax = BigDecimal.ZERO;
        private BigDecimal busIncludeTax = BigDecimal.ZERO;
        private BigDecimal busExcludeTax = BigDecimal.ZERO;
        private BigDecimal carIncludeTax = BigDecimal.ZERO;
        private BigDecimal carExcludeTax = BigDecimal.ZERO;
        private BigDecimal mealIncludeTax = BigDecimal.ZERO;
        private BigDecimal mealExcludeTax = BigDecimal.ZERO;
        private BigDecimal takeawayIncludeTax = BigDecimal.ZERO;
        private BigDecimal takeawayExcludeTax = BigDecimal.ZERO;
        private BigDecimal purchaseIncludeTax = BigDecimal.ZERO;
        private BigDecimal purchaseExcludeTax = BigDecimal.ZERO;
        private BigDecimal flashIncludeTax = BigDecimal.ZERO;
        private BigDecimal flashExcludeTax = BigDecimal.ZERO;
        private BigDecimal expressIncludeTax = BigDecimal.ZERO;
        private BigDecimal expressExcludeTax = BigDecimal.ZERO;
        private BigDecimal freightIncludeTax = BigDecimal.ZERO;
        private BigDecimal freightExcludeTax = BigDecimal.ZERO;
        private BigDecimal otherIncludeTax = BigDecimal.ZERO;
        private BigDecimal otherExcludeTax = BigDecimal.ZERO;
        private BigDecimal virtualCardIncludeTax = BigDecimal.ZERO;
        private BigDecimal virtualCardExcludeTax = BigDecimal.ZERO;
        private BigDecimal corporatePaymentIncludeTax = BigDecimal.ZERO;
        private BigDecimal corporatePaymentExcludeTax = BigDecimal.ZERO;
        private BigDecimal valueAddedServiceIncludeTax = BigDecimal.ZERO;
        private BigDecimal valueAddedServiceExcludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasExcludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFee = BigDecimal.ZERO;
        private BigDecimal accommodationFeeIncludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFeeExcludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFeeSpecial = BigDecimal.ZERO;
        private BigDecimal accommodationFeeSpecialIncludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFeeSpecialExcludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFeeOrdinary = BigDecimal.ZERO;
        private BigDecimal accommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO;
        private BigDecimal accommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO;
        private BigDecimal hotelSpecial = BigDecimal.ZERO;
        private BigDecimal hotelSpecialIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelSpecialExcludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOrdinary = BigDecimal.ZERO;
        private BigDecimal hotelOrdinaryIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOrdinaryExcludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasSpecial = BigDecimal.ZERO;
        private BigDecimal hotelOverseasOrdinary = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFee = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeIncludeTax = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeExcludeTax = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeSpecial = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeSpecialIncludeTax = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeSpecialExcludeTax = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeOrdinary = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO;
        private BigDecimal totalAccommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO;
        private BigDecimal manualPrice = BigDecimal.ZERO;
        private BigDecimal manualPriceTax = BigDecimal.ZERO;
        private BigDecimal trainTax = BigDecimal.ZERO;
        private BigDecimal airportFee = BigDecimal.ZERO;
        private BigDecimal fuelFee = BigDecimal.ZERO;
        private BigDecimal airTicketTotalIncludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketTax = BigDecimal.ZERO;
        //</editor-fold>

        public ProjectInfo(String projectCode, String eventName) {
            this.projectCode = projectCode;
            this.eventName = eventName;
            this.projectName = projectCode;
        }

        //<editor-fold desc="Getters and Setters">
        public String getProjectCode() {
            return projectCode;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getEventName() {
            return eventName;
        }

        public void setEventName(String eventName) {
            this.eventName = eventName;
        }

        public String getType() {
            return type;
        }

        public String getBillCode() {
            return billCode;
        }

        public void setBillCode(String billCode) {
            this.billCode = billCode;
        }

        public String getBillStartTime() {
            return billStartTime;
        }

        public void setBillStartTime(String billStartTime) {
            this.billStartTime = billStartTime;
        }

        public String getBillEndTime() {
            return billEndTime;
        }

        public void setBillEndTime(String billEndTime) {
            this.billEndTime = billEndTime;
        }

        public int getBillStatus() {
            return billStatus;
        }

        public void setBillStatus(int billStatus) {
            this.billStatus = billStatus;
        }

        public String getCompanyId() {
            return companyId;
        }

        public void setCompanyId(String companyId) {
            this.companyId = companyId;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getRelatedCompaniesStr() {
            return String.join(",", relatedCompanies);
        }

        public BigDecimal getAirTicketDomestic() {
            return airTicketDomestic;
        }

        public BigDecimal getAirTicketInternational() {
            return airTicketInternational;
        }

        public BigDecimal getHotel() {
            return hotel;
        }

        public BigDecimal getTrain() {
            return train;
        }

        public BigDecimal getBus() {
            return bus;
        }

        public BigDecimal getCar() {
            return car;
        }

        public BigDecimal getMeal() {
            return meal;
        }

        public BigDecimal getTakeaway() {
            return takeaway;
        }

        public BigDecimal getPurchase() {
            return purchase;
        }

        public BigDecimal getFlash() {
            return flash;
        }

        public BigDecimal getExpress() {
            return express;
        }

        public BigDecimal getFreight() {
            return freight;
        }

        public BigDecimal getOther() {
            return other;
        }

        public BigDecimal getVirtualCard() {
            return virtualCard;
        }

        public BigDecimal getCorporatePayment() {
            return corporatePayment;
        }

        public BigDecimal getValueAddedService() {
            return valueAddedService;
        }

        public BigDecimal getHotelOverseas() {
            return hotelOverseas;
        }

        public BigDecimal getIncludeTaxAmount() {
            return includeTaxAmount;
        }

        public BigDecimal getExcludeTaxAmount() {
            return excludeTaxAmount;
        }

        public BigDecimal getAirTicketDomesticIncludeTax() {
            return airTicketDomesticIncludeTax;
        }

        public BigDecimal getAirTicketDomesticExcludeTax() {
            return airTicketDomesticExcludeTax;
        }

        public BigDecimal getAirTicketInternationalIncludeTax() {
            return airTicketInternationalIncludeTax;
        }

        public BigDecimal getAirTicketInternationalExcludeTax() {
            return airTicketInternationalExcludeTax;
        }

        public BigDecimal getHotelIncludeTax() {
            return hotelIncludeTax;
        }

        public BigDecimal getHotelExcludeTax() {
            return hotelExcludeTax;
        }

        public BigDecimal getTrainIncludeTax() {
            return trainIncludeTax;
        }

        public BigDecimal getTrainExcludeTax() {
            return trainExcludeTax;
        }

        public BigDecimal getBusIncludeTax() {
            return busIncludeTax;
        }

        public BigDecimal getBusExcludeTax() {
            return busExcludeTax;
        }

        public BigDecimal getCarIncludeTax() {
            return carIncludeTax;
        }

        public BigDecimal getCarExcludeTax() {
            return carExcludeTax;
        }

        public BigDecimal getMealIncludeTax() {
            return mealIncludeTax;
        }

        public BigDecimal getMealExcludeTax() {
            return mealExcludeTax;
        }

        public BigDecimal getTakeawayIncludeTax() {
            return takeawayIncludeTax;
        }

        public BigDecimal getTakeawayExcludeTax() {
            return takeawayExcludeTax;
        }

        public BigDecimal getPurchaseIncludeTax() {
            return purchaseIncludeTax;
        }

        public BigDecimal getPurchaseExcludeTax() {
            return purchaseExcludeTax;
        }

        public BigDecimal getFlashIncludeTax() {
            return flashIncludeTax;
        }

        public BigDecimal getFlashExcludeTax() {
            return flashExcludeTax;
        }

        public BigDecimal getExpressIncludeTax() {
            return expressIncludeTax;
        }

        public BigDecimal getExpressExcludeTax() {
            return expressExcludeTax;
        }

        public BigDecimal getFreightIncludeTax() {
            return freightIncludeTax;
        }

        public BigDecimal getFreightExcludeTax() {
            return freightExcludeTax;
        }

        public BigDecimal getOtherIncludeTax() {
            return otherIncludeTax;
        }

        public BigDecimal getOtherExcludeTax() {
            return otherExcludeTax;
        }

        public BigDecimal getVirtualCardIncludeTax() {
            return virtualCardIncludeTax;
        }

        public BigDecimal getVirtualCardExcludeTax() {
            return virtualCardExcludeTax;
        }

        public BigDecimal getCorporatePaymentIncludeTax() {
            return corporatePaymentIncludeTax;
        }

        public BigDecimal getCorporatePaymentExcludeTax() {
            return corporatePaymentExcludeTax;
        }

        public BigDecimal getValueAddedServiceIncludeTax() {
            return valueAddedServiceIncludeTax;
        }

        public BigDecimal getValueAddedServiceExcludeTax() {
            return valueAddedServiceExcludeTax;
        }

        public BigDecimal getHotelOverseasIncludeTax() {
            return hotelOverseasIncludeTax;
        }

        public BigDecimal getHotelOverseasExcludeTax() {
            return hotelOverseasExcludeTax;
        }

        public BigDecimal getAccommodationFee() {
            return accommodationFee;
        }

        public BigDecimal getAccommodationFeeIncludeTax() {
            return accommodationFeeIncludeTax;
        }

        public BigDecimal getAccommodationFeeExcludeTax() {
            return accommodationFeeExcludeTax;
        }

        public BigDecimal getAccommodationFeeSpecial() {
            return accommodationFeeSpecial;
        }

        public BigDecimal getAccommodationFeeSpecialIncludeTax() {
            return accommodationFeeSpecialIncludeTax;
        }

        public BigDecimal getAccommodationFeeSpecialExcludeTax() {
            return accommodationFeeSpecialExcludeTax;
        }

        public BigDecimal getAccommodationFeeOrdinary() {
            return accommodationFeeOrdinary;
        }

        public BigDecimal getAccommodationFeeOrdinaryIncludeTax() {
            return accommodationFeeOrdinaryIncludeTax;
        }

        public BigDecimal getAccommodationFeeOrdinaryExcludeTax() {
            return accommodationFeeOrdinaryExcludeTax;
        }

        public BigDecimal getHotelSpecial() {
            return hotelSpecial;
        }

        public BigDecimal getHotelSpecialIncludeTax() {
            return hotelSpecialIncludeTax;
        }

        public BigDecimal getHotelSpecialExcludeTax() {
            return hotelSpecialExcludeTax;
        }

        public BigDecimal getHotelOrdinary() {
            return hotelOrdinary;
        }

        public BigDecimal getHotelOrdinaryIncludeTax() {
            return hotelOrdinaryIncludeTax;
        }

        public BigDecimal getHotelOrdinaryExcludeTax() {
            return hotelOrdinaryExcludeTax;
        }

        public BigDecimal getHotelOverseasSpecial() {
            return hotelOverseasSpecial;
        }

        public BigDecimal getHotelOverseasOrdinary() {
            return hotelOverseasOrdinary;
        }

        public BigDecimal getTotalAccommodationFee() {
            return totalAccommodationFee;
        }

        public BigDecimal getTotalAccommodationFeeIncludeTax() {
            return totalAccommodationFeeIncludeTax;
        }

        public BigDecimal getTotalAccommodationFeeExcludeTax() {
            return totalAccommodationFeeExcludeTax;
        }

        public BigDecimal getTotalAccommodationFeeSpecial() {
            return totalAccommodationFeeSpecial;
        }

        public BigDecimal getTotalAccommodationFeeSpecialIncludeTax() {
            return totalAccommodationFeeSpecialIncludeTax;
        }

        public BigDecimal getTotalAccommodationFeeSpecialExcludeTax() {
            return totalAccommodationFeeSpecialExcludeTax;
        }

        public BigDecimal getTotalAccommodationFeeOrdinary() {
            return totalAccommodationFeeOrdinary;
        }

        public BigDecimal getTotalAccommodationFeeOrdinaryIncludeTax() {
            return totalAccommodationFeeOrdinaryIncludeTax;
        }

        public BigDecimal getTotalAccommodationFeeOrdinaryExcludeTax() {
            return totalAccommodationFeeOrdinaryExcludeTax;
        }

        public BigDecimal getManualPrice() {
            return manualPrice;
        }

        public BigDecimal getManualPriceTax() {
            return manualPriceTax;
        }

        public BigDecimal getTrainTax() {
            return trainTax;
        }

        public BigDecimal getAirportFee() {
            return airportFee;
        }

        public BigDecimal getFuelFee() {
            return fuelFee;
        }

        public BigDecimal getAirTicketTotalIncludeTax() {
            return airTicketTotalIncludeTax;
        }

        public BigDecimal getAirTicketTax() {
            return airTicketTax;
        }
        //</editor-fold>

        //<editor-fold desc="Accumulator Methods">
        public void addRelatedCompany(String companyId) {
            if (companyId != null && !companyId.isEmpty()) {
                if (this.companyId == null) this.companyId = companyId;
                relatedCompanies.add(companyId);
            }
        }

        public void addCategoryAmount(int category, BigDecimal amount) {
            if (amount == null) return;
            switch (category) {
                case 7:
                    airTicketDomestic = airTicketDomestic.add(amount);
                    break;
                case 40:
                    airTicketInternational = airTicketInternational.add(amount);
                    break;
                case 11:
                    hotel = hotel.add(amount);
                    accommodationFee = accommodationFee.add(amount);
                    break;
                case 15:
                    train = train.add(amount);
                    break;
                case 135:
                    bus = bus.add(amount);
                    break;
                case 3:
                    car = car.add(amount);
                    break;
                case 60:
                    meal = meal.add(amount);
                    break;
                case 50:
                    takeaway = takeaway.add(amount);
                    break;
                case 20:
                    purchase = purchase.add(amount);
                    break;
                case 130:
                    flash = flash.add(amount);
                    break;
                case 131:
                    express = express.add(amount);
                    break;
                case 150:
                    freight = freight.add(amount);
                    break;
                case 911:
                    other = other.add(amount);
                    break;
                case 126:
                    virtualCard = virtualCard.add(amount);
                    break;
                case 128:
                    corporatePayment = corporatePayment.add(amount);
                    break;
                case 913:
                    valueAddedService = valueAddedService.add(amount);
                    break;
                case 110:
                    hotelOverseas = hotelOverseas.add(amount);
                    accommodationFee = accommodationFee.add(amount);
                    break;
                default:
                    other = other.add(amount);
                    break;
            }
        }

        public void addCategoryIncludeTaxAmount(int category, BigDecimal amount) {
            if (amount == null) return;
            this.includeTaxAmount = this.includeTaxAmount.add(amount);
            switch (category) {
                case 7:
                    airTicketDomesticIncludeTax = airTicketDomesticIncludeTax.add(amount);
                    break;
                case 40:
                    airTicketInternationalIncludeTax = airTicketInternationalIncludeTax.add(amount);
                    break;
                case 11:
                    hotelIncludeTax = hotelIncludeTax.add(amount);
                    accommodationFeeIncludeTax = accommodationFeeIncludeTax.add(amount);
                    break;
                case 15:
                    trainIncludeTax = trainIncludeTax.add(amount);
                    break;
                case 135:
                    busIncludeTax = busIncludeTax.add(amount);
                    break;
                case 3:
                    carIncludeTax = carIncludeTax.add(amount);
                    break;
                case 60:
                    mealIncludeTax = mealIncludeTax.add(amount);
                    break;
                case 50:
                    takeawayIncludeTax = takeawayIncludeTax.add(amount);
                    break;
                case 20:
                    purchaseIncludeTax = purchaseIncludeTax.add(amount);
                    break;
                case 130:
                    flashIncludeTax = flashIncludeTax.add(amount);
                    break;
                case 131:
                    expressIncludeTax = expressIncludeTax.add(amount);
                    break;
                case 150:
                    freightIncludeTax = freightIncludeTax.add(amount);
                    break;
                case 911:
                    otherIncludeTax = otherIncludeTax.add(amount);
                    break;
                case 126:
                    virtualCardIncludeTax = virtualCardIncludeTax.add(amount);
                    break;
                case 128:
                    corporatePaymentIncludeTax = corporatePaymentIncludeTax.add(amount);
                    break;
                case 913:
                    valueAddedServiceIncludeTax = valueAddedServiceIncludeTax.add(amount);
                    break;
                case 110:
                    hotelOverseasIncludeTax = hotelOverseasIncludeTax.add(amount);
                    accommodationFeeIncludeTax = accommodationFeeIncludeTax.add(amount);
                    break;
            }
        }

        public void addCategoryExcludeTaxAmount(int category, BigDecimal amount) {
            if (amount == null) return;
            this.excludeTaxAmount = this.excludeTaxAmount.add(amount);
            switch (category) {
                case 7:
                    airTicketDomesticExcludeTax = airTicketDomesticExcludeTax.add(amount);
                    break;
                case 40:
                    airTicketInternationalExcludeTax = airTicketInternationalExcludeTax.add(amount);
                    break;
                case 11:
                    hotelExcludeTax = hotelExcludeTax.add(amount);
                    accommodationFeeExcludeTax = accommodationFeeExcludeTax.add(amount);
                    break;
                case 15:
                    trainExcludeTax = trainExcludeTax.add(amount);
                    updateTrainTax();
                    break;
                case 135:
                    busExcludeTax = busExcludeTax.add(amount);
                    break;
                case 3:
                    carExcludeTax = carExcludeTax.add(amount);
                    break;
                case 60:
                    mealExcludeTax = mealExcludeTax.add(amount);
                    break;
                case 50:
                    takeawayExcludeTax = takeawayExcludeTax.add(amount);
                    break;
                case 20:
                    purchaseExcludeTax = purchaseExcludeTax.add(amount);
                    break;
                case 130:
                    flashExcludeTax = flashExcludeTax.add(amount);
                    break;
                case 131:
                    expressExcludeTax = expressExcludeTax.add(amount);
                    break;
                case 150:
                    freightExcludeTax = freightExcludeTax.add(amount);
                    break;
                case 911:
                    otherExcludeTax = otherExcludeTax.add(amount);
                    break;
                case 126:
                    virtualCardExcludeTax = virtualCardExcludeTax.add(amount);
                    break;
                case 128:
                    corporatePaymentExcludeTax = corporatePaymentExcludeTax.add(amount);
                    break;
                case 913:
                    valueAddedServiceExcludeTax = valueAddedServiceExcludeTax.add(amount);
                    break;
                case 110:
                    hotelOverseasExcludeTax = hotelOverseasExcludeTax.add(amount);
                    accommodationFeeExcludeTax = accommodationFeeExcludeTax.add(amount);
                    break;
            }
        }

        public void updateAirTicketTaxes(BigDecimal includeTax, BigDecimal excludeTax) {
            if (includeTax != null) {
                airTicketTotalIncludeTax = airTicketTotalIncludeTax.add(includeTax);
                if (excludeTax != null) airTicketTax = airTicketTax.add(includeTax.subtract(excludeTax));
            }
        }

        public void addAccommodationFeeSpecial(int category, BigDecimal amount, BigDecimal includeTax, BigDecimal excludeTax) {
            if (amount == null) return;
            accommodationFeeSpecial = accommodationFeeSpecial.add(amount);
            totalAccommodationFeeSpecial = totalAccommodationFeeSpecial.add(amount);
            if (includeTax != null) {
                accommodationFeeSpecialIncludeTax = accommodationFeeSpecialIncludeTax.add(includeTax);
                totalAccommodationFeeSpecialIncludeTax = totalAccommodationFeeSpecialIncludeTax.add(includeTax);
            }
            if (excludeTax != null) {
                accommodationFeeSpecialExcludeTax = accommodationFeeSpecialExcludeTax.add(excludeTax);
                totalAccommodationFeeSpecialExcludeTax = totalAccommodationFeeSpecialExcludeTax.add(excludeTax);
            }
            if (category == 11) hotelSpecial = hotelSpecial.add(amount);
            else if (category == 110) hotelOverseasSpecial = hotelOverseasSpecial.add(amount);
        }

        public void addAccommodationFeeOrdinary(int category, BigDecimal amount, BigDecimal includeTax, BigDecimal excludeTax) {
            if (amount == null) return;
            accommodationFeeOrdinary = accommodationFeeOrdinary.add(amount);
            totalAccommodationFeeOrdinary = totalAccommodationFeeOrdinary.add(amount);
            if (includeTax != null) {
                accommodationFeeOrdinaryIncludeTax = accommodationFeeOrdinaryIncludeTax.add(includeTax);
                totalAccommodationFeeOrdinaryIncludeTax = totalAccommodationFeeOrdinaryIncludeTax.add(includeTax);
            }
            if (excludeTax != null) {
                accommodationFeeOrdinaryExcludeTax = accommodationFeeOrdinaryExcludeTax.add(excludeTax);
                totalAccommodationFeeOrdinaryExcludeTax = totalAccommodationFeeOrdinaryExcludeTax.add(excludeTax);
            }
            if (category == 11) hotelOrdinary = hotelOrdinary.add(amount);
            else if (category == 110) hotelOverseasOrdinary = hotelOverseasOrdinary.add(amount);
        }

        public void addManualPrice(BigDecimal amount) {
            if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
                this.manualPrice = this.manualPrice.add(amount);
                this.manualPriceTax = this.manualPriceTax.add(amount.multiply(new BigDecimal("0.06")).setScale(2, RoundingMode.HALF_UP));
            }
        }

        public void addAirportFee(BigDecimal amount) {
            if (amount != null) this.airportFee = this.airportFee.add(amount);
        }

        public void addFuelFee(BigDecimal amount) {
            if (amount != null) this.fuelFee = this.fuelFee.add(amount);
        }

        // // --- 修改点 1：addManualPrice ---
        // public void addManualPrice(BigDecimal amount) {
        //     if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
        //         this.manualPrice = this.manualPrice.add(amount);
        //         this.manualPriceTax = this.manualPriceTax.add(amount.multiply(new BigDecimal("0.06")).setScale(2, RoundingMode.HALF_UP));
        //
        //         // 【新增】将这笔费用也计入总的含税金额
        //         this.includeTaxAmount = this.includeTaxAmount.add(amount);
        //     }
        // }
        //
        // // --- 修改点 2：addAirportFee ---
        // public void addAirportFee(BigDecimal amount) {
        //     if (amount != null) {
        //         this.airportFee = this.airportFee.add(amount);
        //
        //         // 【新增】将这笔费用也计入总的含税金额
        //         this.includeTaxAmount = this.includeTaxAmount.add(amount);
        //     }
        // }
        //
        // // --- 修改点 3：addFuelFee ---
        // public void addFuelFee(BigDecimal amount) {
        //     if (amount != null) {
        //         this.fuelFee = this.fuelFee.add(amount);
        //
        //         // 【新增】将这笔费用也计入总的含税金额
        //         this.includeTaxAmount = this.includeTaxAmount.add(amount);
        //     }
        // }
        private void updateTrainTax() {
            trainTax = trainIncludeTax.subtract(trainExcludeTax).max(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
        }
        //</editor-fold>
    }

    public static void main(String[] args) {
        FBTJob2 job = new FBTJob2();
        try {
            System.out.println("======= 开始执行本地测试 =======");

            System.out.println("\n[步骤 1/3] 正在从API拉取并处理已结算账单...");
            Map<String, List<Map<String, Object>>> settledBillDetails = job.processSettledBills();
            System.out.println("拉取完成，共处理了 " + settledBillDetails.values().stream().mapToInt(List::size).sum() + " 条消费明细。");

            // --- 新增诊断逻辑 ---
            BigDecimal skippedAmount = BigDecimal.ZERO;
            int skippedCount = 0;
            for (List<Map<String, Object>> details : settledBillDetails.values()) {
                for (Map<String, Object> detail : details) {
                    JSONObject detailJson = new JSONObject(detail);
                    String projectCode = detailJson.getStr("project_code");
                    if (projectCode == null || projectCode.isEmpty()) {
                        skippedCount++;
                        BigDecimal includeTaxAmount = new BigDecimal(detailJson.getStr("include_tax_amount", "0"));
                        skippedAmount = skippedAmount.add(includeTaxAmount);
                    }
                }
            }
            System.out.println("skippedCount = " + skippedCount);
            System.out.println("skippedAmount = " + skippedAmount);
            // --- 诊断逻辑结束 ---
            System.out.println("\n[步骤 2/3] 正在按(项目, 事项)维度聚合数据...");
            Map<String, Map<String, ProjectInfo>> projectEventMap = job.organizeDataByProjectAndEvent(settledBillDetails);
            System.out.println("聚合完成，共得到 " + projectEventMap.size() + " 个项目。");
            // 214
            // job.saveProjectInfoToDatabase(projectEventMap);
            // --- 新增代码开始 ---
            System.out.println("\n[新增] 正在计算所有账单总金额...");
            BigDecimal totalAmountOfAllBills = BigDecimal.ZERO;
            for (Map<String, ProjectInfo> eventMap : projectEventMap.values()) {
                for (ProjectInfo project : eventMap.values()) {
                    // 我们使用参考含税总额(includeTaxAmount)作为每个聚合条目的总金额进行累加
                    totalAmountOfAllBills = totalAmountOfAllBills.add(project.getIncludeTaxAmount());
                }
            }
            System.out.println("=======================================================================");
            System.out.printf("【所有账单总金额（参考含税）】: %s%n", totalAmountOfAllBills.toPlainString());
            System.out.println("=======================================================================");
            // --- 新增代码结束 ---


            System.out.println("\n[步骤 3/3] 开始打印详细聚合结果...");
            System.out.println("=======================================================================");
            BigDecimal totalAmountFromPrinting = BigDecimal.ZERO;
            if (projectEventMap.isEmpty()) {
                System.out.println("未找到任何可供打印的项目数据。");
            } else {
                // --- 验证步骤 2: 初始化打印时累加的金额 ---

                int projectIndex = 1;

                // 外层循环：遍历每个项目
                for (Map.Entry<String, Map<String, ProjectInfo>> projectEntry : projectEventMap.entrySet()) {
                    System.out.printf("%n########################### 项目 %d ###########################%n", projectIndex++);
                    System.out.printf("【项目代码】: %s%n", projectEntry.getKey());
                    System.out.println("-----------------------------------------------------------------------");

                    int eventIndex = 1;
                    // 内层循环：遍历项目下的每个事项
                    for (Map.Entry<String, ProjectInfo> eventEntry : projectEntry.getValue().entrySet()) {
                        ProjectInfo info = eventEntry.getValue();

                        // --- 验证步骤 2.1: 在打印时累加金额 ---
                        totalAmountFromPrinting = totalAmountFromPrinting.add(info.getIncludeTaxAmount());

                        System.out.printf("  -> 事件 %d: 【事项名称】: %s (项目名: %s)%n", eventIndex++, eventEntry.getKey(), info.getProjectName());
                        System.out.println("     ...............................................................");

                        // 打印基本信息
                        System.out.println("     【基本信息】");
                        System.out.printf("       账单号: %s, 账期: %s-%s, 状态: %d%n",
                                info.getBillCode() != null ? info.getBillCode() : "N/A",
                                info.getBillStartTime(), info.getBillEndTime(), info.getBillStatus());
                        System.out.printf("       关联公司: %s%n", info.getRelatedCompaniesStr());

                        // 打印总金额
                        System.out.println("\n     【金额统计（明细）】");
                        printDetailAmount("       参考含税总额", info.getIncludeTaxAmount());
                        printDetailAmount("       参考不含税总额", info.getExcludeTaxAmount());
                        // 打印各类别金额
                        System.out.println("\n     【订单类别金额 (公司支付)】");
                        printDetailAmount("       国内机票 (7)", info.getAirTicketDomestic(), info.getAirTicketDomesticIncludeTax(), info.getAirTicketDomesticExcludeTax());
                        printDetailAmount("       国际机票 (40)", info.getAirTicketInternational(), info.getAirTicketInternationalIncludeTax(), info.getAirTicketInternationalExcludeTax());
                        printDetailAmount("       酒店 (11)", info.getHotel(), info.getHotelIncludeTax(), info.getHotelExcludeTax());
                        printDetailAmount("       海外酒店 (110)", info.getHotelOverseas(), info.getHotelOverseasIncludeTax(), info.getHotelOverseasExcludeTax());
                        printDetailAmount("       火车 (15)", info.getTrain(), info.getTrainIncludeTax(), info.getTrainExcludeTax());
                        printDetailAmount("       汽车票 (135)", info.getBus(), info.getBusIncludeTax(), info.getBusExcludeTax());
                        printDetailAmount("       用车 (3)", info.getCar(), info.getCarIncludeTax(), info.getCarExcludeTax());
                        printDetailAmount("       用餐 (60)", info.getMeal(), info.getMealIncludeTax(), info.getMealExcludeTax());
                        printDetailAmount("       外卖 (50)", info.getTakeaway(), info.getTakeawayIncludeTax(), info.getTakeawayExcludeTax());
                        printDetailAmount("       采购 (20)", info.getPurchase(), info.getPurchaseIncludeTax(), info.getPurchaseExcludeTax());
                        printDetailAmount("       闪送 (130)", info.getFlash(), info.getFlashIncludeTax(), info.getFlashExcludeTax());
                        printDetailAmount("       快递 (131)", info.getExpress(), info.getExpressIncludeTax(), info.getExpressExcludeTax());
                        printDetailAmount("       货运 (150)", info.getFreight(), info.getFreightIncludeTax(), info.getFreightExcludeTax());
                        printDetailAmount("       虚拟卡 (126)", info.getVirtualCard(), info.getVirtualCardIncludeTax(), info.getVirtualCardExcludeTax());
                        printDetailAmount("       对公付款 (128)", info.getCorporatePayment(), info.getCorporatePaymentIncludeTax(), info.getCorporatePaymentExcludeTax());
                        printDetailAmount("       增值服务 (913)", info.getValueAddedService(), info.getValueAddedServiceIncludeTax(), info.getValueAddedServiceExcludeTax());
                        printDetailAmount("       其他订单 (911)", info.getOther(), info.getOtherIncludeTax(), info.getOtherExcludeTax());

                        // 打印住宿费相关
                        System.out.println("\n     【住宿费详细统计 (酒店+海外酒店)】");
                        printDetailAmount("       住宿费总额", info.getAccommodationFee());
                        printDetailAmount("       住宿费总额 (含税)", info.getAccommodationFeeIncludeTax());
                        printDetailAmount("       住宿费总额 (不含税)", info.getAccommodationFeeExcludeTax());
                        System.out.println("       --- 专票部分 ---");
                        printDetailAmount("         专票总额", info.getAccommodationFeeSpecial());
                        printDetailAmount("         专票总额 (含税)", info.getAccommodationFeeSpecialIncludeTax());
                        printDetailAmount("         专票总额 (不含税)", info.getAccommodationFeeSpecialExcludeTax());
                        System.out.println("       --- 普票部分 ---");
                        printDetailAmount("         普票总额", info.getAccommodationFeeOrdinary());
                        printDetailAmount("         普票总额 (含税)", info.getAccommodationFeeOrdinaryIncludeTax());
                        printDetailAmount("         普票总额 (不含税)", info.getAccommodationFeeOrdinaryExcludeTax());

                        // 打印其他费用
                        System.out.println("\n     【其他费用统计】");
                        printDetailAmount("       代打火车服务费", info.getManualPrice());
                        printDetailAmount("       代打火车服务费税额(6%)", info.getManualPriceTax());
                        printDetailAmount("       火车票税额(差额)", info.getTrainTax());
                        printDetailAmount("       机票-机建费", info.getAirportFee());
                        printDetailAmount("       机票-燃油费", info.getFuelFee());
                        printDetailAmount("       机票总含税额(票价+机建+燃油)", info.getAirTicketTotalIncludeTax());
                        printDetailAmount("       机票总税额(差额)", info.getAirTicketTax());
                        System.out.println();
                    }
                }
            }
            System.out.println("=======================================================================");
            System.out.println("打印完成。");

            // --- 验证步骤 3: 对比两个总金额 ---
            System.out.println("\n====================== 金额交叉验证报告 ======================");
            System.out.println("预期 = " + totalAmountOfAllBills);
            System.out.println("实际 = " + totalAmountFromPrinting);

        } catch (Exception e) {
            System.err.println("本地测试过程中发生严重错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 辅助方法，用于打印非零金额。
     *
     * @param label  标签
     * @param amount 金额
     */
    private static void printDetailAmount(String label, BigDecimal amount) {
        if (amount != null && amount.compareTo(BigDecimal.ZERO) != 0) {
            System.out.printf("%-35s: %s%n", label, amount.toPlainString());
        }
    }

    /**
     * 辅助方法的重载版本，用于打印类别金额及其含税/不含税金额。
     *
     * @param label            标签
     * @param totalAmount      类别总金额
     * @param includeTaxAmount 含税金额
     * @param excludeTaxAmount 不含税金额
     */
    private static void printDetailAmount(String label, BigDecimal totalAmount, BigDecimal includeTaxAmount, BigDecimal excludeTaxAmount) {
        if (totalAmount != null && totalAmount.compareTo(BigDecimal.ZERO) != 0) {
            System.out.printf("%-35s: %s (含税: %s, 不含税: %s)%n",
                    label,
                    totalAmount.toPlainString(),
                    includeTaxAmount.toPlainString(),
                    excludeTaxAmount.toPlainString());
        }
    }
}