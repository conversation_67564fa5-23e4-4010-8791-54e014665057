package weaver.interfaces.workflow.action.cyitce.job.fbt.service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/6 15:14
 * @describe 数藤云项目详细信息实体类
 */
public class STYProjectDetailInfo {

    // 通用字段
    private String id;        // id(二级项目名称)

    // 第一个查询：uf_styjsejxmlx表的字段
    private String ejxmbh;    // 二级项目编号
    private String yjbm;      // 一级部门
    private String ejbm;      // 二级部门
    private String sjbm;      // 三级部门
    private String yjxmmc;    // 一级项目名称
    private String yjxmbh;    // 一级项目编号

    // GC类第一个查询：uf_ejxmlx表的字段
    private String yjbmfq;    // 一级部门
    private String ejbmfq;    // 二级部门
    private String sjbmfq;    // 三级部门
    private String ejxmnbbh;  // 二级项目编号
    private String ejxmnbmc;  // 二级项目名称
    private String jsms;      // 计税模式 0 增值税 1 简易计税

    // 第二个查询：uf_styjs_xmlxsq表的字段
    private String sqrbm;     // 费用承担部门
    private String sfsggxm;   // 是否是公共项目（0是 1否）
    private String xmlx;      // 项目类型

    // JL类第二个查询：uf_jl_yjxmlxsqjmbd表的字段
    private String gclx;      // 项目类型

    // GC类第二个查询：uf_yjxmlxjm表的字段
    private String bmcx;      // 费用承担部门

    // 第三个查询：view_bmjz_cw表的字段
    private String cwYjbm;    // 财务一级部门
    private String cwEjbm;    // 财务二级部门
    private String cwSjbm;    // 财务三级部门

    // 项目类型的描述信息
    private String xmlxDesc;  // 项目类型描述

    // 是否是公共项目的描述信息
    private String sfsggxmDesc; // 是否是公共项目描述

    // 计税模式的描述信息
    private String jsmsDesc;  // 计税模式描述

    // 监理公司项目类型的描述信息
    private String gclxDesc;  // 监理公司项目类型描述
    private Map<String, String> map; // 存放对应金额信息

    public void setXmlxDesc(String xmlxDesc) {
        this.xmlxDesc = xmlxDesc;
    }

    public void setSfsggxmDesc(String sfsggxmDesc) {
        this.sfsggxmDesc = sfsggxmDesc;
    }

    public void setJsmsDesc(String jsmsDesc) {
        this.jsmsDesc = jsmsDesc;
    }

    public void setGclxDesc(String gclxDesc) {
        this.gclxDesc = gclxDesc;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    public STYProjectDetailInfo() {
    }

    /**
     * 根据xmlx值获取项目类型描述
     *
     * @return 项目类型描述
     */
    public String getXmlxDescription() {
        if (xmlx == null) {
            return "";
        }

        switch (xmlx) {
            case "0":
                return "软件技术服务项目";
            case "1":
                return "软件产品销售项目";
            case "2":
                return "信息技术咨询项目";
            case "3":
                return "云服务和云产品销售项目";
            case "4":
                return "系统集成项目";
            case "5":
                return "其他项目";
            case "6":
                return "研发项目";
            case "7":
                return "公共项目";
            default:
                return "未知项目类型";
        }
    }

    /**
     * 根据xmlx值获取华来公司项目类型描述
     *
     * @return 华来公司项目类型描述
     */
    public String getHLXmlxDescription() {
        if (xmlx == null) {
            return "";
        }

        switch (xmlx) {
            case "0":
                return "工程项目";
            case "1":
                return "服务项目";
            case "2":
                return "销售项目";
            case "3":
                return "其他";
            case "4":
                return "公共项目";
            case "5":
                return "IT软件开发项目";
            default:
                return "未知项目类型";
        }
    }

    /**
     * 根据gclx值获取监理公司项目类型描述
     *
     * @return 监理公司项目类型描述
     */
    public String getJLGclxDescription() {
        if (gclx == null) {
            return "";
        }

        switch (gclx) {
            case "0":
                return "工程项目";
            case "1":
                return "服务项目";
            case "2":
                return "销售项目";
            case "3":
                return "其他";
            case "4":
                return "公共项目";
            case "5":
                return "研发项目";
            default:
                return "未知项目类型";
        }
    }

    /**
     * 根据sfsggxm值获取是否是公共项目描述
     *
     * @return 是否是公共项目描述
     */
    public String getSfsggxmDescription() {
        if (sfsggxm == null) {
            return "";
        }

        return "0".equals(sfsggxm) ? "是" : "否";
    }

    /**
     * 根据jsms值获取计税模式描述
     *
     * @return 计税模式描述
     */
    public String getJsmsDescription() {
        if (jsms == null) {
            return "";
        }

        return "0".equals(jsms) ? "增值税" : "简易计税";
    }

    /**
     * 设置华来公司的项目类型
     *
     * @param xmlx 项目类型代码
     */
    public void setHLXmlx(String xmlx) {
        this.xmlx = xmlx;
        this.xmlxDesc = getHLXmlxDescription();
    }

    /**
     * 设置监理公司的项目类型
     *
     * @param gclx 项目类型代码
     */
    public void setJLGclx(String gclx) {
        this.gclx = gclx;
        this.gclxDesc = getJLGclxDescription();
    }

    // Getters and Setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEjxmbh() {
        return ejxmbh;
    }

    public void setEjxmbh(String ejxmbh) {
        this.ejxmbh = ejxmbh;
    }

    public String getYjbm() {
        return yjbm;
    }

    public void setYjbm(String yjbm) {
        this.yjbm = yjbm;
    }

    public String getEjbm() {
        return ejbm;
    }

    public void setEjbm(String ejbm) {
        this.ejbm = ejbm;
    }

    public String getSjbm() {
        return sjbm;
    }

    public void setSjbm(String sjbm) {
        this.sjbm = sjbm;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getYjxmbh() {
        return yjxmbh;
    }

    public void setYjxmbh(String yjxmbh) {
        this.yjxmbh = yjxmbh;
    }

    public String getSqrbm() {
        return sqrbm;
    }

    public void setSqrbm(String sqrbm) {
        this.sqrbm = sqrbm;
    }

    public String getSfsggxm() {
        return sfsggxm;
    }

    public void setSfsggxm(String sfsggxm) {
        this.sfsggxm = sfsggxm;
        this.sfsggxmDesc = getSfsggxmDescription();
    }

    public String getXmlx() {
        return xmlx;
    }

    public void setXmlx(String xmlx) {
        this.xmlx = xmlx;
        this.xmlxDesc = getXmlxDescription();
    }

    public String getGclx() {
        return gclx;
    }

    public void setGclx(String gclx) {
        this.gclx = gclx;
        this.gclxDesc = getJLGclxDescription();
    }

    public String getGclxDesc() {
        return gclxDesc;
    }

    public String getCwYjbm() {
        return cwYjbm;
    }

    public void setCwYjbm(String cwYjbm) {
        this.cwYjbm = cwYjbm;
    }

    public String getCwEjbm() {
        return cwEjbm;
    }

    public void setCwEjbm(String cwEjbm) {
        this.cwEjbm = cwEjbm;
    }

    public String getCwSjbm() {
        return cwSjbm;
    }

    public void setCwSjbm(String cwSjbm) {
        this.cwSjbm = cwSjbm;
    }

    public String getXmlxDesc() {
        return xmlxDesc;
    }

    public String getSfsggxmDesc() {
        return sfsggxmDesc;
    }

    public String getYjbmfq() {
        return yjbmfq;
    }

    public void setYjbmfq(String yjbmfq) {
        this.yjbmfq = yjbmfq;
    }

    public String getEjbmfq() {
        return ejbmfq;
    }

    public void setEjbmfq(String ejbmfq) {
        this.ejbmfq = ejbmfq;
    }

    public String getSjbmfq() {
        return sjbmfq;
    }

    public void setSjbmfq(String sjbmfq) {
        this.sjbmfq = sjbmfq;
    }

    public String getEjxmnbbh() {
        return ejxmnbbh;
    }

    public void setEjxmnbbh(String ejxmnbbh) {
        this.ejxmnbbh = ejxmnbbh;
    }

    public String getEjxmnbmc() {
        return ejxmnbmc;
    }

    public void setEjxmnbmc(String ejxmnbmc) {
        this.ejxmnbmc = ejxmnbmc;
    }

    public String getJsms() {
        return jsms;
    }

    public void setJsms(String jsms) {
        this.jsms = jsms;
        this.jsmsDesc = getJsmsDescription();
    }

    public String getJsmsDesc() {
        return jsmsDesc;
    }

    public String getBmcx() {
        return bmcx;
    }

    public void setBmcx(String bmcx) {
        this.bmcx = bmcx;
    }

    @Override
    public String toString() {
        return "STYProjectDetailInfo{" +
                "id='" + id + '\'' +
                ", ejxmbh='" + ejxmbh + '\'' +
                ", yjbm='" + yjbm + '\'' +
                ", ejbm='" + ejbm + '\'' +
                ", sjbm='" + sjbm + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", yjxmbh='" + yjxmbh + '\'' +
                ", yjbmfq='" + yjbmfq + '\'' +
                ", ejbmfq='" + ejbmfq + '\'' +
                ", sjbmfq='" + sjbmfq + '\'' +
                ", ejxmnbbh='" + ejxmnbbh + '\'' +
                ", ejxmnbmc='" + ejxmnbmc + '\'' +
                ", jsms='" + jsms + '\'' +
                ", sqrbm='" + sqrbm + '\'' +
                ", sfsggxm='" + sfsggxm + '\'' +
                ", xmlx='" + xmlx + '\'' +
                ", gclx='" + gclx + '\'' +
                ", bmcx='" + bmcx + '\'' +
                ", cwYjbm='" + cwYjbm + '\'' +
                ", cwEjbm='" + cwEjbm + '\'' +
                ", cwSjbm='" + cwSjbm + '\'' +
                ", xmlxDesc='" + xmlxDesc + '\'' +
                ", sfsggxmDesc='" + sfsggxmDesc + '\'' +
                ", jsmsDesc='" + jsmsDesc + '\'' +
                ", gclxDesc='" + gclxDesc + '\'' +
                ", map=" + map +
                '}';
    }
}