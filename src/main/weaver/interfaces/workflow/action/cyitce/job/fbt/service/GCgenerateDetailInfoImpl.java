package weaver.interfaces.workflow.action.cyitce.job.fbt.service;

import weaver.general.BaseBean;
import weaver.mobile.webservices.workflow.WorkflowDetailTableInfo;
import weaver.mobile.webservices.workflow.WorkflowRequestInfo;
import weaver.mobile.webservices.workflow.WorkflowServiceImpl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/6 15:14
 * @describe 工程
 */
public class GCgenerateDetailInfoImpl implements generateDetailInfo {
    BaseBean log = new BaseBean();
    // 数据库连接参数
    private static final String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
    private static final String URL = "*********************************************************";
    private static final String USERNAME = "lijunhong";
    private static final String PASSWORD = "Cyitce@0106";

    /**
     * 执行SQL查询并返回结果Map
     *
     * @param sql    SQL查询语句
     * @param prefix 结果Map的key前缀
     * @param params 查询参数数组
     * @return 查询结果Map
     */
    private Map<String, String> executeQueryWithParams(String sql, String prefix, Object... params) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, String> resultMap = new HashMap<>();

        try {
            // 获取数据库连接
            conn = getConnection();
            if (conn == null) {
                log.writeLog("获取数据库连接失败");
                return resultMap;
            }

            // 准备查询语句
            ps = conn.prepareStatement(sql);

            // 设置参数
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    ps.setObject(i + 1, params[i]);
                }
            }

            // 执行查询
            rs = ps.executeQuery();

            // 处理结果集
            if (rs.next()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // 获取所有列的值，添加前缀
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    String columnValue = rs.getString(i);
                    // 使用前缀区分不同查询的结果
                    resultMap.put(prefix + columnName, columnValue != null ? columnValue : "");
                }
                log.writeLog("查询成功，结果已存入map，前缀为: " + prefix);
            } else {
                log.writeLog("未找到匹配记录");
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.writeLog("查询出错: " + e.getMessage());
        } finally {
            // 关闭资源
            closeResources(rs, ps, conn);
        }

        return resultMap;
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     */
    private Connection getConnection() {
        try {
            // 加载驱动
            Class.forName(DRIVER);
            // 建立连接
            return DriverManager.getConnection(URL, USERNAME, PASSWORD);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.writeLog("数据库驱动加载失败: " + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            log.writeLog("数据库连接失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 关闭数据库资源
     *
     * @param rs   结果集
     * @param ps   预编译语句
     * @param conn 数据库连接
     */
    private void closeResources(ResultSet rs, PreparedStatement ps, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将查询结果转换为STYProjectDetailInfo对象
     *
     * @param resultMap 查询结果Map
     * @return STYProjectDetailInfo对象
     */
    private STYProjectDetailInfo convertToProjectDetailInfo(Map<String, String> resultMap) {
        STYProjectDetailInfo detailInfo = new STYProjectDetailInfo();

        // 设置id字段
        detailInfo.setId(resultMap.get("1_id"));

        // 设置第一个查询的字段 (uf_ejxmlx表)
        detailInfo.setYjbmfq(resultMap.get("1_yjbmfq"));
        detailInfo.setEjbmfq(resultMap.get("1_ejbmfq"));
        detailInfo.setSjbmfq(resultMap.get("1_sjbmfq"));
        detailInfo.setYjxmmc(resultMap.get("1_yjxmmc"));
        detailInfo.setYjxmbh(resultMap.get("1_yjxmbh"));
        detailInfo.setEjxmnbbh(resultMap.get("1_ejxmnbbh"));
        detailInfo.setEjxmnbmc(resultMap.get("1_ejxmnbmc"));
        detailInfo.setJsms(resultMap.get("1_jsms"));

        // 设置第二个查询的字段 (uf_yjxmlxjm表)
        detailInfo.setBmcx(resultMap.get("2_bmcx"));
        detailInfo.setSfsggxm(resultMap.get("2_sfsggxm"));

        // 设置第三个查询的字段 (view_bmjz_cw表)
        detailInfo.setCwYjbm(resultMap.get("3_yjbm"));
        detailInfo.setCwEjbm(resultMap.get("3_ejbm"));
        detailInfo.setCwSjbm(resultMap.get("3_sjbm"));

        return detailInfo;
    }

    public STYProjectDetailInfo processProjectDetail(Map<String, String> map) {
        Map<String, String> finalResult = new HashMap<>();

        // 第一个查询：查询uf_ejxmlx表
        String firstSql = "select id,yjbmfq,ejbmfq,sjbmfq,yjxmmc,yjxmbh,ejxmnbbh,ejxmnbmc,jsms from uf_ejxmlx where ejxmnbbh=?";
        String ejxmnbbh = map.get("project_code");

        if (ejxmnbbh == null || ejxmnbbh.isEmpty()) {
            log.writeLog("参数code为空，无法执行第一个查询");
            return null;
        }

        Map<String, String> firstResult = executeQueryWithParams(firstSql, "1_", ejxmnbbh);
        finalResult.putAll(firstResult);

        if (firstResult.isEmpty()) {
            log.writeLog("第一个查询未返回结果，无法继续执行");
            return null;
        }

        // 第二个查询：使用第一个查询结果中的ejxmnbbh作为id参数查询uf_yjxmlxjm表
        String ejxmnbbhValue = firstResult.get("1_ejxmnbbh");
        if (ejxmnbbhValue == null || ejxmnbbhValue.isEmpty()) {
            log.writeLog("第一个查询未返回ejxmnbbh字段值，无法执行第二个查询");
            return null;
        }

        String secondSql = "select bmcx,sfsggxm from uf_yjxmlxjm where id=?";
        Map<String, String> secondResult = executeQueryWithParams(secondSql, "2_", ejxmnbbhValue);
        finalResult.putAll(secondResult);

        if (secondResult.isEmpty()) {
            log.writeLog("第二个查询未返回结果，无法继续执行");
            return null;
        }

        // 第三个查询：使用第二个查询结果中的bmcx作为sqbm参数查询view_bmjz_cw表
        String bmcx = secondResult.get("2_bmcx");
        if (bmcx == null || bmcx.isEmpty()) {
            log.writeLog("第二个查询未返回bmcx字段值，无法执行第三个查询");
            return null;
        }

        String thirdSql = "select yjbm,ejbm,sjbm from view_bmjz_cw where sqbm=?";
        Map<String, String> thirdResult = executeQueryWithParams(thirdSql, "3_", bmcx);
        finalResult.putAll(thirdResult);

        // 将查询结果转换为STYProjectDetailInfo对象
        STYProjectDetailInfo projectDetailInfo = convertToProjectDetailInfo(finalResult);

        // 打印项目详细信息
        log.writeLog("项目详细信息: " + projectDetailInfo);

        return projectDetailInfo;
    }


    @Override
    public List<STYProjectDetailInfo> generateDetailInfo(List<Map<String, String>> dataList) {
        List<STYProjectDetailInfo> resultList = new ArrayList<>();

        if (dataList == null || dataList.isEmpty()) {
            log.writeLog("输入数据列表为空，无法执行查询");
            return resultList;
        }

        // 遍历处理每个项目
        for (Map<String, String> map : dataList) {
            STYProjectDetailInfo projectDetail = processProjectDetail(map);
            if (projectDetail != null) {
                resultList.add(projectDetail);
            }
        }

        log.writeLog("共处理 " + resultList.size() + " 个项目详细信息");
        return resultList;
    }

    public void generateFlowData(List<STYProjectDetailInfo> list, String userId, String gszt, int tableIndex) {
        String requestName = FlowUtil.generateExpenseTitle(userId);
        String userName = FlowUtil.userid(userId);
        Map<String, String> fieldMap = new HashMap<>();
        // 获取当前日期并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());
        fieldMap.put("sqr", userName);// 申请人
        fieldMap.put("sqrq", currentDate);// 申请日期
        fieldMap.put("zdrq", currentDate);// 申请日期
        fieldMap.put("zdr", userName);// 申请日期
        fieldMap.put("gszt", gszt);// 公司主体
        Map<String, Boolean[]> viewEditMap = new HashMap<>();
        viewEditMap.put("gszt", new Boolean[]{true, true});
        List<Map<String, String>> detailTableData = new ArrayList<>();

        // 遍历项目列表，将每个项目的属性添加到明细表中
        for (STYProjectDetailInfo project : list) {
            Map<String, String> rowData = new HashMap<>();

            // 添加项目基本信息
            rowData.put("stejxmbh", project.getEjxmbh());  // 二级项目编号
            rowData.put("stejxmmc", project.getId());  // 二级项目名称
            rowData.put("styjxmmc", project.getYjxmmc());  // 一级项目名称
            rowData.put("styjxmbh", project.getYjxmbh());  // 一级项目编号

            // 添加部门信息
            rowData.put("yjbm", project.getYjbm());      // 一级部门
            rowData.put("ejbm", project.getEjbm());      // 二级部门
            rowData.put("sjbm", project.getSjbm());      // 三级部门

            // 添加财务部门信息
            rowData.put("cwyjbm", project.getCwYjbm());  // 财务一级部门
            rowData.put("cwejbm", project.getCwEjbm());  // 财务二级部门
            rowData.put("cwsjbm", project.getCwSjbm());  // 财务三级部门

            // 添加项目类型信息
            rowData.put("xmlx", project.getXmlx());      // 项目类型
            rowData.put("xmlxDesc", project.getXmlxDesc()); // 项目类型描述

            // 添加其他信息
            rowData.put("fycdbm", project.getSqrbm());    // 费用承担部门
            rowData.put("sfsggxm", project.getSfsggxm()); // 是否是公共项目 0是 1否
            rowData.put("sfsggxmDesc", project.getSfsggxmDesc()); // 是否是公共项目描述

            // 添加到明细表数据列表
            detailTableData.add(rowData);
        }
        Map<String, Boolean[]> detailViewEditMap = new HashMap<>();
        // 创建工作流
        WorkflowRequestInfo workflowRequestInfo = FlowUtil.createWorkflowWithDetail(userId, "0", requestName, "2108", "消费账单月度报销流程",
                fieldMap, viewEditMap, detailTableData, detailViewEditMap, tableIndex);
        String requestId = new WorkflowServiceImpl().doCreateWorkflowRequest(workflowRequestInfo, Integer.parseInt(userId), "", "");
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("code", "STYHS-EJXM20240029");

        // new GCgenerateDetailInfoImpl().generateDetailInfo(map);
    }
}
