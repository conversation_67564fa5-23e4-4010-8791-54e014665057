package weaver.interfaces.workflow.action.cyitce.job.fbt;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 9:32
 * @describe
 */
public class FBTJob extends BaseCronJob {
    BaseBean log = new BaseBean();

    @Override
    public void execute() {
        Map<String, List<Map<String, Object>>> settledBillDetails = null;
        try {
            settledBillDetails = processSettledBills();
            log.writeLog("结算中账单处理完成，获取到的companyId数量: " + settledBillDetails.size());
            // 按项目维度整合数据
            Map<String, ProjectInfo> projectInfoMap = organizeDataByProject(settledBillDetails);
            saveProjectInfoToDatabase(projectInfoMap);
            log.writeLog("结算中账单处理完成，");
        } catch (IOException e) {
            log.writeLog("结算中账单处理失败，");
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取认证Token
     *
     * @return token字符串
     * @throws IOException 网络请求异常
     */
    public String getToken() throws IOException {
        // API地址，使用HTTPS协议
        String apiUrl = "https://openapi.fenbeitong.com/openapi/auth/getToken";

        // 创建URL对象
        URL url = new URL(apiUrl);

        // 创建HTTP连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法为POST
        connection.setRequestMethod("POST");

        // 设置Content-Type
        connection.setRequestProperty("Content-Type", "application/json");

        // 允许输出流
        connection.setDoOutput(true);

        // 准备请求参数
        String jsonInputString = "{"
                + "\"app_id\": \"67d7f3243d83426b2c5cc4b0\","
                + "\"app_key\": \"6801e92efe12446af1d556ad\""
                + "}";

        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 获取响应
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            // 解析JSON响应
            String responseStr = response.toString();
            System.out.println("响应内容: " + responseStr);

            // 使用Hutool解析JSON
            JSONObject jsonObj = JSONUtil.parseObj(responseStr);
            int code = jsonObj.getInt("code", -1);

            if (code == 0) {
                // 从data字段获取token
                String token = jsonObj.getStr("data");
                return token;
            } else {
                String msg = jsonObj.getStr("msg", "未知错误");
                throw new IOException("获取Token失败，错误码: " + code + ", 错误信息: " + msg);
            }
        } else {
            throw new IOException("获取Token失败，HTTP响应码: " + responseCode);
        }
    }

    /**
     * 查询商务消费账单详情，获取所有数据
     *
     * @param billCode 分贝通账单编号
     * @return 以companyId为key，列表为value的所有账单详情数据Map
     * @throws IOException 网络请求异常
     */
    public Map<String, List<Map<String, Object>>> getBillBusinessDetails(String billCode) throws IOException {
        // 先获取token
        String token = getToken();
        System.out.println("获取到的Token: " + token);

        // 存储所有账单详情的Map，key为companyId，value为该companyId对应的所有数据列表
        Map<String, List<Map<String, Object>>> allDetailsMap = new HashMap<>();

        // 记录总共处理的记录数
        int totalProcessedRecords = 0;

        // 初始化分页参数
        int pageIndex = 1;
        int pageSize = 100; // 设置为最大值100
        int totalPages = 1;
        boolean hasMorePages = true;

        // 循环获取所有页的数据
        while (hasMorePages) {
            // 获取当前页的数据
            String responseStr = requestBillBusinessDetails(token, billCode, pageIndex, pageSize);

            // 打印部分响应内容用于调试
            String debugResponse = responseStr.length() > 500 ?
                    responseStr.substring(0, 500) + "..." : responseStr;
            System.out.println("第" + pageIndex + "页响应(部分): " + debugResponse);

            try {
                // 使用Hutool解析JSON
                JSONObject jsonObj = JSONUtil.parseObj(responseStr);
                int code = jsonObj.getInt("code", -1);

                if (code != 0) {
                    String msg = jsonObj.getStr("msg", "未知错误");
                    throw new IOException("查询账单详情失败，错误码: " + code + ", 错误信息: " + msg);
                }

                // 获取data部分
                JSONObject dataObj = jsonObj.getJSONObject("data");
                if (dataObj == null) {
                    System.out.println("未找到data部分或data为空");
                    break;
                }

                System.out.println("提取到的data部分: " + dataObj.toString().substring(0, Math.min(100, dataObj.toString().length())) + "...");

                // 获取分页信息
                int totalCount = dataObj.getInt("total_count", 0);
                totalPages = dataObj.getInt("total_pages", 1);
                int currentPageIndex = dataObj.getInt("page_index", 1);
                int currentPageSize = dataObj.getInt("page_size", 100);

                System.out.println("分页信息: 总记录数=" + totalCount + ", 总页数=" + totalPages +
                        ", 当前页=" + currentPageIndex + ", 每页大小=" + currentPageSize);

                // 获取details数组
                if (dataObj.containsKey("details")) {
                    JSONArray detailsArray = dataObj.getJSONArray("details");
                    int pageRecordCount = detailsArray.size();
                    System.out.println("从data中解析到的详情数量: " + pageRecordCount);
                    totalProcessedRecords += pageRecordCount;

                    // 遍历details数组
                    for (int i = 0; i < detailsArray.size(); i++) {
                        JSONObject detailObj = detailsArray.getJSONObject(i);

                        // 打印第一条详情数据用于调试
                        if (i == 0) {
                            System.out.println("第一条详情示例: " + detailObj.toString().substring(0, Math.min(100, detailObj.toString().length())) + "...");
                        }

                        // 将JSONObject转换为Map
                        Map<String, Object> detailMap = jsonObjToMap(detailObj);

                        // 提取companyId作为key
                        String companyId = extractCompanyId(detailObj);

                        if (companyId != null && !companyId.isEmpty()) {
                            // 检查是否已存在相同的companyId
                            if (allDetailsMap.containsKey(companyId)) {
                                // 已存在该companyId，将当前数据添加到对应的列表中
                                allDetailsMap.get(companyId).add(detailMap);
                                System.out.println("向已存在的companyId: " + companyId + " 添加数据，当前数据量: " + allDetailsMap.get(companyId).size());
                            } else {
                                // 不存在该companyId，创建新列表并添加数据
                                List<Map<String, Object>> detailsList = new ArrayList<>();
                                detailsList.add(detailMap);
                                allDetailsMap.put(companyId, detailsList);
                                System.out.println("添加新的companyId: " + companyId);
                            }
                        } else {
                            // 如果没有找到companyId，使用order_id作为备用key
                            String orderId = detailObj.getStr("order_id", "");
                            String backupKey = orderId != null && !orderId.isEmpty() ? "order_" + orderId : "index_" + pageIndex + "_" + i;

                            if (allDetailsMap.containsKey(backupKey)) {
                                allDetailsMap.get(backupKey).add(detailMap);
                                System.out.println("向已存在的备用key: " + backupKey + " 添加数据");
                            } else {
                                List<Map<String, Object>> detailsList = new ArrayList<>();
                                detailsList.add(detailMap);
                                allDetailsMap.put(backupKey, detailsList);
                                System.out.println("使用备用key添加数据: " + backupKey);
                            }
                        }
                    }
                } else {
                    System.out.println("data中未找到details数组");
                }

                // 判断是否还有下一页
                hasMorePages = pageIndex < totalPages;

            } catch (Exception e) {
                System.out.println("解析第" + pageIndex + "页数据时出错: " + e.getMessage());
                e.printStackTrace();
                // 发生异常时终止循环
                hasMorePages = false;
            }

            // 下一页
            pageIndex++;
        }

        System.out.println("总共处理记录数: " + totalProcessedRecords);
        System.out.println("最终获取到的companyId数量: " + allDetailsMap.size());

        // 打印每个companyId对应的数据量
        for (Map.Entry<String, List<Map<String, Object>>> entry : allDetailsMap.entrySet()) {
            System.out.println("CompanyId: " + entry.getKey() + ", 数据量: " + entry.getValue().size());
        }

        return allDetailsMap;
    }

    /**
     * 从详情对象中提取companyId
     *
     * @param detailObj 详情对象
     * @return companyId，如果未找到则返回null
     */
    private String extractCompanyId(JSONObject detailObj) {
        try {
            // 检查是否有cost_attributions属性
            if (detailObj.containsKey("cost_attributions")) {
                JSONArray costAttributions = detailObj.getJSONArray("cost_attributions");

                // 遍历cost_attributions数组，查找type为2的项
                for (int i = 0; i < costAttributions.size(); i++) {
                    JSONObject attribution = costAttributions.getJSONObject(i);
                    if (attribution.getInt("type", 0) == 2) {
                        // 找到type为2的项，获取其details属性
                        if (attribution.containsKey("details")) {
                            JSONArray details = attribution.getJSONArray("details");

                            // 遍历details数组
                            for (int j = 0; j < details.size(); j++) {
                                JSONObject detail = details.getJSONObject(j);

                                // 检查是否有custom_fields属性
                                if (detail.containsKey("custom_fields")) {
                                    JSONArray customFields = detail.getJSONArray("custom_fields");

                                    // 遍历custom_fields数组，查找title为companyId的项
                                    for (int k = 0; k < customFields.size(); k++) {
                                        JSONObject field = customFields.getJSONObject(k);
                                        if ("companyId".equals(field.getStr("title"))) {
                                            // 找到title为companyId的项，返回其detail值
                                            return field.getStr("detail");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("提取companyId时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 将JSONObject转换为Map
     *
     * @param jsonObj JSONObject对象
     * @return Map对象
     */
    private Map<String, Object> jsonObjToMap(JSONObject jsonObj) {
        Map<String, Object> map = new HashMap<>();
        // 正确使用keySet()遍历
        for (Object key : jsonObj.keySet()) {
            Object value = jsonObj.get(key);
            // 根据值的类型进行适当的转换
            if (value instanceof JSONObject) {
                map.put(String.valueOf(key), jsonObjToMap((JSONObject) value));
            } else if (value instanceof JSONArray) {
                map.put(String.valueOf(key), jsonArrayToList((JSONArray) value));
            } else {
                // 对于基本类型值，直接放入map
                map.put(String.valueOf(key), value);
            }
        }
        return map;
    }

    /**
     * 将JSONArray转换为List
     *
     * @param jsonArray JSONArray对象
     * @return List对象
     */
    private List<Object> jsonArrayToList(JSONArray jsonArray) {
        List<Object> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Object value = jsonArray.get(i);
            if (value instanceof JSONObject) {
                list.add(jsonObjToMap((JSONObject) value));
            } else if (value instanceof JSONArray) {
                list.add(jsonArrayToList((JSONArray) value));
            } else {
                list.add(value);
            }
        }
        return list;
    }

    /**
     * 发送查询商务消费账单详情请求
     *
     * @param token     认证token
     * @param billCode  分贝通账单编号
     * @param pageIndex 页码
     * @param pageSize  每页条数
     * @return 响应字符串
     * @throws IOException 网络请求异常
     */
    private String requestBillBusinessDetails(String token, String billCode, int pageIndex, int pageSize) throws IOException {
        // API地址
        String apiUrl = "https://openapi.fenbeitong.com/openapi/bill/business/v1/detail";

        // 创建URL对象
        URL url = new URL(apiUrl);

        // 创建HTTP连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法为POST
        connection.setRequestMethod("POST");

        // 设置请求头
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);

        // 允许输出流
        connection.setDoOutput(true);

        // 准备请求参数
        String jsonInputString = "{"
                + "\"bill_code\": \"" + billCode + "\","
                + "\"page_index\": " + pageIndex + ","
                + "\"page_size\": " + pageSize
                + "}";

        System.out.println("发送请求: " + jsonInputString);

        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 获取响应
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            return response.toString();
        } else {
            throw new IOException("查询账单详情失败，HTTP响应码: " + responseCode);
        }
    }

    /**
     * 查询商务消费账单编号信息，自动根据当前时间设置开始和结束时间
     * 如果当前时间是2025年6月，则开始时间为202505，结束时间为202506
     *
     * @param state     账单状态，1：已出账单 2：已核对（已弃用） 3：结算中 4：已结算 5：未出账单
     * @param pageIndex 页码，默认值1
     * @param pageSize  每页条数，默认20，最大100
     * @return 账单编号信息列表
     * @throws IOException 网络请求异常
     */
    public JSONObject getBillBusinessList(int state, int pageIndex, int pageSize) throws IOException {
        // 获取当前时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int year = calendar.get(java.util.Calendar.YEAR);
        // 月份从0开始，所以需要加1
        int month = calendar.get(java.util.Calendar.MONTH) + 1;

        // 计算上个月和当前月的格式化字符串
        String endTimeStr = String.format("%04d%02d", year, month);

        // 计算上个月
        calendar.add(java.util.Calendar.MONTH, -1);
        int lastMonth = calendar.get(java.util.Calendar.MONTH) + 1;
        int lastMonthYear = calendar.get(java.util.Calendar.YEAR);
        String startTimeStr = String.format("%04d%02d", lastMonthYear, lastMonth);

        System.out.println("根据当前时间自动设置查询时间范围: " + startTimeStr + " 至 " + endTimeStr);

        // 调用带时间参数的方法
        return getBillBusinessList(startTimeStr, endTimeStr, state, pageIndex, pageSize);
    }

    /**
     * 查询商务消费账单编号信息
     *
     * @param startTime 开始时间，格式为：YYYYMM 例如：202503
     * @param endTime   结束时间，格式为：YYYYMM 例如：202503
     * @param state     账单状态，1：已出账单 2：已核对（已弃用） 3：结算中 4：已结算 5：未出账单
     * @param pageIndex 页码，默认值1
     * @param pageSize  每页条数，默认20，最大100
     * @return 账单编号信息列表
     * @throws IOException 网络请求异常
     */
    public JSONObject getBillBusinessList(String startTime, String endTime, int state, int pageIndex, int pageSize) throws IOException {
        // 先获取token
        String token = getToken();
        System.out.println("获取到的Token: " + token);

        // API地址
        String apiUrl = "https://openapi.fenbeitong.com/openapi/bill/business/v1/list";

        // 创建URL对象
        URL url = new URL(apiUrl);

        // 创建HTTP连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法为POST
        connection.setRequestMethod("POST");

        // 设置请求头
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("access-token", token);

        // 允许输出流
        connection.setDoOutput(true);

        // 准备请求参数
        String jsonInputString = "{"
                + "\"start_time\": \"" + startTime + "\","
                + "\"end_time\": \"" + endTime + "\","
                + "\"state\": " + state + ","
                + "\"page_index\": " + pageIndex + ","
                + "\"page_size\": " + pageSize
                + "}";

        System.out.println("发送请求: " + jsonInputString);

        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 获取响应
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            // 解析JSON响应
            String responseStr = response.toString();
            System.out.println("响应内容: " + responseStr);

            // 使用Hutool解析JSON
            JSONObject jsonObj = JSONUtil.parseObj(responseStr);
            int code = jsonObj.getInt("code", -1);

            if (code == 0) {
                return jsonObj;
            } else {
                String msg = jsonObj.getStr("msg", "未知错误");
                throw new IOException("查询商务消费账单编号失败，错误码: " + code + ", 错误信息: " + msg);
            }
        } else {
            throw new IOException("查询商务消费账单编号失败，HTTP响应码: " + responseCode);
        }
    }


    /**
     * 插入账单
     * 将获取到的账单信息存入uf_zdb表
     * 插入前根据code判断是否存在，如果存在则不插入
     *
     * @throws IOException 网络请求异常
     */
    public void insertZD() throws IOException {
        RecordSet rs = new RecordSet();

        try {
            // 获取账单信息
            JSONObject billListResult = getBillBusinessList(1, 1, 100); // 获取所有已出账单

            // 获取bills数组
            JSONObject dataObj = billListResult.getJSONObject("data");
            if (dataObj != null && dataObj.containsKey("bills")) {
                JSONArray billsArray = dataObj.getJSONArray("bills");
                System.out.println("获取到的账单数量: " + billsArray.size());

                int insertCount = 0;
                int skipCount = 0;

                // 遍历每个账单
                for (int i = 0; i < billsArray.size(); i++) {
                    JSONObject bill = billsArray.getJSONObject(i);
                    String code = bill.getStr("code");

                    // 检查是否已存在相同code的记录
                    String checkSql = "SELECT COUNT(1) AS cnt FROM uf_zdb WHERE code = '" + code + "'";
                    rs.execute(checkSql);

                    if (rs.next() && rs.getInt(1) > 0) {
                        // 已存在相同code的记录，跳过
                        System.out.println("账单编号 " + code + " 已存在，跳过插入");
                        skipCount++;
                        continue;
                    }

                    // 不存在相同code的记录，执行插入
                    String startTime = bill.getStr("start_time", "");
                    String endTime = bill.getStr("end_time", "");
                    int state = bill.getInt("state", 0);
                    String couponTotalPrice = bill.getStr("coupon_total_price", "0.00");
                    String personalTotalPrice = bill.getStr("personal_total_price", "0.00");
                    String companyTotalPrice = bill.getStr("company_total_price", "0.00");
                    String fbbTotalPrice = bill.getStr("fbb_total_price", "0.00");
                    String payAfterTotalPrice = bill.getStr("pay_after_total_price", "0.00");
                    String grantBeforeTotalPrice = bill.getStr("grant_before_total_price", "0.00");
                    String payBeforeTotalPrice = bill.getStr("pay_before_total_price", "0.00");
                    String redCouponTotalPrice = bill.getStr("red_coupon_total_price", "0.00");

                    // 构建插入SQL
                    String insertSql = "INSERT INTO uf_zdb (start_time, end_time, code, state, " +
                            "coupon_total_price, personal_total_price, company_total_price, fbb_total_price, " +
                            "pay_after_total_price, grant_before_total_price, pay_before_total_price, red_coupon_total_price) " +
                            "VALUES ('" + startTime + "', '" + endTime + "', '" + code + "', " + state + ", " +
                            "'" + couponTotalPrice + "', '" + personalTotalPrice + "', '" + companyTotalPrice + "', '" + fbbTotalPrice + "', " +
                            "'" + payAfterTotalPrice + "', '" + grantBeforeTotalPrice + "', '" + payBeforeTotalPrice + "', '" + redCouponTotalPrice + "')";

                    // 执行插入
                    boolean insertResult = rs.execute(insertSql);
                    if (insertResult) {
                        System.out.println("成功插入账单编号: " + code);
                        insertCount++;
                    } else {
                        System.out.println("插入账单编号 " + code + " 失败");
                    }
                }

                System.out.println("账单插入完成，成功: " + insertCount + "，跳过(已存在): " + skipCount);
            } else {
                System.out.println("未获取到账单数据");
            }
        } catch (Exception e) {
            System.out.println("插入账单数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 统计项目代码对应的总金额
     * 从账单详情中提取cost_attributions中type为2的details里的code和amount，
     * 并按code进行汇总金额
     *
     * @param billDetails 账单详情数据
     * @return 项目代码对应的总金额Map，key为code，value为总金额
     */
    public Map<String, BigDecimal> calculateProjectAmounts(Map<String, List<Map<String, Object>>> billDetails) {
        Map<String, BigDecimal> projectAmounts = new HashMap<>();

        try {
            // 遍历所有companyId的数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
                List<Map<String, Object>> detailsList = entry.getValue();

                // 遍历每个companyId下的所有详情数据
                for (Map<String, Object> detail : detailsList) {
                    // 检查是否包含cost_attributions字段
                    if (detail.containsKey("cost_attributions")) {
                        List<Object> costAttributions = (List<Object>) detail.get("cost_attributions");

                        // 遍历cost_attributions数组
                        for (Object attrObj : costAttributions) {
                            Map<String, Object> attribution = (Map<String, Object>) attrObj;

                            // 检查type是否为2（项目）
                            if (attribution.containsKey("type") && Integer.valueOf(attribution.get("type").toString()) == 2) {
                                // 获取details数组
                                if (attribution.containsKey("details")) {
                                    List<Object> details = (List<Object>) attribution.get("details");

                                    // 遍历details数组
                                    for (Object detailObj : details) {
                                        Map<String, Object> detailMap = (Map<String, Object>) detailObj;

                                        // 提取code和amount
                                        if (detailMap.containsKey("code") && detailMap.containsKey("amount")) {
                                            String code = detailMap.get("code").toString();
                                            String amountStr = detailMap.get("amount").toString();

                                            try {
                                                // 使用BigDecimal处理金额，保证精确计算
                                                BigDecimal amount = new BigDecimal(amountStr);

                                                // 累加到对应code的总金额中
                                                if (projectAmounts.containsKey(code)) {
                                                    BigDecimal currentTotal = projectAmounts.get(code);
                                                    projectAmounts.put(code, currentTotal.add(amount));
                                                } else {
                                                    projectAmounts.put(code, amount);
                                                }

                                                System.out.println("项目代码: " + code + ", 金额: " + amount);
                                            } catch (NumberFormatException e) {
                                                System.out.println("金额转换失败: " + amountStr + ", " + e.getMessage());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 打印统计结果
            System.out.println("项目金额统计结果:");
            for (Map.Entry<String, BigDecimal> entry : projectAmounts.entrySet()) {
                System.out.println("项目代码: " + entry.getKey() + ", 总金额: " + entry.getValue().toPlainString());
            }

        } catch (Exception e) {
            System.out.println("统计项目金额时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return projectAmounts;
    }

    /**
     * 根据订单类别代码获取订单类别名称
     *
     * @param orderCategory 订单类别代码
     * @return 订单类别名称
     */
    private String getOrderCategoryName(int orderCategory) {
        switch (orderCategory) {
            case 7:
                return "国内机票";
            case 40:
                return "国际机票";
            case 11:
                return "酒店";
            case 15:
                return "火车";
            case 135:
                return "汽车票";
            case 3:
                return "用车";
            case 60:
                return "用餐";
            case 50:
                return "外卖";
            case 20:
                return "采购";
            case 130:
                return "闪送";
            case 131:
                return "快递";
            case 150:
                return "货运";
            case 911:
                return "其他订单";
            case 126:
                return "虚拟卡";
            case 128:
                return "对公付款";
            case 913:
                return "增值服务";
            case 110:
                return "海外酒店";
            default:
                return "未知类别(" + orderCategory + ")";
        }
    }

    /**
     * 统计项目代码和订单类别对应的总金额
     * 从账单详情中提取cost_attributions中type为2的details里的code和amount，
     * 并按code和order_category进行汇总金额
     *
     * @param billDetails 账单详情数据
     * @return 项目代码和订单类别对应的总金额Map，key为"项目代码_订单类别"，value为总金额
     */
    public Map<String, Map<Integer, BigDecimal>> calculateProjectCategoryAmounts(Map<String, List<Map<String, Object>>> billDetails) {
        // 外层Map: 项目代码 -> 内层Map
        // 内层Map: 订单类别 -> 金额
        Map<String, Map<Integer, BigDecimal>> projectCategoryAmounts = new HashMap<>();
        // 订单类别总金额统计
        Map<Integer, BigDecimal> categoryTotalAmounts = new HashMap<>();

        try {
            // 遍历所有companyId的数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
                List<Map<String, Object>> detailsList = entry.getValue();

                // 遍历每个companyId下的所有详情数据
                for (Map<String, Object> detail : detailsList) {
                    // 获取订单类别
                    int orderCategory = 0;
                    if (detail.containsKey("order_category")) {
                        try {
                            orderCategory = Integer.parseInt(detail.get("order_category").toString());
                        } catch (NumberFormatException e) {
                            System.out.println("订单类别转换失败: " + detail.get("order_category") + ", " + e.getMessage());
                        }
                    }

                    // 检查是否包含cost_attributions字段
                    if (detail.containsKey("cost_attributions")) {
                        List<Object> costAttributions = (List<Object>) detail.get("cost_attributions");

                        // 遍历cost_attributions数组
                        for (Object attrObj : costAttributions) {
                            Map<String, Object> attribution = (Map<String, Object>) attrObj;

                            // 检查type是否为2（项目）
                            if (attribution.containsKey("type") && Integer.valueOf(attribution.get("type").toString()) == 2) {
                                // 获取details数组
                                if (attribution.containsKey("details")) {
                                    List<Object> details = (List<Object>) attribution.get("details");

                                    // 遍历details数组
                                    for (Object detailObj : details) {
                                        Map<String, Object> detailMap = (Map<String, Object>) detailObj;

                                        // 提取code和amount
                                        if (detailMap.containsKey("code") && detailMap.containsKey("amount")) {
                                            String code = detailMap.get("code").toString();
                                            String amountStr = detailMap.get("amount").toString();

                                            try {
                                                // 使用BigDecimal处理金额，保证精确计算
                                                BigDecimal amount = new BigDecimal(amountStr);

                                                // 更新项目和订单类别的统计
                                                if (!projectCategoryAmounts.containsKey(code)) {
                                                    projectCategoryAmounts.put(code, new HashMap<>());
                                                }

                                                Map<Integer, BigDecimal> categoryAmounts = projectCategoryAmounts.get(code);
                                                if (categoryAmounts.containsKey(orderCategory)) {
                                                    categoryAmounts.put(orderCategory, categoryAmounts.get(orderCategory).add(amount));
                                                } else {
                                                    categoryAmounts.put(orderCategory, amount);
                                                }

                                                // 更新订单类别总金额统计
                                                if (categoryTotalAmounts.containsKey(orderCategory)) {
                                                    categoryTotalAmounts.put(orderCategory, categoryTotalAmounts.get(orderCategory).add(amount));
                                                } else {
                                                    categoryTotalAmounts.put(orderCategory, amount);
                                                }
                                            } catch (NumberFormatException e) {
                                                System.out.println("金额转换失败: " + amountStr + ", " + e.getMessage());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 打印项目和订单类别的统计结果
            System.out.println("\n按项目和订单类别统计金额:");
            BigDecimal grandTotal = BigDecimal.ZERO;

            for (Map.Entry<String, Map<Integer, BigDecimal>> projectEntry : projectCategoryAmounts.entrySet()) {
                String projectCode = projectEntry.getKey();
                Map<Integer, BigDecimal> categoryAmounts = projectEntry.getValue();
                BigDecimal projectTotal = BigDecimal.ZERO;

                System.out.println("\n项目代码: " + projectCode);

                for (Map.Entry<Integer, BigDecimal> categoryEntry : categoryAmounts.entrySet()) {
                    int category = categoryEntry.getKey();
                    BigDecimal amount = categoryEntry.getValue();
                    projectTotal = projectTotal.add(amount);

                    System.out.println("  - " + getOrderCategoryName(category) + ": " + amount.toPlainString());
                }

                System.out.println("  项目总金额: " + projectTotal.toPlainString());
                grandTotal = grandTotal.add(projectTotal);
            }

            // 打印订单类别总金额统计
            System.out.println("\n按订单类别统计总金额:");
            for (Map.Entry<Integer, BigDecimal> entry : categoryTotalAmounts.entrySet()) {
                System.out.println(getOrderCategoryName(entry.getKey()) + ": " + entry.getValue().toPlainString());
            }

            System.out.println("\n所有项目总金额: " + grandTotal.toPlainString());

        } catch (Exception e) {
            System.out.println("按项目和订单类别统计金额时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return projectCategoryAmounts;
    }

    /**
     * 处理已结算账单
     * 先获取所有账单，筛选出状态为3（结算中）的账单，然后获取这些账单的详细信息
     * 并统计每个项目代码对应的总金额
     *
     * @return 包含所有已结算账单详情的Map，key为companyId，value为对应的详情列表
     * @throws IOException 网络请求异常
     */
    public Map<String, List<Map<String, Object>>> processSettledBills() throws IOException {
        // 存储所有已结算账单详情的Map
        Map<String, List<Map<String, Object>>> allSettledBillDetails = new HashMap<>();

        try {
            // 获取所有账单信息（使用自动时间范围）
            JSONObject billListResult = getBillBusinessList(4, 1, 100); // 获取所有账单，后续筛选状态

            // 获取bills数组
            JSONObject dataObj = billListResult.getJSONObject("data");
            if (dataObj != null && dataObj.containsKey("bills")) {
                JSONArray billsArray = dataObj.getJSONArray("bills");
                System.out.println("获取到的账单总数量: " + billsArray.size());

                int settledBillCount = 0;

                // 遍历每个账单，筛选出状态为3（结算中）的账单
                for (int i = 0; i < billsArray.size(); i++) {
                    JSONObject bill = billsArray.getJSONObject(i);
                    int state = bill.getInt("state", 0);
                    String code = bill.getStr("code", "");

                    // 获取账单的开始时间和结束时间
                    String startTime = bill.getStr("start_time", "");
                    String endTime = bill.getStr("end_time", "");

                    // 检查账单状态是否为3（结算中）
                    if (state == 4 && code != null && !code.isEmpty()) {
                        settledBillCount++;
                        System.out.println("处理第 " + settledBillCount + " 个结算中账单，编号: " + code);

                        try {
                            // 获取该账单的详细信息
                            Map<String, List<Map<String, Object>>> billDetails = getBillBusinessDetails(code);

                            // 为每个账单详情添加账单编号和账单信息
                            for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
                                for (Map<String, Object> detail : entry.getValue()) {
                                    // 添加账单编号
                                    detail.put("bill_code", code);

                                    // 添加账单开始时间、结束时间和状态
                                    detail.put("bill_start_time", startTime);
                                    detail.put("bill_end_time", endTime);
                                    detail.put("bill_status", state);
                                }
                            }

                            // 合并到总结果中
                            for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
                                String companyId = entry.getKey();
                                List<Map<String, Object>> detailsList = entry.getValue();

                                if (allSettledBillDetails.containsKey(companyId)) {
                                    // 已存在该companyId，将当前数据添加到对应的列表中
                                    allSettledBillDetails.get(companyId).addAll(detailsList);
                                } else {
                                    // 不存在该companyId，添加新记录
                                    allSettledBillDetails.put(companyId, new ArrayList<>(detailsList));
                                }
                            }

                            System.out.println("成功获取账单 " + code + " 的详细信息");
                        } catch (Exception e) {
                            System.out.println("获取账单 " + code + " 详细信息时出错: " + e.getMessage());
                        }
                    }
                }

                System.out.println("结算中账单总数: " + settledBillCount);
                System.out.println("获取到的companyId总数: " + allSettledBillDetails.size());

                // 打印每个companyId对应的数据量
                for (Map.Entry<String, List<Map<String, Object>>> entry : allSettledBillDetails.entrySet()) {
                    System.out.println("CompanyId: " + entry.getKey() + ", 数据量: " + entry.getValue().size());
                }

                // 统计项目金额
                Map<String, BigDecimal> projectAmounts = calculateProjectAmounts(allSettledBillDetails);
                System.out.println("共统计 " + projectAmounts.size() + " 个项目的金额");

                // 按项目和订单类别统计金额
                calculateProjectCategoryAmounts(allSettledBillDetails);
            } else {
                System.out.println("未获取到账单数据");
            }
        } catch (Exception e) {
            System.out.println("处理结算中账单时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return allSettledBillDetails;
    }


    /**
     * 按项目维度整合数据，包括项目代码、各类别总金额以及关联的公司信息
     * 
     * @param billDetails 账单详情数据
     * @return 项目信息Map，key为项目代码，value为ProjectInfo对象
     */
    public Map<String, ProjectInfo> organizeDataByProject(Map<String, List<Map<String, Object>>> billDetails) {
        Map<String, ProjectInfo> projectInfoMap = new HashMap<>();
        
        try {
            // 遍历所有companyId的数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : billDetails.entrySet()) {
                String companyId = entry.getKey();
                List<Map<String, Object>> detailsList = entry.getValue();
                
                // 遍历每个companyId下的所有详情数据
                for (Map<String, Object> detail : detailsList) {
                    // 获取账单编号
                    String billCode = "";
                    if (detail.containsKey("bill_code")) {
                        billCode = detail.get("bill_code").toString();
                    }

                    // 获取账单开始时间、结束时间和状态
                    String billStartTime = "";
                    if (detail.containsKey("bill_start_time")) {
                        billStartTime = detail.get("bill_start_time").toString();
                    }

                    String billEndTime = "";
                    if (detail.containsKey("bill_end_time")) {
                        billEndTime = detail.get("bill_end_time").toString();
                    }

                    int billStatus = 0;
                    if (detail.containsKey("bill_status")) {
                        try {
                            billStatus = Integer.parseInt(detail.get("bill_status").toString());
                        } catch (NumberFormatException e) {
                            System.out.println("账单状态转换失败: " + detail.get("bill_status") + ", " + e.getMessage());
                        }
                    }

                    // 获取订单类别
                    int orderCategory = 0;
                    if (detail.containsKey("order_category")) {
                        try {
                            orderCategory = Integer.parseInt(detail.get("order_category").toString());
                        } catch (NumberFormatException e) {
                            System.out.println("订单类别转换失败: " + detail.get("order_category") + ", " + e.getMessage());
                        }
                    }
                    
                    // 获取发票类型
                    String invoiceType = "";
                    if (detail.containsKey("invoice_type")) {
                        invoiceType = detail.get("invoice_type").toString();
                    }
                    
                    // 提取含税和不含税金额
                    BigDecimal includeTaxAmount = null;
                    BigDecimal excludeTaxAmount = null;
                    
                    if (detail.containsKey("include_tax_amount")) {
                        try {
                            includeTaxAmount = new BigDecimal(detail.get("include_tax_amount").toString());
                        } catch (Exception e) {
                            System.out.println("含税金额转换失败: " + detail.get("include_tax_amount") + ", " + e.getMessage());
                        }
                    }
                    
                    if (detail.containsKey("exclude_tax_amount")) {
                        try {
                            excludeTaxAmount = new BigDecimal(detail.get("exclude_tax_amount").toString());
                        } catch (Exception e) {
                            System.out.println("不含税金额转换失败: " + detail.get("exclude_tax_amount") + ", " + e.getMessage());
                        }
                    }
                    
                    // 检查是否包含cost_attributions字段
                    if (detail.containsKey("cost_attributions")) {
                        List<Object> costAttributions = (List<Object>) detail.get("cost_attributions");
                        
                        // 遍历cost_attributions数组
                        for (Object attrObj : costAttributions) {
                            Map<String, Object> attribution = (Map<String, Object>) attrObj;
                            
                            // 检查type是否为2（项目）
                            if (attribution.containsKey("type") && Integer.valueOf(attribution.get("type").toString()) == 2) {
                                // 获取details数组
                                if (attribution.containsKey("details")) {
                                    List<Object> details = (List<Object>) attribution.get("details");
                                    
                                    // 遍历details数组
                                    for (Object detailObj : details) {
                                        Map<String, Object> detailMap = (Map<String, Object>) detailObj;
                                        
                                        // 提取code、name和amount
                                        if (detailMap.containsKey("code") && detailMap.containsKey("amount")) {
                                            String code = detailMap.get("code").toString();
                                            String amountStr = detailMap.get("amount").toString();
                                            String name = detailMap.containsKey("name") ? detailMap.get("name").toString() : null;
                                            
                                            try {
                                                // 使用BigDecimal处理金额，保证精确计算
                                                BigDecimal amount = new BigDecimal(amountStr);
                                                
                                                // 获取或创建ProjectInfo对象
                                                ProjectInfo projectInfo;
                                                if (projectInfoMap.containsKey(code)) {
                                                    projectInfo = projectInfoMap.get(code);
                                                } else {
                                                    projectInfo = new ProjectInfo(code);
                                                    if (name != null) {
                                                        projectInfo.setProjectName(name);
                                                    }
                                                    projectInfoMap.put(code, projectInfo);
                                                }

                                                // 设置账单信息
                                                if (billCode != null && !billCode.isEmpty()) {
                                                    projectInfo.setBillCode(billCode);
                                                }

                                                // 设置账单开始时间、结束时间和状态
                                                if (billStartTime != null && !billStartTime.isEmpty()) {
                                                    projectInfo.setBillStartTime(billStartTime);
                                                }

                                                if (billEndTime != null && !billEndTime.isEmpty()) {
                                                    projectInfo.setBillEndTime(billEndTime);
                                                }

                                                if (billStatus > 0) {
                                                    projectInfo.setBillStatus(billStatus);
                                                }
                                                
                                                // 更新ProjectInfo对象
                                                projectInfo.addCategoryAmount(orderCategory, amount);
                                                projectInfo.addRelatedCompany(companyId);
                                                
                                                // 添加含税和不含税金额到对应的订单类别
                                                if (includeTaxAmount != null) {
                                                    // 使用weight字段作为占比，weight为100.00表示占比为1
                                                    BigDecimal ratio = BigDecimal.ONE;
                                                    if (detailMap.containsKey("weight")) {
                                                        try {
                                                            BigDecimal weight = new BigDecimal(detailMap.get("weight").toString());
                                                            // weight为100.00表示占比为1，需要除以100
                                                            ratio = weight.divide(new BigDecimal("100"), 10, BigDecimal.ROUND_HALF_UP);
                                                        } catch (Exception e) {
                                                            System.out.println("weight转换失败: " + detailMap.get("weight") + ", " + e.getMessage());
                                                        }
                                                    }
                                                    
                                                    // 计算该项目应分配的含税金额
                                                    BigDecimal categoryIncludeTaxAmount = includeTaxAmount.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    
                                                    // 添加到对应类别的含税金额
                                                    projectInfo.addCategoryIncludeTaxAmount(orderCategory, categoryIncludeTaxAmount);
                                                    
                                                    // 处理住宿费的专票和普票
                                                    if ((orderCategory == 11 || orderCategory == 110) && categoryIncludeTaxAmount != null) {
                                                        BigDecimal categoryExcludeTaxAmount = null;
                                                if (excludeTaxAmount != null) {
                                                            categoryExcludeTaxAmount = excludeTaxAmount.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                        }
                                                        
                                                        // 根据发票类型添加到专票或普票
                                                        if ("专票".equals(invoiceType)) {
                                                            projectInfo.addAccommodationFeeSpecial(orderCategory, amount, categoryIncludeTaxAmount, categoryExcludeTaxAmount);
                                                        } else {
                                                            // 其他发票类型（包括普票、未知类型）都计入普票
                                                            projectInfo.addAccommodationFeeOrdinary(orderCategory, amount, categoryIncludeTaxAmount, categoryExcludeTaxAmount);
                                                        }
                                                    }
                                                }
                                                
                                                if (excludeTaxAmount != null) {
                                                    // 使用weight字段作为占比，weight为100.00表示占比为1
                                                    BigDecimal ratio = BigDecimal.ONE;
                                                    if (detailMap.containsKey("weight")) {
                                                        try {
                                                            BigDecimal weight = new BigDecimal(detailMap.get("weight").toString());
                                                            // weight为100.00表示占比为1，需要除以100
                                                            ratio = weight.divide(new BigDecimal("100"), 10, BigDecimal.ROUND_HALF_UP);
                                                        } catch (Exception e) {
                                                            System.out.println("weight转换失败: " + detailMap.get("weight") + ", " + e.getMessage());
                                                        }
                                                    }
                                                    
                                                    // 计算该项目应分配的不含税金额
                                                    BigDecimal categoryExcludeTaxAmount = excludeTaxAmount.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    
                                                    // 添加到对应类别的不含税金额
                                                    projectInfo.addCategoryExcludeTaxAmount(orderCategory, categoryExcludeTaxAmount);
                                                }
                                                
                                                // 添加额外信息
                                                for (String key : detailMap.keySet()) {
                                                    if (!key.equals("code") && !key.equals("amount") && !key.equals("name")) {
                                                        projectInfo.addAdditionalInfo(key, detailMap.get(key));
                                                    }
                                                }
                                                
                                                // 添加订单详情中的其他信息
                                                for (String key : detail.keySet()) {
                                                    if (!key.equals("cost_attributions") && !key.equals("order_category") &&
                                                        !key.equals("include_tax_amount") && !key.equals("exclude_tax_amount") &&
                                                        !key.equals("invoice_type")) {
                                                        String infoKey = "order_" + key;
                                                        if (!projectInfo.getAllAdditionalInfo().containsKey(infoKey)) {
                                                            projectInfo.addAdditionalInfo(infoKey, detail.get(key));
                                                        }
                                                    }
                                                }
                                                
                                                // 处理代打火车服务费
                                                if (detail.containsKey("manual_price")) {
                                                    try {
                                                        BigDecimal manualPrice = new BigDecimal(detail.get("manual_price").toString());
                                                        // 使用weight字段作为占比
                                                        BigDecimal ratio = BigDecimal.ONE;
                                                        if (detailMap.containsKey("weight")) {
                                                            try {
                                                                BigDecimal weight = new BigDecimal(detailMap.get("weight").toString());
                                                                ratio = weight.divide(new BigDecimal("100"), 10, BigDecimal.ROUND_HALF_UP);
                                                            } catch (Exception e) {
                                                                System.out.println("weight转换失败: " + detailMap.get("weight") + ", " + e.getMessage());
                                                            }
                                                        }
                                                        // 计算该项目应分配的代打火车服务费
                                                        BigDecimal projectManualPrice = manualPrice.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                        projectInfo.addManualPrice(projectManualPrice);
                                                    } catch (Exception e) {
                                                        System.out.println("代打火车服务费转换失败: " + detail.get("manual_price") + ", " + e.getMessage());
                                                    }
                                                }
                                                
                                                // 处理机建费和燃油费（仅针对国内机票和国际机票）
                                                if (orderCategory == 7 || orderCategory == 40) { // 7:国内机票, 40:国际机票
                                                    // 处理机建费
                                                    if (detail.containsKey("airport_fee")) {
                                                        try {
                                                            BigDecimal airportFee = new BigDecimal(detail.get("airport_fee").toString());
                                                            // 使用weight字段作为占比
                                                            BigDecimal ratio = BigDecimal.ONE;
                                                            if (detailMap.containsKey("weight")) {
                                                                try {
                                                                    BigDecimal weight = new BigDecimal(detailMap.get("weight").toString());
                                                                    ratio = weight.divide(new BigDecimal("100"), 10, BigDecimal.ROUND_HALF_UP);
                                                                } catch (Exception e) {
                                                                    System.out.println("weight转换失败: " + detailMap.get("weight") + ", " + e.getMessage());
                                                                }
                                                            }
                                                            // 计算该项目应分配的机建费
                                                            BigDecimal projectAirportFee = airportFee.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                            projectInfo.addAirportFee(projectAirportFee);
                                                        } catch (Exception e) {
                                                            System.out.println("机建费转换失败: " + detail.get("airport_fee") + ", " + e.getMessage());
                                                        }
                                                    }
                                                    
                                                    // 处理燃油费
                                                    if (detail.containsKey("fuel_fee")) {
                                                        try {
                                                            BigDecimal fuelFee = new BigDecimal(detail.get("fuel_fee").toString());
                                                            // 使用weight字段作为占比
                                                            BigDecimal ratio = BigDecimal.ONE;
                                                            if (detailMap.containsKey("weight")) {
                                                                try {
                                                                    BigDecimal weight = new BigDecimal(detailMap.get("weight").toString());
                                                                    ratio = weight.divide(new BigDecimal("100"), 10, BigDecimal.ROUND_HALF_UP);
                                                                } catch (Exception e) {
                                                                    System.out.println("weight转换失败: " + detailMap.get("weight") + ", " + e.getMessage());
                                                                }
                                                            }
                                                            // 计算该项目应分配的燃油费
                                                            BigDecimal projectFuelFee = fuelFee.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                            projectInfo.addFuelFee(projectFuelFee);
                                                        } catch (Exception e) {
                                                            System.out.println("燃油费转换失败: " + detail.get("fuel_fee") + ", " + e.getMessage());
                                                        }
                                                    }
                                                }
                                            } catch (NumberFormatException e) {
                                                System.out.println("金额转换失败: " + amountStr + ", " + e.getMessage());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 打印项目信息
            System.out.println("\n按项目维度整合的数据:");
            for (ProjectInfo project : projectInfoMap.values()) {
                System.out.println("\n========================================");
                System.out.println("项目代码: " + project.getProjectCode());
                System.out.println("项目名称: " + project.getProjectName());
                System.out.println("项目总金额: " + project.getTotalAmount().toPlainString());
                System.out.println("参考含税总金额: " + project.getIncludeTaxAmount().toPlainString());
                System.out.println("参考不含税总金额: " + project.getExcludeTaxAmount().toPlainString());

                // 打印机票含税总额和税额
                if (project.getAirTicketTotalIncludeTax().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("\n机票信息:");
                    System.out.println("  机票含税总额(国内+国际): " + project.getAirTicketTotalIncludeTax().toPlainString());
                    System.out.println("  机票税额(9%): " + project.getAirTicketTax().toPlainString());
                }

                // 打印公司信息
                System.out.println("\n公司信息:");
                System.out.println("  主要公司ID: " + (project.getCompanyId() != null ? project.getCompanyId() : "无"));
                System.out.println("  主要公司名称: " + (project.getCompanyName() != null ? project.getCompanyName() : "无"));
                System.out.println("  关联公司列表: " + (project.getRelatedCompaniesStr() != null ? project.getRelatedCompaniesStr() : "无"));

                // 打印账单信息
                System.out.println("\n账单信息:");
                System.out.println("  账单编号: " + (project.getBillCode() != null ? project.getBillCode() : "无"));
                System.out.println("  账单开始时间: " + (project.getBillStartTime() != null ? project.getBillStartTime() : "无"));
                System.out.println("  账单结束时间: " + (project.getBillEndTime() != null ? project.getBillEndTime() : "无"));
                System.out.println("  账单状态: " + project.getBillStatusDesc());
                
                // 打印住宿费相关信息
                if (project.getAccommodationFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("\n住宿费总计:");
                    System.out.println("  总金额: " + project.getAccommodationFee().toPlainString());
                    System.out.println("  含税金额: " + project.getAccommodationFeeIncludeTax().toPlainString());
                    System.out.println("  不含税金额: " + project.getAccommodationFeeExcludeTax().toPlainString());
                    
                    // 打印专票信息
                    if (project.getAccommodationFeeSpecial().compareTo(BigDecimal.ZERO) > 0) {
                        System.out.println("\n  住宿费专票:");
                        System.out.println("    总金额: " + project.getAccommodationFeeSpecial().toPlainString());
                        System.out.println("    含税金额: " + project.getAccommodationFeeSpecialIncludeTax().toPlainString());
                        System.out.println("    不含税金额: " + project.getAccommodationFeeSpecialExcludeTax().toPlainString());
                        
                        // 打印酒店专票
                        if (project.getHotelSpecial().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    酒店专票:");
                            System.out.println("      总金额: " + project.getHotelSpecial().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelSpecialIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelSpecialExcludeTax().toPlainString());
                        }
                        
                        // 打印海外酒店专票
                        if (project.getHotelOverseasSpecial().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    海外酒店专票:");
                            System.out.println("      总金额: " + project.getHotelOverseasSpecial().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOverseasSpecialIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOverseasSpecialExcludeTax().toPlainString());
                        }
                    }
                    
                    // 打印普票信息
                    if (project.getAccommodationFeeOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                        System.out.println("\n  住宿费普票:");
                        System.out.println("    总金额: " + project.getAccommodationFeeOrdinary().toPlainString());
                        System.out.println("    含税金额: " + project.getAccommodationFeeOrdinaryIncludeTax().toPlainString());
                        System.out.println("    不含税金额: " + project.getAccommodationFeeOrdinaryExcludeTax().toPlainString());
                        
                        // 打印酒店普票
                        if (project.getHotelOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    酒店普票:");
                            System.out.println("      总金额: " + project.getHotelOrdinary().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOrdinaryIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOrdinaryExcludeTax().toPlainString());
                        }
                        
                        // 打印海外酒店普票
                        if (project.getHotelOverseasOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    海外酒店普票:");
                            System.out.println("      总金额: " + project.getHotelOverseasOrdinary().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOverseasOrdinaryIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOverseasOrdinaryExcludeTax().toPlainString());
                        }
                    }
                }
                
                // 打印关联公司
                System.out.println("\n关联公司列表 (" + project.getRelatedCompanies().size() + "个):");
                for (String company : project.getRelatedCompanies()) {
                    System.out.println("  - " + company);
                }
                
                // 打印各订单类别金额
                System.out.println("\n订单类别金额明细:");
                
                // 使用直接属性访问而不是Map
                if (project.getAirTicketDomestic().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 国内机票 (ID:7): " + project.getAirTicketDomestic().toPlainString() + 
                                     " (含税: " + project.getAirTicketDomesticIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getAirTicketDomesticExcludeTax().toPlainString() + ")");
                }
                if (project.getAirTicketInternational().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 国际机票 (ID:40): " + project.getAirTicketInternational().toPlainString() + 
                                     " (含税: " + project.getAirTicketInternationalIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getAirTicketInternationalExcludeTax().toPlainString() + ")");
                }
                
                // 打印机建费和燃油费（如果有）
                if (project.getAirportFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 机建费: " + project.getAirportFee().toPlainString());
                }
                if (project.getFuelFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 燃油费: " + project.getFuelFee().toPlainString());
                }
                
                if (project.getHotel().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 酒店 (ID:11): " + project.getHotel().toPlainString() + 
                                     " (含税: " + project.getHotelIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getHotelExcludeTax().toPlainString() + ")");
                }
                if (project.getTrain().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 火车 (ID:15): " + project.getTrain().toPlainString() + 
                                     " (含税: " + project.getTrainIncludeTax().toPlainString() + 
                            ", 不含税: " + project.getTrainExcludeTax().toPlainString() +
                            ", 税额: " + project.getTrainTax().toPlainString() + ")");
                }
                
                // 打印代打火车服务费（如果有）
                if (project.getManualPrice().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 代打火车服务费: " + project.getManualPrice().toPlainString() +
                            " (税额(6%): " + project.getManualPriceTax().toPlainString() + ")");
                }
                
                if (project.getBus().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 汽车票 (ID:135): " + project.getBus().toPlainString() + 
                                     " (含税: " + project.getBusIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getBusExcludeTax().toPlainString() + ")");
                }
                if (project.getCar().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 用车 (ID:3): " + project.getCar().toPlainString() + 
                                     " (含税: " + project.getCarIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getCarExcludeTax().toPlainString() + ")");
                }
                if (project.getMeal().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 用餐 (ID:60): " + project.getMeal().toPlainString() + 
                                     " (含税: " + project.getMealIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getMealExcludeTax().toPlainString() + ")");
                }
                if (project.getTakeaway().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 外卖 (ID:50): " + project.getTakeaway().toPlainString() + 
                                     " (含税: " + project.getTakeawayIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getTakeawayExcludeTax().toPlainString() + ")");
                }
                if (project.getPurchase().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 采购 (ID:20): " + project.getPurchase().toPlainString() + 
                                     " (含税: " + project.getPurchaseIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getPurchaseExcludeTax().toPlainString() + ")");
                }
                if (project.getFlash().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 闪送 (ID:130): " + project.getFlash().toPlainString() + 
                                     " (含税: " + project.getFlashIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getFlashExcludeTax().toPlainString() + ")");
                }
                if (project.getExpress().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 快递 (ID:131): " + project.getExpress().toPlainString() + 
                                     " (含税: " + project.getExpressIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getExpressExcludeTax().toPlainString() + ")");
                }
                if (project.getFreight().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 货运 (ID:150): " + project.getFreight().toPlainString() + 
                                     " (含税: " + project.getFreightIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getFreightExcludeTax().toPlainString() + ")");
                }
                if (project.getOther().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 其他订单 (ID:911): " + project.getOther().toPlainString() + 
                                     " (含税: " + project.getOtherIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getOtherExcludeTax().toPlainString() + ")");
                }
                if (project.getVirtualCard().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 虚拟卡 (ID:126): " + project.getVirtualCard().toPlainString() + 
                                     " (含税: " + project.getVirtualCardIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getVirtualCardExcludeTax().toPlainString() + ")");
                }
                if (project.getCorporatePayment().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 对公付款 (ID:128): " + project.getCorporatePayment().toPlainString() + 
                                     " (含税: " + project.getCorporatePaymentIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getCorporatePaymentExcludeTax().toPlainString() + ")");
                }
                if (project.getValueAddedService().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 增值服务 (ID:913): " + project.getValueAddedService().toPlainString() + 
                                     " (含税: " + project.getValueAddedServiceIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getValueAddedServiceExcludeTax().toPlainString() + ")");
                }
                if (project.getHotelOverseas().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 海外酒店 (ID:110): " + project.getHotelOverseas().toPlainString() + 
                                     " (含税: " + project.getHotelOverseasIncludeTax().toPlainString() + 
                                     ", 不含税: " + project.getHotelOverseasExcludeTax().toPlainString() + ")");
                }
                
                // 打印额外信息
                System.out.println("\n额外信息 (" + project.getAllAdditionalInfo().size() + "项):");
                Map<String, Object> additionalInfo = project.getAllAdditionalInfo();
                if (additionalInfo.isEmpty()) {
                    System.out.println("  (无额外信息)");
                } else {
                    // 按照键名排序
                    List<String> sortedKeys = new ArrayList<>(additionalInfo.keySet());
                    Collections.sort(sortedKeys);
                    
                    int maxDisplayItems = 10; // 最多显示的额外信息数量
                    int displayCount = 0;
                    
                    for (String key : sortedKeys) {
                        if (displayCount < maxDisplayItems) {
                            Object value = additionalInfo.get(key);
                            String valueStr = (value != null) ? value.toString() : "null";
                            // 如果值太长，截断显示
                            if (valueStr.length() > 100) {
                                valueStr = valueStr.substring(0, 97) + "...";
                            }
                            System.out.println("  - " + key + ": " + valueStr);
                            displayCount++;
                        } else {
                            System.out.println("  ... 以及其他 " + (sortedKeys.size() - maxDisplayItems) + " 项信息 (已省略)");
                            break;
                        }
                    }
                }
                
                System.out.println("========================================");
            }
            
        } catch (Exception e) {
            System.out.println("按项目维度整合数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        return projectInfoMap;
    }

    /**
     * 将项目信息保存到数据库
     * 使用原生JDBC批量插入数据
     * 
     * @param projectInfoMap 项目信息Map
     */
    public void saveProjectInfoToDatabase(Map<String, ProjectInfo> projectInfoMap) {
        if (projectInfoMap == null || projectInfoMap.isEmpty()) {
            System.out.println("没有项目信息可保存");
            return;
        }

        // 数据库连接信息
        String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String URL = "*********************************************************";
        String USERNAME = "lijunhong";
        String PASSWORD = "Cyitce@0106";
        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            // 加载驱动
            Class.forName(DRIVER);

            // 获取当前日期和时间
            String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String currentTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
            
            // 建立连接
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            conn.setAutoCommit(false); // 关闭自动提交，开启事务

            // 准备插入语句
            String insertSql = "INSERT INTO uf_fbt_project_info (" +
                    "formmodeid, modedatacreater, modedatacreatertype, modedatacreatedate, modedatacreatetime, MODEUUID, " +
                    "project_code, project_name, bill_code, bill_start_time, bill_end_time, bill_status, " +
                    "company_id, company_name, related_companies, " +
                          "air_ticket_domestic, air_ticket_international, hotel, train, bus, car, " +
                          "meal, takeaway, purchase, flash, express, freight, other, " +
                          "virtual_card, corporate_payment, value_added_service, hotel_overseas, " +
                        "include_tax_amount, exclude_tax_amount, " +
                    // 保留的普通字段
                        "hotel_include_tax, hotel_exclude_tax, " +
                        "train_include_tax, train_exclude_tax, " +
                        "bus_include_tax, bus_exclude_tax, " +
                        "car_include_tax, car_exclude_tax, " +
                        "meal_include_tax, meal_exclude_tax, " +
                        "takeaway_include_tax, takeaway_exclude_tax, " +
                        "purchase_include_tax, purchase_exclude_tax, " +
                        "flash_include_tax, flash_exclude_tax, " +
                        "express_include_tax, express_exclude_tax, " +
                        "freight_include_tax, freight_exclude_tax, " +
                        "other_include_tax, other_exclude_tax, " +
                        "virtual_card_include_tax, virtual_card_exclude_tax, " +
                        "accommodation_fee, accommodation_fee_include_tax, accommodation_fee_exclude_tax, " +
                    "accommodation_fee_special, accommodation_fee_ordinary, " +
                        "hotel_special, hotel_special_include_tax, hotel_special_exclude_tax, " +
                        "hotel_ordinary, hotel_ordinary_include_tax, hotel_ordinary_exclude_tax, " +
                    "hotel_overseas_special, hotel_overseas_ordinary, " +
                    "total_accommodation_fee, " +
                    // 缩写字段
                    "atdit, atdet, atiit, atiet, cpit, cpet, vasit, vaset, hoit, " +
                    "afsit, afset, hosit, hoset, afoit, afoet, hooit, hooet, " +
                    "tafit, tafet, tafs, tafsit, tafset, tafo, tafoit, tafoet, " +
                    "manual_price, manual_price_tax, train_tax, airport_fee, fuel_fee, " +
                    "air_ticket_total_include_tax, air_ticket_tax, " +
                    "create_date, create_time) " +
                    "VALUES (" +
                    // 系统字段 - 6个参数
                    "?, ?, ?, ?, ?, ?, " +
                    // 基本信息字段 - 9个参数
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    // 订单类别金额字段 - 17个参数
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    // 总金额字段 - 2个参数
                    "?, ?, " +
                    // 保留的普通字段值 - 38个参数
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    "?, ?, ?, ?, " +
                    "?, ?, ?, ?, ?, " +
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    // 缩写字段的值 - 25个参数
                    "?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                    "?, ?, ?, ?, ?, ?, ?, ?, " +
                    "?, ?, ?, ?, ?, ?, ?, ?, " +
                    // 其他费用相关字段 - 5个参数
                    "?, ?, ?, ?, ?, " +
                    // 机票含税总额和税额 - 2个参数
                    "?, ?, " +
                    // 日期时间 - 2个参数
                    "?, ?)";

            // 计算占位符的数量
            int count = 0;
            for (int i = 0; i < insertSql.length(); i++) {
                if (insertSql.charAt(i) == '?') {
                    count++;
                }
            }
            System.out.println("SQL语句中的占位符总数: " + count);

            pstmt = conn.prepareStatement(insertSql);

            int batchSize = 0;
            final int MAX_BATCH_SIZE = 100; // 每100条记录提交一次

            // 获取当前日期和时间
            Map<String, String> currentDateTime = getCurrentDateTimeMap();

            // 遍历所有项目信息，执行批量插入
            for (ProjectInfo project : projectInfoMap.values()) {
                String projectCode = project.getProjectCode();
                String projectName = project.getProjectName();
                String billCode = project.getBillCode() != null ? project.getBillCode() : "";
                String billStartTime = project.getBillStartTime() != null ? project.getBillStartTime() : "";
                String billEndTime = project.getBillEndTime() != null ? project.getBillEndTime() : "";
                int billStatus = project.getBillStatus();

                int paramIndex = 1;

                // 系统字段 - 6个参数
                pstmt.setInt(paramIndex++, 1077); // formmodeid - 固定值1077
                pstmt.setInt(paramIndex++, 3810); // modedatacreater - 固定值3810
                pstmt.setInt(paramIndex++, 0);    // modedatacreatertype - 固定值0
                pstmt.setString(paramIndex++, currentDateTime.get("day")); // modedatacreatedate
                pstmt.setString(paramIndex++, currentDateTime.get("time")); // modedatacreatetime
                pstmt.setString(paramIndex++, UUID.randomUUID().toString()); // MODEUUID

                // 基本信息字段 - 9个参数
                pstmt.setString(paramIndex++, projectCode);
                pstmt.setString(paramIndex++, projectName);
                pstmt.setString(paramIndex++, billCode);
                pstmt.setString(paramIndex++, billStartTime);
                pstmt.setString(paramIndex++, billEndTime);
                pstmt.setInt(paramIndex++, billStatus);
                pstmt.setString(paramIndex++, project.getCompanyId());
                pstmt.setString(paramIndex++, project.getCompanyName());
                pstmt.setString(paramIndex++, project.getRelatedCompaniesStr());

                // 订单类别金额字段 - 17个参数
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomestic());
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternational());
                pstmt.setBigDecimal(paramIndex++, project.getHotel());
                pstmt.setBigDecimal(paramIndex++, project.getTrain());
                pstmt.setBigDecimal(paramIndex++, project.getBus());
                pstmt.setBigDecimal(paramIndex++, project.getCar());
                pstmt.setBigDecimal(paramIndex++, project.getMeal());
                pstmt.setBigDecimal(paramIndex++, project.getTakeaway());
                pstmt.setBigDecimal(paramIndex++, project.getPurchase());
                pstmt.setBigDecimal(paramIndex++, project.getFlash());
                pstmt.setBigDecimal(paramIndex++, project.getExpress());
                pstmt.setBigDecimal(paramIndex++, project.getFreight());
                pstmt.setBigDecimal(paramIndex++, project.getOther());
                pstmt.setBigDecimal(paramIndex++, project.getVirtualCard());
                pstmt.setBigDecimal(paramIndex++, project.getCorporatePayment());
                pstmt.setBigDecimal(paramIndex++, project.getValueAddedService());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseas());

                // 总金额字段 - 2个参数
                pstmt.setBigDecimal(paramIndex++, project.getIncludeTaxAmount());
                pstmt.setBigDecimal(paramIndex++, project.getExcludeTaxAmount());

                // 设置保留的普通字段值 - 38个参数
                pstmt.setBigDecimal(paramIndex++, project.getHotelIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getHotelExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getTrainIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getTrainExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getBusIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getBusExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getCarIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getCarExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getMealIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getMealExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getTakeawayIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getTakeawayExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getPurchaseIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getPurchaseExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getFlashIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getFlashExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getExpressIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getExpressExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getFreightIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getFreightExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getOtherIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getOtherExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getVirtualCardIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getVirtualCardExcludeTax());

                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFee());
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecial());
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinary());

                pstmt.setBigDecimal(paramIndex++, project.getHotelSpecial());
                pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getHotelSpecialExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinary());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOrdinaryExcludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecial());
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinary());
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFee());

                // 设置缩写字段的值 - 25个参数
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticIncludeTax());     // atdit
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketDomesticExcludeTax());     // atdet
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalIncludeTax()); // atiit
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketInternationalExcludeTax()); // atiet
                pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentIncludeTax());      // cpit
                pstmt.setBigDecimal(paramIndex++, project.getCorporatePaymentExcludeTax());      // cpet
                pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceIncludeTax());     // vasit
                pstmt.setBigDecimal(paramIndex++, project.getValueAddedServiceExcludeTax());     // vaset
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasIncludeTax());         // hoit

                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialIncludeTax());   // afsit
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeSpecialExcludeTax());   // afset
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecialIncludeTax());      // hosit
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasSpecialExcludeTax());      // hoset
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryIncludeTax());  // afoit
                pstmt.setBigDecimal(paramIndex++, project.getAccommodationFeeOrdinaryExcludeTax());  // afoet
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinaryIncludeTax());     // hooit
                pstmt.setBigDecimal(paramIndex++, project.getHotelOverseasOrdinaryExcludeTax());     // hooet

                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeIncludeTax());     // tafit
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeExcludeTax());     // tafet
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecial());        // tafs
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialIncludeTax()); // tafsit
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeSpecialExcludeTax()); // tafset
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinary());       // tafo
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryIncludeTax()); // tafoit
                pstmt.setBigDecimal(paramIndex++, project.getTotalAccommodationFeeOrdinaryExcludeTax()); // tafoet

                // 其他费用相关字段和日期时间 - 7个参数
                pstmt.setBigDecimal(paramIndex++, project.getManualPrice());
                pstmt.setBigDecimal(paramIndex++, project.getManualPriceTax());
                pstmt.setBigDecimal(paramIndex++, project.getTrainTax());
                pstmt.setBigDecimal(paramIndex++, project.getAirportFee());
                pstmt.setBigDecimal(paramIndex++, project.getFuelFee());

                // 机票含税总额和税额 - 2个参数
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketTotalIncludeTax());
                pstmt.setBigDecimal(paramIndex++, project.getAirTicketTax());

                pstmt.setString(paramIndex++, currentDate);
                pstmt.setString(paramIndex++, currentTime);

                System.out.println("实际绑定的参数总数: " + (paramIndex - 1));

                // 添加到批处理
                pstmt.addBatch();
                batchSize++;

                // 每MAX_BATCH_SIZE条记录提交一次
                if (batchSize >= MAX_BATCH_SIZE) {
                    pstmt.executeBatch();
                    conn.commit();
                    batchSize = 0;
                    System.out.println("已批量提交 " + MAX_BATCH_SIZE + " 条记录");
                }

                System.out.println("添加项目信息到批处理: " + projectCode + " - " + projectName +
                        ", 账单: " + billCode +
                        ", 账单时间: " + billStartTime + " 至 " + billEndTime +
                        ", 状态: " + project.getBillStatusDesc());
            }

            // 提交剩余的批处理
            if (batchSize > 0) {
                pstmt.executeBatch();
                conn.commit();
                System.out.println("已批量提交剩余 " + batchSize + " 条记录");
            }

            // // 批量插入项目与公司的关联信息
            // String insertRelationSql = "INSERT INTO fbt_project_company (project_code, company_id, bill_code, bill_start_time, bill_end_time, bill_status, create_date, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            // try (PreparedStatement relationPstmt = conn.prepareStatement(insertRelationSql)) {
            //
            //     batchSize = 0;
            //
            //     for (ProjectInfo project : projectInfoMap.values()) {
            //         String projectCode = project.getProjectCode();
            //         String billCode = project.getBillCode() != null ? project.getBillCode() : "";
            //         String billStartTime = project.getBillStartTime() != null ? project.getBillStartTime() : "";
            //         String billEndTime = project.getBillEndTime() != null ? project.getBillEndTime() : "";
            //         int billStatus = project.getBillStatus();
            //
            //         for (String companyId : project.getRelatedCompanies()) {
            //             relationPstmt.setString(1, projectCode);
            //             relationPstmt.setString(2, companyId);
            //             relationPstmt.setString(3, billCode);
            //             relationPstmt.setString(4, billStartTime);
            //             relationPstmt.setString(5, billEndTime);
            //             relationPstmt.setInt(6, billStatus);
            //             relationPstmt.setString(7, currentDate);
            //             relationPstmt.setString(8, currentTime);
            //
            //             relationPstmt.addBatch();
            //             batchSize++;
            //
            //             if (batchSize >= MAX_BATCH_SIZE) {
            //                 relationPstmt.executeBatch();
            //                 conn.commit();
            //                 batchSize = 0;
            //                 System.out.println("已批量提交 " + MAX_BATCH_SIZE + " 条公司关联记录");
            //             }
            //         }
            //     }
            //
            //     if (batchSize > 0) {
            //         relationPstmt.executeBatch();
            //         conn.commit();
            //         System.out.println("已批量提交剩余 " + batchSize + " 条公司关联记录");
            //     }
            // }

            System.out.println("项目信息批量保存完成");
            
        } catch (Exception e) {
            // 发生异常时回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            System.out.println("保存项目信息到数据库时出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
            e.printStackTrace();
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) throws IOException {
        FBTJob job = new FBTJob();
        
        try {
            Map<String, List<Map<String, Object>>> settledBillDetails = job.processSettledBills();
            System.out.println("结算中账单处理完成，获取到的companyId数量: " + settledBillDetails.size());
            
            // 按项目维度整合数据
            Map<String, ProjectInfo> projectInfoMap = job.organizeDataByProject(settledBillDetails);
            //job.saveProjectInfoToDatabase(projectInfoMap);
            // 打印项目维度的统计信息
            System.out.println("\n===== 项目维度统计信息 =====");
            for (ProjectInfo project : projectInfoMap.values()) {
                System.out.println("\n项目代码: " + project.getProjectCode());
                System.out.println("项目名称: " + project.getProjectName());
                System.out.println("项目总金额: " + project.getTotalAmount().toPlainString());
                System.out.println("总含税金额: " + project.getIncludeTaxAmount().toPlainString());
                System.out.println("总不含税金额: " + project.getExcludeTaxAmount().toPlainString());
                
                // 打印机票含税总额和税额
                if (project.getAirTicketTotalIncludeTax().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("\n机票信息:");
                    System.out.println("  机票含税总额(国内+国际): " + project.getAirTicketTotalIncludeTax().toPlainString());
                    System.out.println("  机票税额(9%): " + project.getAirTicketTax().toPlainString());
                }

                // 打印公司信息
                System.out.println("\n公司信息:");
                System.out.println("  主要公司ID: " + (project.getCompanyId() != null ? project.getCompanyId() : "无"));
                System.out.println("  主要公司名称: " + (project.getCompanyName() != null ? project.getCompanyName() : "无"));
                System.out.println("  关联公司列表: " + (project.getRelatedCompaniesStr() != null ? project.getRelatedCompaniesStr() : "无"));
                System.out.println("  关联公司数量: " + project.getRelatedCompanies().size());

                // 打印账单信息
                System.out.println("\n账单信息:");
                System.out.println("  账单编号: " + (project.getBillCode() != null ? project.getBillCode() : "无"));
                System.out.println("  账单开始时间: " + (project.getBillStartTime() != null ? project.getBillStartTime() : "无"));
                System.out.println("  账单结束时间: " + (project.getBillEndTime() != null ? project.getBillEndTime() : "无"));
                System.out.println("  账单状态: " + project.getBillStatusDesc());

                // 打印住宿费专票和普票信息
                if (project.getAccommodationFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("\n住宿费总计:");
                System.out.println("  总金额: " + project.getAccommodationFee().toPlainString());
                System.out.println("  含税金额: " + project.getAccommodationFeeIncludeTax().toPlainString());
                System.out.println("  不含税金额: " + project.getAccommodationFeeExcludeTax().toPlainString());
                
                    // 打印专票信息
                    if (project.getAccommodationFeeSpecial().compareTo(BigDecimal.ZERO) > 0) {
                        System.out.println("\n  住宿费专票:");
                        System.out.println("    总金额: " + project.getAccommodationFeeSpecial().toPlainString());
                        System.out.println("    含税金额: " + project.getAccommodationFeeSpecialIncludeTax().toPlainString());
                        System.out.println("    不含税金额: " + project.getAccommodationFeeSpecialExcludeTax().toPlainString());

                        // 打印酒店专票
                        if (project.getHotelSpecial().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    酒店专票:");
                            System.out.println("      总金额: " + project.getHotelSpecial().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelSpecialIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelSpecialExcludeTax().toPlainString());
                        }

                        // 打印海外酒店专票
                        if (project.getHotelOverseasSpecial().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    海外酒店专票:");
                            System.out.println("      总金额: " + project.getHotelOverseasSpecial().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOverseasSpecialIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOverseasSpecialExcludeTax().toPlainString());
                        }
                    }

                    // 打印普票信息
                    if (project.getAccommodationFeeOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                        System.out.println("\n  住宿费普票:");
                        System.out.println("    总金额: " + project.getAccommodationFeeOrdinary().toPlainString());
                        System.out.println("    含税金额: " + project.getAccommodationFeeOrdinaryIncludeTax().toPlainString());
                        System.out.println("    不含税金额: " + project.getAccommodationFeeOrdinaryExcludeTax().toPlainString());

                        // 打印酒店普票
                        if (project.getHotelOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    酒店普票:");
                            System.out.println("      总金额: " + project.getHotelOrdinary().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOrdinaryIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOrdinaryExcludeTax().toPlainString());
                        }

                        // 打印海外酒店普票
                        if (project.getHotelOverseasOrdinary().compareTo(BigDecimal.ZERO) > 0) {
                            System.out.println("\n    海外酒店普票:");
                            System.out.println("      总金额: " + project.getHotelOverseasOrdinary().toPlainString());
                            System.out.println("      含税金额: " + project.getHotelOverseasOrdinaryIncludeTax().toPlainString());
                            System.out.println("      不含税金额: " + project.getHotelOverseasOrdinaryExcludeTax().toPlainString());
                        }
                    }
                }

                // 打印关联公司
                System.out.println("\n关联公司列表 (" + project.getRelatedCompanies().size() + "个):");
                for (String company : project.getRelatedCompanies()) {
                    System.out.println("  - " + company);
                }

                // 打印各订单类别金额
                System.out.println("\n订单类别金额明细:");

                // 使用直接属性访问而不是Map
                if (project.getAirTicketDomestic().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 国内机票 (ID:7): " + project.getAirTicketDomestic().toPlainString() +
                            " (含税: " + project.getAirTicketDomesticIncludeTax().toPlainString() +
                            ", 不含税: " + project.getAirTicketDomesticExcludeTax().toPlainString() + ")");
                }
                if (project.getAirTicketInternational().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 国际机票 (ID:40): " + project.getAirTicketInternational().toPlainString() +
                            " (含税: " + project.getAirTicketInternationalIncludeTax().toPlainString() +
                            ", 不含税: " + project.getAirTicketInternationalExcludeTax().toPlainString() + ")");
                }

                // 打印机建费和燃油费（如果有）
                if (project.getAirportFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 机建费: " + project.getAirportFee().toPlainString());
                }
                if (project.getFuelFee().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 燃油费: " + project.getFuelFee().toPlainString());
                }

                if (project.getHotel().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 酒店 (ID:11): " + project.getHotel().toPlainString() +
                            " (含税: " + project.getHotelIncludeTax().toPlainString() +
                            ", 不含税: " + project.getHotelExcludeTax().toPlainString() + ")");
                }
                if (project.getTrain().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 火车 (ID:15): " + project.getTrain().toPlainString() +
                            " (含税: " + project.getTrainIncludeTax().toPlainString() +
                            ", 不含税: " + project.getTrainExcludeTax().toPlainString() +
                            ", 税额: " + project.getTrainTax().toPlainString() + ")");
                }

                // 打印代打火车服务费（如果有）
                if (project.getManualPrice().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 代打火车服务费: " + project.getManualPrice().toPlainString() +
                            " (税额(6%): " + project.getManualPriceTax().toPlainString() + ")");
                }

                if (project.getBus().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 汽车票 (ID:135): " + project.getBus().toPlainString() +
                            " (含税: " + project.getBusIncludeTax().toPlainString() +
                            ", 不含税: " + project.getBusExcludeTax().toPlainString() + ")");
                }
                if (project.getCar().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 用车 (ID:3): " + project.getCar().toPlainString() +
                            " (含税: " + project.getCarIncludeTax().toPlainString() +
                            ", 不含税: " + project.getCarExcludeTax().toPlainString() + ")");
                }
                if (project.getMeal().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 用餐 (ID:60): " + project.getMeal().toPlainString() +
                            " (含税: " + project.getMealIncludeTax().toPlainString() +
                            ", 不含税: " + project.getMealExcludeTax().toPlainString() + ")");
                }
                if (project.getTakeaway().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 外卖 (ID:50): " + project.getTakeaway().toPlainString() +
                            " (含税: " + project.getTakeawayIncludeTax().toPlainString() +
                            ", 不含税: " + project.getTakeawayExcludeTax().toPlainString() + ")");
                }
                if (project.getPurchase().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 采购 (ID:20): " + project.getPurchase().toPlainString() +
                            " (含税: " + project.getPurchaseIncludeTax().toPlainString() +
                            ", 不含税: " + project.getPurchaseExcludeTax().toPlainString() + ")");
                }
                if (project.getFlash().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 闪送 (ID:130): " + project.getFlash().toPlainString() +
                            " (含税: " + project.getFlashIncludeTax().toPlainString() +
                            ", 不含税: " + project.getFlashExcludeTax().toPlainString() + ")");
                }
                if (project.getExpress().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 快递 (ID:131): " + project.getExpress().toPlainString() +
                            " (含税: " + project.getExpressIncludeTax().toPlainString() +
                            ", 不含税: " + project.getExpressExcludeTax().toPlainString() + ")");
                }
                if (project.getFreight().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 货运 (ID:150): " + project.getFreight().toPlainString() +
                            " (含税: " + project.getFreightIncludeTax().toPlainString() +
                            ", 不含税: " + project.getFreightExcludeTax().toPlainString() + ")");
                }
                if (project.getOther().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 其他订单 (ID:911): " + project.getOther().toPlainString() +
                            " (含税: " + project.getOtherIncludeTax().toPlainString() +
                            ", 不含税: " + project.getOtherExcludeTax().toPlainString() + ")");
                }
                if (project.getVirtualCard().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 虚拟卡 (ID:126): " + project.getVirtualCard().toPlainString() +
                            " (含税: " + project.getVirtualCardIncludeTax().toPlainString() +
                            ", 不含税: " + project.getVirtualCardExcludeTax().toPlainString() + ")");
                }
                if (project.getCorporatePayment().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 对公付款 (ID:128): " + project.getCorporatePayment().toPlainString() +
                            " (含税: " + project.getCorporatePaymentIncludeTax().toPlainString() +
                            ", 不含税: " + project.getCorporatePaymentExcludeTax().toPlainString() + ")");
                }
                if (project.getValueAddedService().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 增值服务 (ID:913): " + project.getValueAddedService().toPlainString() +
                            " (含税: " + project.getValueAddedServiceIncludeTax().toPlainString() +
                            ", 不含税: " + project.getValueAddedServiceExcludeTax().toPlainString() + ")");
                }
                if (project.getHotelOverseas().compareTo(BigDecimal.ZERO) > 0) {
                    System.out.println("  - 海外酒店 (ID:110): " + project.getHotelOverseas().toPlainString() +
                            " (含税: " + project.getHotelOverseasIncludeTax().toPlainString() +
                            ", 不含税: " + project.getHotelOverseasExcludeTax().toPlainString() + ")");
                }

                // 打印额外信息
                System.out.println("\n额外信息 (" + project.getAllAdditionalInfo().size() + "项):");
                Map<String, Object> additionalInfo = project.getAllAdditionalInfo();
                if (additionalInfo.isEmpty()) {
                    System.out.println("  (无额外信息)");
                } else {
                    // 按照键名排序
                    List<String> sortedKeys = new ArrayList<>(additionalInfo.keySet());
                    Collections.sort(sortedKeys);

                    int maxDisplayItems = 10; // 最多显示的额外信息数量
                    int displayCount = 0;

                    for (String key : sortedKeys) {
                        if (displayCount < maxDisplayItems) {
                            Object value = additionalInfo.get(key);
                            String valueStr = (value != null) ? value.toString() : "null";
                            // 如果值太长，截断显示
                            if (valueStr.length() > 100) {
                                valueStr = valueStr.substring(0, 97) + "...";
                            }
                            System.out.println("  - " + key + ": " + valueStr);
                            displayCount++;
                        } else {
                            System.out.println("  ... 以及其他 " + (sortedKeys.size() - maxDisplayItems) + " 项信息 (已省略)");
                            break;
                        }
                    }
                }

                System.out.println("========================================");
            }
            
            // 计算所有项目的总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalIncludeTaxAmount = BigDecimal.ZERO;
            BigDecimal totalExcludeTaxAmount = BigDecimal.ZERO;
            
            // 住宿费相关总计
            BigDecimal totalAccommodationFee = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeIncludeTax = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeExcludeTax = BigDecimal.ZERO;
            
            // 住宿费专票总计
            BigDecimal totalAccommodationFeeSpecial = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeSpecialIncludeTax = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeSpecialExcludeTax = BigDecimal.ZERO;
            
            // 住宿费普票总计
            BigDecimal totalAccommodationFeeOrdinary = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO;
            BigDecimal totalAccommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO;
            
            // 整合住宿费总计
            BigDecimal totalIntegratedAccommodationFee = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeIncludeTax = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeExcludeTax = BigDecimal.ZERO;
            
            // 整合住宿费专票总计
            BigDecimal totalIntegratedAccommodationFeeSpecial = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeSpecialIncludeTax = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeSpecialExcludeTax = BigDecimal.ZERO;
            
            // 整合住宿费普票总计
            BigDecimal totalIntegratedAccommodationFeeOrdinary = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO;
            BigDecimal totalIntegratedAccommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO;
            
            // 代打火车服务费总计
            BigDecimal totalManualPrice = BigDecimal.ZERO;
            // 代打火车服务费税额总计
            BigDecimal totalManualPriceTax = BigDecimal.ZERO;
            
            // 机建费总计
            BigDecimal totalAirportFee = BigDecimal.ZERO;
            
            // 燃油费总计
            BigDecimal totalFuelFee = BigDecimal.ZERO;

            // 火车票税额总计
            BigDecimal totalTrainTax = BigDecimal.ZERO;

            // 机票含税总额和税额总计
            BigDecimal totalAirTicketTotalIncludeTax = BigDecimal.ZERO;
            BigDecimal totalAirTicketTax = BigDecimal.ZERO;
            
            for (ProjectInfo project : projectInfoMap.values()) {
                totalAmount = totalAmount.add(project.getTotalAmount());
                totalIncludeTaxAmount = totalIncludeTaxAmount.add(project.getIncludeTaxAmount());
                totalExcludeTaxAmount = totalExcludeTaxAmount.add(project.getExcludeTaxAmount());
                
                // 累加住宿费总计
                totalAccommodationFee = totalAccommodationFee.add(project.getAccommodationFee());
                totalAccommodationFeeIncludeTax = totalAccommodationFeeIncludeTax.add(project.getAccommodationFeeIncludeTax());
                totalAccommodationFeeExcludeTax = totalAccommodationFeeExcludeTax.add(project.getAccommodationFeeExcludeTax());
                
                // 累加住宿费专票
                totalAccommodationFeeSpecial = totalAccommodationFeeSpecial.add(project.getAccommodationFeeSpecial());
                totalAccommodationFeeSpecialIncludeTax = totalAccommodationFeeSpecialIncludeTax.add(project.getAccommodationFeeSpecialIncludeTax());
                totalAccommodationFeeSpecialExcludeTax = totalAccommodationFeeSpecialExcludeTax.add(project.getAccommodationFeeSpecialExcludeTax());
                
                // 累加住宿费普票
                totalAccommodationFeeOrdinary = totalAccommodationFeeOrdinary.add(project.getAccommodationFeeOrdinary());
                totalAccommodationFeeOrdinaryIncludeTax = totalAccommodationFeeOrdinaryIncludeTax.add(project.getAccommodationFeeOrdinaryIncludeTax());
                totalAccommodationFeeOrdinaryExcludeTax = totalAccommodationFeeOrdinaryExcludeTax.add(project.getAccommodationFeeOrdinaryExcludeTax());
                
                // 累加整合住宿费数据
                totalIntegratedAccommodationFee = totalIntegratedAccommodationFee.add(project.getTotalAccommodationFee());
                totalIntegratedAccommodationFeeIncludeTax = totalIntegratedAccommodationFeeIncludeTax.add(project.getTotalAccommodationFeeIncludeTax());
                totalIntegratedAccommodationFeeExcludeTax = totalIntegratedAccommodationFeeExcludeTax.add(project.getTotalAccommodationFeeExcludeTax());
                
                totalIntegratedAccommodationFeeSpecial = totalIntegratedAccommodationFeeSpecial.add(project.getTotalAccommodationFeeSpecial());
                totalIntegratedAccommodationFeeSpecialIncludeTax = totalIntegratedAccommodationFeeSpecialIncludeTax.add(project.getTotalAccommodationFeeSpecialIncludeTax());
                totalIntegratedAccommodationFeeSpecialExcludeTax = totalIntegratedAccommodationFeeSpecialExcludeTax.add(project.getTotalAccommodationFeeSpecialExcludeTax());
                
                totalIntegratedAccommodationFeeOrdinary = totalIntegratedAccommodationFeeOrdinary.add(project.getTotalAccommodationFeeOrdinary());
                totalIntegratedAccommodationFeeOrdinaryIncludeTax = totalIntegratedAccommodationFeeOrdinaryIncludeTax.add(project.getTotalAccommodationFeeOrdinaryIncludeTax());
                totalIntegratedAccommodationFeeOrdinaryExcludeTax = totalIntegratedAccommodationFeeOrdinaryExcludeTax.add(project.getTotalAccommodationFeeOrdinaryExcludeTax());
                
                // 累加代打火车服务费、代打火车服务费税额、机建费和燃油费
                totalManualPrice = totalManualPrice.add(project.getManualPrice());
                totalManualPriceTax = totalManualPriceTax.add(project.getManualPriceTax());
                totalAirportFee = totalAirportFee.add(project.getAirportFee());
                totalFuelFee = totalFuelFee.add(project.getFuelFee());

                // 累加火车票税额
                totalTrainTax = totalTrainTax.add(project.getTrainTax());

                // 累加机票含税总额和税额
                totalAirTicketTotalIncludeTax = totalAirTicketTotalIncludeTax.add(project.getAirTicketTotalIncludeTax());
                totalAirTicketTax = totalAirTicketTax.add(project.getAirTicketTax());
            }
            
            System.out.println("\n===== 总计 =====");
            System.out.println("所有项目总金额: " + totalAmount.toPlainString());
            System.out.println("所有项目总含税金额: " + totalIncludeTaxAmount.toPlainString());
            System.out.println("所有项目总不含税金额: " + totalExcludeTaxAmount.toPlainString());
            
            // 打印代打火车服务费、代打火车服务费税额、机建费和燃油费总计
            if (totalManualPrice.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println("\n代打火车服务费总计: " + totalManualPrice.toPlainString());
                System.out.println("代打火车服务费税额总计(6%): " + totalManualPriceTax.toPlainString());
            }
            if (totalAirportFee.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println("机建费总计: " + totalAirportFee.toPlainString());
            }
            if (totalFuelFee.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println("燃油费总计: " + totalFuelFee.toPlainString());
            }
            
            // 打印机票含税总额和税额总计
            if (totalAirTicketTotalIncludeTax.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println("\n机票含税总额和税额总计:");
                System.out.println("机票含税总额总计: " + totalAirTicketTotalIncludeTax.toPlainString());
                System.out.println("机票税额总计(9%): " + totalAirTicketTax.toPlainString());
            }
            
            // 打印住宿费总计信息
            System.out.println("\n===== 住宿费总计信息 =====");
            System.out.println("住宿费总金额: " + totalAccommodationFee.toPlainString());
            System.out.println("住宿费总含税金额: " + totalAccommodationFeeIncludeTax.toPlainString());
            System.out.println("住宿费总不含税金额: " + totalAccommodationFeeExcludeTax.toPlainString());
            
            // 打印住宿费专票总计
            System.out.println("\n住宿费专票总计:");
            System.out.println("  总金额: " + totalAccommodationFeeSpecial.toPlainString());
            System.out.println("  含税金额: " + totalAccommodationFeeSpecialIncludeTax.toPlainString());
            System.out.println("  不含税金额: " + totalAccommodationFeeSpecialExcludeTax.toPlainString());
            
            // 打印住宿费普票总计
            System.out.println("\n住宿费普票总计:");
            System.out.println("  总金额: " + totalAccommodationFeeOrdinary.toPlainString());
            System.out.println("  含税金额: " + totalAccommodationFeeOrdinaryIncludeTax.toPlainString());
            System.out.println("  不含税金额: " + totalAccommodationFeeOrdinaryExcludeTax.toPlainString());
            
            // 计算专票和普票占比
            if (totalAccommodationFee.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal specialRatio = totalAccommodationFeeSpecial.multiply(new BigDecimal("100")).divide(totalAccommodationFee, 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal ordinaryRatio = totalAccommodationFeeOrdinary.multiply(new BigDecimal("100")).divide(totalAccommodationFee, 2, BigDecimal.ROUND_HALF_UP);
                
                System.out.println("\n住宿费发票类型占比:");
                System.out.println("  专票占比: " + specialRatio.toPlainString() + "%");
                System.out.println("  普票占比: " + ordinaryRatio.toPlainString() + "%");
            }
            
            // 保存项目信息到数据库
            System.out.println("\n保存项目信息到数据库:");
            // job.saveProjectInfoToDatabase(projectInfoMap);
            
            // 打印整合住宿费总计信息
            System.out.println("\n===== 整合住宿费总计信息（酒店+海外酒店） =====");
            
            System.out.println("\n整合住宿费总计:");
            System.out.println("  总金额: " + totalIntegratedAccommodationFee.toPlainString());
            System.out.println("  含税金额: " + totalIntegratedAccommodationFeeIncludeTax.toPlainString());
            System.out.println("  不含税金额: " + totalIntegratedAccommodationFeeExcludeTax.toPlainString());
            
            System.out.println("\n整合住宿费专票总计:");
            System.out.println("  总金额: " + totalIntegratedAccommodationFeeSpecial.toPlainString());
            System.out.println("  含税金额: " + totalIntegratedAccommodationFeeSpecialIncludeTax.toPlainString());
            System.out.println("  不含税金额: " + totalIntegratedAccommodationFeeSpecialExcludeTax.toPlainString());
            
            System.out.println("\n整合住宿费普票总计:");
            System.out.println("  总金额: " + totalIntegratedAccommodationFeeOrdinary.toPlainString());
            System.out.println("  含税金额: " + totalIntegratedAccommodationFeeOrdinaryIncludeTax.toPlainString());
            System.out.println("  不含税金额: " + totalIntegratedAccommodationFeeOrdinaryExcludeTax.toPlainString());

            // 打印火车票相关统计
            if (totalTrainTax.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println("\n火车票税额总计: " + totalTrainTax.toPlainString());
            }
        } catch (Exception e) {
            System.out.println("处理结算中账单时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main1(String[] args) {
        FBTJob job = new FBTJob();
        // 演示新增的批量查询功能
        System.out.println("\n===== 演示批量查询功能 =====");

        // 准备ID列表
        List<Integer> idsToQuery = new ArrayList<>();
        idsToQuery.add(1);
        idsToQuery.add(2);

        List<Map<String, String>> projectInfoList = job.getProjectInfoByIds(idsToQuery);

        // 打印查询结果
        System.out.println("\n查询结果 (" + projectInfoList.size() + " 条记录):");
        for (Map<String, String> projectInfo : projectInfoList) {
            System.out.println("\n项目信息:");
            System.out.println("  ID: " + projectInfo.get("id"));
            System.out.println("  项目代码: " + projectInfo.get("project_code"));
            System.out.println("  项目名称: " + projectInfo.get("project_name"));
            System.out.println("  公司ID: " + projectInfo.get("company_id"));
            System.out.println("  公司名称: " + projectInfo.get("company_name"));
            System.out.println("  关联公司: " + projectInfo.get("related_companies"));
        }

        // 演示单个查询功能
        System.out.println("\n===== 演示单个查询功能 =====");

        // 查询单个ID
        Integer singleId = 1;
        Map<String, String> singleProjectInfo = job.getProjectInfoById(singleId);

        if (!singleProjectInfo.isEmpty()) {
            System.out.println("\n单个项目信息:");
            System.out.println("  ID: " + singleProjectInfo.get("id"));
            System.out.println("  项目代码: " + singleProjectInfo.get("project_code"));
            System.out.println("  项目名称: " + singleProjectInfo.get("project_name"));
            System.out.println("  公司ID: " + singleProjectInfo.get("company_id"));
            System.out.println("  公司名称: " + singleProjectInfo.get("company_name"));
            System.out.println("  关联公司: " + singleProjectInfo.get("related_companies"));
        } else {
            System.out.println("\n未找到ID为 " + singleId + " 的项目信息");
        }
    }

    /**
     * 生成字段映射文件，列出所有数据库字段及其中文注释
     */
    public static void generateFieldMappingFile() {
        try {
            String filePath = "fbt_project_info_fields.txt";
            BufferedWriter writer = new BufferedWriter(new java.io.FileWriter(filePath));

            writer.write("# 分贝通项目信息表(uf_fbt_project_info)字段映射\n");
            writer.write("# 格式: 字段名 | 中文注释\n");
            writer.write("# 生成日期: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n\n");

            // 基本信息字段
            writer.write("## 基本信息字段\n");
            writer.write("id | 主键ID\n");
            writer.write("project_code | 项目代码\n");
            writer.write("project_name | 项目名称\n");
            writer.write("bill_code | 账单编号\n");
            writer.write("bill_start_time | 账单开始时间\n");
            writer.write("bill_end_time | 账单结束时间\n");
            writer.write("bill_status | 账单状态(1:已出账单 2:已核对 3:结算中 4:已结算 5:未出账单)\n");
            writer.write("company_id | 主要公司ID\n");
            writer.write("company_name | 主要公司名称\n");
            writer.write("related_companies | 关联公司ID列表(逗号分隔)\n\n");

            // 订单类别金额字段
            writer.write("## 订单类别金额字段\n");
            writer.write("air_ticket_domestic | 国内机票金额\n");
            writer.write("air_ticket_international | 国际机票金额\n");
            writer.write("hotel | 酒店金额\n");
            writer.write("train | 火车金额\n");
            writer.write("bus | 汽车票金额\n");
            writer.write("car | 用车金额\n");
            writer.write("meal | 用餐金额\n");
            writer.write("takeaway | 外卖金额\n");
            writer.write("purchase | 采购金额\n");
            writer.write("flash | 闪送金额\n");
            writer.write("express | 快递金额\n");
            writer.write("freight | 货运金额\n");
            writer.write("other | 其他订单金额\n");
            writer.write("virtual_card | 虚拟卡金额\n");
            writer.write("corporate_payment | 对公付款金额\n");
            writer.write("value_added_service | 增值服务金额\n");
            writer.write("hotel_overseas | 海外酒店金额\n\n");

            // 含税和不含税总金额
            writer.write("## 含税和不含税总金额\n");
            writer.write("include_tax_amount | 含税总金额\n");
            writer.write("exclude_tax_amount | 不含税总金额\n\n");

            // 各类别含税金额
            writer.write("## 各类别含税金额\n");
            writer.write("air_ticket_domestic_include_tax | 国内机票含税金额\n");
            writer.write("air_ticket_international_include_tax | 国际机票含税金额\n");
            writer.write("hotel_include_tax | 酒店含税金额\n");
            writer.write("train_include_tax | 火车含税金额\n");
            writer.write("bus_include_tax | 汽车票含税金额\n");
            writer.write("car_include_tax | 用车含税金额\n");
            writer.write("meal_include_tax | 用餐含税金额\n");
            writer.write("takeaway_include_tax | 外卖含税金额\n");
            writer.write("purchase_include_tax | 采购含税金额\n");
            writer.write("flash_include_tax | 闪送含税金额\n");
            writer.write("express_include_tax | 快递含税金额\n");
            writer.write("freight_include_tax | 货运含税金额\n");
            writer.write("other_include_tax | 其他订单含税金额\n");
            writer.write("virtual_card_include_tax | 虚拟卡含税金额\n");
            writer.write("corporate_payment_include_tax | 对公付款含税金额\n");
            writer.write("value_added_service_include_tax | 增值服务含税金额\n");
            writer.write("hotel_overseas_include_tax | 海外酒店含税金额\n\n");

            // 各类别不含税金额
            writer.write("## 各类别不含税金额\n");
            writer.write("air_ticket_domestic_exclude_tax | 国内机票不含税金额\n");
            writer.write("air_ticket_international_exclude_tax | 国际机票不含税金额\n");
            writer.write("hotel_exclude_tax | 酒店不含税金额\n");
            writer.write("train_exclude_tax | 火车不含税金额\n");
            writer.write("bus_exclude_tax | 汽车票不含税金额\n");
            writer.write("car_exclude_tax | 用车不含税金额\n");
            writer.write("meal_exclude_tax | 用餐不含税金额\n");
            writer.write("takeaway_exclude_tax | 外卖不含税金额\n");
            writer.write("purchase_exclude_tax | 采购不含税金额\n");
            writer.write("flash_exclude_tax | 闪送不含税金额\n");
            writer.write("express_exclude_tax | 快递不含税金额\n");
            writer.write("freight_exclude_tax | 货运不含税金额\n");
            writer.write("other_exclude_tax | 其他订单不含税金额\n");
            writer.write("virtual_card_exclude_tax | 虚拟卡不含税金额\n");
            writer.write("corporate_payment_exclude_tax | 对公付款不含税金额\n");
            writer.write("value_added_service_exclude_tax | 增值服务不含税金额\n");
            writer.write("hotel_overseas_exclude_tax | 海外酒店不含税金额\n\n");

            // 住宿费相关字段
            writer.write("## 住宿费相关字段\n");
            writer.write("accommodation_fee | 住宿费总金额\n");
            writer.write("accommodation_fee_include_tax | 住宿费含税总金额\n");
            writer.write("accommodation_fee_exclude_tax | 住宿费不含税总金额\n");
            writer.write("accommodation_fee_special | 住宿费专票总金额\n");
            writer.write("accommodation_fee_special_include_tax | 住宿费专票含税金额\n");
            writer.write("accommodation_fee_special_exclude_tax | 住宿费专票不含税金额\n");
            writer.write("accommodation_fee_ordinary | 住宿费普票总金额\n");
            writer.write("accommodation_fee_ordinary_include_tax | 住宿费普票含税金额\n");
            writer.write("accommodation_fee_ordinary_exclude_tax | 住宿费普票不含税金额\n\n");

            // 酒店专票和普票
            writer.write("## 酒店专票和普票\n");
            writer.write("hotel_special | 酒店专票金额\n");
            writer.write("hotel_special_include_tax | 酒店专票含税金额\n");
            writer.write("hotel_special_exclude_tax | 酒店专票不含税金额\n");
            writer.write("hotel_ordinary | 酒店普票金额\n");
            writer.write("hotel_ordinary_include_tax | 酒店普票含税金额\n");
            writer.write("hotel_ordinary_exclude_tax | 酒店普票不含税金额\n");
            writer.write("hotel_overseas_special | 海外酒店专票金额\n");
            writer.write("hotel_overseas_special_include_tax | 海外酒店专票含税金额\n");
            writer.write("hotel_overseas_special_exclude_tax | 海外酒店专票不含税金额\n");
            writer.write("hotel_overseas_ordinary | 海外酒店普票金额\n");
            writer.write("hotel_overseas_ordinary_include_tax | 海外酒店普票含税金额\n");
            writer.write("hotel_overseas_ordinary_exclude_tax | 海外酒店普票不含税金额\n\n");

            // 整合住宿费
            writer.write("## 整合住宿费(酒店+海外酒店)\n");
            writer.write("total_accommodation_fee | 整合住宿费总金额\n");
            writer.write("total_accommodation_fee_include_tax | 整合住宿费含税总金额\n");
            writer.write("total_accommodation_fee_exclude_tax | 整合住宿费不含税总金额\n");
            writer.write("total_accommodation_fee_special | 整合住宿费专票总金额\n");
            writer.write("total_accommodation_fee_special_include_tax | 整合住宿费专票含税金额\n");
            writer.write("total_accommodation_fee_special_exclude_tax | 整合住宿费专票不含税金额\n");
            writer.write("total_accommodation_fee_ordinary | 整合住宿费普票总金额\n");
            writer.write("total_accommodation_fee_ordinary_include_tax | 整合住宿费普票含税金额\n");
            writer.write("total_accommodation_fee_ordinary_exclude_tax | 整合住宿费普票不含税金额\n\n");

            // 其他费用
            writer.write("## 其他费用\n");
            writer.write("manual_price | 代打火车服务费\n");
            writer.write("manual_price_tax | 代打火车服务费税额(6%)\n");
            writer.write("train_tax | 火车票税额\n");
            writer.write("airport_fee | 机建费\n");
            writer.write("fuel_fee | 燃油费\n");
            writer.write("air_ticket_total_include_tax | 机票含税总额(国内+国际)\n");
            writer.write("air_ticket_tax | 机票税额(9%)\n\n");

            // 创建日期和时间
            writer.write("## 创建日期和时间\n");
            writer.write("create_date | 创建日期\n");
            writer.write("create_time | 创建时间\n");

            writer.close();
            System.out.println("字段映射文件已生成: " + filePath);
        } catch (IOException e) {
            System.out.println("生成字段映射文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 项目信息类，用于存储项目相关的所有信息
     */
    public static class ProjectInfo {
        private String projectCode; // 项目代码
        private String projectName; // 项目名称
        private String billCode; // 账单编号
        private String billStartTime; // 账单开始时间
        private String billEndTime; // 账单结束时间
        private int billStatus; // 账单状态
        private Set<String> relatedCompanies; // 关联的公司ID
        private String companyId; // 主要关联公司ID（第一个添加的公司）
        private String companyName; // 主要关联公司名称
        private String relatedCompaniesStr; // 所有关联公司ID的字符串，以逗号分隔
        private Map<String, Object> additionalInfo; // 额外信息

        // 各订单类别金额作为单独的属性
        private BigDecimal airTicketDomestic = BigDecimal.ZERO;       // 国内机票 (7)
        private BigDecimal airTicketInternational = BigDecimal.ZERO;  // 国际机票 (40)
        private BigDecimal hotel = BigDecimal.ZERO;                   // 酒店 (11)
        private BigDecimal train = BigDecimal.ZERO;                   // 火车 (15)
        private BigDecimal bus = BigDecimal.ZERO;                     // 汽车票 (135)
        private BigDecimal car = BigDecimal.ZERO;                     // 用车 (3)
        private BigDecimal meal = BigDecimal.ZERO;                    // 用餐 (60)
        private BigDecimal takeaway = BigDecimal.ZERO;                // 外卖 (50)
        private BigDecimal purchase = BigDecimal.ZERO;                // 采购 (20)
        private BigDecimal flash = BigDecimal.ZERO;                   // 闪送 (130)
        private BigDecimal express = BigDecimal.ZERO;                 // 快递 (131)
        private BigDecimal freight = BigDecimal.ZERO;                 // 货运 (150)
        private BigDecimal other = BigDecimal.ZERO;                   // 其他订单 (911)
        private BigDecimal virtualCard = BigDecimal.ZERO;             // 虚拟卡 (126)
        private BigDecimal corporatePayment = BigDecimal.ZERO;        // 对公付款 (128)
        private BigDecimal valueAddedService = BigDecimal.ZERO;       // 增值服务 (913)
        private BigDecimal hotelOverseas = BigDecimal.ZERO;           // 海外酒店 (110)
        
        // 添加含税和不含税总金额
        private BigDecimal includeTaxAmount = BigDecimal.ZERO;        // 参考含税总金额
        private BigDecimal excludeTaxAmount = BigDecimal.ZERO;        // 参考不含税总金额
        
        // 新增住宿费相关属性（酒店+海外酒店）
        private BigDecimal accommodationFee = BigDecimal.ZERO;        // 住宿费总金额
        private BigDecimal accommodationFeeIncludeTax = BigDecimal.ZERO; // 住宿费含税总金额
        private BigDecimal accommodationFeeExcludeTax = BigDecimal.ZERO; // 住宿费不含税总金额
        
        // 住宿费专票相关属性
        private BigDecimal accommodationFeeSpecial = BigDecimal.ZERO;       // 住宿费专票总金额
        private BigDecimal accommodationFeeSpecialIncludeTax = BigDecimal.ZERO; // 住宿费专票含税金额
        private BigDecimal accommodationFeeSpecialExcludeTax = BigDecimal.ZERO; // 住宿费专票不含税金额
        
        // 住宿费普票相关属性
        private BigDecimal accommodationFeeOrdinary = BigDecimal.ZERO;      // 住宿费普票总金额
        private BigDecimal accommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO; // 住宿费普票含税金额
        private BigDecimal accommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO; // 住宿费普票不含税金额

        // 为每个订单类别添加含税和不含税金额
        // 国内机票 (7)
        private BigDecimal airTicketDomesticIncludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketDomesticExcludeTax = BigDecimal.ZERO;
        
        // 国际机票 (40)
        private BigDecimal airTicketInternationalIncludeTax = BigDecimal.ZERO;
        private BigDecimal airTicketInternationalExcludeTax = BigDecimal.ZERO;

        // 新增机票含税总额和税额字段
        private BigDecimal airTicketTotalIncludeTax = BigDecimal.ZERO;  // 机票含税总额(国内+国际)
        private BigDecimal airTicketTax = BigDecimal.ZERO;              // 机票税额(含税总额*9%)
        
        // 酒店 (11)
        private BigDecimal hotelIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelExcludeTax = BigDecimal.ZERO;
        
        // 火车 (15)
        private BigDecimal trainIncludeTax = BigDecimal.ZERO;
        private BigDecimal trainExcludeTax = BigDecimal.ZERO;
        
        // 汽车票 (135)
        private BigDecimal busIncludeTax = BigDecimal.ZERO;
        private BigDecimal busExcludeTax = BigDecimal.ZERO;
        
        // 用车 (3)
        private BigDecimal carIncludeTax = BigDecimal.ZERO;
        private BigDecimal carExcludeTax = BigDecimal.ZERO;
        
        // 用餐 (60)
        private BigDecimal mealIncludeTax = BigDecimal.ZERO;
        private BigDecimal mealExcludeTax = BigDecimal.ZERO;
        
        // 外卖 (50)
        private BigDecimal takeawayIncludeTax = BigDecimal.ZERO;
        private BigDecimal takeawayExcludeTax = BigDecimal.ZERO;
        
        // 采购 (20)
        private BigDecimal purchaseIncludeTax = BigDecimal.ZERO;
        private BigDecimal purchaseExcludeTax = BigDecimal.ZERO;
        
        // 闪送 (130)
        private BigDecimal flashIncludeTax = BigDecimal.ZERO;
        private BigDecimal flashExcludeTax = BigDecimal.ZERO;
        
        // 快递 (131)
        private BigDecimal expressIncludeTax = BigDecimal.ZERO;
        private BigDecimal expressExcludeTax = BigDecimal.ZERO;
        
        // 货运 (150)
        private BigDecimal freightIncludeTax = BigDecimal.ZERO;
        private BigDecimal freightExcludeTax = BigDecimal.ZERO;
        
        // 其他订单 (911)
        private BigDecimal otherIncludeTax = BigDecimal.ZERO;
        private BigDecimal otherExcludeTax = BigDecimal.ZERO;
        
        // 虚拟卡 (126)
        private BigDecimal virtualCardIncludeTax = BigDecimal.ZERO;
        private BigDecimal virtualCardExcludeTax = BigDecimal.ZERO;
        
        // 对公付款 (128)
        private BigDecimal corporatePaymentIncludeTax = BigDecimal.ZERO;
        private BigDecimal corporatePaymentExcludeTax = BigDecimal.ZERO;
        
        // 增值服务 (913)
        private BigDecimal valueAddedServiceIncludeTax = BigDecimal.ZERO;
        private BigDecimal valueAddedServiceExcludeTax = BigDecimal.ZERO;
        
        // 海外酒店 (110)
        private BigDecimal hotelOverseasIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasExcludeTax = BigDecimal.ZERO;
        
        // 酒店专票 (11)
        private BigDecimal hotelSpecial = BigDecimal.ZERO;
        private BigDecimal hotelSpecialIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelSpecialExcludeTax = BigDecimal.ZERO;
        
        // 酒店普票 (11)
        private BigDecimal hotelOrdinary = BigDecimal.ZERO;
        private BigDecimal hotelOrdinaryIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOrdinaryExcludeTax = BigDecimal.ZERO;
        
        // 海外酒店专票 (110)
        private BigDecimal hotelOverseasSpecial = BigDecimal.ZERO;
        private BigDecimal hotelOverseasSpecialIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasSpecialExcludeTax = BigDecimal.ZERO;
        
        // 海外酒店普票 (110)
        private BigDecimal hotelOverseasOrdinary = BigDecimal.ZERO;
        private BigDecimal hotelOverseasOrdinaryIncludeTax = BigDecimal.ZERO;
        private BigDecimal hotelOverseasOrdinaryExcludeTax = BigDecimal.ZERO;
        
        // 整合的住宿费数据（酒店+海外酒店）
        private BigDecimal totalAccommodationFee = BigDecimal.ZERO;        // 住宿费总金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeIncludeTax = BigDecimal.ZERO; // 住宿费含税总金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeExcludeTax = BigDecimal.ZERO; // 住宿费不含税总金额（酒店+海外酒店）
        
        // 整合的住宿费专票数据（酒店专票+海外酒店专票）
        private BigDecimal totalAccommodationFeeSpecial = BigDecimal.ZERO;       // 住宿费专票总金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeSpecialIncludeTax = BigDecimal.ZERO; // 住宿费专票含税金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeSpecialExcludeTax = BigDecimal.ZERO; // 住宿费专票不含税金额（酒店+海外酒店）
        
        // 整合的住宿费普票数据（酒店普票+海外酒店普票）
        private BigDecimal totalAccommodationFeeOrdinary = BigDecimal.ZERO;      // 住宿费普票总金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeOrdinaryIncludeTax = BigDecimal.ZERO; // 住宿费普票含税金额（酒店+海外酒店）
        private BigDecimal totalAccommodationFeeOrdinaryExcludeTax = BigDecimal.ZERO; // 住宿费普票不含税金额（酒店+海外酒店）
        
        // 代打火车服务费
        private BigDecimal manualPrice = BigDecimal.ZERO;

        // 新增代打火车服务费税额
        private BigDecimal manualPriceTax = BigDecimal.ZERO;
        
        // 机建费 (国内机票和国际机票)
        private BigDecimal airportFee = BigDecimal.ZERO;
        
        // 燃油费 (国内机票和国际机票)
        private BigDecimal fuelFee = BigDecimal.ZERO;

        // 火车票税额
        private BigDecimal trainTax = BigDecimal.ZERO;

        public ProjectInfo(String projectCode) {
            this.projectCode = projectCode;
            this.relatedCompanies = new HashSet<>();
            this.additionalInfo = new HashMap<>();
            this.billStatus = 0; // 默认状态
            this.relatedCompaniesStr = ""; // 初始化为空字符串
        }

        public String getProjectCode() {
            return projectCode;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getProjectName() {
            return projectName != null ? projectName : projectCode;
        }

        public String getBillCode() {
            return billCode;
        }

        public void setBillCode(String billCode) {
            this.billCode = billCode;
        }

        public String getBillStartTime() {
            return billStartTime;
        }

        public void setBillStartTime(String billStartTime) {
            this.billStartTime = billStartTime;
        }

        public String getBillEndTime() {
            return billEndTime;
        }

        public void setBillEndTime(String billEndTime) {
            this.billEndTime = billEndTime;
        }

        public int getBillStatus() {
            return billStatus;
        }

        public void setBillStatus(int billStatus) {
            this.billStatus = billStatus;
        }

        // 获取账单状态的描述
        public String getBillStatusDesc() {
            switch (billStatus) {
                case 1:
                    return "已出账单";
                case 2:
                    return "已核对（已弃用）";
                case 3:
                    return "结算中";
                case 4:
                    return "已结算";
                case 5:
                    return "未出账单";
                default:
                    return "未知状态(" + billStatus + ")";
            }
        }

        /**
         * 添加订单类别金额
         *
         * @param category 订单类别ID
         * @param amount   金额
         */
        public void addCategoryAmount(int category, BigDecimal amount) {
            switch (category) {
                case 7:  // 国内机票
                    airTicketDomestic = airTicketDomestic.add(amount);
                    break;
                case 40: // 国际机票
                    airTicketInternational = airTicketInternational.add(amount);
                    break;
                case 11: // 酒店
                    hotel = hotel.add(amount);
                    // 更新住宿费总金额
                    accommodationFee = accommodationFee.add(amount);
                    break;
                case 15: // 火车
                    train = train.add(amount);
                    break;
                case 135: // 汽车票
                    bus = bus.add(amount);
                    break;
                case 3:  // 用车
                    car = car.add(amount);
                    break;
                case 60: // 用餐
                    meal = meal.add(amount);
                    break;
                case 50: // 外卖
                    takeaway = takeaway.add(amount);
                    break;
                case 20: // 采购
                    purchase = purchase.add(amount);
                    break;
                case 130: // 闪送
                    flash = flash.add(amount);
                    break;
                case 131: // 快递
                    express = express.add(amount);
                    break;
                case 150: // 货运
                    freight = freight.add(amount);
                    break;
                case 911: // 其他订单
                    other = other.add(amount);
                    break;
                case 126: // 虚拟卡
                    virtualCard = virtualCard.add(amount);
                    break;
                case 128: // 对公付款
                    corporatePayment = corporatePayment.add(amount);
                    break;
                case 913: // 增值服务
                    valueAddedService = valueAddedService.add(amount);
                    break;
                case 110: // 海外酒店
                    hotelOverseas = hotelOverseas.add(amount);
                    // 更新住宿费总金额
                    accommodationFee = accommodationFee.add(amount);
                    break;
                default:
                    // 未知类别，记录到其他
                    other = other.add(amount);
                    break;
            }
        }

        /**
         * 添加订单类别含税金额
         *
         * @param category 订单类别ID
         * @param amount   含税金额
         */
        public void addCategoryIncludeTaxAmount(int category, BigDecimal amount) {
            if (amount == null) return;
            
            switch (category) {
                case 7:  // 国内机票
                    airTicketDomesticIncludeTax = airTicketDomesticIncludeTax.add(amount);
                    // 更新机票含税总额和税额
                    updateAirTicketTaxes(amount);
                    break;
                case 40: // 国际机票
                    airTicketInternationalIncludeTax = airTicketInternationalIncludeTax.add(amount);
                    // 更新机票含税总额和税额
                    updateAirTicketTaxes(amount);
                    break;
                case 11: // 酒店
                    hotelIncludeTax = hotelIncludeTax.add(amount);
                    // 更新住宿费含税总金额
                    accommodationFeeIncludeTax = accommodationFeeIncludeTax.add(amount);
                    break;
                case 15: // 火车
                    trainIncludeTax = trainIncludeTax.add(amount);
                    // 如果不含税金额已经存在，计算并更新税额
                    updateTrainTax();
                    break;
                case 135: // 汽车票
                    busIncludeTax = busIncludeTax.add(amount);
                    break;
                case 3:  // 用车
                    carIncludeTax = carIncludeTax.add(amount);
                    break;
                case 60: // 用餐
                    mealIncludeTax = mealIncludeTax.add(amount);
                    break;
                case 50: // 外卖
                    takeawayIncludeTax = takeawayIncludeTax.add(amount);
                    break;
                case 20: // 采购
                    purchaseIncludeTax = purchaseIncludeTax.add(amount);
                    break;
                case 130: // 闪送
                    flashIncludeTax = flashIncludeTax.add(amount);
                    break;
                case 131: // 快递
                    expressIncludeTax = expressIncludeTax.add(amount);
                    break;
                case 150: // 货运
                    freightIncludeTax = freightIncludeTax.add(amount);
                    break;
                case 911: // 其他订单
                    otherIncludeTax = otherIncludeTax.add(amount);
                    break;
                case 126: // 虚拟卡
                    virtualCardIncludeTax = virtualCardIncludeTax.add(amount);
                    break;
                case 128: // 对公付款
                    corporatePaymentIncludeTax = corporatePaymentIncludeTax.add(amount);
                    break;
                case 913: // 增值服务
                    valueAddedServiceIncludeTax = valueAddedServiceIncludeTax.add(amount);
                    break;
                case 110: // 海外酒店
                    hotelOverseasIncludeTax = hotelOverseasIncludeTax.add(amount);
                    // 更新住宿费含税总金额
                    accommodationFeeIncludeTax = accommodationFeeIncludeTax.add(amount);
                    break;
                default:
                    // 未知类别，记录到其他
                    otherIncludeTax = otherIncludeTax.add(amount);
                    break;
            }
            
            // 同时更新总含税金额
            includeTaxAmount = includeTaxAmount.add(amount);
        }

        /**
         * 添加订单类别不含税金额
         *
         * @param category 订单类别ID
         * @param amount   不含税金额
         */
        public void addCategoryExcludeTaxAmount(int category, BigDecimal amount) {
            if (amount == null) return;
            
            switch (category) {
                case 7:  // 国内机票
                    airTicketDomesticExcludeTax = airTicketDomesticExcludeTax.add(amount);
                    break;
                case 40: // 国际机票
                    airTicketInternationalExcludeTax = airTicketInternationalExcludeTax.add(amount);
                    break;
                case 11: // 酒店
                    hotelExcludeTax = hotelExcludeTax.add(amount);
                    // 更新住宿费不含税总金额
                    accommodationFeeExcludeTax = accommodationFeeExcludeTax.add(amount);
                    break;
                case 15: // 火车
                    trainExcludeTax = trainExcludeTax.add(amount);
                    // 如果含税金额已经存在，计算并更新税额
                    updateTrainTax();
                    break;
                case 135: // 汽车票
                    busExcludeTax = busExcludeTax.add(amount);
                    break;
                case 3:  // 用车
                    carExcludeTax = carExcludeTax.add(amount);
                    break;
                case 60: // 用餐
                    mealExcludeTax = mealExcludeTax.add(amount);
                    break;
                case 50: // 外卖
                    takeawayExcludeTax = takeawayExcludeTax.add(amount);
                    break;
                case 20: // 采购
                    purchaseExcludeTax = purchaseExcludeTax.add(amount);
                    break;
                case 130: // 闪送
                    flashExcludeTax = flashExcludeTax.add(amount);
                    break;
                case 131: // 快递
                    expressExcludeTax = expressExcludeTax.add(amount);
                    break;
                case 150: // 货运
                    freightExcludeTax = freightExcludeTax.add(amount);
                    break;
                case 911: // 其他订单
                    otherExcludeTax = otherExcludeTax.add(amount);
                    break;
                case 126: // 虚拟卡
                    virtualCardExcludeTax = virtualCardExcludeTax.add(amount);
                    break;
                case 128: // 对公付款
                    corporatePaymentExcludeTax = corporatePaymentExcludeTax.add(amount);
                    break;
                case 913: // 增值服务
                    valueAddedServiceExcludeTax = valueAddedServiceExcludeTax.add(amount);
                    break;
                case 110: // 海外酒店
                    hotelOverseasExcludeTax = hotelOverseasExcludeTax.add(amount);
                    // 更新住宿费不含税总金额
                    accommodationFeeExcludeTax = accommodationFeeExcludeTax.add(amount);
                    break;
                default:
                    // 未知类别，记录到其他
                    otherExcludeTax = otherExcludeTax.add(amount);
                    break;
            }
            
            // 同时更新总不含税金额
            excludeTaxAmount = excludeTaxAmount.add(amount);
        }
        
        /**
         * 添加住宿费专票金额（根据发票类型和订单类别）
         * 
         * @param category         订单类别ID（11:酒店 或 110:海外酒店）
         * @param amount           金额
         * @param includeTaxAmount 含税金额
         * @param excludeTaxAmount 不含税金额
         */
        public void addAccommodationFeeSpecial(int category, BigDecimal amount, BigDecimal includeTaxAmount, BigDecimal excludeTaxAmount) {
            if (amount == null) return;
            
            accommodationFeeSpecial = accommodationFeeSpecial.add(amount);
            
            if (includeTaxAmount != null) {
                accommodationFeeSpecialIncludeTax = accommodationFeeSpecialIncludeTax.add(includeTaxAmount);
            }
            
            if (excludeTaxAmount != null) {
                accommodationFeeSpecialExcludeTax = accommodationFeeSpecialExcludeTax.add(excludeTaxAmount);
            }
            
            // 更新整合的住宿费专票数据
            totalAccommodationFeeSpecial = totalAccommodationFeeSpecial.add(amount);
            if (includeTaxAmount != null) {
                totalAccommodationFeeSpecialIncludeTax = totalAccommodationFeeSpecialIncludeTax.add(includeTaxAmount);
            }
            if (excludeTaxAmount != null) {
                totalAccommodationFeeSpecialExcludeTax = totalAccommodationFeeSpecialExcludeTax.add(excludeTaxAmount);
            }
            
            // 更新整合的住宿费总数据
            totalAccommodationFee = totalAccommodationFee.add(amount);
            if (includeTaxAmount != null) {
                totalAccommodationFeeIncludeTax = totalAccommodationFeeIncludeTax.add(includeTaxAmount);
            }
            if (excludeTaxAmount != null) {
                totalAccommodationFeeExcludeTax = totalAccommodationFeeExcludeTax.add(excludeTaxAmount);
            }
            
            // 根据类别更新对应的酒店专票或海外酒店专票金额
            if (category == 11) { // 酒店
                hotelSpecial = hotelSpecial.add(amount);
                if (includeTaxAmount != null) {
                    hotelSpecialIncludeTax = hotelSpecialIncludeTax.add(includeTaxAmount);
                }
                if (excludeTaxAmount != null) {
                    hotelSpecialExcludeTax = hotelSpecialExcludeTax.add(excludeTaxAmount);
                }
            } else if (category == 110) { // 海外酒店
                hotelOverseasSpecial = hotelOverseasSpecial.add(amount);
                if (includeTaxAmount != null) {
                    hotelOverseasSpecialIncludeTax = hotelOverseasSpecialIncludeTax.add(includeTaxAmount);
                }
                if (excludeTaxAmount != null) {
                    hotelOverseasSpecialExcludeTax = hotelOverseasSpecialExcludeTax.add(excludeTaxAmount);
                }
            }
        }
        
        /**
         * 添加住宿费普票金额（根据发票类型和订单类别）
         * 
         * @param category         订单类别ID（11:酒店 或 110:海外酒店）
         * @param amount           金额
         * @param includeTaxAmount 含税金额
         * @param excludeTaxAmount 不含税金额
         */
        public void addAccommodationFeeOrdinary(int category, BigDecimal amount, BigDecimal includeTaxAmount, BigDecimal excludeTaxAmount) {
            if (amount == null) return;
            
            accommodationFeeOrdinary = accommodationFeeOrdinary.add(amount);
            
            if (includeTaxAmount != null) {
                accommodationFeeOrdinaryIncludeTax = accommodationFeeOrdinaryIncludeTax.add(includeTaxAmount);
            }
            
            if (excludeTaxAmount != null) {
                accommodationFeeOrdinaryExcludeTax = accommodationFeeOrdinaryExcludeTax.add(excludeTaxAmount);
            }
            
            // 更新整合的住宿费普票数据
            totalAccommodationFeeOrdinary = totalAccommodationFeeOrdinary.add(amount);
            if (includeTaxAmount != null) {
                totalAccommodationFeeOrdinaryIncludeTax = totalAccommodationFeeOrdinaryIncludeTax.add(includeTaxAmount);
            }
            if (excludeTaxAmount != null) {
                totalAccommodationFeeOrdinaryExcludeTax = totalAccommodationFeeOrdinaryExcludeTax.add(excludeTaxAmount);
            }
            
            // 更新整合的住宿费总数据
            totalAccommodationFee = totalAccommodationFee.add(amount);
            if (includeTaxAmount != null) {
                totalAccommodationFeeIncludeTax = totalAccommodationFeeIncludeTax.add(includeTaxAmount);
            }
            if (excludeTaxAmount != null) {
                totalAccommodationFeeExcludeTax = totalAccommodationFeeExcludeTax.add(excludeTaxAmount);
            }
            
            // 根据类别更新对应的酒店普票或海外酒店普票金额
            if (category == 11) { // 酒店
                hotelOrdinary = hotelOrdinary.add(amount);
                if (includeTaxAmount != null) {
                    hotelOrdinaryIncludeTax = hotelOrdinaryIncludeTax.add(includeTaxAmount);
                }
                if (excludeTaxAmount != null) {
                    hotelOrdinaryExcludeTax = hotelOrdinaryExcludeTax.add(excludeTaxAmount);
                }
            } else if (category == 110) { // 海外酒店
                hotelOverseasOrdinary = hotelOverseasOrdinary.add(amount);
                if (includeTaxAmount != null) {
                    hotelOverseasOrdinaryIncludeTax = hotelOverseasOrdinaryIncludeTax.add(includeTaxAmount);
                }
                if (excludeTaxAmount != null) {
                    hotelOverseasOrdinaryExcludeTax = hotelOverseasOrdinaryExcludeTax.add(excludeTaxAmount);
                }
            }
        }
        
        // 以下是新增的getter方法
        
        public BigDecimal getAccommodationFee() {
            return accommodationFee;
        }
        
        public BigDecimal getAccommodationFeeIncludeTax() {
            return accommodationFeeIncludeTax;
        }
        
        public BigDecimal getAccommodationFeeExcludeTax() {
            return accommodationFeeExcludeTax;
        }
        
        public BigDecimal getAccommodationFeeSpecial() {
            return accommodationFeeSpecial;
        }
        
        public BigDecimal getAccommodationFeeSpecialIncludeTax() {
            return accommodationFeeSpecialIncludeTax;
        }
        
        public BigDecimal getAccommodationFeeSpecialExcludeTax() {
            return accommodationFeeSpecialExcludeTax;
        }
        
        public BigDecimal getAccommodationFeeOrdinary() {
            return accommodationFeeOrdinary;
        }
        
        public BigDecimal getAccommodationFeeOrdinaryIncludeTax() {
            return accommodationFeeOrdinaryIncludeTax;
        }
        
        public BigDecimal getAccommodationFeeOrdinaryExcludeTax() {
            return accommodationFeeOrdinaryExcludeTax;
        }
        
        public BigDecimal getHotelSpecial() {
            return hotelSpecial;
        }
        
        public BigDecimal getHotelSpecialIncludeTax() {
            return hotelSpecialIncludeTax;
        }
        
        public BigDecimal getHotelSpecialExcludeTax() {
            return hotelSpecialExcludeTax;
        }
        
        public BigDecimal getHotelOrdinary() {
            return hotelOrdinary;
        }
        
        public BigDecimal getHotelOrdinaryIncludeTax() {
            return hotelOrdinaryIncludeTax;
        }
        
        public BigDecimal getHotelOrdinaryExcludeTax() {
            return hotelOrdinaryExcludeTax;
        }
        
        public BigDecimal getHotelOverseasSpecial() {
            return hotelOverseasSpecial;
        }
        
        public BigDecimal getHotelOverseasSpecialIncludeTax() {
            return hotelOverseasSpecialIncludeTax;
        }
        
        public BigDecimal getHotelOverseasSpecialExcludeTax() {
            return hotelOverseasSpecialExcludeTax;
        }
        
        public BigDecimal getHotelOverseasOrdinary() {
            return hotelOverseasOrdinary;
        }
        
        public BigDecimal getHotelOverseasOrdinaryIncludeTax() {
            return hotelOverseasOrdinaryIncludeTax;
        }
        
        public BigDecimal getHotelOverseasOrdinaryExcludeTax() {
            return hotelOverseasOrdinaryExcludeTax;
        }

        // 以下是原有的方法
        // ... existing code ...

        /**
         * 添加订单类别金额
         *
         * @param category 订单类别ID
         * @param amount   金额
         */
        // public void addCategoryAmount(int category, BigDecimal amount) {
        //     switch (category) {
        //         case 7:  // 国内机票
        //             airTicketDomestic = airTicketDomestic.add(amount);
        //             break;
        //         case 40: // 国际机票
        //             airTicketInternational = airTicketInternational.add(amount);
        //             break;
        //         case 11: // 酒店
        //             hotel = hotel.add(amount);
        //             break;
        //         case 15: // 火车
        //             train = train.add(amount);
        //             break;
        //         case 135: // 汽车票
        //             bus = bus.add(amount);
        //             break;
        //         case 3:  // 用车
        //             car = car.add(amount);
        //             break;
        //         case 60: // 用餐
        //             meal = meal.add(amount);
        //             break;
        //         case 50: // 外卖
        //             takeaway = takeaway.add(amount);
        //             break;
        //         case 20: // 采购
        //             purchase = purchase.add(amount);
        //             break;
        //         case 130: // 闪送
        //             flash = flash.add(amount);
        //             break;
        //         case 131: // 快递
        //             express = express.add(amount);
        //             break;
        //         case 150: // 货运
        //             freight = freight.add(amount);
        //             break;
        //         case 911: // 其他订单
        //             other = other.add(amount);
        //             break;
        //         case 126: // 虚拟卡
        //             virtualCard = virtualCard.add(amount);
        //             break;
        //         case 128: // 对公付款
        //             corporatePayment = corporatePayment.add(amount);
        //             break;
        //         case 913: // 增值服务
        //             valueAddedService = valueAddedService.add(amount);
        //             break;
        //         case 110: // 海外酒店
        //             hotelOverseas = hotelOverseas.add(amount);
        //             break;
        //         default:
        //             // 未知类别，记录到其他
        //             other = other.add(amount);
        //             break;
        //     }
        // }
        //
        // /**
        //  * 添加订单类别含税金额
        //  *
        //  * @param category 订单类别ID
        //  * @param amount   含税金额
        //  */
        // public void addCategoryIncludeTaxAmount(int category, BigDecimal amount) {
        //     if (amount == null) return;
        //
        //     switch (category) {
        //         case 7:  // 国内机票
        //             airTicketDomesticIncludeTax = airTicketDomesticIncludeTax.add(amount);
        //             break;
        //         case 40: // 国际机票
        //             airTicketInternationalIncludeTax = airTicketInternationalIncludeTax.add(amount);
        //             break;
        //         case 11: // 酒店
        //             hotelIncludeTax = hotelIncludeTax.add(amount);
        //             break;
        //         case 15: // 火车
        //             trainIncludeTax = trainIncludeTax.add(amount);
        //             break;
        //         case 135: // 汽车票
        //             busIncludeTax = busIncludeTax.add(amount);
        //             break;
        //         case 3:  // 用车
        //             carIncludeTax = carIncludeTax.add(amount);
        //             break;
        //         case 60: // 用餐
        //             mealIncludeTax = mealIncludeTax.add(amount);
        //             break;
        //         case 50: // 外卖
        //             takeawayIncludeTax = takeawayIncludeTax.add(amount);
        //             break;
        //         case 20: // 采购
        //             purchaseIncludeTax = purchaseIncludeTax.add(amount);
        //             break;
        //         case 130: // 闪送
        //             flashIncludeTax = flashIncludeTax.add(amount);
        //             break;
        //         case 131: // 快递
        //             expressIncludeTax = expressIncludeTax.add(amount);
        //             break;
        //         case 150: // 货运
        //             freightIncludeTax = freightIncludeTax.add(amount);
        //             break;
        //         case 911: // 其他订单
        //             otherIncludeTax = otherIncludeTax.add(amount);
        //             break;
        //         case 126: // 虚拟卡
        //             virtualCardIncludeTax = virtualCardIncludeTax.add(amount);
        //             break;
        //         case 128: // 对公付款
        //             corporatePaymentIncludeTax = corporatePaymentIncludeTax.add(amount);
        //             break;
        //         case 913: // 增值服务
        //             valueAddedServiceIncludeTax = valueAddedServiceIncludeTax.add(amount);
        //             break;
        //         case 110: // 海外酒店
        //             hotelOverseasIncludeTax = hotelOverseasIncludeTax.add(amount);
        //             break;
        //         default:
        //             // 未知类别，记录到其他
        //             otherIncludeTax = otherIncludeTax.add(amount);
        //             break;
        //     }
        //
        //     // 同时更新总含税金额
        //     includeTaxAmount = includeTaxAmount.add(amount);
        // }
        //
        // /**
        //  * 添加订单类别不含税金额
        //  *
        //  * @param category 订单类别ID
        //  * @param amount   不含税金额
        //  */
        // public void addCategoryExcludeTaxAmount(int category, BigDecimal amount) {
        //     if (amount == null) return;
        //
        //     switch (category) {
        //         case 7:  // 国内机票
        //             airTicketDomesticExcludeTax = airTicketDomesticExcludeTax.add(amount);
        //             break;
        //         case 40: // 国际机票
        //             airTicketInternationalExcludeTax = airTicketInternationalExcludeTax.add(amount);
        //             break;
        //         case 11: // 酒店
        //             hotelExcludeTax = hotelExcludeTax.add(amount);
        //             break;
        //         case 15: // 火车
        //             trainExcludeTax = trainExcludeTax.add(amount);
        //             break;
        //         case 135: // 汽车票
        //             busExcludeTax = busExcludeTax.add(amount);
        //             break;
        //         case 3:  // 用车
        //             carExcludeTax = carExcludeTax.add(amount);
        //             break;
        //         case 60: // 用餐
        //             mealExcludeTax = mealExcludeTax.add(amount);
        //             break;
        //         case 50: // 外卖
        //             takeawayExcludeTax = takeawayExcludeTax.add(amount);
        //             break;
        //         case 20: // 采购
        //             purchaseExcludeTax = purchaseExcludeTax.add(amount);
        //             break;
        //         case 130: // 闪送
        //             flashExcludeTax = flashExcludeTax.add(amount);
        //             break;
        //         case 131: // 快递
        //             expressExcludeTax = expressExcludeTax.add(amount);
        //             break;
        //         case 150: // 货运
        //             freightExcludeTax = freightExcludeTax.add(amount);
        //             break;
        //         case 911: // 其他订单
        //             otherExcludeTax = otherExcludeTax.add(amount);
        //             break;
        //         case 126: // 虚拟卡
        //             virtualCardExcludeTax = virtualCardExcludeTax.add(amount);
        //             break;
        //         case 128: // 对公付款
        //             corporatePaymentExcludeTax = corporatePaymentExcludeTax.add(amount);
        //             break;
        //         case 913: // 增值服务
        //             valueAddedServiceExcludeTax = valueAddedServiceExcludeTax.add(amount);
        //             break;
        //         case 110: // 海外酒店
        //             hotelOverseasExcludeTax = hotelOverseasExcludeTax.add(amount);
        //             break;
        //         default:
        //             // 未知类别，记录到其他
        //             otherExcludeTax = otherExcludeTax.add(amount);
        //             break;
        //     }
        //
        //     // 同时更新总不含税金额
        //     excludeTaxAmount = excludeTaxAmount.add(amount);
        // }

        /**
         * 获取订单类别金额Map
         *
         * @return 订单类别金额Map，key为类别ID，value为金额
         */
        public Map<Integer, BigDecimal> getCategoryAmounts() {
            Map<Integer, BigDecimal> categoryAmounts = new HashMap<>();

            // 只添加非零金额的类别
            if (airTicketDomestic.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(7, airTicketDomestic);
            }
            if (airTicketInternational.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(40, airTicketInternational);
            }
            if (hotel.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(11, hotel);
            }
            if (train.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(15, train);
            }
            if (bus.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(135, bus);
            }
            if (car.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(3, car);
            }
            if (meal.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(60, meal);
            }
            if (takeaway.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(50, takeaway);
            }
            if (purchase.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(20, purchase);
            }
            if (flash.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(130, flash);
            }
            if (express.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(131, express);
            }
            if (freight.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(150, freight);
            }
            if (other.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(911, other);
            }
            if (virtualCard.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(126, virtualCard);
            }
            if (corporatePayment.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(128, corporatePayment);
            }
            if (valueAddedService.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(913, valueAddedService);
            }
            if (hotelOverseas.compareTo(BigDecimal.ZERO) > 0) {
                categoryAmounts.put(110, hotelOverseas);
            }

            return categoryAmounts;
        }

        /**
         * 获取指定类别的金额
         *
         * @param category 类别ID
         * @return 金额
         */
        public BigDecimal getCategoryAmount(int category) {
            switch (category) {
                case 7:
                    return airTicketDomestic;
                case 40:
                    return airTicketInternational;
                case 11:
                    return hotel;
                case 15:
                    return train;
                case 135:
                    return bus;
                case 3:
                    return car;
                case 60:
                    return meal;
                case 50:
                    return takeaway;
                case 20:
                    return purchase;
                case 130:
                    return flash;
                case 131:
                    return express;
                case 150:
                    return freight;
                case 911:
                    return other;
                case 126:
                    return virtualCard;
                case 128:
                    return corporatePayment;
                case 913:
                    return valueAddedService;
                case 110:
                    return hotelOverseas;
                default:
                    return BigDecimal.ZERO;
            }
        }

        /**
         * 获取指定类别的含税金额
         *
         * @param category 类别ID
         * @return 含税金额
         */
        public BigDecimal getCategoryIncludeTaxAmount(int category) {
            switch (category) {
                case 7:
                    return airTicketDomesticIncludeTax;
                case 40:
                    return airTicketInternationalIncludeTax;
                case 11:
                    return hotelIncludeTax;
                case 15:
                    return trainIncludeTax;
                case 135:
                    return busIncludeTax;
                case 3:
                    return carIncludeTax;
                case 60:
                    return mealIncludeTax;
                case 50:
                    return takeawayIncludeTax;
                case 20:
                    return purchaseIncludeTax;
                case 130:
                    return flashIncludeTax;
                case 131:
                    return expressIncludeTax;
                case 150:
                    return freightIncludeTax;
                case 911:
                    return otherIncludeTax;
                case 126:
                    return virtualCardIncludeTax;
                case 128:
                    return corporatePaymentIncludeTax;
                case 913:
                    return valueAddedServiceIncludeTax;
                case 110:
                    return hotelOverseasIncludeTax;
                default:
                    return BigDecimal.ZERO;
            }
        }

        /**
         * 获取指定类别的不含税金额
         *
         * @param category 类别ID
         * @return 不含税金额
         */
        public BigDecimal getCategoryExcludeTaxAmount(int category) {
            switch (category) {
                case 7:
                    return airTicketDomesticExcludeTax;
                case 40:
                    return airTicketInternationalExcludeTax;
                case 11:
                    return hotelExcludeTax;
                case 15:
                    return trainExcludeTax;
                case 135:
                    return busExcludeTax;
                case 3:
                    return carExcludeTax;
                case 60:
                    return mealExcludeTax;
                case 50:
                    return takeawayExcludeTax;
                case 20:
                    return purchaseExcludeTax;
                case 130:
                    return flashExcludeTax;
                case 131:
                    return expressExcludeTax;
                case 150:
                    return freightExcludeTax;
                case 911:
                    return otherExcludeTax;
                case 126:
                    return virtualCardExcludeTax;
                case 128:
                    return corporatePaymentExcludeTax;
                case 913:
                    return valueAddedServiceExcludeTax;
                case 110:
                    return hotelOverseasExcludeTax;
                default:
                    return BigDecimal.ZERO;
            }
        }

        public void addRelatedCompany(String companyId) {
            relatedCompanies.add(companyId);

            // 如果是第一个添加的公司，设置为主要公司
            if (this.companyId == null || this.companyId.isEmpty()) {
                this.companyId = companyId;

                // 尝试从CompanyEnum获取公司名称
                try {
                    int id = Integer.parseInt(companyId);
                    CompanyEnum company = CompanyEnum.getById(id);
                    if (company != null) {
                        this.companyName = company.getName();
                    } else {
                        this.companyName = "未知公司(" + companyId + ")";
                    }
                } catch (NumberFormatException e) {
                    // 如果公司ID不是数字，直接使用ID作为名称
                    this.companyName = companyId;
                }
            }

            // 更新关联公司字符串
            updateRelatedCompaniesStr();
        }

        /**
         * 更新关联公司字符串
         */
        private void updateRelatedCompaniesStr() {
            if (relatedCompanies.isEmpty()) {
                relatedCompaniesStr = "";
                return;
            }

            StringBuilder sb = new StringBuilder();
            for (String id : relatedCompanies) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append(id);
            }
            relatedCompaniesStr = sb.toString();
        }

        public Set<String> getRelatedCompanies() {
            return relatedCompanies;
        }

        public String getCompanyId() {
            return companyId;
        }

        public void setCompanyId(String companyId) {
            this.companyId = companyId;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getRelatedCompaniesStr() {
            return relatedCompaniesStr;
        }

        public void setRelatedCompaniesStr(String relatedCompaniesStr) {
            this.relatedCompaniesStr = relatedCompaniesStr;
        }

        public void addAdditionalInfo(String key, Object value) {
            additionalInfo.put(key, value);
        }

        public Object getAdditionalInfo(String key) {
            return additionalInfo.get(key);
        }

        public Map<String, Object> getAllAdditionalInfo() {
            return additionalInfo;
        }

        public BigDecimal getTotalAmount() {
            BigDecimal total = BigDecimal.ZERO;
            total = total.add(airTicketDomestic)
                    .add(airTicketInternational)
                    .add(hotel)
                    .add(train)
                    .add(bus)
                    .add(car)
                    .add(meal)
                    .add(takeaway)
                    .add(purchase)
                    .add(flash)
                    .add(express)
                    .add(freight)
                    .add(other)
                    .add(virtualCard)
                    .add(corporatePayment)
                    .add(valueAddedService)
                    .add(hotelOverseas);
            return total;
        }

        // Getter方法
        public BigDecimal getAirTicketDomestic() {
            return airTicketDomestic;
        }

        public BigDecimal getAirTicketInternational() {
            return airTicketInternational;
        }

        public BigDecimal getHotel() {
            return hotel;
        }

        public BigDecimal getTrain() {
            return train;
        }

        public BigDecimal getBus() {
            return bus;
        }

        public BigDecimal getCar() {
            return car;
        }

        public BigDecimal getMeal() {
            return meal;
        }

        public BigDecimal getTakeaway() {
            return takeaway;
        }

        public BigDecimal getPurchase() {
            return purchase;
        }

        public BigDecimal getFlash() {
            return flash;
        }

        public BigDecimal getExpress() {
            return express;
        }

        public BigDecimal getFreight() {
            return freight;
        }

        public BigDecimal getOther() {
            return other;
        }

        public BigDecimal getVirtualCard() {
            return virtualCard;
        }

        public BigDecimal getCorporatePayment() {
            return corporatePayment;
        }

        public BigDecimal getValueAddedService() {
            return valueAddedService;
        }

        public BigDecimal getHotelOverseas() {
            return hotelOverseas;
        }

        /**
         * 添加含税金额
         * 
         * @param amount 金额
         */
        public void addIncludeTaxAmount(BigDecimal amount) {
            if (amount != null) {
                this.includeTaxAmount = this.includeTaxAmount.add(amount);
            }
        }
        
        /**
         * 添加不含税金额
         * 
         * @param amount 金额
         */
        public void addExcludeTaxAmount(BigDecimal amount) {
            if (amount != null) {
                this.excludeTaxAmount = this.excludeTaxAmount.add(amount);
            }
        }

        // Getter方法
        public BigDecimal getIncludeTaxAmount() {
            return includeTaxAmount;
        }
        
        public BigDecimal getExcludeTaxAmount() {
            return excludeTaxAmount;
        }

        // 各类别含税金额的Getter方法
        public BigDecimal getAirTicketDomesticIncludeTax() {
            return airTicketDomesticIncludeTax;
        }

        public BigDecimal getAirTicketInternationalIncludeTax() {
            return airTicketInternationalIncludeTax;
        }

        public BigDecimal getHotelIncludeTax() {
            return hotelIncludeTax;
        }

        public BigDecimal getTrainIncludeTax() {
            return trainIncludeTax;
        }

        public BigDecimal getBusIncludeTax() {
            return busIncludeTax;
        }

        public BigDecimal getCarIncludeTax() {
            return carIncludeTax;
        }

        public BigDecimal getMealIncludeTax() {
            return mealIncludeTax;
        }

        public BigDecimal getTakeawayIncludeTax() {
            return takeawayIncludeTax;
        }

        public BigDecimal getPurchaseIncludeTax() {
            return purchaseIncludeTax;
        }

        public BigDecimal getFlashIncludeTax() {
            return flashIncludeTax;
        }

        public BigDecimal getExpressIncludeTax() {
            return expressIncludeTax;
        }

        public BigDecimal getFreightIncludeTax() {
            return freightIncludeTax;
        }

        public BigDecimal getOtherIncludeTax() {
            return otherIncludeTax;
        }

        public BigDecimal getVirtualCardIncludeTax() {
            return virtualCardIncludeTax;
        }

        public BigDecimal getCorporatePaymentIncludeTax() {
            return corporatePaymentIncludeTax;
        }

        public BigDecimal getValueAddedServiceIncludeTax() {
            return valueAddedServiceIncludeTax;
        }

        public BigDecimal getHotelOverseasIncludeTax() {
            return hotelOverseasIncludeTax;
        }

        // 各类别不含税金额的Getter方法
        public BigDecimal getAirTicketDomesticExcludeTax() {
            return airTicketDomesticExcludeTax;
        }

        public BigDecimal getAirTicketInternationalExcludeTax() {
            return airTicketInternationalExcludeTax;
        }

        public BigDecimal getHotelExcludeTax() {
            return hotelExcludeTax;
        }

        public BigDecimal getTrainExcludeTax() {
            return trainExcludeTax;
        }

        public BigDecimal getBusExcludeTax() {
            return busExcludeTax;
        }

        public BigDecimal getCarExcludeTax() {
            return carExcludeTax;
        }

        public BigDecimal getMealExcludeTax() {
            return mealExcludeTax;
        }

        public BigDecimal getTakeawayExcludeTax() {
            return takeawayExcludeTax;
        }

        public BigDecimal getPurchaseExcludeTax() {
            return purchaseExcludeTax;
        }

        public BigDecimal getFlashExcludeTax() {
            return flashExcludeTax;
        }

        public BigDecimal getExpressExcludeTax() {
            return expressExcludeTax;
        }

        public BigDecimal getFreightExcludeTax() {
            return freightExcludeTax;
        }

        public BigDecimal getOtherExcludeTax() {
            return otherExcludeTax;
        }

        public BigDecimal getVirtualCardExcludeTax() {
            return virtualCardExcludeTax;
        }

        public BigDecimal getCorporatePaymentExcludeTax() {
            return corporatePaymentExcludeTax;
        }

        public BigDecimal getValueAddedServiceExcludeTax() {
            return valueAddedServiceExcludeTax;
        }

        public BigDecimal getHotelOverseasExcludeTax() {
            return hotelOverseasExcludeTax;
        }
        
        // 整合住宿费数据的getter方法
        public BigDecimal getTotalAccommodationFee() {
            return totalAccommodationFee;
        }
        
        public BigDecimal getTotalAccommodationFeeIncludeTax() {
            return totalAccommodationFeeIncludeTax;
        }
        
        public BigDecimal getTotalAccommodationFeeExcludeTax() {
            return totalAccommodationFeeExcludeTax;
        }
        
        // 整合住宿费专票数据的getter方法
        public BigDecimal getTotalAccommodationFeeSpecial() {
            return totalAccommodationFeeSpecial;
        }
        
        public BigDecimal getTotalAccommodationFeeSpecialIncludeTax() {
            return totalAccommodationFeeSpecialIncludeTax;
        }
        
        public BigDecimal getTotalAccommodationFeeSpecialExcludeTax() {
            return totalAccommodationFeeSpecialExcludeTax;
        }
        
        // 整合住宿费普票数据的getter方法
        public BigDecimal getTotalAccommodationFeeOrdinary() {
            return totalAccommodationFeeOrdinary;
        }
        
        public BigDecimal getTotalAccommodationFeeOrdinaryIncludeTax() {
            return totalAccommodationFeeOrdinaryIncludeTax;
        }
        
        public BigDecimal getTotalAccommodationFeeOrdinaryExcludeTax() {
            return totalAccommodationFeeOrdinaryExcludeTax;
        }
        
        // 代打火车服务费相关方法
        public void addManualPrice(BigDecimal amount) {
            if (amount != null) {
                this.manualPrice = this.manualPrice.add(amount);
                // 计算并累加代打火车服务费税额 (代打火车票服务费*0.06)
                BigDecimal tax = amount.multiply(new BigDecimal("0.06")).setScale(2, BigDecimal.ROUND_HALF_UP);
                this.manualPriceTax = this.manualPriceTax.add(tax);
            }
        }
        
        public BigDecimal getManualPrice() {
            return manualPrice;
        }

        // 新增获取代打火车服务费税额的方法
        public BigDecimal getManualPriceTax() {
            return manualPriceTax;
        }
        
        // 机建费相关方法
        public void addAirportFee(BigDecimal amount) {
            if (amount != null) {
                this.airportFee = this.airportFee.add(amount);
            }
        }
        
        public BigDecimal getAirportFee() {
            return airportFee;
        }
        
        // 燃油费相关方法
        public void addFuelFee(BigDecimal amount) {
            if (amount != null) {
                this.fuelFee = this.fuelFee.add(amount);
            }
        }
        
        public BigDecimal getFuelFee() {
            return fuelFee;
        }

        /**
         * 计算并更新火车票税额
         */
        private void updateTrainTax() {
            // 计算火车票税额 = 含税金额 - 不含税金额
            trainTax = trainIncludeTax.subtract(trainExcludeTax).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 确保税额不为负数
            if (trainTax.compareTo(BigDecimal.ZERO) < 0) {
                trainTax = BigDecimal.ZERO;
            }
        }

        /**
         * 获取火车票税额
         *
         * @return 火车票税额
         */
        public BigDecimal getTrainTax() {
            return trainTax;
        }

        /**
         * 更新机票含税总额和税额
         *
         * @param amount 新增的含税金额
         */
        private void updateAirTicketTaxes(BigDecimal amount) {
            // 累加到机票含税总额
            airTicketTotalIncludeTax = airTicketTotalIncludeTax.add(amount);

            // 计算税额 = 含税总额 * 9%
            airTicketTax = airTicketTotalIncludeTax.multiply(new BigDecimal("0.09"));
        }

        // 新增获取机票含税总额和税额的方法
        public BigDecimal getAirTicketTotalIncludeTax() {
            return airTicketTotalIncludeTax;
        }

        public BigDecimal getAirTicketTax() {
            return airTicketTax;
        }
    }

    /**
     * 根据批量ID获取项目信息
     * 使用原生JDBC查询，返回List<Map<String, String>>结构
     *
     * @param idList 数据库表主键ID列表
     * @return List<Map < String, String>>结构的项目信息，key为字段名，value为字段值（为空时返回""）
     */
    public List<Map<String, String>> getProjectInfoByIds(List<Integer> idList) {
        List<Map<String, String>> resultList = new ArrayList<>();

        if (idList == null || idList.isEmpty()) {
            System.out.println("ID列表为空，无法查询");
            return resultList;
        }

        // 数据库连接信息
        String DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String URL = "*********************************************************";
        String USERNAME = "lijunhong";
        String PASSWORD = "Cyitce@0106";
        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            // 加载驱动
            Class.forName(DRIVER);

            // 建立连接
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);

            // 构建IN子句
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < idList.size(); i++) {
                if (i > 0) {
                    inClause.append(",");
                }
                inClause.append("?");
            }

            // 准备查询语句
            String sql = "SELECT * FROM uf_fbt_project_info WHERE id IN (" + inClause.toString() + ")";
            pstmt = conn.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < idList.size(); i++) {
                pstmt.setInt(i + 1, idList.get(i));
            }

            System.out.println("执行查询: " + sql + " 参数: " + idList);

            // 执行查询
            java.sql.ResultSet rs = pstmt.executeQuery();
            java.sql.ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 字段名映射，用于将缩写字段名映射为原字段名
            Map<String, String> abbreviationsToOriginal = new HashMap<>();
            abbreviationsToOriginal.put("atdit", "air_ticket_domestic_include_tax");
            abbreviationsToOriginal.put("atdet", "air_ticket_domestic_exclude_tax");
            abbreviationsToOriginal.put("atiit", "air_ticket_international_include_tax");
            abbreviationsToOriginal.put("atiet", "air_ticket_international_exclude_tax");
            abbreviationsToOriginal.put("cpit", "corporate_payment_include_tax");
            abbreviationsToOriginal.put("cpet", "corporate_payment_exclude_tax");
            abbreviationsToOriginal.put("vasit", "value_added_service_include_tax");
            abbreviationsToOriginal.put("vaset", "value_added_service_exclude_tax");
            abbreviationsToOriginal.put("hoit", "hotel_overseas_include_tax");
            abbreviationsToOriginal.put("afsit", "accommodation_fee_special_include_tax");
            abbreviationsToOriginal.put("afset", "accommodation_fee_special_exclude_tax");
            abbreviationsToOriginal.put("hosit", "hotel_overseas_special_include_tax");
            abbreviationsToOriginal.put("hoset", "hotel_overseas_special_exclude_tax");
            abbreviationsToOriginal.put("afoit", "accommodation_fee_ordinary_include_tax");
            abbreviationsToOriginal.put("afoet", "accommodation_fee_ordinary_exclude_tax");
            abbreviationsToOriginal.put("hooit", "hotel_overseas_ordinary_include_tax");
            abbreviationsToOriginal.put("hooet", "hotel_overseas_ordinary_exclude_tax");
            abbreviationsToOriginal.put("tafit", "total_accommodation_fee_include_tax");
            abbreviationsToOriginal.put("tafet", "total_accommodation_fee_exclude_tax");
            abbreviationsToOriginal.put("tafs", "total_accommodation_fee_special");
            abbreviationsToOriginal.put("tafsit", "total_accommodation_fee_special_include_tax");
            abbreviationsToOriginal.put("tafset", "total_accommodation_fee_special_exclude_tax");
            abbreviationsToOriginal.put("tafo", "total_accommodation_fee_ordinary");
            abbreviationsToOriginal.put("tafoit", "total_accommodation_fee_ordinary_include_tax");
            abbreviationsToOriginal.put("tafoet", "total_accommodation_fee_ordinary_exclude_tax");

            // 遍历结果集
            while (rs.next()) {
                Map<String, String> rowMap = new HashMap<>();

                // 获取每一列的数据
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);

                    // 将所有值转换为字符串，如果值为null，设置为空字符串
                    String strValue = value != null ? value.toString() : "";
                    rowMap.put(columnName, strValue);

                    // 如果是缩写字段，添加对应的长字段名映射，以保持向后兼容性
                    if (abbreviationsToOriginal.containsKey(columnName)) {
                        rowMap.put(abbreviationsToOriginal.get(columnName), strValue);
                    }
                }

                resultList.add(rowMap);
            }

            System.out.println("查询完成，获取到 " + resultList.size() + " 条记录");

        } catch (Exception e) {
            System.out.println("查询项目信息时出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }

        return resultList;
    }

    /**
     * 根据单个ID获取项目信息
     *
     * @param id 数据库表主键ID
     * @return 项目信息Map，key为字段名，value为字段值（为空时返回""）
     */
    public Map<String, String> getProjectInfoById(Integer id) {
        if (id == null || id <= 0) {
            System.out.println("ID无效，无法查询");
            return new HashMap<>();
        }

        List<Integer> idList = new ArrayList<>();
        idList.add(id);

        List<Map<String, String>> resultList = getProjectInfoByIds(idList);

        // 如果有结果，返回第一条记录
        if (!resultList.isEmpty()) {
            return resultList.get(0);
        }

        // 没有结果，返回空Map
        return new HashMap<>();
    }


    public static Map<String, String> getCurrentDateTimeMap() {
        // 创建一个 HashMap 用于存储日期和时间
        Map<String, String> dateTimeMap = new HashMap<>();

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义日期格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期并存储到 map 中
        dateTimeMap.put("day", currentDate.format(dateFormatter));

        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 定义时间格式
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        // 格式化时间并存储到 map 中
        dateTimeMap.put("time", currentTime.format(timeFormatter));

        return dateTimeMap;
    }
}

/**
 * 所属费用公司主体  gszt
 * 账单所属月份 zdszyf 0对应1月  1对应2月 以此类推
 * 账单所属年份 zdsznf  是否是年份  2025
 */


