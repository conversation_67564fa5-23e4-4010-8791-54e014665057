package weaver.interfaces.workflow.action.cyitce.po;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.Pinyin4jUtil;
import weaver.soa.workflow.request.RequestInfo;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 生成人员的账号和编号
 */
public class GetUserSystemInfoAction extends BaseBean implements Action {
    public String loginidName;//登录账号
    public String passwordName;//登录密码
    public String codeName;//编号
    public String name;//姓名
    public String resourceIdName;//人员id

    @Override
    public String execute(RequestInfo requestInfo) {
        String loginid="";
        String password = "Abcd1234";
        String workcode="";
        String xm = "";
        String resourceIdVal = "";
        String andSql = "";

        if("".equals(loginidName)||loginidName==null){
            loginidName = "dlzh";
        }
        if ("".equals(passwordName)||passwordName==null){
            passwordName = "dlmm";
        }
        if ("".equals(codeName)||codeName==null) {
            codeName = "bh";
        }
        if ("".equals(name)||name==null) {
            name = "xm";
        }

        try {
            //        获取requestid
            String requestId = requestInfo.getRequestid();
            //        获取表单名称
            String tablename = requestInfo.getRequestManager().getBillTableName();

            RecordSet rs = new RecordSet();
            writeLog("select "+name+","+codeName+" from "+tablename+" where requestid="+requestId);
            String sql = "select "+name+","+codeName+" from "+tablename+" where requestid="+requestId;
            rs.execute(sql);
            //获取姓名首写字母
            if(rs.next()){
                xm = rs.getString("xm");
                //获取字符串中的中文
                Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]+");
                Matcher matcher = pattern.matcher(xm);
                StringBuilder sb = new StringBuilder();
                while (matcher.find()) {
                    sb.append(matcher.group());
                }
                xm = sb.toString();
                loginid = Pinyin4jUtil.getFirstPinYin(xm);
                workcode = rs.getString(2);
            }
            writeLog("首写字母："+loginid);

            //获取人员编号
            if(workcode==null||"".equals(workcode)){
                rs.executeProc("HrmResourceWorkCodeMaxNum_Get", "");
                rs.next();
                workcode = String.valueOf(rs.getInt(1));
                workcode = leftPad("H",workcode,4);

//                if(isHire){
//                    rs.executeProc("HrmResourceWorkCodeMaxNum_Get", "");
//                    rs.next();
//                    workcode = String.valueOf(rs.getInt(1));
//                    workcode = leftPad("H",workcode,4);
//                }else {
//                    sql = "select max(workcode)+1 from hrmresource";
//                    rs.execute(sql);
//                    rs.next();
//                    workcode=rs.getString(1);
//                }
                writeLog("人员编号："+workcode);
            }

            sql = "select workcode from hrmresource where workcode="+workcode;
            rs.execute(sql);
            writeLog("判断workcode："+workcode);
            if(rs.next()){
                writeLog("账号已存在");
                requestInfo.getRequestManager().setMessagecontent("账号已经存在，请联系IT处理");
                return Action.FAILURE_AND_CONTINUE;
            }

            //人员id
            sql = "select currentid from SequenceIndex where indexdesc='resourceid'";
            rs.execute(sql);
            resourceIdVal = rs.getString(1);
            if (!"".equals(resourceIdName)&&resourceIdName!=null) {
                andSql = ","+resourceIdName+"="+resourceIdVal;
            }
            writeLog("人员id："+resourceIdVal);
            writeLog("andSql："+andSql);

            loginid = loginid + workcode;
            xm = xm + workcode;
            writeLog("update "+tablename+" set "+loginidName+"="+loginid+","+passwordName+"="+password+","+codeName+"="+workcode+","+name+"="+xm+" where requestid="+requestId);
            sql = "update "+tablename+" set "+loginidName+"=?,"+passwordName+"=?,"+codeName+"=?,"+name+"=?"+andSql+" where requestid=?";
            boolean boo = rs.executeUpdate(sql, new Object[]{loginid, password, workcode, xm, requestId});
            if (!boo) {
                requestInfo.getRequestManager().setMessagecontent("人员账号信息生成失败");
                return Action.FAILURE_AND_CONTINUE;
            }else {
                return Action.SUCCESS;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return Action.FAILURE_AND_CONTINUE;
    }

    private String leftPad(String pre,String num,int len){
        while (num.length()<len){
            num="0"+num;
        }
        return pre+num;
    }
}
