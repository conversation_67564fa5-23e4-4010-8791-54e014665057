package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 发票作废释放冻结金额
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-07-04  16:27
 * @Version: 1.0
 */
public class ActionUpdateDdjeInFpzf2 extends BaseBean implements Action  {
    public ActionUpdateDdjeInFpzf2() {
    }

    public String execute(RequestInfo request) {
        String requestid = request.getRequestid();
        Property[] property = request.getMainTableInfo().getProperty();
        DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
        int billid = request.getRequestManager().getBillid();
        DetailTable dt = detailtable[0];
        String dtTableName = dt.getTableDBName();
        this.writeLog("明细表表名  :" + dtTableName);
        this.writeLog("明细表requestid  :" + requestid);
        this.writeLog("明细表mianid  :" + billid);
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        recordSetTrans.setAutoCommit(false);

        Map<String,String> mMap = new HashMap<>();
        for(Property p:property){
            mMap.put(p.getName(),p.getValue());
        }

        int fpzt;
        String fphm;
        int chzt;
        String chje;
        int mid;

        BigDecimal zfjeB = null;

        try {
            rs.execute("select fpzt,fphm,chje,id from " + dtTableName + " where mainid=" + billid);
            while (rs.next()){
                fpzt = rs.getInt(1);
                fphm = rs.getString(2);
                chje = rs.getString(3);//这里表示负数
                zfjeB = new BigDecimal(chje).abs();
                mid = rs.getInt(4);
                writeLog(fpzt);
                writeLog(fphm);
                writeLog(chje);
                writeLog(mid);

                String orderid = "";
                BigDecimal sfdjje = null;

                writeLog("main fpzt："+mMap.get("fpzt"));
                writeLog("zfjeB:"+zfjeB);

                if("0".equals(mMap.get("fpzt"))||"1".equals(mMap.get("fpzt"))){
                    writeLog("发票冲红作废");
                    rs2.executeQuery("select a.ddbh,b.kphsje from uf_ddkpsq a inner join uf_fptzdjb b on a.fpbhwb=b.fphm where b.fphm= ?",new Object[]{fphm});
                    if (rs2.next()) {
                        do {
                            writeLog("loop zfjeB:"+zfjeB);

                            //冲红金额是否已经释放完
                            if(zfjeB.compareTo(new BigDecimal(0))<=0){
                                break;
                            }

                            orderid = rs2.getString(1);
                            sfdjje = new BigDecimal(rs2.getString(2));

                            //释放冲红金额并从zfjeB扣除已经释放的金额
                            if(zfjeB.compareTo(sfdjje)>=0){
                                zfjeB = zfjeB.subtract(sfdjje).setScale(2, RoundingMode.HALF_UP);
                                chje = "-"+sfdjje.toString();
                            }else {
                                chje = "-"+zfjeB.toString();
                                zfjeB = new BigDecimal(0);
                            }

                            writeLog("订单id："+orderid);
                            recordSetTrans.execute("insert into uf_htkplcdjjemx(requestid,OrderID,djje,mxid,kpbh,type) values("+requestid+",'"+orderid+"',"+chje+","+mid+",'"+mMap.get("lcbh")+"',0)");
                        } while(rs2.next());
                    } else {
                        rs2.executeQuery("select a.htmc,b.kphsje from uf_htkpsq a inner join uf_fptzdjb b on a.fphmwb=b.fphm where b.fphm = ?",new Object[]{fphm});
                        if(rs2.next()){
                            //冲红金额是否已经释放完
                            if(zfjeB.compareTo(new BigDecimal(0))<=0){
                                break;
                            }

                            orderid = rs2.getString(1);
                            sfdjje = new BigDecimal(rs2.getString(2));

                            //释放冲红金额并从zfjeB扣除已经释放的金额
                            if(zfjeB.compareTo(sfdjje)>=0){
                                zfjeB = zfjeB.subtract(sfdjje).setScale(2, RoundingMode.HALF_UP);
                                chje = "-"+sfdjje.toString();
                            }else {
                                chje = "-"+zfjeB.toString();
                                zfjeB = new BigDecimal(0);
                            }

                            writeLog("合同id："+orderid);
                            recordSetTrans.execute("insert into uf_htkplcdjjemx(requestid,OrderID,djje,mxid,kpbh,type) values("+requestid+",'"+orderid+"',"+chje+","+mid+",'"+mMap.get("lcbh")+"',1)");
                        }
                    }
                }
            }

            recordSetTrans.commit();
            return Action.SUCCESS;
        }catch (Exception e){
            recordSetTrans.rollback();
            this.writeLog("更新失败，失败原因为" + e.getMessage());
            request.getRequestManager().setMessageid("流程提交失败");
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}