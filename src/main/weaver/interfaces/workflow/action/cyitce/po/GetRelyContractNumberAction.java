package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

public class GetRelyContractNumberAction extends BaseBean implements Action {

    public String ContractNubmerName;//合同登记流程编号字段名
    public String mainContractIdName;//主合同id字段名


    @Override
    public String execute(RequestInfo requestInfo) {
        String lsh = "1";
        String mainContractId = "";//主合同id
        String mainContractNumber = "";
        String ContractNumber = "";//流程内部合同编号

        if("".equals(ContractNubmerName)||ContractNubmerName==null){
            ContractNubmerName = "nbhtbh";
        }
        if("".equals(mainContractIdName)||mainContractIdName==null){
            mainContractIdName = "glzht";
        }
        //主表数据
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        writeLog("获取主表信息-------");
        for(int i = 0; i < properties.length; ++i) {
            if(mainContractIdName.equals(properties[i].getName())){
                mainContractId = properties[i].getValue();
                writeLog("主合同id:"+mainContractId);
            }
            if(ContractNubmerName.equals(properties[i].getName())){
                ContractNumber = properties[i].getValue();
            }
        }
        //是否关联主合同
        if("".equals(mainContractId)||mainContractId==null){
            writeLog("没有关联主合同，不是附属合同");
            return Action.SUCCESS;
        }

//        获取requestid
        String requestId = requestInfo.getRequestid();
        writeLog("获取流程requestid:"+requestId);
//        获取表单名称
        String tablename = requestInfo.getRequestManager().getBillTableName();
//        主合同信息
        writeLog("获取主合同信息sql："+"select nbhtbh from uf_kgdthtbd where id="+mainContractId);
        RecordSet rs = new RecordSet();
        rs.execute("select nbhtbh from uf_kgdthtbd where id="+mainContractId);
        rs.next();
        mainContractNumber = rs.getString("nbhtbh");

//       判断流程是否已经生成合同编号
        if(!"".equals(ContractNumber)&&ContractNumber!=null){
            writeLog("已经存在附属合同内部合同编号,nbhtbh:"+ContractNumber);
            if(ContractNumber.indexOf(mainContractNumber)!=-1){
                writeLog("主合同没有改变，无需重新生成新的编号！");
                return Action.SUCCESS;
            }
        }

        writeLog("开始创建附属合同编号");
        writeLog("主合同Id："+mainContractId);
        writeLog("主合同编号："+ContractNumber);
//        获取附属合同流水号
        writeLog("获取流水号sql："+"select * from uf_htddlshjlb where htmc="+mainContractId);
        rs.execute("select * from uf_htddlshjlb where htmc="+mainContractId);
        if(rs.next()){
            lsh = "".equals(Util.null2String(rs.getString("bcxyhtlsh")))?"1":rs.getString("bcxyhtlsh");
        }else {
            writeLog("主合同下的第一个附属合同");
//            RecordSet rs4 = new RecordSet();
            writeLog("初始化流水号sql："+"insert into uf_htddlshjlb(htmc,lsh,bcxyhtlsh) value("+mainContractId+","+1+","+1+")");
            rs.execute("insert into uf_htddlshjlb(htmc,lsh,bcxyhtlsh) values("+mainContractId+","+1+","+1+")");
        }

        writeLog("获取流水号："+lsh);
//        附属合同编号
        boolean isConNum = false;
        do{
            ContractNumber = mainContractNumber+"-"+lsh;
            writeLog("创建附属合同编号："+ContractNumber);
            writeLog("附属合同起始流水号变更sql："+"update uf_htddlshjlb set bcxyhtlsh="+(Integer.valueOf(lsh)+1)+" where htmc="+mainContractId);
            rs.execute("update uf_htddlshjlb set bcxyhtlsh="+(Integer.valueOf(lsh)+1)+" where htmc="+mainContractId);
            writeLog("查看附属合同编号是否已经存在sql："+"select "+ContractNubmerName+" from "+tablename+" where "+ContractNubmerName+"='"+ContractNumber+"'");
            rs.execute("select "+ContractNubmerName+" from "+tablename+" where "+ContractNubmerName+"='"+ContractNumber+"'");
            if(rs.next()){
                isConNum = true;
            }
            rs.execute("select nbhtbh from uf_kgdthtbd where nbhtbh='"+ContractNumber+"'");
            if(rs.next()){
                isConNum = true;
            }
            lsh = String.valueOf(Integer.valueOf(lsh)+1);
        }while (isConNum);


//        创建附属合同编号
        writeLog("更新附属合同编号sql："+"update "+tablename+" set "+ContractNubmerName+"='"+ContractNumber+"' where requestId="+requestId);
        rs.execute("update "+tablename+" set "+ContractNubmerName+"='"+ContractNumber+"' where requestId="+requestId);
        writeLog("更改流程编号记录表的流程编号sql："+"update workflow_codeseqrecord set workflowCode='"+ContractNumber+"' where requestId="+requestId);
        rs.execute("update workflow_codeseqrecord set workflowCode='"+ContractNumber+"' where requestId="+requestId);
        writeLog("更改流程编号sql："+"update workflow_requestbase set requestmark='"+ContractNumber+"' where requestId="+requestId);
        rs.execute("update workflow_requestbase set requestmark='"+ContractNumber+"' where requestId="+requestId);
        return Action.SUCCESS;
    }
}