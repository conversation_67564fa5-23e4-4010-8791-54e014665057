package weaver.interfaces.workflow.action.cyitce.po.income;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.core.exception.ECException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;
import weaver.interfaces.workflow.action.cyitce.manager.filesystem.FileInfo;
import weaver.interfaces.workflow.action.cyitce.manager.paasspace.PaasSpaceToken;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.interfaces.workflow.action.cyitce.util.WorkflowManage;
import weaver.soa.workflow.request.*;

import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.util.*;

/**
 * @ClassName: IncomeCostWorkFlowTriggerAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-22  16:53
 * @Version: 1.0
 */
public class IncomeCostWorkFlowTriggerAction2 extends BaseBean implements Action {
    String FILESYS = AddressManagementPool.getIpAddress("FILESYS");

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog("--------录入决算池----------");
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
        Row[] rows = null;
        Cell[] cells = null;
        int userid = WorkflowManage.getRequestCreater(requestInfo.getRequestid());
        User user = new User(userid);
        Map mainMap = new HashMap();
        String token = null;

        RecordSet rs = new RecordSet();

        Map<String,Object> paasbody = null;

        FileInfo fileInfo = new FileInfo();

        DecimalFormat df = new DecimalFormat("#.00");

        for (Property p:propertys){
            mainMap.put(p.getName(),Util.null2String(p.getValue()));
            writeLog("IncomeCostWorkFlowTriggerAction  fieldname:"+p.getName()+"=====fieldvalue:"+p.getValue());
        }

        try {
            //获取paas平台token
            PaasSpaceToken paas = new PaasSpaceToken();
            token = paas.token(user.getMobile());

            //税率对应的已开票未决算金额
            List<Map<String,String>> detail1Sum = new ArrayList<>();
            rs.executeQuery("select kpsl,ISNULL(SUM(fpsywjsje),0) fpsywjsje from uf_jl_fptzdjb where ejxmmc=? and kpsl is not null group by kpsl",mainMap.get("ejxmmc"));
            while (rs.next()){
                Map<String,String> map = new HashMap<>();
                String ls = JoinFieldManage.getSelectName("kpsl","uf_jl_fptzdjb",rs.getString(1));
                Float lsxs = Float.parseFloat(ls.substring(0,ls.indexOf("%")))/100;
                map.put("kpsl",String.valueOf(lsxs));
                map.put("fpsywjsje",rs.getString(2));
                String se = df.format(Float.parseFloat(rs.getString(2))*lsxs);
                map.put("fpsywjsse",se);
                detail1Sum.add(map);
            }

            //封装请求参数
            paasbody = new HashMap<>();

            JSONObject obj1 = new JSONObject();
            obj1.put("secondaryProjectId", mainMap.get("ejxmmc"));
            obj1.put("firstProjectId", mainMap.get("yjxmmc"));
            obj1.put("projectCode", mainMap.get("yjxmbh"));

            rs.executeQuery("SELECT b.provincename FROM uf_jl_yjxmlxsqjmbd a inner join HrmProvince b on a.sf=b.id where a.id=?",mainMap.get("yjxmmc"));
            if(rs.next()){
                obj1.put("area", rs.getString(1));
            }else {
                new Exception("一级项目缺少省份");
            }

            rs.executeQuery("SELECT b.yzdw1,c.khgysbh FROM uf_jl_yjxmlxsqjmbd a \n" +
                    "inner join uf_jl_skhtdjbaspbd b on a.htmc=b.id\n" +
                    "inner join uf_jl_khygysjcxxb c on b.yzdw1=c.id\n" +
                    "where a.id=?",mainMap.get("yjxmmc"));
            if(rs.next()){
                //客户id
                obj1.put("customName", rs.getString(1));
                //客户编码
                obj1.put("customCode", rs.getString(2));
            }else {
                //客户id
                obj1.put("customName", "");
                //客户编码
                obj1.put("customCode", "");
            }

            obj1.put("costedAmount", mainMap.get("bccbbhshj"));
            obj1.put("incomeFaAmount", mainMap.get("bcsrqrjebhs"));

            Calendar c = Calendar.getInstance();
            String year = String.valueOf(c.get(Calendar.YEAR));
            String month = String.valueOf(c.get(Calendar.MONTH)+1);
            if(month.length()==1){
                month = "0"+month;
            }
            String day = String.valueOf(c.get(Calendar.DATE));
            if(day.length()==1){
                day = "0"+day;
            }
            String currDate = year+"-"+month+"-"+ day;
            obj1.put("faAuditTime", currDate);

            obj1.put("isAudited", false);
            obj1.put("faFlowCodeName", mainMap.get("lcbh"));
            obj1.put("faFlowCode", mainMap.get("xmjstzid"));
            rs.execute("select xmmc from uf_jl_yjxmlxsqjmbd where id="+mainMap.get("yjxmmc"));
            if (rs.next()){
                obj1.put("firstProjectName", rs.getString(1));
            }else {
                throw new ECException("一级项目不存在！");
            }

            obj1.put("firstProjectCode", mainMap.get("yjxmbh"));
            obj1.put("firstProjectSum", mainMap.get("yjxmljjsjebhs"));
            obj1.put("firstProjectCost", mainMap.get("yjxmljjscbbhs"));

            rs.executeQuery("select ejxmnbmc from uf_jl_ejxmlxsqjmbd where id=?",mainMap.get("ejxmmc"));
            rs.next();
            obj1.put("spName", rs.getString(1));
            obj1.put("spCode", mainMap.get("ejxmbh"));

            rs.executeQuery("SELECT yjbm,ejbm,sjbm FROM uf_jl_yjxmlxsqjmbd where id=?",mainMap.get("yjxmmc"));
            if(rs.next()){
                obj1.put("firstDeptId", rs.getString(1));
                obj1.put("secondaryDeptId", rs.getString(2));
                obj1.put("thirdDeptId", rs.getString(3));
            }

            obj1.put("grossProfitMargin", mainMap.get("mlrl"));
            obj1.put("totalIncome", mainMap.get("srqrjebhs"));
            obj1.put("addupAuditedAmount", mainMap.get("ljyjsjebhs"));
            obj1.put("isLastAudit", mainMap.get("sfszhycjs"));
            paasbody.put("faPond",obj1);

            JSONArray faAuditAmountList = new JSONArray();
            DetailTable dt1 = detailTables[0];
            rows = dt1.getRow();
            for (int i=0;i<rows.length;++i){
                cells = rows[i].getCell();
                JSONObject faAuditAmountListObj = new JSONObject();
                JSONArray faFileInfos = new JSONArray();
                String taxAmount = "";
                String incomeNotTax = "";
                String sl = "";

                for (int j=0;j<cells.length;++j){
                    Cell cc = cells[j];
                    JSONObject faFileInfosObj = new JSONObject();

                    if(cc.getName().indexOf("CoustomerFile_js")!=-1&&!"".equals(Util.null2String(cc.getValue()))){
                        faFileInfosObj.put("fileId",cc.getValue());
                        faFileInfosObj.put("fileName",fileInfo.getFileName(cc.getValue(),user));
                        faFileInfosObj.put("downloadUrl",FILESYS+"/file-system/file_center/download?ids="+cc.getValue());
                        faFileInfosObj.put("viewUrl",FILESYS+"/file-system/file_center/OAdownload?id="+cc.getValue());
                        faFileInfos.add(faFileInfosObj);
                    }else if("jscl".equals(cc.getName())){
                        rs.executeQuery("select lcbh from uf_jl_jsclgdjmbd where id=?",cc.getValue());
                        rs.next();
                        faAuditAmountListObj.put("itemCode",rs.getString(1));
                    }else if("slxs".equals(cc.getName())){
                        faAuditAmountListObj.put("taxRate",cc.getValue());
                        sl = cc.getValue();
                    }else if("sl".equals(cc.getName())){
                        faAuditAmountListObj.put("taxId",cc.getValue());
                    }else if("bcsrqrzzsxxsj".equals(cc.getName())){
                        faAuditAmountListObj.put("taxAmount",cc.getValue());
                        taxAmount = cc.getValue();
                    }else if("bcsrqrbhs".equals(cc.getName())){
                        faAuditAmountListObj.put("incomeNotTax",cc.getValue());
                        incomeNotTax = cc.getValue();
                    }
                }
                faAuditAmountListObj.put("faFileInfos",faFileInfos);

                float f1 = Float.parseFloat(taxAmount);
                float f2 = Float.parseFloat(incomeNotTax);
                faAuditAmountListObj.put("incomeAndTax",f1+f2);

                faAuditAmountListObj.put("thisIncomeInvoiced",0);
                faAuditAmountListObj.put("thisIncomeVat",0);
                for (int n=0;n<detail1Sum.size();++n){
                    Map m = detail1Sum.get(n);
                    if(sl.equals(m.get("kpsl"))){
                        faAuditAmountListObj.put("thisIncomeInvoiced",m.get("fpsywjsje"));
                        faAuditAmountListObj.put("thisIncomeVat",m.get("fpsywjsse"));
                        break;
                    }
                }

                faAuditAmountList.add(faAuditAmountListObj);
            }
            paasbody.put("faAuditAmountList",faAuditAmountList);

            JSONArray faCostList = new JSONArray();
            JSONObject faCostListObj = new JSONObject();

            faCostListObj.put("subjectName","直接人工");
            faCostListObj.put("subjectCode","500101");
            faCostListObj.put("costTotalAmount",nullToZero((String)mainMap.get("bcxjzjrg")));
            faCostListObj.put("historyAuditedCost",nullToZero((String)mainMap.get("lszjrg")));
            faCostList.add(faCostListObj);

            JSONObject faCostListObj2 = new JSONObject();
            faCostListObj2.put("subjectName","直接材料");
            faCostListObj2.put("subjectCode","500102");
            faCostListObj2.put("costTotalAmount",nullToZero((String)mainMap.get("bcxjzjcl")));
            faCostListObj2.put("historyAuditedCost",nullToZero((String)mainMap.get("lszjcl")));
            faCostList.add(faCostListObj2);

            JSONObject faCostListObj3 = new JSONObject();
            faCostListObj3.put("subjectName","劳务外协");
            faCostListObj3.put("subjectCode","500103");
            faCostListObj3.put("costTotalAmount",nullToZero((String)mainMap.get("bcxjlwwx")));
            faCostListObj3.put("historyAuditedCost",nullToZero((String)mainMap.get("lslwwx")));
            faCostList.add(faCostListObj3);

            JSONObject faCostListObj4 = new JSONObject();
            faCostListObj4.put("subjectName","工程管理费（制造费用）");
            faCostListObj4.put("subjectCode","500105");
            faCostListObj4.put("costTotalAmount",nullToZero((String)mainMap.get("bcxjgcglfzzfy")));
            faCostListObj4.put("historyAuditedCost",nullToZero((String)mainMap.get("lsgcglfzzfy")));
            faCostList.add(faCostListObj4);

            JSONObject faCostListObj5 = new JSONObject();
            faCostListObj5.put("subjectName","其他成本（罚款、诉讼、滞纳金等费用）");
            faCostListObj5.put("subjectCode","500104");
            faCostListObj5.put("costTotalAmount",nullToZero((String)mainMap.get("bcxjqtcb")));
            faCostListObj5.put("historyAuditedCost",nullToZero((String)mainMap.get("lsqtcb")));
            faCostList.add(faCostListObj5);

            paasbody.put("faCostList",faCostList);
        }catch (Exception e){
            requestInfo.getRequestManager().setMessage(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        //触发paas平台接口入决算池
        HttpClient client = null;
        HttpPost post=null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;

        try {
            client = HttpClients.createDefault();
            post = new HttpPost(AddressManagementPool.getIpAddress("PAAS_countfa")+"/pond/access");
            post.addHeader("Content-Type", "application/json;charset=utf-8");
            post.addHeader("Authorization", token);
            StringEntity requestentity=new StringEntity(JSONObject.toJSONString(paasbody), Charset.forName("UTF-8"));
            requestentity.setContentType(ContentType.APPLICATION_JSON.toString());
            post.setEntity(requestentity);
            response = client.execute(post);
            httpEntity = response.getEntity();
            String info = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(info);
            writeLog("====------==httpdata income===------==="+jsonObject.toJSONString());

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                throw new Exception("录入决算池失败！");
            }

            if("FAIL".equals(jsonObject.getString("code"))){
                throw new Exception("录入决算池失败！msg："+jsonObject.getString("msg"));
            }
        }catch (Exception e){
            requestInfo.getRequestManager().setMessage(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    private String nullToZero(String num){
        if("".equals(num)||num==null){
            return "0";
        }
        return num;
    }
}