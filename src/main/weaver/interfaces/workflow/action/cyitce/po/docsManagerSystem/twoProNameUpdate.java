package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONObject;
import com.api.integration.BaseBean;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 修改文件管理系统二级项目名称
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-06  15:52
 * @Version: 1.0
 */
public class twoProNameUpdate extends BaseBean implements Action {
    private String url = OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/modifyName";

    public String projectCodeFieldName;
    public String newProjectNameFieldName;
    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String projectCode = "";
        String newProjectName = "";
        String error = "修改文件管理系统二级项目名称失败";

        HttpClient client = null;
        URIBuilder builder = null;
        HttpGet get = null;
        HttpResponse response = null;
        try {
            int ret = 0;
            for(int i = 0; i < properties.length; ++i) {
                if (properties[i].getName().equals(projectCodeFieldName)) {
                    projectCode = properties[i].getValue();
                    ret++;
                }else if(properties[i].getName().equals(newProjectNameFieldName)){
                    newProjectName = properties[i].getValue();
                    ret++;
                }else if (ret==2){
                    break;
                }
            }

            if("".equals(projectCode)){
                return Action.SUCCESS;
            }

            if("".equals(newProjectName)){
                return Action.SUCCESS;
            }

            client = HttpClients.createDefault();
            builder = new URIBuilder(url);
            builder.addParameter("code",projectCode);
            builder.addParameter("newName",newProjectName+projectCode);
            get = new HttpGet(builder.build());
            get.addHeader("Tenant-Id","000000");
            get.addHeader("Blade-Auth", Token.getToken(user.getLoginid(),user.getLastname()));
            get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            response = client.execute(get);

            if(response.getStatusLine().getStatusCode()!=200){
                error = "修改文件管理系统二级项目名称失败,httpCode:"+response.getStatusLine().getStatusCode();
                throw new Exception(error);
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(error);
            e.getMessage();
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}