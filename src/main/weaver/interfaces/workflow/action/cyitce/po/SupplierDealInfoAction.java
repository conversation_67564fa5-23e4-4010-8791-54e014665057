package weaver.interfaces.workflow.action.cyitce.po;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 交易详情数据
 */
public class SupplierDealInfoAction extends BaseBean implements Action {
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final static String YFK = "YFK";
    private final static String CWFK = "CWFK";

    @Override
    public String execute(RequestInfo requestInfo) {
        String jybh="";
        String jyje="";
        String jysj=sdf.format(new Date());
        String fkf="重庆信科通信工程有限公司";
        String htbh="";
        String ddbh="";


        String type=null;//流程类型
        String fieldname="";
        String tablename=null;
        String sql=null;
        int id;

        List<Map<String,Object>> list = new ArrayList<>();
        RecordSet rs = new RecordSet();

        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        id = requestInfo.getRequestManager().getBillid();
        tablename = requestInfo.getRequestManager().getBillTableName();

        for(int i = 0; i < properties.length; ++i) {
            if (properties[i].getName().equals("jzdh")) {
                jybh = properties[i].getValue();
                type=YFK;
                continue;
            }else if(properties[i].getName().equals("fkbh")){
                jybh = properties[i].getValue();
                type=CWFK;
                continue;
            }
        }

        //流程中交易金额字段名称
        if(YFK.equals(type)){
            fieldname = "fkje";
            tablename = tablename + "_dt2";
        }else if (CWFK.equals(type)){
            fieldname = "bcfkje";
            tablename = tablename + "_dt1";
        }

        //交易信息
//        sql = "select ? from ? where mainid=?";
        writeLog("select "+fieldname+" from "+tablename+" where mainid="+id);
//        rs.executeQuery(sql,new Object[]{fieldname,tablename,id});
        rs.execute("select "+fieldname+" from "+tablename+" where mainid="+id);
        while (rs.next()){
            jyje = rs.getString(fieldname);
            Map<String,Object> map = new HashMap<>();
            map.put("dealNo",jybh);
            map.put("dealAmount",jyje);
            map.put("dealTime",jysj);
            map.put("payer",fkf);
            map.put("contractNo",htbh);
            map.put("orderNo",ddbh);
            list.add(map);
        }

        int code = raise(JSON.toJSONString(list));
        writeLog("code:"+code);

        if(code==1){
            writeLog("请求失败！");
            return Action.FAILURE_AND_CONTINUE;// 数据操作状态
        }
        writeLog("======请求成功====");
        return Action.SUCCESS;
    }

    /**
     *
     * @description 调用
     * @param entity_json 参数json格式
     * @return
     */
    private int raise(String entity_json){
        writeLog("参数信息"+entity_json);
        writeLog("raise----进来了");
        String urlString="https://supplier.cyitce.com/api/supplier-deal/deal/saveDealInfo";
        HttpClient client=null;
        PostMethod post=null;
        Integer result=1;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            //添加参数
//            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            RequestEntity requestEntity = new StringRequestEntity(entity_json, "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("SupplierDealInfoAction----raise----code:"+post.getStatusCode());
            String body = new String(post.getResponseBody(), "utf-8");
            writeLog("返回值："+body);
//            String success = JSONObject.parseObject(body).getString("success");
            if(post.getStatusCode() == 200){
                result=0;
            }
            result = 1;
            writeLog("httpCode: "+result);
        } catch (HttpException e) {
            e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            writeLog("raise---finally");
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }
}
