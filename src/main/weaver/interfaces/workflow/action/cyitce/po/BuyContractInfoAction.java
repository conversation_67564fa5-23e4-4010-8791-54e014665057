package weaver.interfaces.workflow.action.cyitce.po;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashSet;


public class BuyContractInfoAction extends BaseBean implements Action {

    public  String[] obj = new String[]{"qyfzr",
            "xmjl",
            "sqr",
            "sqrq",
            "sqbm",
            "yjbm1",
            "ejbm1",
            "sjbm1",
            "xmz1",
            "yjxmmc",
            "yjxmbh",
            "ejxmmc",
            "ejxmbh",
            "yjbm",
            "ejbm",
            "sjbm",
            "xmz",
            "sf",
            "cs",
            "qx",
            "fplx",
            "slxz",
            "hsjg",
            "bhsjg",
            "cgdl",
            "xmszsf",
            "xmszcs",
            "xmszqx",
            "htkssj",
            "htzzsj",
            "ywqyfzr",
            "bz",
            "fj",
            "xmszqywb",
            "yjxmjkli",
            "htszyjbm",
            "ywqybm",
            "fkbl",
            "fzrlxdh",
            "fzryxdz",
            "htxs",
            "htmc",
            "htbh",
            "htlx",
            "ywlx",
            "sfbcxy",
            "cgxl",
            "fksj",
            "fkje",
            "fkxz",
            "fkblfds",
            "fktj",
            "gzlx",
            "htkjjbz",
            "wyzrjfx",
            "gysywqyig",
            "gysmc",
            "sfsggxm",
            "htszejbm",
            "htszsjbm",
            "htszxmz",
            "zht",
            "gysbh",
            "gyszczt",
            "gysywqy",
            "sfdyskht",
            "skhtmc",
            "skhtbh",
            "sfdyskdd",
            "ddmc",
            "ddbh",
            "fkjehj",
            "sqrszbm",
            "htsmjsc",
            "sldx",
            "slsjz",
            "shrxxdz",
            "shrdh",
            "shr",
    };
    public String[] obj_detail = new String[]{"fkje","fkxz","fkbl","fktj","fksj"};
    public String[] obj_detail_result = new String[]{"payPrice","payNature","payRoportion","paymentWays","estimatedTime"};

    @Override
    public String execute(RequestInfo requestInfo) {
        String mainTablename=null;
        String detailTablename=null;

        //主表数据
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        //明细表数据
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        mainTablename = requestInfo.getRequestManager().getBillTableName();
        detailTablename = mainTablename + "_dt1";

        JSONObject jsonObject = new JSONObject();
        writeLog("获取主表信息-------");
        JSONObject contractBaseInfo = new JSONObject();
        for(int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            int fieldcount = 0;
            while (fieldcount<obj.length) {
                if(name.equals(obj[fieldcount])){
                    writeLog("主表字段: "+name+"="+properties[i].getValue());
                    contractBaseInfo.put(name,getForeignStringValue(name,Util.null2String(properties[i].getValue()),mainTablename));
                    fieldcount = obj.length;
                }else {
                    ++fieldcount;
                }
            }
        }

//        额外增加的字段
        contractBaseInfo.put("hrmlastname",getForeignStringValue("sqr",Util.null2String(contractBaseInfo.getString("sqr")),mainTablename));

        writeLog("获取明细表字段--------");
        JSONArray contractPayMent = new JSONArray();
        if (detailtable.length>0) {
            DetailTable dt = detailtable[0];// 指定明细表
            Row[] s = dt.getRow();// 当前明细表的所有数据,按行存储
            for (int j = 0; j < s.length; j++) {
                JSONObject jsonObject1 = new JSONObject();
                writeLog("BuyContractInfoAction------明细表1第" + j + "条数据:");
                Row r = s[j];// 指定行
                Cell c[] = r.getCell();// 每行数据再按列存储
                for (int k = 0; k < c.length; k++) {
                    Cell c1 = c[k];// 指定列
                    String name = c1.getName();// 明细字段名称
                    writeLog("BuyContractInfoAction------名行表字段名:" + name);
                    int fieldcount = 0;
                    while (fieldcount<obj_detail.length) {
                        if(name.equals(obj_detail[fieldcount])){
                            writeLog("明细字段: "+name+"="+c1.getValue());
                            jsonObject1.put(obj_detail_result[fieldcount],getForeignStringValue(name, Util.null2String(c1.getValue()),detailTablename));
                            fieldcount = obj_detail.length;
                        }else {
                            ++fieldcount;
                        }
                    }
                }
                contractPayMent.add(jsonObject1);
            }
        }
        jsonObject.put("contractBaseInfo",contractBaseInfo);
        jsonObject.put("contractPayMent",contractPayMent);

        int code = raise(jsonObject.toJSONString());
        if(code!=0){
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    /**
     *
     * @description 调用
     * @param entity_json 参数json格式
     * @return
     */
    private int raise(String entity_json){
        writeLog("参数信息"+entity_json);
        writeLog("raise----进来了");
        String urlString="https://supplier.cyitce.com/api/supplier-system/contract/saveContractInfo";
        HttpClient client=null;
        PostMethod post=null;
        Integer result=1;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            //添加参数
//            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            RequestEntity requestEntity = new StringRequestEntity(entity_json, "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("BuyContractInfoAction----raise----code:"+post.getStatusCode());
            String body = new String(post.getResponseBody(), "utf-8");
            writeLog("返回值："+body);
//            String success = JSONObject.parseObject(body).getString("success");
            if("true".equals(body)&&post.getStatusCode()==200){
                result=0;
            }
            writeLog("httpCode: "+result);
        } catch (HttpException e) {
            e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            writeLog("raise---finally");
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }

    /**
     * 获取外键字段文本值
     * @return
     */
    private String getForeignStringValue(String field,String value,String workflow_tablename){
        if("".equals(value)){
            return value;
        }

        String strValue=null;

        String fieldName=null;
        String tableName=null;
        String id=null;

        //人员
        HashSet<String> set = new HashSet<>();
//        set.add("sqr");
        set.add("qyfzr");
        set.add("xmjl");
        set.add("yjxmjkli");
        set.add("shr");
        if(set.contains(field)){
            fieldName = "lastname";
            tableName = "HrmResource";
            id = value;
        }

        //部门
        HashSet<String> set2 = new HashSet<>();
        set2.add("sqbm");
        set2.add("sqrszbm");
        set2.add("yjbm1");
        set2.add("ejbm1");
        set2.add("xmz1");
        set2.add("yjbm");
        set2.add("ejbm");
        set2.add("xmz");
        set2.add("htszyjbm");
        set2.add("htszejbm");
        set2.add("htszsjbm");
        set2.add("htszxmz");
        if(set2.contains(field)){
            fieldName = "departmentname";
            tableName = "HrmDepartment";
            id = value;
        }

        //一级项目
        if("yjxmmc".equals(field)){
            fieldName = "xmmc";
            tableName = "uf_yjxmlxjm";
            id = value;
        }

        //二级项目
        if("ejxmmc".equals(field)){
            fieldName = "jfxmmc";
            tableName = "uf_ejxmlx";
            id = value;
        }

//        省份
        if("sf".equals(field)||"gysywqy".equals(field)||"xmszsf".equals(field)){
            fieldName = "provincename";
            tableName = "HrmProvince";
            id = value;
        }
//        城市
        if("cs".equals(field)||"gysywqyig".equals(field)||"xmszcs".equals(field)){
            fieldName = "cityname";
            tableName = "HrmCity";
            id = value;
        }
//        区县
        if("qx".equals(field)||"xmszqx".equals(field)){
            fieldName = "cityname";
            tableName = "hrmcitytwo";
            id = value;
        }
//        供应商
        if("gysmc".equals(field)){
            fieldName = "gysmc";
            tableName = "uf_VendorAccountInfo";
            id = value;
        }

//       下拉框
        HashSet<String> set3 = new HashSet<>();
        set3.add("htxs");
        set3.add("gyszczt");
        set3.add("htlx");
        set3.add("ywlx");
        set3.add("sfbcxy");
        set3.add("sfdyskht");
        set3.add("sfdyskdd");
        set3.add("fplx");
        set3.add("slxz");
        set3.add("cgdl");
        set3.add("cgxl");
        set3.add("gzlx");
        set3.add("sldx");
        set3.add("fkxz");
        if(set3.contains(field)){
            fieldName = field;
            tableName = workflow_tablename;
            id = value;
        }

//        收款合同
        if("skhtmc".equals(field)){
            fieldName = "htmc";
            tableName = "uf_kgdthtbd";
            id = value;
        }

//        订单
        if("ddmc".equals(field)){
            fieldName = "ddmc";
            tableName = "uf_dddjbd";
            id = value;
        }

        if(fieldName==null||tableName==null||id==null){
            return value;
        }

        RecordSet rs = new RecordSet();
        String selectItemSql = "select selectname from workflow_SelectItem where fieldid = (SELECT a.id as fieldid from workflow_billfield a,workflow_bill b where a.billid = b.id and b.tablename = '"+tableName+"' and fieldname = '"+fieldName+"') and selectvalue in ("+id+")";
        String sql = "select "+fieldName+" from "+tableName+" where id="+id;
        if(workflow_tablename.equals(tableName)){
            rs.execute(selectItemSql);
        }else {
            rs.execute(sql);
        }
        if (rs.next()){
            strValue=rs.getString(1);
        }
        return strValue;
    }
}
