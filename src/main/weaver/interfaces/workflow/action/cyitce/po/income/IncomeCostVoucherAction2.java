package weaver.interfaces.workflow.action.cyitce.po.income;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.KylinDataSource;
import weaver.soa.workflow.request.*;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 监理公司-收入成本确认流程V1插入明细3和明细4
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-12-04  10:21
 * @Version: 1.0
 */
public class IncomeCostVoucherAction2 extends BaseBean implements Action {

    private char separator = Util.getSeparator();
    @Override
    public String execute(RequestInfo requestInfo) {
        String kmbm1 = "11220202";
        String kmbm2 = "6001";
        String kmbm3 = "2221010302";
        String kmbm4 = "11220202";
        String kmbm5 = "2221010302";
        String kmbm6 = "6401";
        //劳务外协未入账成本
        //借
        String kmbm61 = "50010301";
        String kmbm62 = "50010302";
        String kmbm63 = "50010303";
        //贷
        String kmbm7 = "22020201";
        String kmbm71 = "50010301";
        String kmbm72 = "50010302";
        String kmbm73 = "50010303";

        //直接材料未入账成本
        //借
        String kmbm8 = "500102";
        String kmbm9 = "1403";
        //贷
        String kmbm10 = "22020202";
        String kmbm11 = "1403";
        String kmbm12 = "500102";

        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
        String mainTable = requestInfo.getRequestManager().getBillTableName();
        String mainid = null;
        Row[] rows = null;
        Cell[] cells = null;

        String currTableName1 = mainTable + "_dt3";
        String currTableName2 = mainTable + "_dt4";
        Map mainMap = new HashMap();

        RecordSetTrans recordSetTrans = new RecordSetTrans();
        recordSetTrans.setAutoCommit(false);

        RecordSet rs = new RecordSet();

        KylinDataSource kds = new KylinDataSource();

        for (Property p : propertys) {
            mainMap.put(p.getName(), Util.null2String(p.getValue()));
        }

        DecimalFormat df = new DecimalFormat("#.##");

        float f = 0;

        try {
            String gysbm = String.valueOf(mainMap.get("khbm"));

            //加载kylin配置
            kds.dataSource();
            rs.executeQuery("select id from "+mainTable+" where requestid=?",requestInfo.getRequestid());
            rs.next();
            mainid = rs.getString(1);

            rs.execute("delete from "+currTableName1+" where mainid="+mainid);
            rs.execute("delete from "+currTableName2+" where mainid="+mainid);

            BigDecimal num1 = new BigDecimal((String)mainMap.get("bcsrqrbhshj"));
            BigDecimal num2 = new BigDecimal((String)mainMap.get("bcsrqrzzsxxsjhj"));

            //借方科目
            recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm1+"','"+num1.add(num2)+"','"+gysbm+"')");

            f = Float.parseFloat((String) mainMap.get("bcsrqrjebhszykpwjszzsxxsehj"));
            if (f != 0) {
                f = -f;
                recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm4+"','"+f+"','"+gysbm+"')");
            }

            recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm6+"','"+mainMap.get("bcxjhj")+"','"+gysbm+"')");

            //贷方科目
            recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm2+"','"+mainMap.get("bcsrqrjebhs")+"','"+gysbm+"')");

            rows = detailTables[0].getRow();
            for (Row r : rows) {
                cells = r.getCell();
                String sl = "";
                float var1 = 0;
                float var2 = 0;
                for (Cell c : cells) {
                    if ("sl".equals(c.getName())) {
                        rs.executeQuery("SELECT c.selectname FROM workflow_billfield a,workflow_bill b,workflow_SelectItem c \n" +
                                " where a.billid = b.id and  a.id=c.fieldid \n" +
                                "and b.tablename =? and  a.fieldname=? and a.viewtype=? and c.selectvalue =?", new Object[]{mainTable, c.getName(), 1, c.getValue()});
                        rs.next();
                        sl = Util.null2String(rs.getString(1));
                    } else if ("bcsrqrzzsxxsj".equals(c.getName())) {
                        var1 = Float.parseFloat(c.getValue());
                    } else if ("bcsrqrjebhszykpwjszzsxxse".equals(c.getName())) {
                        var2 = -Float.parseFloat(c.getValue());
                    }
                }

                if(var1!=0){
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,sl,gysbm) values('"+mainid+"','"+kmbm3+"','"+var1+"','"+sl+"','"+gysbm+"')");
                }
                if(var2!=0){
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,sl,gysbm) values('"+mainid+"','"+kmbm5+"','"+var2+"','"+sl+"','"+gysbm+"')");
                }
            }
            //U8成本科目分录
            rs.execute("select kmbm from uf_srcbkm where fx="+1);
            while (rs.next()){
                Double tjje = 0.0;
                //从麒麟数据库拉取数据
                kds.executeQuery("select sum(md)-sum(mc) " +
                        "from dwd_jl_oa_cw_gl_accvouch_603_2023_df_T " +
                        "where iyear>=2023\n" +
                        "AND citem_id = '"+mainMap.get("ejxmbh")+"' AND ccode = '"+rs.getString(1)+"' AND iflag=-1 group by ccode");
                if(kds.next()){
                    tjje= Double.parseDouble(kds.getString(1));
                }

                //从麒麟数据库拉取数据
                kds.executeQuery("select sum(mb) " +
                        "from dwd_jl_oa_cw_gl_accass_603_2023_df_T " +
                        "where iYPeriod = 202301\n" +
                        "AND citem_id = '"+mainMap.get("ejxmbh")+"' AND ccode = '"+rs.getString(1)+"' group by ccode");
                if(kds.next()){
                    tjje += Double.parseDouble(kds.getString(1));
                }

                if(tjje!=0){
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+rs.getString(1)+"','"+df.format(tjje)+"','"+gysbm+"')");
                }
            }
            //劳务外协
            rs.execute("select * from "+mainTable+"_dt2 where mainid="+mainid);
            while (rs.next()){
                String fylx = Util.null2String(rs.getString("fylx"));
                if("".equals(fylx)){
                    throw new Exception("劳务外协暂估成本未选择费用类型！");
                }
                //借方 贷方
                if("0".equals(fylx)){
                    recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm61+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm71+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                }else if("1".equals(fylx)){
                    recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm62+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm72+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                }else if("2".equals(fylx)){
                    recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm63+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                    recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm73+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                }
                //贷方
                recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm7+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
            }
            //直接材料
            rs.execute("select * from "+mainTable+"_dt5 where mainid="+mainid);
            while (rs.next()){
                //借方
                recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm8+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                recordSetTrans.execute("insert into " + currTableName1 + "(mainid,jfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm9+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                //贷方
                recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm10+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm11+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
                recordSetTrans.execute("insert into " + currTableName2 + "(mainid,dfkmbm,tjje,gysbm) values('"+mainid+"','"+kmbm12+"','"+rs.getString("jebhsjebkdksj")+"','"+rs.getString("gysbm")+"')");
            }
            //清除金额为0的科目分录
            recordSetTrans.execute("delete from "+currTableName1+" where tjje=0 or tjje is null");
            recordSetTrans.execute("delete from "+currTableName2+" where tjje=0 or tjje is null");

            recordSetTrans.commit();
        }catch (Exception e) {
            recordSetTrans.rollback();
            requestInfo.getRequestManager().setMessage(e.getMessage());
            e.printStackTrace();
            return Action.FAILURE_AND_CONTINUE;
        }finally {
            try {
                if(kds!=null){
                    kds.close();
                }
            }catch (Exception e){

            }
        }

        return Action.SUCCESS;
    }


}