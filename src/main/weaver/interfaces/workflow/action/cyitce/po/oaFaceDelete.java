package weaver.interfaces.workflow.action.cyitce.po;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import com.weaver.general.BaseBean;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.invoice.RSAUtils;
import weaver.interfaces.workflow.action.cyitce.util.CookieUtil;
import weaver.soa.workflow.request.RequestInfo;

import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

/**
 * 人员离职删除人脸数据（离职流程对接my-page）
 *
 *
 */
public class oaFaceDelete extends BaseBean implements Action {

//    public static final String FUNCTION_GET_TOKEN_URL="https://mypagebackend.cyitce.com/authGetToken";
    public static final String FUNCTION_GET_TOKEN_URL="https://oa.cyitce.com/api/cyitce/mypage/getToken";
    public static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9RsiydxWwMkCGLvL2NMTEZvBTv78B7XMJkGqb+aAmIjVo+KDCzx8MYVmHW11DgJx5xv6IhdUzXxO1o6AfdyJvPIg+kflYUF8EOImWLIaJ3a+6aDuCRPdkeDy/FVnK+GMhH6yXcoCPe17xzIQw9FezYOJmjoGPFQoM14T4K3NRIQIDAQAB";


    @Override
    public String execute(RequestInfo request) {
        String statue = Action.SUCCESS;// 数据操作状态
        String requestid = request.getRequestid();//请求ID

        RecordSet rs = new RecordSet();
        rs.execute("SELECT creater FROM workflow_requestbase where requestid="+requestid);
        rs.next();
        RecordSet rs2 = new RecordSet();
        rs2.execute("SELECT loginid FROM HrmResource where id="+rs.getString("creater"));
        rs2.next();
        String loginid = rs2.getString("loginid");
        Map<String,String> map = new HashMap<>();
        map.put("loginId",loginid);
        int code = raise(JSON.toJSONString(map),getToken());
        writeLog("code:"+code);

        if(code!=200){
            writeLog("code 不为 200");
            statue = Action.FAILURE_AND_CONTINUE;// 数据操作状态
        }
        writeLog("======结束====");
        return statue;
    }

    /**
     *
     * @description 调用
     * @param entity_json 人员信息json格式
     * @param tokenString token
     * @return
     */
    private int raise(String entity_json, String tokenString){
        writeLog(entity_json+"------"+tokenString);
        writeLog("raise----进来了");
        String urlString="https://mypagebackend.cyitce.com/authLeft";
        HttpClient client=null;
        PostMethod post=null;
        Integer result=null;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            //添加参数
            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            RequestEntity requestEntity = new StringRequestEntity(entity_json, "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("oaFaceDelete----raise----code:"+post.getStatusCode());
            result = post.getStatusCode();
            writeLog("httpCode: "+result);
        } catch (HttpException e) {
            e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            writeLog("raise---finally");
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }

    /**
     *
     * @description 获取token
     * @param
     * @return
     */
    private String getToken(){
        String urlString=FUNCTION_GET_TOKEN_URL;
        HttpClient client=null;
        GetMethod get=null;
        String result=null;
        try {
            client=new HttpClient();
            get=new GetMethod(urlString);
            String cookid = CookieUtil.getCookies("oacs2564","Abcd1234");
            writeLog(cookid);
            //添加参数
            get.setRequestHeader("cookie","ecology_JSessionId="+cookid);
            get.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            //执行
            client.executeMethod(get);
            writeLog("oaFaceDelete----getToken----code:"+get.getStatusCode());
            String info = new String(get.getResponseBody(), "utf-8");
            result  =(String) JSONObject.parseObject(info).get("access_token");
            writeLog("tokenInfo： "+ info);
            writeLog("access_token： "+ result);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }finally {
            //关闭连接，释放资源
            get.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }


    /**
     *
     * @description 通过公钥获取token
     * @param loginId
     * @return
     */
    public  String getToken(String loginId) {
        writeLog("getToken----getToken----进来了");
        HttpClient client=null;
        PostMethod post=null;
        String token="";
        try {
            client=new HttpClient();
            post = new PostMethod(FUNCTION_GET_TOKEN_URL);
            //获取公钥
            PublicKey publicKey = RSAUtils.getPublicKey(PUBLIC_KEY);
            //RSA加密
            String secret = RSAUtils.encrypt(loginId,publicKey);
            writeLog("secret:"+secret);
            //添加参数
            HashMap<String, String> map = new HashMap(1);
            map.put("secret", secret);
            RequestEntity requestEntity = new StringRequestEntity(JSON.toJSONString(map), "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("getToke--psot---code:"+post.getStatusCode());
            //接口返回信息
            String result = new String(post.getResponseBody(), "UTF-8");
            token = JSONObject.parseObject(result).get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return token;
    }
}
