package weaver.interfaces.workflow.action.cyitce.po.car;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 用车归还校验
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-24  16:25
 * @Version: 1.0
 */
public class CarReturnCheck extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String var="-1";

        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p:property){
            if(p.getName().equals("cph")){
                var=p.getValue();
                break;
            }
        }

        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("select a.requestid from formtable_main_1881 a inner join workflow_currentoperator b on a.requestid=b.requestid where a.cph=? and b.nownodetype in (1,2)",new Object[]{var});
            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆归还审核中");
                return Action.FAILURE_AND_CONTINUE;
            }

            rs.executeQuery("select lcbh from uf_clghb where cph=?",new Object[]{var});
            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆已归还");
                return Action.FAILURE_AND_CONTINUE;
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}