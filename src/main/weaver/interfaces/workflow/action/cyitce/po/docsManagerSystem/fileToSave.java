package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.service.impl.CoustomerFieldServiceImpl;
import com.engine.common.util.ServiceUtil;
import com.weaver.general.Util;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.soa.workflow.request.RequestInfo;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class fileToSave extends BaseBean implements Action {
    String url= OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/file";

    String url2= OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/editFileInfo";

    @Override
    public String execute(RequestInfo requestInfo) {
        String loginId = "";
        String lastName = "";
        String fieldName = "CoustomerFile";
        String formid = "-"+requestInfo.getRequestManager().getBillTableName().split("_")[2];

        String files = "";

        HttpClient client = null;
        HttpGet get = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            RecordSet rs = new RecordSet();
            RecordSet rs2 = new RecordSet();

            lastName = requestInfo.getCreatorid();
            rs.execute("select loginid from hrmresource where lastname='"+lastName+"'");
            rs.next();
            loginId = Util.null2String(rs.getString(1));

            rs.execute("SELECT fieldname,viewtype,detailtable,id FROM workflow_billfield where billid="+formid+" and fieldname like '"+fieldName+"%' and fieldhtmltype=1 and type=1");
            while (rs.next()){
                if("0".equals(rs.getString(2))){
                    rs2.execute("select "+rs.getString(1)+" from "+requestInfo.getRequestManager().getBillTableName()+" where requestid="+requestInfo.getRequestid());
                    while (rs2.next()){
                        if(Util.null2String(rs2.getString(1))!=""){
                            files=files+rs2.getString(1)+",";
                        }
                    }
                }else{
                    rs2.execute("select "+rs.getString(1)+" from "+rs.getString(3)+" where mainid="+requestInfo.getRequestManager().getBillid());
                    while (rs2.next()){
                        if(Util.null2String(rs2.getString(1))!=""){
                            files=files+rs2.getString(1)+",";
                        }
                    }
                }
            }

            if(files.length()>1){
                files=files.substring(0,files.lastIndexOf(","));

                //更新附件的流程编号
                String[] ids = files.split(",");
                Map<String,String> map = new HashMap<>();
                List<Map> list = new ArrayList<>();
                for (String id:ids){
                    map.put("id",id);
                    map.put("flowCode",getWorkflowCode(requestInfo.getWorkflowid(),requestInfo.getRequestid()));
                    list.add(map);
                }

                client = HttpClients.createDefault();
                post = new HttpPost(url2);
                post.addHeader("Tenant-Id","000000");
                post.addHeader("Blade-Auth", Token.getToken(loginId,lastName));
                post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
                StringEntity requestentity=new StringEntity(JSONObject.toJSONString(list), Charset.forName("UTF-8"));
                requestentity.setContentType(ContentType.APPLICATION_JSON.toString());
                post.setEntity(requestentity);
                response = client.execute(post);

                writeLog("httpCode:"+response.getStatusLine().getStatusCode() );
                writeLog("data:"+EntityUtils.toString(response.getEntity()));

                if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                    requestInfo.getRequestManager().setMessagecontent("更新附件流程编号失败!");
                    throw new Exception("更新附件流程编号失败!");
                }

                //文件归档
                writeLog("files:"+files);
                client = HttpClients.createDefault();
                builder = new URIBuilder(url);
                builder.addParameter("ids",files);
                get = new HttpGet(builder.build());
                get.addHeader("Tenant-Id","000000");
                get.addHeader("Blade-Auth", Token.getToken(loginId,lastName));
                get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
                response = client.execute(get);

                writeLog("httpCode:"+response.getStatusLine().getStatusCode() );
                writeLog("data:"+EntityUtils.toString(response.getEntity()));

                if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                    requestInfo.getRequestManager().setMessagecontent("文件归档失败!");
                    throw new Exception("文件归档失败!");
                }
            }
            return Action.SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    public String getWorkflowCode(String workFlowId,String requestId){
        Map<String,Object> map = new HashMap<>();
        map.put("workflowid",workFlowId);
        map.put("requestid",requestId);
        Map p = ServiceUtil.getService(CoustomerFieldServiceImpl.class).getWorkflowInfo(map);
        writeLog("返回的流程编号："+p.toString());
        return (String)p.get("requestCode");
    }
}
