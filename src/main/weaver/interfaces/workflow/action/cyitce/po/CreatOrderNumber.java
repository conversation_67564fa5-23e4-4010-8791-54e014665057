package weaver.interfaces.workflow.action.cyitce.po;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @Title: CreatProcedureNumber
 * @Description: 订单审批登记 创建订单编号
 * @author: 李建潘
 * @date 2021年9月28日
 */
public class CreatOrderNumber extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        String ddbh=null;
        String pre = "DD";//流水前缀
        String lsh="1";

        writeLog("开始创建订单编号");

//        获取requestid
        String requestId = requestInfo.getRequestid();

        writeLog("获取订单登记流程requestid:"+requestId);

//        获取表单名称
        String tablename = requestInfo.getRequestManager().getBillTableName();
//        合同信息
        writeLog("获取合同信息sql："+"select * from "+tablename+" where requestId="+requestId);
        RecordSet rs = new RecordSet();
        rs.execute("select * from "+tablename+" where requestId="+requestId);
        rs.next();
//        判断是否已经有订单编号
        if(!"".equals(rs.getString("ddbh"))&&rs.getString("ddbh")!=null){
            writeLog("已经存在订单编号,ddbh:"+rs.getString("ddbh"));
            return Action.SUCCESS;
        }
        String htid = rs.getString("htmc");
        String htbh = rs.getString("kjhtbh");
        writeLog("合同Id："+htid);
        writeLog("合同编号："+htbh);
//        获取订单流水号
        RecordSet rs2 = new RecordSet();
        writeLog("获取流水号sql："+"select * from uf_htddlshjlb where htmc="+htid);
        rs2.execute("select * from uf_htddlshjlb where htmc="+htid);

        if(rs2.next()){
            lsh = rs2.getString("lsh");
        }else {
            writeLog("合同下的第一个订单");
            RecordSet rs4 = new RecordSet();
            writeLog("初始化流水号sql："+"insert into uf_htddlshjlb(htmc,lsh) value("+htid+","+1+")");
            rs4.execute("insert into uf_htddlshjlb(htmc,lsh) values("+htid+","+1+")");
        }
        writeLog("获取流水号："+lsh);
//        流水号位数转换
        lsh=lshChange(lsh);
        writeLog("流水号转型为："+lsh);
//        订单编号
        RecordSet rs5 = new RecordSet();
        RecordSet rs6 = new RecordSet();
        do{
            ddbh = htbh+"-"+pre+lsh;
            writeLog("创建订单编号："+ddbh);
            writeLog("合同起始流水号变更sql："+"update uf_htddlshjlb set lsh="+(Integer.valueOf(lsh)+1)+" where htmc="+htid);
            rs6.execute("update uf_htddlshjlb set lsh="+(Integer.valueOf(lsh)+1)+" where htmc="+htid);
            writeLog("查看订单编号是否已经存在sql："+"select ddbh from "+tablename+" where ddbh='"+ddbh+"'");
            rs5.execute("select ddbh from "+tablename+" where ddbh='"+ddbh+"'");
            lsh = lshChange(String.valueOf(Integer.valueOf(lsh)+1));
        }while (rs5.next());

//        更改流程编号及订单编号
        RecordSet rs7 = new RecordSet();
//        RecordSet rs8 = new RecordSet();
        RecordSet rs9 = new RecordSet();
        writeLog("更新订单编号sql："+"update "+tablename+" set ddbh='"+ddbh+"' where requestId="+requestId);
        rs7.execute("update "+tablename+" set ddbh='"+ddbh+"' where requestId="+requestId);
//        writeLog("更改流程编号记录表的流程编号sql："+"update workflow_codeseqrecord set workflowCode='"+ddbh+"' where requestId="+requestId);
//        rs8.execute("update workflow_codeseqrecord set workflowCode='"+ddbh+"' where requestId="+requestId);
        writeLog("更改流程编号sql："+"update workflow_requestbase set requestmark='"+ddbh+"' where requestId="+requestId);
        rs9.execute("update workflow_requestbase set requestmark='"+ddbh+"' where requestId="+requestId);
        return Action.SUCCESS;
    }

    public String lshChange(String lsh){
        switch(lsh.length()){
            case 1:lsh="000"+lsh;break;
            case 2:lsh="00"+lsh;break;
            case 3:lsh="0"+lsh;break;
            default:break;
        }
        return lsh;
    }
}