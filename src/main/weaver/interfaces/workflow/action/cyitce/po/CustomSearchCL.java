package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractCustomSqlConditionJavaCode;
import weaver.general.BaseBean;
import weaver.hrm.User;

import javax.rmi.CORBA.Util;
import java.util.Map;

/**
 * @Description: 2019差旅台账自定义查询条件
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/29 17:13
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/8/29      HONOR          v1.0.0               修改原因
 */
public class CustomSearchCL extends AbstractCustomSqlConditionJavaCode {

    BaseBean log = new BaseBean();

    /**
     * 生成SQL查询限制条件
     * @param param
     *  param包含(但不限于)以下数据
     *  user 当前用户
     *
     * @return
     *  返回的查询限制条件的格式举例为: t1.a = '1' and t1.b = '3' and t1.c like '%22%'
     *  其中t1为表单主表表名的别名
     */
    @Override
    public String generateSqlCondition(Map<String, Object> param) throws Exception {
        User user = (User)param.get("user");
        String sqlCondition = "(t1.bxr="+user.getUID();
        String sql = null;
        int yjbm=-1;
        String oneProjectId="";
        String twoProjectCode="";

        RecordSet rs = new RecordSet();

        sql = "SELECT yjbm FROM view_bmjz WHERE sqbm ="+user.getUserDepartment();
        rs.execute(sql);
        rs.next();
        yjbm = rs.getInt(1);


        //查询范围无限制
        sql = "select resourceid from HrmRoleMembers WHERE roleid in (175,171,2,87) and resourceid = "+user.getUID();
        rs.execute(sql);
        if(rs.next()){
            sqlCondition+=" or 1=1";
        }else if ("311,335,336,338".indexOf(user.getUserDepartment()) != -1) {
                sqlCondition += " or t1.yjbm = " + yjbm;
        }else{
            if (yjbm == 309) {
                sqlCondition += " or t1.yjbm in (select bm from Matrixtable_9 where yjz like '%" + user.getUID() + "%')";
                sqlCondition += " or t1.sjbm in (select bm from Matrixtable_9 where sjz like '%" + user.getUID() + "%')";
            }

            //角色
            sql = "select resourceid,resourcetype,seclevelfrom,seclevelto,jobtitlelevel,subdepid from HrmRoleMembers WHERE roleid in (174)";
            rs.execute(sql);
            while (rs.next()) {
                if (rs.getInt("resourcetype") == 1 && rs.getInt("resourceid") == user.getUID()) {
                    sqlCondition += " or t1.yjbm = " + yjbm;
                    break;
                }
                if (rs.getInt("resourcetype") == 2 && rs.getInt("resourceid") == user.getUserSubCompany1() && isSeclevel(user.getSeclevel(), rs)) {
                    sqlCondition += " or t1.yjbm = " + yjbm;
                    break;
                }
                if (rs.getInt("resourcetype") == 3 && rs.getInt("resourceid") == user.getUserDepartment() && isSeclevel(user.getSeclevel(), rs)) {
                    sqlCondition += " or t1.yjbm = " + yjbm;
                    break;
                }
                if (rs.getInt("resourcetype") == 5) {
                    if (rs.getInt("subdepid") == user.getUserSubCompany1() && rs.getString("resourceid").equals(user.getJobtitle()) && rs.getInt("jobtitlelevel") == 2) {
                        sqlCondition += " or t1.yjbm = " + yjbm;
                        break;
                    } else if (rs.getInt("subdepid") == user.getUserSubCompany1() && rs.getString("resourceid").equals(user.getJobtitle()) && rs.getInt("jobtitlelevel") == 3) {
                        sqlCondition += " or t1.yjbm = " + yjbm;
                        break;
                    }
                }
            }

            if(user.getUID()==809){
                sqlCondition+=" or t1.yjbm = 265)";
            }

            sql = "select ejz from Matrixtable_9 where bm=270 and ejz like '%"+user.getUID()+"%'";
            rs.execute(sql);
            if(rs.next()){
                sqlCondition+=" or t1.yjbm=92";
            }

            sql = "select id from uf_yjxmlxjm where xmfzr like '%"+user.getUID()+"%'";
            rs.execute(sql);
            while (rs.next()){
                oneProjectId+=rs.getString(1)+",";
            }
            if(oneProjectId!=""){
                sqlCondition+=" or t1.yjxmmc in ("+oneProjectId.substring(0,oneProjectId.length()-1)+")";
            }

            sql = "select ejxmnbbh from uf_ejxmlx where xmjlspm like '%"+user.getUID()+"%'";
            rs.execute(sql);
            while (rs.next()){
                twoProjectCode+="'"+rs.getString(1)+"',";
            }
            if(twoProjectCode!=""){
                sqlCondition+=" or t1.ejxmbh in ("+twoProjectCode.substring(0,twoProjectCode.length()-1)+")";
            }
        }

        sqlCondition+=")";
        return sqlCondition;
    }

    private boolean isSeclevel(String seclevel,RecordSet rs){
        return Integer.valueOf(seclevel)>=rs.getInt("seclevelfrom")&&Integer.valueOf(seclevel)<=rs.getInt("seclevelto");
    }


}
