package weaver.interfaces.workflow.action.cyitce.po;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

public class CreateTwoProjectCodeAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String yjxmCode;//一级项目编号
        String yjxmid;
        int lsh=1;//流水号
        String lshStr;//流水号 3位
        String ejxmCode;//二级项目编号
        String sql;
        RecordSet rs = new RecordSet();

        requestInfo.getRequestManager().setMessagecontent("二级项目编号生成失败");
        String requestid = requestInfo.getRequestid();
        try {
            //判断是否已经生成了编号
            sql = "select ejxmnbbh from formtable_main_100 where requestid="+requestid;
            rs.execute(sql);
            rs.next();
            if(rs.getString(1).length()>0){
                return Action.SUCCESS;
            }

            //获取一级项目编号
            sql = "select yjxmbh,yjxmmc from formtable_main_100 where requestid="+requestid;
            writeLog(sql);
            rs.execute(sql);
            rs.next();
            yjxmCode = rs.getString(1);
            yjxmid = rs.getString(2);
            if(yjxmid==null||"".equals(yjxmid)){
                requestInfo.getRequestManager().setMessagecontent("一级项目为空，二级项目编号生成失败!");
                throw new Exception("一级项目为空，二级项目编号生成失败!");
            }

            //流水号
            sql = "select ejxmlsh from uf_ejxmlshdjb where yjxm="+yjxmid;
            writeLog(sql);
            rs.execute(sql);
            if(rs.next()){
                lsh = rs.getInt(1);
                lshStr = lshFormatChange(lsh,3);
                sql = "update uf_ejxmlshdjb set ejxmlsh=ejxmlsh+1 where yjxm="+yjxmid;
                writeLog(sql);
            }else {
                lshStr = lshFormatChange(lsh,3);
                sql = "insert into uf_ejxmlshdjb(yjxm,ejxmlsh) values("+yjxmid+","+(lsh+1)+")";
                writeLog(sql);
            }
            if(lshStr==null||"".equals(lshStr)){
                requestInfo.getRequestManager().setMessagecontent("流水号为空，二级项目编号生成失败!");
                throw new Exception("流水号为空，二级项目编号生成失败!");
            }
            rs.execute(sql);


            ejxmCode = yjxmCode+lshStr;
            sql = "update formtable_main_100 set ejxmnbbh='"+ejxmCode+"' where requestid="+requestid;

            rs.execute(sql);
            rs.execute("update workflow_requestbase set requestmark='"+ejxmCode+"' where requestId="+requestid);
            writeLog(sql);
            rs.execute(sql);

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
        }

        return Action.FAILURE_AND_CONTINUE;
    }

    private String lshFormatChange(int i,int length){
        String lsh = String.valueOf(i);
        int iLength = lsh.length();
        int fillLength = length-iLength;

        if(fillLength>0){
            for (int j=0;j<fillLength;++j){
                lsh = "0"+lsh;
            }
        }
        return lsh;
    }
}
