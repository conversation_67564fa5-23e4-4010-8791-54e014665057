package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PasswordUtil;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LoginUpdateAction extends BaseBean implements Action {

    //系统字段
    public String loginidName;//登录账号
    public String passwordName;//登录密码
    public String aqjbName;//安全级别
    public String bememberdate;//入团日期
    public String bepartydate;//入党日期
    public String companystartdate;//入职日期
    public String workstartdate;//参加工作日期
    //自定义字段  scopeid=1
    public String field1;//职称评定时间
    public String field2;//专业技术
    public String field3;//专业
    public String field4;//毕业院校
    public String field5;//毕业时间
    public String field6;//邮编
    public String field7;//个人资质证书(建模)
    public String field9;//员工身份
    public String field10;//岗位等级
    public String field11;//能力认证等级
    public String field12;//入职身份
    public String field16;//职称类型
    public String field13;//职称等级
    public String field29;//紧急联系人
    public String field30;//紧急联系人电话
    public String field32;//民族（新）
    public String field33;//政治面貌（新）
    public String field34;//户口性质
    public String field49;//移动电话
    public String field47;//私人邮箱
    public String field53;//性别
    //自定义字段 scopeid=-1
    public String field37;//社保购买地
    public String field38;//公积金购买地
    public String field39;//是否购买公积金
    //自定义字段 scopeid=3
    public String field31;//合同类型
    @Override
    public String execute(RequestInfo requestInfo) {
        String id;
        boolean boo=false;
        RecordSet rs = new RecordSet();
        String sql;


        //流程字段
        StringBuffer sysAndSql = new StringBuffer();
        String[] sysFieldList = {loginidName,passwordName,aqjbName,bememberdate,bepartydate,companystartdate,workstartdate};
        String[] sysFieldNameList = {"dlzh","dlmm","aqjb","bememberdate","bepartydate","companystartdate","workstartdate"};
        String[] sysFieldValueList = {"","Abcd1234","-5","","","",""};
        //自定义字段  scopeid=1
        StringBuffer andSql = new StringBuffer();
        StringBuffer valueSql = new StringBuffer();
        String[] fieldList = {field1,field2,field3,field4,field5,field6,field7,field9,field10,field11,field12,field13,field16,field29,field30,field32,field33,field34,field49,field47,field53};
        String[] fieldNameList = {"field1","field2","field3","field4","field5","field6","field7","field9","field10","field11","field12","field13","field16","field29","field30","field32","field33","field34","field49","field47","field53"};
        //自定义字段 scopeid=-1
        StringBuffer cusAndSql01 = new StringBuffer();
        String[] fieldList01 = {field37,field38,field39};
        String[] fieldNameList01 = {"field37","field38","field39"};
        String[] fieldListValue01 = new String[3];
        //自定义字段 scopeid=3
        StringBuffer andSql3 = new StringBuffer();
        StringBuffer valueSql3 = new StringBuffer();
        String[] fieldList3 = {field31};
        String[] fieldNameList3 = {"field31"};

        getFieldName(sysFieldList,sysFieldNameList);
        getFieldName(fieldList,fieldNameList);
        getFieldName(fieldList3,fieldNameList3);
        getFieldName(fieldList01,fieldNameList01);

        //主表数据
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String requestid = requestInfo.getRequestid();//请求ID

        writeLog("获取主表信息-------");
        for(int i = 0; i < properties.length; ++i) {
            writeLog("系统字段------");
            getSql(properties[i],sysFieldList,sysFieldValueList,sysFieldNameList,sysAndSql);
            writeLog("自定义字段 scopeid=1------");
            getSql(valueSql,andSql,properties[i],fieldList,fieldNameList);
            writeLog("自定义字段 scopeid=-1------");
            getSql(properties[i],fieldList01,fieldListValue01,fieldNameList01,cusAndSql01);
            writeLog("自定义字段 scopeid=3------");
            getSql(valueSql3,andSql3,properties[i],fieldList3,fieldNameList3);
        }
        if(sysAndSql.length()>0){
            writeLog("删除sql末尾逗号："+sysAndSql.toString());
            sysAndSql.deleteCharAt(sysAndSql.lastIndexOf(","));
            writeLog("删除sql末尾逗号成功："+sysAndSql.toString());
        }


        writeLog(loginidName+":"+sysFieldValueList[0]);
        writeLog(passwordName+":"+sysFieldValueList[1]);
        writeLog(aqjbName+":"+sysFieldValueList[2]);

        try {
            sql = "select currentid-1 from SequenceIndex where indexdesc='resourceid'";
            rs.execute(sql);
            rs.next();
            id = String.valueOf(rs.getInt(1));
            writeLog("人员id:"+id);
            String[] info = PasswordUtil.encrypt(sysFieldValueList[1]);
            sql = "update HrmResource set loginid = ?,password =?,salt=?,seclevel=? where id =?";
            boo = rs.executeUpdate(sql, new Object[]{sysFieldValueList[0], info[0], info[1],sysFieldValueList[2],id});
            writeLog("loginidName:"+boo);

            //自定义字段 scopeid=1
            andSql.append("id");
            valueSql.append(id);
            sql = "insert into cus_fielddata(scope,scopeid,"+andSql+") values('HrmCustomFieldByInfoType',1,"+valueSql+")";
            writeLog("insert:"+sql);
            rs.execute(sql);
            //自定义字段 scopeid=-1
            if(cusAndSql01.length()>0){
                sql = "update cus_fielddata set "+cusAndSql01+" where id ="+id+" and scopeid=-1";
                writeLog("update:"+sql);
                rs.execute(sql);
            }
            //自定义字段 scopeid=3
            andSql3.append("id");
            valueSql3.append(id);
            sql = "insert into cus_fielddata(scope,scopeid,"+andSql3+") values('HrmCustomFieldByInfoType',3,"+valueSql3+")";
            writeLog("insert:"+sql);
            rs.execute(sql);

            if(sysAndSql.length()>0){
                sql = "update HrmResource set "+sysAndSql+" where id ="+id;
                writeLog("update:"+sql);
                rs.execute(sql);
            }
        }catch (Exception e){
            writeLog(e);
        }
        return Action.SUCCESS;
    }

    private static void getFieldName(String[] fieldList,String[] fieldNameList){
        BaseBean log = new BaseBean();
        for (int i=0;i<fieldList.length;++i){
            if("".equals(fieldList[i])||fieldList[i]==null){
                fieldList[i]=fieldNameList[i];
            }
            log.writeLog(fieldNameList[i]+"--"+fieldList[i]);
        }
    }

    private static void getSql(StringBuffer valueSql,StringBuffer andSql,Property properties,String[] fieldList,String[] fieldNameList){
        BaseBean log = new BaseBean();
        String propName = properties.getName();
        String propVal = properties.getValue();
        for(int i=0;i<fieldList.length;++i){
            if(fieldList[i].equals(propName)&&isvalue(propVal)){
                log.writeLog(fieldNameList[i]+"--"+fieldList[i]+":"+propVal);
                andSql.append(fieldNameList[i]);
                andSql.append(",");
                valueSql.append("'");
                valueSql.append(propVal);
                valueSql.append("',");
                break;
            }
        }
    }

    private static void getSql(Property properties,String[] sysFieldList,String[] sysFieldValueList,String[] sysFieldNameList,StringBuffer sysAndSql){
        BaseBean log = new BaseBean();
        String propName = properties.getName();
        String propVal = properties.getValue();
        for(int i=0;i<sysFieldList.length;++i){
            if(sysFieldList[i].equals(propName)&&isvalue(propVal)){
                sysFieldValueList[i] = propVal;
                if(i>=3){
                    sysAndSql.append(sysFieldNameList[i]);
                    sysAndSql.append("='");
                    sysAndSql.append(propVal);
                    sysAndSql.append("',");
                }
                log.writeLog(sysFieldNameList[i]+"--"+propVal);
                break;
            }
        }
    }

    private static Boolean isvalue(String str){
        if("".equals(str)||str==null){
            return false;
        }
        return true;
    }
}
