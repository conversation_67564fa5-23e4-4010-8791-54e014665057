package weaver.interfaces.workflow.action.cyitce.po.contract;

public enum TaxRate {
    TAX_233(233, "0%"),
    TAX_234(234, "1%"),
    TAX_235(235, "3%"),
    TAX_502(502, "5%"),
    TAX_236(236, "6%"),
    TAX_237(237, "9%"),
    TAX_238(238, "10%"),
    TAX_239(239, "11%"),
    TAX_240(240, "13%"),
    TAX_241(241, "16%"),
    TAX_242(242, "17%"),
    TAX_503(503, "多税率");

    private final int id;
    private final String text;

    TaxRate(int id, String text) {
        this.id = id;
        this.text = text;
    }

    public int getId() {
        return id;
    }

    public String getText() {
        return text;
    }

    public static TaxRate fromId(int id) {
        for (TaxRate rate : values()) {
            if (rate.getId() == id) {
                return rate;
            }
        }
        throw new IllegalArgumentException("No TaxRate found with id: " + id);
    }
}