package weaver.interfaces.workflow.action.cyitce.po.contract;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.BuyContract;
import weaver.interfaces.workflow.action.cyitce.doc.Action.*;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.HttpRequestManage;
import weaver.interfaces.workflow.action.cyitce.service.buyContract.*;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.WorkflowManage;
import weaver.interfaces.workflow.action.cyitce.util.freemarker.FreemarkerCreateDocx;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: CreateBuyContractWordAction2
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-09-26  15:26
 * @Version: 1.0
 */
public class CreateBuyContractWordAction2 extends BaseBean implements Action {
    // doc输出文件目录
    private String outUrl = BuyContract.outUrl + "/tempDoc/";
    //    // doc输出文件目录
    private String modeUrl = BuyContract.outUrl + "/template/";

    // OA文件上传接口
    private String outApiUrl = BuyContract.outApiUrl;
    RequestInfo requestInfo = null;

    private String wordIdName = "gzfj";
    private String yjxmmcId = "";
    private String yjxmmcName = "";

    @Override
    public String execute(RequestInfo requestInfo) {
        this.requestInfo = requestInfo;
        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p : property) {
            if ("yjxmmc".equals(p.getName())) {
                yjxmmcId = p.getValue();
            }
        }


        RecordSet rs = null;
        String htzl = "";
        FileOutputStream fos = null;

        ContractFileMerg cfm = new ContractFileMerg(outUrl + "merg/", outUrl + "temp/");

        try {

            if (!StringUtils.hasText(yjxmmcId)) {
                yjxmmcName = "默认-";
            } else {
                RecordSet recordSet = new RecordSet();
                recordSet.execute("select xmmc  from uf_yjxmlxjm where id=" + yjxmmcId);
                yjxmmcName = Util.null2String(recordSet.getString("xmmc"));
                if (StringUtils.hasText(yjxmmcName)) {
                    yjxmmcName = yjxmmcName + "-";
                } else {
                    yjxmmcName = "默认-";
                }
            }


            rs = new RecordSet();
            rs.execute("select htzl,htxs from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid());
            rs.next();

            if ("".equals(Util.null2String(rs.getString(1)))|| "0".equals(Util.null2String(rs.getString(2)))) {
                return Action.SUCCESS;
            }

            // 选择合同模板
            // 生成临时的word
            htzl = Util.null2String(rs.getString(1));

            switch (htzl) {
                case "0":
                    Contract1 co1 = new Contract1();
                    if ("0".equals(co1.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("clzpht.zip", yjxmmcName + "车辆租赁合同.docx", ModeUrl.CLZPHT_URL, co1.getImageInfoList(), co1, wordIdName);
                    break;
                case "1":
                    Contract2 co2 = new Contract2();
                    if ("0".equals(co2.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("fwzpht.zip", yjxmmcName + "房屋租赁合同.docx", ModeUrl.FWZPHT_URL, co2.getImageInfoList(), co2, wordIdName);
                    break;
                case "2":
                    Contract3 co3 = new Contract3();
                    if ("0".equals(co3.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("lwfbht.zip", yjxmmcName + "劳务分包合同.docx", ModeUrl.LWFBHT_URL, co3.getImageInfoList(), co3, wordIdName);
                    executMode("fj1.zip", yjxmmcName + "关于规范发票行为及收款账户的不可撤销承诺函.docx", ModeUrl.LWFBHT_FJ1_URL, co3, "zlfwcqz");
                    executMode("fj2.zip", yjxmmcName + "项目安全生产与文明施工管理承诺书.docx", ModeUrl.LWFBHT_FJ2_URL, co3, "czrsfzyyzz");
                    executMode("fj3.zip", yjxmmcName + "廉洁诚信承诺书.docx", ModeUrl.LWFBHT_FJ3_URL, co3, "fjs");
                    executMode("fj3d1.zip", yjxmmcName + "有关联人员认证声明书.docx", ModeUrl.LWFBHT_FJ3d1_URL, co3, "fjs");
                    executMode("fj3d2.zip", yjxmmcName + "无关联人员认证声明书.docx", ModeUrl.LWFBHT_FJ3d2_URL, co3, "fjs");
                    executMode("fj4.zip", yjxmmcName + "不拖欠民工工资承诺书.docx", ModeUrl.LWFBHT_FJ4_URL, co3, "fjsi");
                    executMode("fj13.zip", yjxmmcName + "机具、辅材、低值易耗品范围.docx", ModeUrl.LWFBHT_FJ13_URL, co3, "fjshis");
                    break;
                case "3":
                    Contract4 co4 = new Contract4();
                    if ("0".equals(co4.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("clgxht.zip", yjxmmcName+"材料购销合同.docx", ModeUrl.CLGXHT_URL, co4.getImageInfoList(), co4, wordIdName);
                    break;
                case "4":
                    Contract5 co5 = new Contract5();
                    if ("0".equals(co5.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("jsxmfwkjht.zip", yjxmmcName+"技术项目服务框架合同.docx", ModeUrl.JSXMFWKJHT_URL, co5.getImageInfoList(), co5, wordIdName);
                    break;
                case "5":
                    Contract4 co6 = new Contract4();
                    if ("0".equals(co6.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("sbgxht.zip", yjxmmcName+"设备购销合同.docx", ModeUrl.SBGXHT_URL, new ArrayList<>(), co6, wordIdName);
                    break;
                case "6":
                    LWWXToElectronDocAction lwwx = new LWWXToElectronDocAction();
                    String res = lwwx.execute(requestInfo);

                    if ("0".equals(res)) {
                        requestInfo.getRequestManager().setMessage("采购合同生成失败！");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                    break;
            }

            cfm.delete();
            return Action.SUCCESS;
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            writeLog(sw.toString());
            writeLog(e.getMessage());
        } finally {
            try {
                cfm.delete();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }

        return Action.FAILURE_AND_CONTINUE;
    }

    /**
     * @param requestInfo
     * @return java.lang.String
     * @Description: 上传附件接口参数
     * @Author: lijianpan
     * @date: 2022/6/27 15:19
     */
    private String getUploadBody(RequestInfo requestInfo) {
        // 上传附件接口参数设置
        JSONObject entity_json = new JSONObject();
        entity_json.put("category", WorkflowManage.getDocPath(requestInfo.getRequestManager().getFormid()));// 存放目录
        entity_json.put("isFirstUploadFile", "9");// 附件状态
        entity_json.put("workflowid", requestInfo.getRequestManager().getWorkflowid());// 流程workflowid
        entity_json.put("listType", "list");
        entity_json.put("f_weaver_belongto_userid", requestInfo.getCreatorid());// 附件归属人
        entity_json.put("f_weaver_belongto_usertype", "0");
        return entity_json.toJSONString();
    }

    private void executMode(String z, String o, String t, List<ImageInfo> im, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(outUrl + o)), im, t, z);
        upload(o, outUrl + o, fieldName);
    }

    private String executMode(String z, String o, String t, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(outUrl + o)), new ArrayList<>(), t, z);
        upload(o, outUrl + o, fieldName);
        return outUrl + o;
    }

    private String executMode2(String z, String path, String o, String t, Object c) throws Exception {
        writeLog("电子合同路径：" + path + o);
        CommonUtil.setPropertyValue(c);
        writeLog("电子合同路径2：" + path + o);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(path + o)), new ArrayList<>(), t, z);
        writeLog("电子合同路径3：" + path + o);
        return path + o;
    }

    private String executMode2(String z, String path, String o, String t, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(path + o)), new ArrayList<>(), t, z);
        upload2(o, path + o, fieldName);
        return path + o;
    }

    private void upload(String o, String filePath, String fieldName) throws Exception {
        HttpRequestManage http = new HttpRequestManage();
        String wordId = http.raise(o, filePath, outApiUrl, getUploadBody(requestInfo));
        File file = new File(filePath);
        if (wordId == null || "".equals(wordId)) {
            file.delete();
            requestInfo.getRequestManager().setMessagecontent("生成的word上传失败！");
            throw new Exception("生成的word上传失败！");
        }
        writeLog("附件准备删除");
        // 删除临时的word
        file.delete();

        writeLog("电子附件字段更新");
        // 返回文档id到流程中
        RecordSet rs = new RecordSet();
        String sql = "select " + fieldName + " from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid();
        rs.execute(sql);
        rs.next();
        if ("".equals(Util.null2String(rs.getString(1)))) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        } else if (!wordIdName.equals(fieldName)) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=concat(" + fieldName + ",',','" + wordId + "') where requestid=" + requestInfo.getRequestid();
        } else {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        }
        rs.execute(sql);
    }

    private void upload2(String o, String filePath, String fieldName) throws Exception {
        HttpRequestManage http = new HttpRequestManage();
        String wordId = http.raise(o, filePath, outApiUrl, getUploadBody(requestInfo));
        File file = new File(filePath);
        if (wordId == null || "".equals(wordId)) {
            file.delete();
            requestInfo.getRequestManager().setMessagecontent("生成的word上传失败！");
            throw new Exception("生成的word上传失败！");
        }

        writeLog("电子附件字段更新");
        // 返回文档id到流程中
        RecordSet rs = new RecordSet();
        String sql = "select " + fieldName + " from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid();
        rs.execute(sql);
        rs.next();
        if ("".equals(Util.null2String(rs.getString(1)))) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        } else if (!wordIdName.equals(fieldName)) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=concat(" + fieldName + ",',','" + wordId + "') where requestid=" + requestInfo.getRequestid();
        } else {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        }
        rs.execute(sql);
    }

    private boolean updateFile(String fieldName) {
        RecordSet rs = new RecordSet();
        boolean boo = rs.execute("update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=null where requestid=" + requestInfo.getRequestid());
        return boo;
    }
}