package weaver.interfaces.workflow.action.cyitce.po;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * 添加人员安全级别
 */
public class SeclevelSetAction extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        String aqjb = "";
        String loginid = "";
        String sql = "";
        boolean status = false;
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        requestInfo.getDescription();

        for(int i = 0; i < properties.length; ++i) {
            if (properties[i].getName().equals("aqjb")) {
                aqjb = properties[i].getValue();
            }else if(properties[i].getName().equals("dlzh")){
                loginid = properties[i].getValue();
            }
        }

        try {
            RecordSet rs = new RecordSet();
            writeLog("update HrmResource set seclevel="+aqjb+" where loginid="+loginid);
            sql = "update HrmResource set seclevel=? where loginid=?";
            rs.executeUpdate(sql,new Object[]{aqjb,loginid});
        }catch (Exception e){
            e.printStackTrace();
        }
        return Action.SUCCESS;
    }
}
