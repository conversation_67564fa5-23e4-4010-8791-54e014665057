package weaver.interfaces.workflow.action.cyitce.po.income;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;
import weaver.interfaces.workflow.action.cyitce.manager.paasspace.PaasSpaceToken;
import weaver.interfaces.workflow.action.cyitce.util.WorkflowManage;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: IncomeCostWorkFlowEndAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-28  09:38
 * @Version: 1.0
 */
public class IncomeCostWorkFlowEndAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map mainMap = new HashMap();

        //触发paas平台接口入决算池
        HttpClient client = null;
        HttpPost post=null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;

        int userid = WorkflowManage.getRequestCreater(requestInfo.getRequestid());
        User user = new User(userid);

        for (Property p:propertys){
            mainMap.put(p.getName(), Util.null2String(p.getValue()));
        }

        Map m = new HashMap();
        try {
            //获取paas平台token
            PaasSpaceToken paas = new PaasSpaceToken();
            String token = paas.token(user.getMobile());

            client = HttpClients.createDefault();
//            builder = new URIBuilder(AddressManagementPool.getIpAddress("PAAS_countfa")+"/pond/change_status");
//            builder.addParameter("projectCode",String.valueOf(mainMap.get("ejxmbh")));
//            builder.addParameter("isSuccess","true");
//            get = new HttpGet(builder.build());
//            response = client.execute(get);
            m.put("projectCode",String.valueOf(mainMap.get("ejxmbh")));
            m.put("isSuccess",true);
            post = new HttpPost(AddressManagementPool.getIpAddress("PAAS_countfa")+"/pond/change_status");
            post.addHeader("Content-Type", "application/json;charset=utf-8");
            post.addHeader("Authorization", token);
            StringEntity requestentity=new StringEntity(JSONObject.toJSONString(m), Charset.forName("UTF-8"));
            requestentity.setContentType(ContentType.APPLICATION_JSON.toString());
            post.setEntity(requestentity);
            response = client.execute(post);
            httpEntity = response.getEntity();
            String info = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(info);
            writeLog(jsonObject.toJSONString());

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                new Exception("pass平台项目决算归档失败！");
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessage(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}