package weaver.interfaces.workflow.action.cyitce.po.car;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 项目用车冲突校验
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-24  16:18
 * @Version: 1.0
 */
public class CarUseConFlictCheck extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog("用车申请冲突校验=====");

        String var="-1";
        String var1="";
        String var2="";
        String var3="";
        String var4="";
        String var5="";

        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p:property){
            if(p.getName().equals("cph")){
                var=p.getValue();
            }else if(p.getName().equals("ycjh")){
                var1=p.getValue();
            }else if(p.getName().equals("yjcfsj")){
                var2=p.getValue();
            }else if(p.getName().equals("yjfcsj")){
                var3=p.getValue();
            }else if(p.getName().equals("cqsyyjkssj")){
                var4=p.getValue();
            }else if(p.getName().equals("cqsyyjjssj")){
                var5=p.getValue();
            }
        }

        RecordSet rs = new RecordSet();
        try {
            if(var1.equals("0")){
                rs.executeQuery("select cph from uf_ycsqb where cph=? and yjcfsj<=? and yjfcsj>=?",new Object[]{var,var3,var2});
            }else if(var1.equals("1")){
                rs.executeQuery("select cph from uf_ycsqb where cph=? and cqsyyjkssj<=? and cqsyyjjssj>=?",new Object[]{var,var5,var4});
            }

            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆所选用车时间段冲突");
                return Action.FAILURE_AND_CONTINUE;
            }

            if(var1.equals("0")){
                rs.executeQuery("SELECT a.cph FROM formtable_main_1880 a \n" +
                        "inner join workflow_nownode b on a.requestid=b.requestid \n" +
                        "where b.nownodetype in (1,2) and a.cph=? and a.yjcfsj<=? and a.yjfcsj>=?",new Object[]{var,var3,var2});
            }else if(var1.equals("1")){
                rs.executeQuery("SELECT a.cph FROM formtable_main_1880 a \n" +
                        "inner join workflow_nownode b on a.requestid=b.requestid \n" +
                        "where b.nownodetype in (1,2) and a.cph=? and a.cqsyyjkssj<=? and a.cqsyyjjssj>=?",new Object[]{var,var5,var4});
            }

            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆所选用车时间段冲突");
                return Action.FAILURE_AND_CONTINUE;
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}