package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import com.weaver.general.Util;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;

import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.soa.workflow.request.RequestInfo;


public class createPorjectAction extends BaseBean implements Action {
    String url= OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/createProject";

    @Override
    public String execute(RequestInfo requestInfo) {
        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response = null;
        writeLog("=====================文件管理系统创建项目===========================");
        String loginId = "";
        String lastName = "";

        try {
            RecordSet rs = new RecordSet();
            RecordSet rs2 = new RecordSet();
            JSONObject obj = new JSONObject();

            rs.execute("SELECT creater FROM workflow_requestbase where requestid=" + requestInfo.getRequestid());
            rs.next();
            rs.execute("select loginid,lastname from hrmresource where id="+rs.getString(1));
            rs.next();
            loginId = Util.null2String(rs.getString(1));
            lastName = Util.null2String(rs.getString(2));
            obj.put("createUsername", Util.null2String(rs.getString(1)));
            obj.put("createRealname", Util.null2String(rs.getString(2)));

            rs.execute("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid="+requestInfo.getRequestid());
            rs.next();
            String[] strings = rs.getString("cyry").split(",");
            rs2.execute("SELECT fieldname FROM workflow_billfield where billid="+requestInfo.getRequestManager().getFormid()+" and fieldname like 'CoustomerFile%' and fieldhtmltype=1 and type=1");
            String ids = "";
            while (rs2.next()) {
                String id = Util.null2String(rs.getString(rs2.getString(1)));
                if(!"".equals(id)){
                    ids = ids+id;
                    if(ids.lastIndexOf(",")!=(ids.length()-1)){
                        ids = ids+",";
                    }
                }
            }
            if(ids.length()>0){
                ids.substring(0,ids.lastIndexOf(","));
            }
            obj.put("ids",toList(ids));
            obj.put("projectCode",Util.null2String(rs.getString("xmbh")));
            obj.put("projectName",Util.null2String(rs.getString("xmmc")));

            String deptId = Util.null2String(rs.getString("sqrbm"));
            obj.put("deptId",deptId);
            rs.execute("select yjbm,ejbm,sjbm from view_bmjz where sqbm="+deptId);
            rs.next();
            String deptName = "";
            for (int i=1;i<=3;++i){
                rs2.execute("select departmentname from hrmdepartment where id="+Util.null2String(rs.getString(i)));
                rs2.next();
                deptName = deptName+rs2.getString(1);
                if(i<3){
                    deptName = deptName + ",";
                }
            }
            obj.put("deptName",deptName);

            for (int i=0;i<strings.length;++i){
                rs.execute("select loginid from hrmresource where id="+strings[i]);
                if(rs.next()){
                    strings[i]=rs.getString(1);
                }else {
                    strings[i]="";
                }
                writeLog("usernames="+rs.getString(1));
            }
            obj.put("usernames",strings);

            writeLog("============== obj ："+obj.toString());
            client = HttpClients.createDefault();
            post = new HttpPost(url);
            post.addHeader("Authorization","Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            post.addHeader("Tenant-Id","000000");
            post.addHeader("Blade-Auth", Token.getToken(loginId,lastName));
            StringEntity entity1 = new StringEntity(obj.toString(),"UTF-8");
            entity1.setContentEncoding("UTF-8");
            entity1.setContentType("application/json");
            post.setEntity(entity1);
            response = client.execute(post);

            writeLog("============== data："+EntityUtils.toString(response.getEntity()));
            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                writeLog("文件管理系统创建项目失败");
                requestInfo.getRequestManager().setMessagecontent("文件管理系统创建项目失败!");
                throw new Exception("文件管理系统创建项目失败!");
            }
            return Action.SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    private String[] toList(String str){
        writeLog("ids value :"+str);
        if("".equals(str)||str==null){
            return new String[]{};
        }
        return str.split(",");
    }
}
