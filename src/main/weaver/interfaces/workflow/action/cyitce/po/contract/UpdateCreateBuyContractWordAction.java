package weaver.interfaces.workflow.action.cyitce.po.contract;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.Action.update.UpdateLWWXToElectronDocAction;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: UpdateCreateBuyContractWordAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-03-05  15:35
 * @Version: 1.0
 */
public class UpdateCreateBuyContractWordAction extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        RecordSet rs = null;
        String ret = Action.SUCCESS;

        try {
            rs = new RecordSet();
            rs.execute("select htzl from "+requestInfo.getRequestManager().getBillTableName()+" where requestid="+requestInfo.getRequestid());
            rs.next();

            String htzl = Util.null2String(rs.getString(1));
            if("".equals(htzl)){
                return Action.SUCCESS;
            }

            switch (htzl){
                case "0"://车辆租赁
                    break;
                case "1"://房屋租赁
                    break;
                case "3"://材料购销
                    break;
                case "4"://项目技术服务
                    break;
                case "5"://设备购销
                    break;
                case "6"://劳务外协
                    UpdateLWWXToElectronDocAction lwwx = new UpdateLWWXToElectronDocAction();
                    ret = lwwx.execute(requestInfo);
                    break;
            }

        }catch (Exception e){
            return FAILURE_AND_CONTINUE;
        }

        return ret;
    }
}