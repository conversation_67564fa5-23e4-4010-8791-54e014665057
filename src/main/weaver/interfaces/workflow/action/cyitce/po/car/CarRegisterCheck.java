package weaver.interfaces.workflow.action.cyitce.po.car;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 车辆注册校验
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-24  16:21
 * @Version: 1.0
 */
public class CarRegisterCheck extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String var="-1";

        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p:property){
            if(p.getName().equals("cph")){
                var=p.getValue();
                break;
            }
        }

        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("select cph from uf_catregister where cph=?",new Object[]{var});

            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆已登记");
                return Action.FAILURE_AND_CONTINUE;
            }

            rs.executeQuery("select cph from formtable_main_1874 a inner join workflow_currentoperator b on a.requestid=b.requestid where b.nownodetype in (1,2) and cph=?",new Object[]{var});

            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("车辆登记审核中");
                return Action.FAILURE_AND_CONTINUE;
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}