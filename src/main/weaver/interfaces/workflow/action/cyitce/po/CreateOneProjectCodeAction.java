package weaver.interfaces.workflow.action.cyitce.po;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * @Title: CreatProcedureNumber
 * @Description: 一级项目编号规则
 * @author: 李建潘
 * @date 2022年5月23日
 */
public class CreateOneProjectCodeAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String yjxmmc = "";
        String year = null;//年份 2位
        String type = null;//项目类型
        String deptCode = null;//部门编码
        String deptid = null;//部门id
        int slh=10;//流水号
        String slhStr = "010";//流水号 3位
        String yjxmCode = null;
        String sql = null;
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);
        RecordSet rs2 = new RecordSet();

        requestInfo.getRequestManager().setMessagecontent("一级项目编号生成失败");
        String requestid = requestInfo.getRequestid();

        try {
//          判断是否已经生成了编号
            sql = "select xmbh,xmmc from formtable_main_45 where requestid="+requestid;
            rs.execute(sql);
            rs.next();
            yjxmmc = rs.getString(2);
            if(rs.getString(1).length()>0){
                return Action.SUCCESS;
            }

            Calendar cal = Calendar.getInstance();
            year = String.valueOf(cal.get(Calendar.YEAR)).substring(2,4);
            writeLog("年份:"+year);
            if(year==null||"".equals(year)){
                requestInfo.getRequestManager().setMessagecontent("年份为空，一级项目编号生成失败!");
                throw new Exception("年份为空，一级项目编号生成失败!");
            }

            //项目类型-编号
            Map<Integer,String> typeMap = new HashMap<>();
            typeMap.put(0,"WJ");
            typeMap.put(1,"WW");
            typeMap.put(2,"WY");
            typeMap.put(3,"JC");
            typeMap.put(4,"QT");
            typeMap.put(5,"I");

            sql = "select gclx,sqrbm,sfsggxm,sjbm from formtable_main_45 where requestid="+requestid;
            writeLog(sql);
            rs.execute(sql);
            rs.next();
            type = typeMap.get(rs.getInt(1));
            if(type==null||"".equals(type)){
                requestInfo.getRequestManager().setMessagecontent("项目类型为空，一级项目编号生成失败!");
                throw new Exception("项目类型为空，一级项目编号生成失败!");
            }
            writeLog("项目类型:"+type);

            //判断是否是公共项目
            if(rs.getInt(3)==0){
                writeLog("公共项目");
                deptid = rs.getString(2);
                type = "P";
                writeLog("deptid："+deptid);
                if(deptid==null||"".equals(deptid)){
                    requestInfo.getRequestManager().setMessagecontent("公共项目（包含售前）费用承担部门为空，一级项目编号生成失败!");
                    throw new Exception("公共项目（包含售前）费用承担部门为空，一级项目编号生成失败!");
                }
            }else {
                writeLog("非公共项目");
                deptid = rs.getString(4);
                writeLog("deptid："+deptid);
                if(deptid==null||"".equals(deptid)){
                    requestInfo.getRequestManager().setMessagecontent("生产项目三级部门为空，一级项目编号生成失败!");
                    throw new Exception("生产项目三级部门为空，一级项目编号生成失败!");
                }
            }
            writeLog("项目归属部门ID:"+deptid);

            //部门编码
            sql = "select u8bmbh from uf_oau8dm where oabmid="+deptid;
            writeLog(sql);
            rs2.execute(sql);
            if(rs2.next()){
                deptCode = rs2.getString(1);
            }else {
                sql = "select departmentcode from HrmDepartment where id="+deptid;
                rs2.execute(sql);
                if(rs2.next()){
                    deptCode = rs2.getString(1);
                }
            }

            if(deptCode==null||"".equals(deptCode)){
                requestInfo.getRequestManager().setMessagecontent("部门编码为空，一级项目编号生成失败!");
                throw new Exception("部门编码为空，一级项目编号生成失败!");
            }
            writeLog("部门编码："+deptCode);

            //判断是否是公共项目
            if(rs.getInt(3)==0){
                writeLog("是否是售前公共项目:"+yjxmmc.contains("售前"));
                if(yjxmmc.contains("售前")){
                    slhStr = "002";
                }else {
                    slhStr = "001";
                }
            }else {
                //流水号
                sql = "select yjxmlsh from uf_bmxmlshdjb where bm="+deptid;
                writeLog(sql);
                rs.execute(sql);
                if(rs.next()){
                    slh = rs.getInt(1);
                    writeLog("部门当前流水号："+slh);
                    slhStr = lshFormatChange(slh,3);
                    sql = "update uf_bmxmlshdjb set yjxmlsh=yjxmlsh+1 where bm="+deptid;
                    writeLog(sql);
                }else {
                    slhStr = lshFormatChange(slh,3);
                    sql = "insert into uf_bmxmlshdjb(bm,yjxmlsh) values("+deptid+","+(slh+1)+")";
                }
                if(slhStr==null||"".equals(slhStr)){
                    requestInfo.getRequestManager().setMessagecontent("流水号为空，一级项目编号生成失败!");
                    throw new Exception("流水号为空，一级项目编号生成失败!");
                }
                rs.execute(sql);
            }

            yjxmCode = year+type+deptCode+slhStr;
            writeLog("一级项目编号："+yjxmCode);
            sql = "update formtable_main_45 set xmbh='"+yjxmCode+"' where requestid="+requestid;
            writeLog(sql);
            rs.execute(sql);
            rs.execute("update workflow_requestbase set requestmark='"+yjxmCode+"' where requestId="+requestid);
            writeLog(sql);
            rs.execute(sql);

            rs.commit();
            return Action.SUCCESS;
        }catch (Exception e){
            rs.rollback();
            e.printStackTrace();
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    private String lshFormatChange(int i,int length){
        String lsh = String.valueOf(i);
        int iLength = lsh.length();
        int fillLength = length-iLength;

        if(fillLength>0){
            for (int j=0;j<fillLength;++j){
                lsh = "0"+lsh;
            }
        }
        return lsh;
    }
}
