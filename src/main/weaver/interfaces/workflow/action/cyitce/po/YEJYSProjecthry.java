package weaver.interfaces.workflow.action.cyitce.po;

import com.alibaba.fastjson.JSONObject;
import com.api.formmode.page.util.Util;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.manager.paasspace.PaasSpaceToken;
import weaver.interfaces.workflow.action.cyitce.util.WorkflowManage;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 一级项目预算审批状态修改
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-07  14:28
 * @Version: 1.0
 */
public class YEJYSProjecthry extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog("一级项目预算审批状态修改");

        User user = new User(WorkflowManage.getRequestCreater(requestInfo.getRequestid()));

        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String value;
        Map<String, String> result = new HashMap();

        for(int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            value = Util.null2String(properties[i].getValue());
            result.put(name, value);
        }

        HttpClient client;
        HttpPost post;
        String token;

        try {
            PaasSpaceToken paas = new PaasSpaceToken();
            token = paas.token(user.getMobile());

            writeLog("======获取 token =====:"+token);

            JSONObject obj1 = new JSONObject();
            obj1.put("id", result.get("jlid"));
            obj1.put("status", 0);

            writeLog("输入参数："+obj1.toJSONString());

            client = HttpClients.createDefault();
            post = new HttpPost("https://cyitdigital.cqcyit.com:1443/api/countf/record/oaReturn");
            post.addHeader("Content-Type", "application/json;charset=utf-8");
            post.addHeader("Authorization", token);
            StringEntity requestEntity = new StringEntity(obj1.toJSONString(), ContentType.APPLICATION_JSON);
            post.setEntity(requestEntity);
            HttpResponse response = client.execute(post);
            HttpEntity httpEntity = response.getEntity();
            String info = EntityUtils.toString(httpEntity);
            JSONObject jsonObject = JSONObject.parseObject(info);

            writeLog("返回参数："+jsonObject.toJSONString());

            if (response.getStatusLine().getStatusCode() != 200) {
                throw new Exception("录入预算系统失败！");
            } else if (!"SUCCESS".equals(jsonObject.getString("code"))) {
                throw new Exception("录入预算系统失败！msg：" + jsonObject.getString("msg"));
            } else {
                return Action.SUCCESS;
            }
        } catch (Exception var17) {
            requestInfo.getRequestManager().setMessage(var17.getMessage());
            writeLog("异常：" + var17);
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}