package weaver.interfaces.workflow.action.cyitce.po;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 发票状态变更
 */
public class InvoiceStatusUpdateAction extends BaseBean implements Action {
    public String status;

    private final static String PENDINGREVIEW_SET_URL = "https://supplier.cyitce.com/api/supplier-invoice/invoice/pendingReview";
    private final static String DOINGREVIEW_SET_URL = "https://supplier.cyitce.com/api/supplier-invoice/invoice/doingReview";
    private final static String DONEREVIEW_SET_URL = "https://supplier.cyitce.com/api/supplier-invoice/invoice/doneReview";

    @Override
    public String execute(RequestInfo requestInfo) {
        int actionstatus=0;

        JSONObject entity_json=new JSONObject();
        JSONArray array = new JSONArray();
        //取明细数据        
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        if (detailtable.length>0) {
            for (int i = 0; i < detailtable.length; i++) {
                DetailTable dt = detailtable[i];// 指定明细表               
                Row[] s = dt.getRow();// 当前明细表的所有数据,按行存储             
                for (int j = 0; j < s.length; j++) {
                    writeLog("InvoiceStatus------明细表1第"+j+"条数据:");
                    String invoiceNum=null;//发票号
                    Row r = s[j];// 指定行                    
                    Cell c[] = r.getCell();// 每行数据再按列存储      
                    for (int k = 0; k < c.length; k++) {
                        Cell c1 = c[k];// 指定列          
                        String name = c1.getName();// 明细字段名称  
                        writeLog("InvoiceStatus------名行表字段名:"+name);
                        if (name.equals("fphwb")) {//发票号
                            invoiceNum=c1.getValue().trim();
                            array.add(invoiceNum);
                            writeLog("InvoiceStatus------fph:"+invoiceNum);
                        }
                    }
                }
            }
        }
        entity_json.put("invoiceNoList", array);
        writeLog("invoiceNoList:"+entity_json.toString());
        writeLog("parms:status="+status);
        if("0".equals(status)){
            writeLog("status="+status);
            actionstatus = raise(entity_json.toJSONString(),PENDINGREVIEW_SET_URL);
        }else if("1".equals(status)){
            writeLog("status="+status);
            actionstatus = raise(entity_json.toJSONString(),DOINGREVIEW_SET_URL);
        }else if("2".equals(status)){
            writeLog("status="+status);
            actionstatus = raise(entity_json.toJSONString(),DONEREVIEW_SET_URL);
        }

        if(actionstatus==0){
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    /**
     *
     * @description 调用
     * @param entity_json 参数json格式
     * @return
     */
    private int raise(String entity_json,String url){
        writeLog("参数信息"+entity_json);
        writeLog("raise----进来了");
        HttpClient client=null;
        PostMethod post=null;
        Integer result=0;
        try {
            client=new HttpClient();
            post=new PostMethod(url);
            //添加参数
//            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            RequestEntity requestEntity = new StringRequestEntity(entity_json, "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("SupplierDealInfoAction----raise----code:"+post.getStatusCode());

            String body = new String(post.getResponseBody(),"UTF-8");
            writeLog("body: "+body);
            String success = JSONObject.parseObject(body).getString("success");
            writeLog("success: "+success);
            if(post.getStatusCode()==200&&"true".equals(success)){
                result = 1;
            }
        } catch (HttpException e) {
            e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            writeLog("raise---finally");
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }
}
