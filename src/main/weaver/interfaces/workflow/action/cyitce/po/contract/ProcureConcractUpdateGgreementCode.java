package weaver.interfaces.workflow.action.cyitce.po.contract;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 采购合同信息变更编号
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-11  14:34
 * @Version: 1.0
 */
public class ProcureConcractUpdateGgreementCode extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();

        RecordSet rs = new RecordSet();

        rs.execute("select * from "+table+" where requestid="+requestInfo.getRequestid());
        rs.next();

        if(!"".equals(Util.null2String(rs.getString("lcbh")))){
            writeLog("已经存在采购合同变更协议编号");
            return Action.SUCCESS;
        }

        try {
            String cght = Util.null2String(rs.getString("cghtmc"));
            if("".equals(cght)){
                requestInfo.getRequestManager().setMessagecontent("没有选择采购合同");
                throw new Exception("没有选择采购合同");
            }
            String cghtCode = Util.null2String(rs.getString("cghtbh"));
            if("".equals(cghtCode)){
                requestInfo.getRequestManager().setMessagecontent("采购合同编号为空");
                throw new Exception("采购合同编号字段为空");
            }

            rs.execute("select * from uf_cgddlsh where type=1 and cght="+cght);
            String lsh="1";
            if(rs.next()){
                lsh = Util.null2String(rs.getString("lsh"));
            }else {
                rs.execute("insert into uf_cgddlsh(cght,lsh) values("+cght+",1,"+lsh+")");
            }

            String orderCode = cghtCode+"-"+"BG"+numToString(lsh,4);
            writeLog("采购合同变更协议编号："+orderCode);

            //更新采购合同变更协议编号
            boolean boo = rs.execute("update "+table+" set lcbh='"+orderCode+"' where requestid="+requestInfo.getRequestid());

            if(!boo){
                requestInfo.getRequestManager().setMessagecontent("采购订单编号生成失败");
                throw new Exception("更新采购订单编号失败，orderCode："+orderCode);
            }

            //更改流程编号
            writeLog("更改流程编号sql："+"update workflow_requestbase set requestmark='"+orderCode+"' where requestId="+requestInfo.getRequestid());
            rs.execute("update workflow_requestbase set requestmark='"+orderCode+"' where requestId="+requestInfo.getRequestid());

            //流水号更新
            rs.execute("update uf_cgddlsh set lsh="+(Integer.valueOf(lsh)+1)+" where type=1 and cght="+cght);

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    public String numToString(String num,int len){
        int l = num.length();
        for (int i=0;i<len-l;++i){
            num = "0"+num;
        }
        return num;
    }
}