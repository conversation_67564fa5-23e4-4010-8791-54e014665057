package weaver.interfaces.workflow.action.cyitce.po.car;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 驾驶员注册校验
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-24  16:23
 * @Version: 1.0
 */
public class DriverRegisterCheck extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String jsy="-1";

        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p:property){
            if(p.getName().equals("jsrxm")){
                jsy=p.getValue();
                break;
            }
        }

        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("select jsrxm from uf_jsydjb where jsrxm=?",new Object[]{jsy});
            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("驾驶人已登记");
                return Action.FAILURE_AND_CONTINUE;
            }

            rs.executeQuery("select jsrxm from formtable_main_1877 a inner join workflow_currentoperator b on a.requestid=b.requestid where b.nownodetype in (1,2) and jsrxm=?",new Object[]{jsy});
            if (rs.next()){
                requestInfo.getRequestManager().setMessagecontent("驾驶人登记审核中");
                return Action.FAILURE_AND_CONTINUE;
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}