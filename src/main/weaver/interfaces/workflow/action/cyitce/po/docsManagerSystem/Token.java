package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.interfaces.workflow.action.cyitce.invoice.RSAUtils;

import java.security.PublicKey;

public class Token {

    private static String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnC72BNzcnNkSFPVDHzmzLP/5F2ffygKzS7owIfBBjdikvH3ddcfikpOq0o+DqtE5eMOTpOog6c699xFwKu3gHjxAXDUc0N+rYdteuBBZsDa2KTLGXInqgQn9+ah+lpPqwVnaR9eUiVbeSsPbqB59pbie/eTOSuswAATmeEQHKv0oFhORxczlhw21e0EwMyTcBLKguuTp/O/upawBj0Qxln693IK3MKUtonM98qnCH78YwixvN96NNiMqc3dOr2PBGSyUL7tuVltL9gGmlbVXF6eiunss2lka7/5n4MoXNoF1z4eKqnsG+wfXqdeRae9qBjM43e8V0qYBmn5ylfevuwIDAQAB";
    static String url = OutServiceUrl.FILE_Sys_Url+"/blade-auth/oauth/token";

    public static String getToken(String loginid,String name){
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            client = HttpClients.createDefault();
            builder = new URIBuilder(url);
            //获取公钥
            PublicKey publicKey = RSAUtils.getPublicKey(PUBLIC_KEY);
            String user = "{\"loginId\":\""+loginid+"\",\"timestamp\":\""+System.currentTimeMillis()+"\",\"name\":\""+name+"\"}";

            //RSA加密
            String secret = RSAUtils.encrypt(user,publicKey);
            builder.addParameter("tenantId","000000");
            builder.addParameter("grant_type","oa_secret");
            builder.addParameter("secret",secret);
            post = new HttpPost(builder.build());
            post.addHeader("Content-Type", "application/json");
            post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            response = client.execute(post);


            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }
            httpEntity = response.getEntity();


            return JSONObject.parseObject(EntityUtils.toString(httpEntity)).getString("access_token");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        System.out.println(getToken("ljp1212","Ljp19770226"));
    }
}
