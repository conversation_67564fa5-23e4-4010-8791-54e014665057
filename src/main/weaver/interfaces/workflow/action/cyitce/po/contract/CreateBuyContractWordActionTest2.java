package weaver.interfaces.workflow.action.cyitce.po.contract;

import com.alibaba.fastjson.JSONObject;
import com.caucho.xpath.expr.Var;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.BuyContract;
import weaver.interfaces.workflow.action.cyitce.doc.Action.LWWXToElectronDocAction;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.HttpRequestManage;
import weaver.interfaces.workflow.action.cyitce.service.buyContract.*;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.interfaces.workflow.action.cyitce.util.WorkflowManage;
import weaver.interfaces.workflow.action.cyitce.util.freemarker.FreemarkerCreateDocx;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.io.*;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @ClassName: CreateBuyContractWordAction2
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-09-26  15:26
 * @Version: 1.0
 */
public class CreateBuyContractWordActionTest2 extends BaseBean implements Action {
    // doc输出文件目录
    private String outUrl = BuyContract.outUrl + "/tempDoc/";
    //    // doc输出文件目录
    private String modeUrl = BuyContract.outUrl + "/template/";

    // OA文件上传接口
    private String outApiUrl = BuyContract.outApiUrl;
    RequestInfo requestInfo = null;

    private String wordIdName = "gzfj";
    private String yjxmmcId = "";
    private String yjxmmcName = "";
    private String lcbh = "";
    private static String slxzEmpty = "0";


    @Override
    public String execute(RequestInfo requestInfo) {
        ConverToChinesePart conver = new ConverToChinesePart();
        this.requestInfo = requestInfo;
        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p : property) {
            writeLog(p.getName() + ":" + p.getValue());
            if ("yjxmmc".equals(p.getName())) {
                yjxmmcId = p.getValue();
            }
            if ("htbh".equals(p.getName())) {
                writeLog("流程编号：" + p.getValue());
                lcbh = p.getValue() + "-";
            }
        }


        RecordSet rs = null;
        String htzl = "";
        FileOutputStream fos = null;

        ContractFileMerg cfm = new ContractFileMerg(outUrl + "merg/", outUrl + "temp/");

        try {

            if (!StringUtils.hasText(yjxmmcId)) {
                yjxmmcName = "默认-";
            } else {
                RecordSet recordSet = new RecordSet();
                recordSet.execute("select xmmc  from uf_yjxmlxjm where id=" + yjxmmcId);
                yjxmmcName = Util.null2String(recordSet.getString("xmmc"));
                if (StringUtils.hasText(yjxmmcName)) {
                    yjxmmcName = yjxmmcName + "-";
                } else {
                    yjxmmcName = "默认-";
                }
            }

            lcbh = yjxmmcName + lcbh;

            rs = new RecordSet();
            rs.execute("select htzl,htxs,yjxmmc from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid());
            rs.next();

            //纸质合同
            if ("".equals(Util.null2String(rs.getString(1)))|| "0".equals(Util.null2String(rs.getString(2)))) {
                return Action.SUCCESS;
            }

            // 选择合同模板
            // 生成临时的word
            htzl = Util.null2String(rs.getString(1));

            switch (htzl) {
                case "0":
                    Contract1 co1 = new Contract1();
                    if ("0".equals(co1.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    writeLog("车辆租聘");
                    String zlmsjzffs = co1.getZlmsjzffs();

                    writeLog("车辆租聘 zlmsjzffs " + zlmsjzffs);

                    int tag = 0;

                    if ("1".equals(zlmsjzffs)) {
                        String bbgsfhs = co1.getBgsfhs();
                        writeLog("bbgsfhs : " + bbgsfhs);
                        if ("含税价".equals(bbgsfhs)) {
                            tag = 0;
                        } else if ("不含税价".equals(bbgsfhs)) {
                            tag = 1;
                        } else {
                            requestInfo.getRequestManager().setMessage("请选择是否含税！");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        // 第一种
                        writeLog("包干模式   quantity: " + co1.getZlqxdwy() + ", unitStr: " + co1.getBgms() + ", amountStr: " + co1.getBgje() + ", taxRateStr: " + co1.getBgjhssl() + ", taxIndicator: " + tag);
                        String s = calculateVAT1(co1.getZlqxdwy(), co1.getBgms(), co1.getBgje(), co1.getBgjhssl(), tag);
                        if (StringUtils.hasText(s)) {
                            co1.setBgzzsj(s);
                        } else {
                            requestInfo.getRequestManager().setMessage("增值税金计算错误！");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                    } else if ("2".equals(zlmsjzffs)) {
                        String bbgsfhs = co1.getBbgsfhs();
                        if ("含税价".equals(bbgsfhs)) {
                            tag = 0;
                        } else if ("不含税价".equals(bbgsfhs)) {
                            tag = 1;
                        } else {
                            requestInfo.getRequestManager().setMessage("请选择是否含税！");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        writeLog("co1.getBbgsfhs()");
                        writeLog("不包干模式   quantity: " + co1.getZlqxdwy() + ", unitStr: " + co1.getBbgms() + ", amountStr: " + co1.getBbgmsjbzj() + ", taxRateStr: " + co1.getBbgjhssl() + ", taxIndicator: " + tag + ", driverFeeStr: " + co1.getJsyrgf() + ", driverUnitStr: " + co1.getJsyfwfms() + ", jsy: " + co1.getSfdjsy() + ", cdf: " + co1.getBbgmsqtfycdr());
                        String s = calculateVATWithDriverFee(co1.getZlqxdwy(), co1.getBbgms(), co1.getBbgmsjbzj(), co1.getBbgjhssl(), tag, co1.getJsyrgf(), co1.getJsyfwfms(), co1.getSfdjsy(), co1.getBbgmsqtfycdr());
                        if (StringUtils.hasText(s)) {
                            writeLog("金额: s: " + s);
                            co1.setBbgzzsj(s);
                        } else {
                            requestInfo.getRequestManager().setMessage("增值税金计算错误！");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                    }
                    writeLog("车辆租聘 zlmsjzffs2 " + zlmsjzffs);
                    // co1.setBgzzsj("123");
                    // co1.setBbgzzsj("456");
                    executMode("clzpht.zip", lcbh + "车辆租赁合同.docx", ModeTestUrl.CLZPHT_URL, co1.getImageInfoList(), co1, wordIdName);
                    break;
                case "1":
                    Contract2 co2 = new Contract2();
                    if ("0".equals(co2.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    Map<String, String> map = processContract(co2.getHsjg(), co2.getSlxz());
                    String v = calculateDifference(co2.getHsjg(), co2.getBhsjg());
                    co2.setZzsj(v);
                    co2.setDxzzsj(conver.convertToChinese(v));
                    executMode("fwzpht.zip", lcbh + "房屋租赁合同.docx", ModeTestUrl.FWZPHT_URL, co2.getImageInfoList(), co2, wordIdName);
                    break;
                case "2":
                    Contract3 co3 = new Contract3();
                    if ("0".equals(co3.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("lwfbht.zip", lcbh + "劳务分包合同.docx", ModeTestUrl.LWFBHT_URL, co3.getImageInfoList(), co3, wordIdName);
                    executMode("fj1.zip", "关于规范发票行为及收款账户的不可撤销承诺函.docx", ModeTestUrl.LWFBHT_FJ1_URL, co3, "zlfwcqz");
                    executMode("fj2.zip", "项目安全生产与文明施工管理承诺书.docx", ModeTestUrl.LWFBHT_FJ2_URL, co3, "czrsfzyyzz");
                    executMode("fj3.zip", "廉洁诚信承诺书.docx", ModeTestUrl.LWFBHT_FJ3_URL, co3, "fjs");
                    executMode("fj3d1.zip", "有关联人员认证声明书.docx", ModeTestUrl.LWFBHT_FJ3d1_URL, co3, "fjs");
                    executMode("fj3d2.zip", "无关联人员认证声明书.docx", ModeTestUrl.LWFBHT_FJ3d2_URL, co3, "fjs");
                    executMode("fj4.zip", "不拖欠民工工资承诺书.docx", ModeTestUrl.LWFBHT_FJ4_URL, co3, "fjsi");
                    executMode("fj13.zip", "机具、辅材、低值易耗品范围.docx", ModeTestUrl.LWFBHT_FJ13_URL, co3, "fjshis");
                    break;
                case "3":
                    Contract4 co4 = new Contract4();
                    if ("0".equals(co4.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    writeLog("Contract4   " + co4.getHsjg() + " " + co4.getSlxz());
                    String v3 = calculateDifference(co4.getHsjg(), co4.getBhsjg());
                    co4.setZzsj(v3);
                    co4.setDxzzsj(conver.convertToChinese(v3));
                    executMode("clgxht.zip", lcbh + "材料购销合同.docx", ModeTestUrl.CLGXHT_URL, co4.getImageInfoList(), co4, wordIdName);
                    break;
                case "4":
                    Contract5 co5 = new Contract5();
                    if ("0".equals(co5.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    executMode("jsxmfwkjht.zip", lcbh + "技术项目服务框架合同.docx", ModeTestUrl.JSXMFWKJHT_URL, co5.getImageInfoList(), co5, wordIdName);
                    break;
                case "5":
                    Contract4 co6 = new Contract4();
                    if ("0".equals(co6.execute(requestInfo))) {
                        throw new Exception("contract 类赋值处理失败！");
                    }
                    writeLog("Contract4   " + co6.getHsjg() + " " + co6.getSlxz());
                    String v6 = calculateDifference(co6.getHsjg(), co6.getBhsjg());
                    co6.setZzsj(v6);
                    co6.setDxzzsj(conver.convertToChinese(v6));
                    executMode("sbgxht.zip", lcbh + "设备购销合同.docx", ModeTestUrl.SBGXHT_URL, new ArrayList<>(), co6, wordIdName);
                    break;
                case "6":
                    LWWXToElectronDocAction lwwx = new LWWXToElectronDocAction();

                    String res = lwwx.execute(requestInfo);

                    if ("0".equals(res)) {
                        requestInfo.getRequestManager().setMessage("采购合同生成失败！");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                    break;
            }

            cfm.delete();
            return Action.SUCCESS;
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            writeLog(sw.toString());
            writeLog(e.getMessage());
        } finally {
            try {
                cfm.delete();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }

        return Action.FAILURE_AND_CONTINUE;
    }

    public static Map<String, String> processContract(String hsjg, String slxz) {
        Map<String, String> resultMap = new HashMap<>();
        if (!slxzEmpty.equals(slxz)) {
            String s1 = calculateVATTax(hsjg, slxz);
            resultMap.put("zzsj", calculateVATTax(hsjg, slxz));
            resultMap.put("dxzzsj", new ConverToChinesePart().convertToChinese(s1));
            return resultMap;
        } else {
            resultMap.put("zzsj", "0");
            resultMap.put("dxzzsj", "零元整");
            return resultMap;
        }
    }

    /**
     * @param requestInfo
     * @return java.lang.String
     * @Description: 上传附件接口参数
     * @Author: lijianpan
     * @date: 2022/6/27 15:19
     */
    private String getUploadBody(RequestInfo requestInfo) {
        // 上传附件接口参数设置
        JSONObject entity_json = new JSONObject();
        entity_json.put("category", WorkflowManage.getDocPath(requestInfo.getRequestManager().getFormid()));// 存放目录
        entity_json.put("isFirstUploadFile", "9");// 附件状态
        entity_json.put("workflowid", requestInfo.getRequestManager().getWorkflowid());// 流程workflowid
        entity_json.put("listType", "list");
        entity_json.put("f_weaver_belongto_userid", requestInfo.getCreatorid());// 附件归属人
        entity_json.put("f_weaver_belongto_usertype", "0");
        return entity_json.toJSONString();
    }

    private void executMode(String z, String o, String t, List<ImageInfo> im, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(outUrl + o)), im, t, z);
        upload(o, outUrl + o, fieldName);
    }

    private String executMode(String z, String o, String t, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(outUrl + o)), new ArrayList<>(), t, z);
        upload(o, outUrl + o, fieldName);
        return outUrl + o;
    }

    private String executMode2(String z, String path, String o, String t, Object c) throws Exception {
        writeLog("电子合同路径：" + path + o);
        CommonUtil.setPropertyValue(c);
        writeLog("电子合同路径2：" + path + o);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(path + o)), new ArrayList<>(), t, z);
        writeLog("电子合同路径3：" + path + o);
        return path + o;
    }

    private String executMode2(String z, String path, String o, String t, Object c, String fieldName) throws Exception {
        CommonUtil.setPropertyValue(c);
        FreemarkerCreateDocx.createDocx(c, new FileOutputStream(new File(path + o)), new ArrayList<>(), t, z);
        upload2(o, path + o, fieldName);
        return path + o;
    }

    private void upload(String o, String filePath, String fieldName) throws Exception {
        HttpRequestManage http = new HttpRequestManage();
        String wordId = http.raise(o, filePath, outApiUrl, getUploadBody(requestInfo));
        File file = new File(filePath);
        if (wordId == null || "".equals(wordId)) {
            file.delete();
            requestInfo.getRequestManager().setMessagecontent("生成的word上传失败！");
            throw new Exception("生成的word上传失败！");
        }
        writeLog("附件准备删除");
        // 删除临时的word
        file.delete();

        writeLog("电子附件字段更新");
        // 返回文档id到流程中
        RecordSet rs = new RecordSet();
        String sql = "select " + fieldName + " from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid();
        rs.execute(sql);
        rs.next();
        if ("".equals(Util.null2String(rs.getString(1)))) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        } else if (!wordIdName.equals(fieldName)) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=concat(" + fieldName + ",',','" + wordId + "') where requestid=" + requestInfo.getRequestid();
        } else {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        }
        rs.execute(sql);
    }

    private void upload2(String o, String filePath, String fieldName) throws Exception {
        HttpRequestManage http = new HttpRequestManage();
        String wordId = http.raise(o, filePath, outApiUrl, getUploadBody(requestInfo));
        File file = new File(filePath);
        if (wordId == null || "".equals(wordId)) {
            file.delete();
            requestInfo.getRequestManager().setMessagecontent("生成的word上传失败！");
            throw new Exception("生成的word上传失败！");
        }

        writeLog("电子附件字段更新");
        // 返回文档id到流程中
        RecordSet rs = new RecordSet();
        String sql = "select " + fieldName + " from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=" + requestInfo.getRequestid();
        rs.execute(sql);
        rs.next();
        if ("".equals(Util.null2String(rs.getString(1)))) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        } else if (!wordIdName.equals(fieldName)) {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=concat(" + fieldName + ",',','" + wordId + "') where requestid=" + requestInfo.getRequestid();
        } else {
            sql = "update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "='" + wordId + "' where requestid=" + requestInfo.getRequestid();
        }
        rs.execute(sql);
    }

    private boolean updateFile(String fieldName) {
        RecordSet rs = new RecordSet();
        boolean boo = rs.execute("update " + requestInfo.getRequestManager().getBillTableName() + " set " + fieldName + "=null where requestid=" + requestInfo.getRequestid());
        return boo;
    }


    public static String calculateVATTax(String includedTaxPrice, String taxRateStr) {
        try {
            double taxRate = Double.parseDouble(taxRateStr);
            if (taxRate == 0) {
                return "0.00";
            }
            double price = Double.parseDouble(includedTaxPrice);
            double vatTax = price / (1 + taxRate) * taxRate;

            // 使用DecimalFormat保留两位小数
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(vatTax);

        } catch (NumberFormatException e) {
            // 若输入的价格或税率格式有误，打印错误信息并返回 0.0
            System.err.println("输入的价格或税率格式不正确，请检查输入。");
            return null;
        }

    }

    public static double calculateSalary(LocalDate startDate, LocalDate endDate, double monthlySalary) {
        double total = 0;

        // 如果开始日期和结束日期在同一个月
        if (startDate.getYear() == endDate.getYear() && startDate.getMonth() == endDate.getMonth()) {
            YearMonth yearMonth = YearMonth.from(startDate);
            int daysInMonth = yearMonth.lengthOfMonth();
            double dailySalary = monthlySalary / daysInMonth;
            long daysWorked = ChronoUnit.DAYS.between(startDate, endDate) + 1;
            total = dailySalary * daysWorked;
        } else {
            // 开始日期所在月的处理
            YearMonth startYearMonth = YearMonth.from(startDate);
            int startDaysInMonth = startYearMonth.lengthOfMonth();
            double startDailySalary = monthlySalary / startDaysInMonth;
            int startDaysWorked = startDaysInMonth - startDate.getDayOfMonth() + 1;
            total += startDailySalary * startDaysWorked;

            // 中间月份的处理
            LocalDate currentDate = startDate.plusMonths(1).withDayOfMonth(1);
            while (currentDate.getYear() < endDate.getYear() || (currentDate.getYear() == endDate.getYear() && currentDate.getMonth().compareTo(endDate.getMonth()) < 0)) {
                total += monthlySalary;
                currentDate = currentDate.plusMonths(1);
            }

            // 结束日期所在月的处理
            YearMonth endYearMonth = YearMonth.from(endDate);
            int endDaysInMonth = endYearMonth.lengthOfMonth();
            double endDailySalary = monthlySalary / endDaysInMonth;
            int endDaysWorked = endDate.getDayOfMonth();
            total += endDailySalary * endDaysWorked;
        }
        return total;
    }

    /**
     * 计算指定日期范围内的增值税税金
     *
     * @param startDateStr 开始日期字符串，格式为 "yyyy-MM-dd"
     * @param endDateStr   结束日期字符串，格式为 "yyyy-MM-dd"
     * @param dailyWageStr 每日工资字符串，支持整数或两位小数形式，如 "600" 或 "60.50"
     * @param taxRateStr   税率字符串，格式为 "X%"，例如 "6%"
     * @param taxIndicator 含税标识，0 代表工资不含税，1 代表工资含税 zlqxdwy
     * @return 计算得到的增值税税金，以字符串形式返回，保留两位小数并带上单位“元”；若输入参数有误，返回 null
     */
    public static String calculateVAT(String startDateStr, String endDateStr, String dailyWageStr, String taxRateStr, int taxIndicator) {
        double dailyWage;
        try {
            // 将每日工资字符串转换为 double 类型
            dailyWage = Double.parseDouble(dailyWageStr);
            // 简单验证每日工资是否为负数
            if (dailyWage < 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            // 若每日工资字符串格式错误，记录错误日志
            return null;
        }

        // 创建日期格式化对象，用于将日期字符串转换为日期对象
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 检查是否为多税率或者 0% 税率
            if (taxRateStr.contains("多税率") || "0%".equals(taxRateStr)) {
                return "0.00";
            }

            // 将输入的开始日期字符串转换为 Date 对象
            Date startDate = sdf.parse(startDateStr);
            // 将输入的结束日期字符串转换为 Date 对象
            Date endDate = sdf.parse(endDateStr);
            // 计算两个日期之间的时间差（毫秒）
            long diff = endDate.getTime() - startDate.getTime();
            // 将时间差转换为天数，并加 1 以包含起始和结束日期
            int totalDays = (int) (diff / (1000 * 60 * 60 * 24)) + 1;
            // 根据每日工资和总天数计算总金额
            double totalAmount = dailyWage * totalDays;

            // 处理税率字符串，去掉百分号并转换为小数
            double taxRate = Double.parseDouble(taxRateStr.replace("%", "")) / 100;

            double vat;
            if (taxIndicator == 0) { // 工资不含税的情况
                // 增值税税金 = 不含税金额 * 税率
                vat = totalAmount * taxRate;
            } else if (taxIndicator == 1) { // 工资含税的情况
                // 增值税税金 = 含税价 / (1 + 税率) * 税率
                vat = totalAmount / (1 + taxRate) * taxRate;
            } else {
                // 若含税标识输入无效，记录错误日志
                return null;
            }

            // 创建 DecimalFormat 对象，用于将结果保留两位小数
            DecimalFormat df = new DecimalFormat("0.00");
            // 将计算结果格式化为保留两位小数的字符串，并添加货币单位
            return df.format(vat) + " 元";
        } catch (ParseException e) {
            // 若日期字符串格式错误，记录错误日志
            return null;
        } catch (NumberFormatException e) {
            // 若税率字符串格式错误，记录错误日志
            return null;
        }
    }

    /**
     * 计算增值税税金
     *
     * @param quantity     数量，例如工作天数或月数
     * @param unit         单位，"天" 或 "月"
     * @param amountStr    金额字符串，支持整数或两位小数形式，如 "600" 或 "60.50"
     * @param taxRateStr   税率字符串，格式为 "X%"，例如 "6%"
     * @param taxIndicator 含税标识，0 代表金额不含税，1 代表金额含税
     * @return 计算得到的增值税税金，以字符串形式返回，保留两位小数并带上单位“元”；若输入参数有误，返回相应的错误提示信息
     */
    public static String calculateVAT1(String quantity, String unit, String amountStr, String taxRateStr, int taxIndicator) {
        double parsedQuantity;
        try {
            parsedQuantity = Double.parseDouble(quantity);
            if (parsedQuantity <= 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        double amount;
        try {
            amount = Double.parseDouble(amountStr);
            if (amount < 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        if (!"0".equals(unit) && !"1".equals(unit)) {
            return null;
        }

        try {
            if (taxRateStr.contains("多税率") || "0%".equals(taxRateStr)) {
                return "0.00";
            }

            double taxRate = Double.parseDouble(taxRateStr.replace("%", "")) / 100;

            double totalAmount;
            if ("0".equals(unit)) {
                totalAmount = amount * parsedQuantity * 30;
            } else {
                totalAmount = amount * parsedQuantity;
            }

            double vat;
            if (taxIndicator == 1) {
                vat = totalAmount * taxRate;
            } else if (taxIndicator == 0) {
                vat = totalAmount / (1 + taxRate) * taxRate;
            } else {
                return null;
            }

            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(vat);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 计算包含驾驶员费用的增值税金
     *
     * @param quantity      业务数量，需为大于 0 的数字字符串，例如工作月数
     * @param unitStr       业务时间单位，"0" 代表天，"1" 代表月
     * @param amountStr     业务金额，需为有效的数字字符串，且不能为负数
     * @param taxRateStr    税率，需为 "X%" 格式的字符串
     * @param taxIndicator  含税标识，0 代表金额不含税，1 代表金额含税
     * @param driverFeeStr  驾驶员服务费，需为有效的数字字符串，且不能为负数
     * @param driverUnitStr 驾驶员服务时间单位，"0" 代表天，"1" 代表月
     * @param jsy           是否计算驾驶员费用的判断参数之一，"0" 表示参与判断计算
     * @param cdf           是否计算驾驶员费用的判断参数之一，"0" 表示参与判断计算
     * @return 计算得到的增值税金，格式为保留两位小数的字符串；若输入参数有误，返回 null
     */
    public static String calculateVATWithDriverFee(String quantity, String unitStr, String amountStr, String taxRateStr, int taxIndicator, String driverFeeStr, String driverUnitStr, String jsy, String cdf) {
        // 先判断是否需要计算驾驶员费用
        if (!"0".equals(jsy) || !"0".equals(cdf)) {
            return calculateVAT1(quantity, unitStr, amountStr, taxRateStr, taxIndicator);
        }

        double parsedQuantity;
        try {
            parsedQuantity = Double.parseDouble(quantity);
            if (parsedQuantity <= 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        double amount;
        try {
            amount = Double.parseDouble(amountStr);
            if (amount < 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        String unit;
        if ("0".equals(unitStr)) {
            unit = "天";
        } else if ("1".equals(unitStr)) {
            unit = "月";
        } else {
            return null;
        }

        double driverFee;
        try {
            driverFee = Double.parseDouble(driverFeeStr);
            if (driverFee < 0) {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        String driverUnit;
        if ("0".equals(driverUnitStr)) {
            driverUnit = "天";
        } else if ("1".equals(driverUnitStr)) {
            driverUnit = "月";
        } else {
            return null;
        }

        try {
            if (taxRateStr.contains("多税率") || "0%".equals(taxRateStr)) {
                return "0.00";
            }

            double taxRate = Double.parseDouble(taxRateStr.replace("%", "")) / 100;

            double totalAmount;
            if ("天".equals(unit)) {
                totalAmount = amount * parsedQuantity * 30;
            } else {
                totalAmount = amount * parsedQuantity;
            }

            // 计算驾驶员服务费对应的金额
            double driverTotalFee;
            if ("天".equals(driverUnit)) {
                driverTotalFee = driverFee * parsedQuantity * 30;
            } else {
                driverTotalFee = driverFee * parsedQuantity;
            }

            // 总价加上驾驶员服务费
            totalAmount += driverTotalFee;

            double vat;
            if (taxIndicator == 1) {
                vat = totalAmount * taxRate;
            } else if (taxIndicator == 0) {
                vat = totalAmount / (1 + taxRate) * taxRate;
            } else {
                return null;
            }

            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(vat);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static void main(String[] args) {
        // String dateStr1 = "2025-03-13";
        // String dateStr2 = "2025-04-13";
        // double monthlySalary = 7000;
        // // 包干模式 0 天 1 月
        // // 包干是否含税0 含1不含
        //
        // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // LocalDate startDate = LocalDate.parse(dateStr1, formatter);
        // LocalDate endDate = LocalDate.parse(dateStr2, formatter);
        //
        // double totalSalary = calculateSalary(startDate, endDate, monthlySalary);
        // // 使用 DecimalFormat 格式化工资结果
        // DecimalFormat df = new DecimalFormat("#.00");
        // String formattedSalary = df.format(totalSalary);
        // System.out.println("应得工资为: " + formattedSalary + " 元");


        // String vat = calculateVAT(dateStr1, dateStr2, "100", "1%", 0);
        // String vat1 = calculateVAT(dateStr1, dateStr2, "100.50", "3%", 1);
        // String vat1 = calculateVAT1("1", "1", "10050", "6%", 1);
        // String vat2 = calculateVAT1("1", "1", "10050", "6%", 1);
        // String vat23 = calculateVATWithDriverFee("1", "1", "10050", "6%", 1, "1000", "1", "0", "0");
        // String vat233 = calculateVATWithDriverFee("1", "1", "10050", "6%", 1, "1000", "0", "0", "0");
        // String vat2334 = calculateVATWithDriverFee("1", "1", "10050", "6%", 1, "1000", "0", "1", "");
        // String vat2335 = calculateVATWithDriverFee("1", "1", "10050", "6%", 1, "1000", "0", "1", "");
        // System.out.println("vat = " + vat1);
        // System.out.println("vat1 = " + vat2);
        // System.out.println("vat23 = " + vat23);
        // System.out.println("vat233 = " + vat233);
        // System.out.println("vat233 = " + vat2334);
        // System.out.println("vat233 = " + vat2335);
        // String s = calculateVATWithDriverFee("1", "1", "10050", "6%", 0, "1000", "1", "0", "0");
        // System.out.println("s = " + s);
        String s = calculateVAT1("0", "0", "1", "1%", 0);
        System.out.println("s = " + s);
    }

    /**
     * 计算含税金额和不含税金额的差值，并将结果以保留两位小数的字符串形式返回。
     * 若差值为负数，则返回 "0.00"。
     *
     * @param h 含税金额，字符串类型
     * @param y 不含税金额，字符串类型
     * @return 差值，保留两位小数的字符串，若差值为负则返回 "0.00"
     */
    public static String calculateDifference(String h, String y) {
        try {
            // 将字符串转换为 double 类型
            double taxedAmount = Double.parseDouble(h);
            double untaxedAmount = Double.parseDouble(y);

            // 计算差值
            double difference = taxedAmount - untaxedAmount;

            // 若差值为负数，将差值设为 0
            if (difference < 0) {
                difference = 0;
            }

            // 保留两位小数
            difference = (double) Math.round(difference * 100) / 100;

            // 将结果转换为字符串
            return String.format("%.2f", difference);
        } catch (NumberFormatException e) {
            return "0.00";
        }
    }


    public static void main1(String[] args) {
        // String s = calculateVATTax("1", "0.01");
        // System.out.println("s = " + s);
        // ConverToChinesePart conver = new ConverToChinesePart();
        // String s1 = conver.convertToChinese("500.19");
        // System.out.println("s1 = " + s1);
        System.out.println(calculateDifference("500.19666", "499.99"));
    }

}