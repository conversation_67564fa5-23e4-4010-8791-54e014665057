package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.service.SendEmail;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.soa.workflow.request.RequestInfo;

import java.util.*;

/**
 * @ClassName: 发送邮件
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-03  16:07
 * @Version: 1.0
 */
public class sendEmailAction extends BaseBean implements Action {
    public String spm;//项目经理
    public String zb;//质量安全部人员
    public String proBm;//项目归属部门

    @Override
    public String execute(RequestInfo requestInfo) {
        String tableNmae = requestInfo.getRequestManager().getBillTableName();
        String requestid = requestInfo.getRequestid();

        RecordSet rs = new RecordSet();
        String sub = "数藤平台流程信息";//邮件标题
        List<Map<String,Object>> mails = new ArrayList<>();//收件人邮箱
        String lcbh = "";//流程编号
        String sqr = "";//申请人
        String sj = "";//申请日期
        StringBuffer content = new StringBuffer("");//内容

        writeLog("=======开始发送邮件=========");

        try {
            String sql = "select * from "+tableNmae+" where requestid="+requestid;
            rs.execute(sql);
            if (rs.next()){
                lcbh = Util.null2String(rs.getString("lcbh"));
                spm = Util.null2String(rs.getString("spm"));
                zb = Util.null2String(rs.getString("zlaqbry"));
                proBm = Util.null2String(rs.getString("xmgzbm"));
                sqr = new User(rs.getInt("jlr")).getLastname();
                sj = Util.null2String(rs.getString("jlsj"));
            }

            sql = "select * from "+tableNmae+"_dt1 where mainid="+requestInfo.getRequestManager().getBillid();
            rs.execute(sql);
            while (rs.next()){
                if(!"6".equals(Util.null2String(rs.getString("mxys")))){
                    Map<String,Object> map = new HashMap<>();
                    map.put("mail",receivers(CommonUtil.parseFloatAndInit(rs.getString("dxdfzb"))));
                    map.put("khpjyd",Util.null2String(rs.getString("khpjyd")));
                    map.put("dxzfz",Util.null2String(rs.getString("dxzfz")));
                    map.put("sjkhdf",Util.null2String(rs.getString("sjkhdf")));
                    map.put("dxdfzb",Util.null2String(rs.getString("dxdfzb")));
                    mails.add(map);
                    writeLog("====== sendEmail dateil map:"+map.toString());
                }
            }

            SendEmail se = new SendEmail();

            for (int i=0;i<mails.size();++i){
                List<String> mailsDetail = (List)mails.get(i).get("mail");
                writeLog("===== sendEmail mails size:"+mailsDetail.size());
                for (int j=0;j<mailsDetail.size();++j){
                    writeLog("===== sendEmail mails:"+mailsDetail.get(j));
                    se.setInternetAddress("CYIT",sub,"<EMAIL>",mailsDetail.get(j));
                    content.append("<h1>客户满意度数据录入流程-"+sqr+"-"+sj+"</h1>"+
                            "<p>流程编号："+lcbh+"</p>\n" +
                            "<p>-------------内容----------------</p>\n" +
                            "<p>考核评价要点："+mails.get(i).get("khpjyd")+"</p>\n" +
                            "<p>单项总分值："+mails.get(i).get("dxzfz")+"</p>\n" +
                            "<p>实际考核得分："+mails.get(i).get("sjkhdf")+"</p>\n" +
                            "<p>单项得分占比："+String.format("%.2f",CommonUtil.parseFloatAndInit(Util.null2String(mails.get(i).get("dxdfzb")))*100)+"%</p>");
;                   se.setContent(content.toString());
                    se.send();
                }
            }

            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            writeLog("=====  sendEmail error:"+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    //推送邮箱
    public List<String> receivers(Float f){
        RecordSet rs = new RecordSet();
        String sql;
        List<String> mails = new ArrayList<>();
        String mail;

        if(f>=0.85&&f<0.9){
            mail = getUserEmail(spm);
            if(!"".equals(mail)&&!mails.contains(mail)){
                mails.add(mail);
            }
        }else if(f>=0.75&&f<0.85){
            mail = getUserEmail(zb);
            if(!"".equals(mail)&&!mails.contains(mail)){
                mails.add(mail);
            }

            rs.execute("select sjz,sjf from Matrixtable_9 where bm="+proBm);
            while (rs.next()){
                setMails(mails,rs,2);
            }
        }else if(f<0.75){
            rs.execute("select yjz,yjf,ejz,ejf from Matrixtable_9 where bm="+proBm);
            while (rs.next()){
                setMails(mails,rs,4);
            }
        }
        return mails;
    }

    public void setMails(List<String> list,RecordSet rs,int i){
        Set<String> set = new HashSet<>();
        for(int x=1;x<=i;++x){
            String mail = getUserEmail(rs.getString(x));
            if(!"".equals(mail)&&!set.contains(mail)){
                set.add(mail);
                list.add(mail);
            }
        }
    }

    //获取人员电子邮箱
    public String getUserEmail(String userId){
        writeLog("userId : "+userId);

        if("".equals(Util.null2String(userId))){
            writeLog("userId is 空字符串");
            return "";
        }

        String mail = "";
        RecordSet rs = new RecordSet();

        rs.execute("select email from hrmresource where id="+userId);
        if(rs.next()){
            return Util.null2String(rs.getString(1));
        }

        rs.execute("select field47 from cus_fielddata where id="+userId);
        while (rs.next()){
            return Util.null2String(rs.getString(1));
        }

        return mail;
    }
}