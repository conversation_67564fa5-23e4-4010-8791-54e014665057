package weaver.interfaces.workflow.action.cyitce.po;

import weaver.conn.RecordSetTrans;
import weaver.interfaces.workflow.action.Action;
import weaver.general.BaseBean;
import weaver.soa.workflow.request.RequestInfo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


/**
 * @ClassName: OaAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-12-19  11:04
 * @Version: 1.0
 */
public class Action202312191216 extends BaseBean implements Action {
    public String nodetype;

    /**
     * 流程路径节点后选择aciton后,会在节点提交后执行此方法。
     */
    @Override
    public String execute(RequestInfo request) {
        String tableName = request.getRequestManager().getBillTableName();
        String requestid = request.getRequestid();
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);

        Calendar now = Calendar.getInstance();
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormatter.format(now.getTime());

        SimpleDateFormat timeFormatter = new SimpleDateFormat("hh:mm");
        String currentTime = timeFormatter.format(now.getTime());

        try {
            if("0".equals(nodetype)){
                rst.execute("update "+tableName+" set lctjrq='"+currentDate+"',lctjsj='"+currentTime+"' where requestid='"+requestid+"'");
            }else if ("3".equals(nodetype)) {
                rst.execute("update "+tableName+" set lcgdrq='"+currentDate+"',lcgdsj='"+currentTime+"' where requestid='"+requestid+"'");
            }else {
                return Action.SUCCESS;
            }

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            request.getRequestManager().setMessageid("90001");
            request.getRequestManager().setMessagecontent("系统异常终止流程提交："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }
}