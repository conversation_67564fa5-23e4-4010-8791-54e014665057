package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 修改文件管理系统一级项目名称
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-06  15:12
 * @Version: 1.0
 */
public class oneProjectNameUpdate extends BaseBean implements Action {
    private String url = OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/modifyName";

    public String projectCodeFieldName;
    public String newProjectNameFieldName;
    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String projectCode = "";
        String newProjectName = "";
        String error = "修改文件管理系统一级项目名称失败";

        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response = null;
        try {
            int ret = 0;
            for(int i = 0; i < properties.length; ++i) {
                if (properties[i].getName().equals(projectCodeFieldName)) {
                    projectCode = properties[i].getValue();
                    ret++;
                }else if(properties[i].getName().equals(newProjectNameFieldName)){
                    newProjectName = properties[i].getValue();
                    ret++;
                }else if (ret==2){
                    break;
                }
            }

            if("".equals(projectCode)){
                return Action.SUCCESS;
            }

            if("".equals(newProjectName)){
                return Action.SUCCESS;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("projectName",newProjectName);
            jsonObject.put("projectCode",projectCode);

            client = HttpClients.createDefault();
            post = new HttpPost(url);
            post.addHeader("Tenant-Id","000000");
            post.addHeader("Blade-Auth", Token.getToken(user.getLoginid(),user.getLastname()));
            post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            StringEntity entity1 = new StringEntity(jsonObject.toString(),"UTF-8");
            entity1.setContentEncoding("UTF-8");
            entity1.setContentType("application/json");
            post.setEntity(entity1);
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode()!=200){
                error = "修改文件管理系统一级项目名称失败,httpCode:"+response.getStatusLine().getStatusCode();
                throw new Exception(error);
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(error);
            e.getMessage();
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}