package weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.general.Util;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.config.OutServiceUrl;
import weaver.soa.workflow.request.RequestInfo;

/**
 * @ClassName: 当前流程文件的二级项目名称更新
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-24  14:39
 * @Version: 1.0
 */
public class twoProjectNameUpdate extends BaseBean implements Action {
    String url= OutServiceUrl.FILE_Sys_Url+"/file-system/file_center/editFileInfo";

    @Override
    public String execute(RequestInfo requestInfo) {
        String loginId;
        String lastName;
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();

        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response = null;
        try {
            JSONArray array = new JSONArray();
            rs.execute("select loginid,lastname from hrmresource where lastname='"+requestInfo.getCreatorid()+"'");
            rs.next();
            loginId = Util.null2String(rs.getString(1));
            lastName = Util.null2String(rs.getString(2));
            rs.execute("select * from formtable_main_100 where id="+requestInfo.getRequestManager().getBillid());
            rs.next();
            String ejxmmc = Util.null2String(rs.getString("ejxmnbmc"));
            rs2.execute("SELECT fieldname FROM workflow_billfield where billid="+requestInfo.getRequestManager().getFormid()+" and fieldname like 'CoustomerFile%' and fieldhtmltype=1 and type=1");
            String ids = "";
            while (rs2.next()) {
                String id = Util.null2String(rs.getString(rs2.getString(1)));
                if(!"".equals(id)){
                    ids = ids+id;
                    if(ids.lastIndexOf(",")!=(ids.length()-1)){
                        ids = ids+",";
                    }
                }
            }
            if(ids.length()>0){
                ids.substring(0,ids.lastIndexOf(","));
            }

            if(Util.null2String(ids)!=""){
                String[] idList = ids.split(",");
                for (String s:idList){
                    JSONObject obj = new JSONObject();
                    obj.put("id",s);
                    obj.put("secondaryProjectName",ejxmmc);

                    array.add(obj);
                }

                client = HttpClients.createDefault();
                post = new HttpPost(url);
                post.addHeader("Tenant-Id","000000");
                post.addHeader("Blade-Auth", Token.getToken(loginId,lastName));
                post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
                StringEntity entity1 = new StringEntity(array.toString(),"UTF-8");
                entity1.setContentEncoding("UTF-8");
                entity1.setContentType("application/json");
                post.setEntity(entity1);
                response = client.execute(post);

                writeLog("httpCode:"+response.getStatusLine().getStatusCode() );
                writeLog("data:"+ EntityUtils.toString(response.getEntity()));
                if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                    requestInfo.getRequestManager().setMessagecontent("文件归档失败!");
                    throw new Exception("文件归档失败!");
                }
            }
            return Action.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            writeLog(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}