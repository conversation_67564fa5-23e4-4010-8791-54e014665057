package weaver.interfaces.workflow.action.cyitce.po.contract;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.upgradetool.wscheck.Util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName: updateContractAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-04-30  14:25
 * @Version: 1.0
 */
public class updateContractAction extends BaseBean implements Action {
    private String separate = ",";

    private String sqlUpdateSetAdd(String str,String addstr){
        return "".equals(str)?addstr:str+separate+addstr;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tableName = requestInfo.getRequestManager().getBillTableName();
        RecordSet rs = new RecordSet();
        RecordSetTrans rst = new RecordSetTrans();
        RecordSetTrans dRst = new RecordSetTrans();
        RecordSetTrans uRst = new RecordSetTrans();
        rst.setAutoCommit(false);
        dRst.setAutoCommit(false);
        uRst.setAutoCommit(false);

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        //变更内容
        Set<String> set = null;
        //合同id
        String ContractId="";
        //合同种类
        String ContractType="";
        //变更后有效期结束时间
        String enddate="";
        //更新字段
        String sqlUpdateSet = "";
        try {
            rs.executeQuery("select cghtmc,bgnr,htzl,enddate from "+tableName+" where requestid=?",requestid);
            rs.next();
            ContractId = Util.null2String(rs.getString(1));
            set = Arrays.stream(Util.null2String(rs.getString(2)).split(",")).collect(Collectors.toSet());
            ContractType = Util.null2String(rs.getString(3));
            enddate = Util.null2String(rs.getString(4));
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent("获取表单内容失败！请检查表单是否填写完整");
            return Action.FAILURE_AND_CONTINUE;
        }

        try {
            if(set.contains("0")&&!set.contains("5")){
                sqlUpdateSet = sqlUpdateSetAdd(sqlUpdateSet,"htkssj=b.startdate,htzzsj=b.enddate");
                LocalDate convertedDate = LocalDate.parse(enddate);
                LocalDate currDate = LocalDate.now();
                if(convertedDate.compareTo(currDate)>=0){
                    sqlUpdateSet = sqlUpdateSetAdd(sqlUpdateSet,"zt=0");
                }
            }
            if(set.contains("2")){
                sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"sldx=b.bghsl");
            }
            if(set.contains("3")){
                if("2".equals(ContractType)||"6".equals(ContractType)){
                    sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"fkfs=b.bghfkfs,qtfkfs1=b.bghqtfkfs");
                }else if ("3".equals(ContractType)||"5".equals(ContractType)){
                    sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"fkfslw=b.bghfkfs");
                }else if ("4".equals(ContractType)){
                    sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"fkfsrj=b.bghfkfs,qtfkfsjsfw=b.bghqtfkfs");
                }
            }

            if(set.contains("5")){
                sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"zt=1");
            }
            if(set.contains("6")){
                sqlUpdateSet =sqlUpdateSetAdd(sqlUpdateSet,"zhmc=b.gghhm,khyx=b.gghkhx,yxkh=b.gghzh,lxdzyf=b.gghdz,fzrlxdh=b.gghlxdh");
            }

            if(!"".equals(sqlUpdateSet)){
                rst.executeUpdate("update a set \n" + sqlUpdateSet +
                        " from uf_cghtspbd a inner join "+tableName+" b on a.id=" + ContractId+
                        " where b.requestid="+requestid);
            }

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            e.printStackTrace();
            writeLog(e.getMessage());
            requestInfo.getRequestManager().setMessagecontent("采购合同信息变更失败！err："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }
}