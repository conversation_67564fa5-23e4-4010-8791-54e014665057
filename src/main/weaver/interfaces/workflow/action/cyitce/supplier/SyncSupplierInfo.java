package weaver.interfaces.workflow.action.cyitce.supplier;


import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.SupplierServiceImpl;
import com.engine.common.util.ServiceUtil;
import com.weaver.general.BaseBean;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.Map;


/**
 *
 * @description OA供应商登记流程结束时同步到供应商平台中
 * @return
 */
public class SyncSupplierInfo extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        int billid = request.getRequestManager().getBillid();
        String tableName = request.getRequestManager().getBillTableName();

        writeLog("SyncSupplierInfo begin :" + tableName + ",billid :" + billid);
        JSONObject json = new JSONObject();
        Map<String, String> map = new HashMap<>();

        Property[] properties = request.getMainTableInfo().getProperty();
        DetailTable[] detailTable = request.getDetailTableInfo().getDetailTable();

        String name = "";
        String value = "";

        try {
            for (int i = 0; i < properties.length; i++) {
                name = properties[i].getName();
                value = Util.null2String(properties[i].getValue());
                map.put(name, value);
            }
            writeLog("SyncSupplierInfo map:" + map.toString());

            JSONObject supplierBankInfo = null;
            JSONObject supplierQualificationInfo = null;
            if (detailTable.length > 0) {
                DetailTable dt = detailTable[1];// 指定明细表1,银行账户信息明细表
                Row[] s = dt.getRow();// 当前明细表的所有数据,按行存储
                supplierBankInfo = new JSONObject();
                for (int j = 0; j < s.length; j++) {
                    Row r = s[j];// 指定行
                    Cell c[] = r.getCell();// 每行数据再按列存储
                    Map<String, String> rowsMap = new HashMap<>();
                    for (int k = 0; k < c.length; k++) {
                        Cell c1 = c[k];// 指定列
                        name = c1.getName();// 明细字段名称
                        value = c1.getValue();// 明细字段的值
                        rowsMap.put(name, value);
                    }
                    writeLog("SyncSupplierInfo  rowsMap" + j + ":" + rowsMap.toString());
                    supplierBankInfo.put(j + "", rowsMap);
                }

                DetailTable dt2 = detailTable[3];// 指定明细表3,供应商资质信息
                Row[] s2 = dt2.getRow();// 当前明细表的所有数据,按行存储
                supplierQualificationInfo = new JSONObject();
                for (int j = 0; j < s2.length; j++) {
                    Row r = s2[j];// 指定行
                    Cell c[] = r.getCell();// 每行数据再按列存储
                    Map<String, String> rowsMap = new HashMap<>();
                    for (int k = 0; k < c.length; k++) {
                        Cell c1 = c[k];// 指定列
                        name = c1.getName();// 明细字段名称
                        value = c1.getValue();// 明细字段的值
                        if("zzzp".equals(name)){
                            name = c1.getName();// 明细字段名称
                            if(Util.null2String(c1.getValue())!=""){
                                ReturnData rd = ServiceUtil.getService(SupplierServiceImpl.class).upLoadSupplierSystemFile(c1.getValue());
                                if(!rd.getSuccess()){
                                    throw new Exception("明细3第"+(j+1)+"行的附件上传到供应商平台失败！请联系管理员处理");
                                }
                                value = (String)rd.getData();
                            }
                        }
                        rowsMap.put(name, value);
                    }
                    supplierQualificationInfo.put(j + "", rowsMap);
                }
            }

            json.put("supplierNumber", map.get("gysbh"));//供应商编号
            json.put("ucc",map.get("tyshxydm"));//统一社会信用代码
            json.put("telephone",map.get("frdbdh"));//电话号码
            json.put("supplierName",map.get("gysmc"));//供应商名称
            //json.put("email",map.get());//邮箱
            json.put("supplierBaseInfo",map);//供应商基础信息
            json.put("supplierBankInfo",supplierBankInfo);//供应商银行信息
            json.put("supplierQualificationInfo",supplierQualificationInfo);//供应商资质信息

            writeLog("SyncSupplierInfo json:" + json.toJSONString());

            request.getRequestManager().setMessagecontent("json:" + json.toJSONString());

            int flag = raise(json.toJSONString());
            writeLog("SyncSupplierInfo flag=" + flag);
            if (flag != 0) {
                throw new Exception("数据同步到供应商平台失败！请联系管理员处理");
            }
        }catch (Exception e){
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }

    private int raise(String entity_json){
        writeLog("SyncSupplierInfo 参数信息"+entity_json);
        writeLog("SyncSupplierInfo raise----begin");
        String urlString="https://supplier.cyitce.com/api/supplier-system/authentication/supplierCallBack";
//        String urlString="http://192.168.170.61:30000/supplier-system/authentication/supplierCallBack";
        HttpClient client=null;
        PostMethod post=null;
        Integer result=1;
        try {
            client=new HttpClient();
            post=new PostMethod(urlString);
            //添加参数
//            post.addRequestHeader("Authorization", tokenString);
            post.setRequestHeader("Content-Type", "application/json;charset=utf-8");
            RequestEntity requestEntity = new StringRequestEntity(entity_json, "application/json", "UTF-8");
            post.setRequestEntity(requestEntity);
            //执行
            client.executeMethod(post);
            writeLog("SyncSupplierInfo----raise----code:"+post.getStatusCode());
            String body = new String(post.getResponseBody(), "utf-8");
            writeLog("SyncSupplierInfo 返回值："+body);
//            String success = JSONObject.parseObject(body).getString("success");
            if(body.contains("200")){
                result=0;
            }
            writeLog("SyncSupplierInfo httpCode: "+result);
        } catch (HttpException e) {
            e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            writeLog("SyncSupplierInfo raise---finally");
            //关闭连接，释放资源
            post.releaseConnection();
            ((SimpleHttpConnectionManager) client.getHttpConnectionManager()).shutdown();
        }
        return result;
    }
}
