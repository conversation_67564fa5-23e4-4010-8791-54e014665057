package weaver.interfaces.workflow.action.cyitce.supplier;

import com.api.formmode.page.util.Util;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: 数藤云计算-供应商登记流程供应商注册接口
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-16  16:01
 * @Version: 1.0
 */
public class SupplierApproval20230816 extends BaseBean implements Action {
    public String lb;

    public String execute(RequestInfo request) {
        writeLog("SupplierApproval执行");

        RecordSet rs = new RecordSet();
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        String crmcode = "";
        String id = "";
        DateFormat dateFormat_jyrq = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String today = dateFormat_jyrq.format(date);
        dateFormat_jyrq = new SimpleDateFormat("HH:mm:ss");
        String time = dateFormat_jyrq.format(date);
        int billid = request.getRequestManager().getBillid();
        String table = request.getRequestManager().getBillTableName();
        Map<String, String> result = new HashMap();
        Property[] properties = request.getMainTableInfo().getProperty();

        try {
            if(lb==null||"".equals(lb)){
                throw new Exception("提交失败，请联系管理员，eg：自定义接口 SupplierApproval20230816参数lb的值错误");
            }

            int lbb;
            lbb = Integer.valueOf(lb);

            int i;
            String name;
            for(i = 0; i < properties.length; ++i) {
                name = properties[i].getName();
                String value = Util.null2String(properties[i].getValue());
                result.put(name, value);
            }

            //判断供应商还是客户
            if(lbb==0){
                //该供应商名称的数据是否存在
                rs.executeQuery("SELECT gysbh,id,lb FROM uf_st_CustomerInfo WHERE gysmc=?",result.get("gysmc"));
                if(rs.next()){
                    String flb = Util.null2String(rs.getString(3));
                    String gysbh = Util.null2String(rs.getString(1));
                    //该数据是否标记为了供应商
                    boolean isNumberExist = Arrays.stream(flb.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Integer::parseInt)
                            .anyMatch(number -> number == lbb);
                    if(!isNumberExist){
                        flb = flb.length()==0?"0":flb+","+0;
                        rst.executeUpdate("update uf_st_CustomerInfo set lb=? where gysbh=?",flb,gysbh);
                    }

                    rs.executeQuery("select gysbh from uf_st_gysdj where gysbh=?",gysbh);
                    if(rs.next()){
                        throw new Exception("供应商已登记！");
                    }else {
                        crmcode = rs.getString(1);
                        id = rs.getString(2);
                    }
                }else {
                    rst.execute("INSERT INTO uf_st_CustomerInfo(gysmc,status,fzr,lb) VALUES('" + result.get("gysmc") + "','2','" + result.get("sqr")+ "',"+lb+")");
                    rs.executeQuery("SELECT gysbh,id FROM uf_st_CustomerInfo WHERE gysmc=?",result.get("gysmc"));
                    if(rs.next()){
                        crmcode = rs.getString(1);
                        id = rs.getString(2);
                    }else {
                        throw new Exception("供应商注册失败！");
                    }
                }

                rst.execute("UPDATE "+table+" SET gysbh='" + crmcode + "',gysxx='"+id+"' WHERE id=" + billid);
            }else if(lbb==1){
                rs.executeQuery("SELECT gysbh,id,lb FROM uf_st_CustomerInfo WHERE gysmc=?",result.get("khmc"));
                if(rs.next()) {
                    String flb = Util.null2String(rs.getString(3));
                    String khbh = Util.null2String(rs.getString(1));
                    //该数据是否标记为了供应商
                    boolean isNumberExist = Arrays.stream(flb.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Integer::parseInt)
                            .anyMatch(number -> number == lbb);
                    if (!isNumberExist) {
                        flb = flb.length() == 0 ? "1" : flb + "," + 1;
                        rst.executeUpdate("update uf_st_CustomerInfo set lb=? where gysbh=?",flb,khbh);
                    }

                    rs.executeQuery("select khbh from uf_styjs_khdj where khbh=?",khbh);
                    if (rs.next()) {
                        throw new Exception("客户已登记！");
                    } else {
                        crmcode = rs.getString(1);
                        id = rs.getString(2);
                    }
                }else {
                    rst.execute("INSERT INTO uf_st_CustomerInfo(gysmc,status,fzr,lb) VALUES('" + result.get("khmc") + "','2','" + result.get("sqr")+ "',"+lb+")");
                    rs.executeQuery("SELECT gysbh,id FROM uf_st_CustomerInfo WHERE gysmc=?",result.get("khmc"));
                    if(rs.next()){
                        crmcode = rs.getString(1);
                        id = rs.getString(2);
                    }else {
                        throw new Exception("客户注册失败！");
                    }
                }

                rst.execute("UPDATE "+table+" SET khbh='" + crmcode + "',khxx='"+id+"' WHERE id=" + billid);
            }else {
                throw new Exception("提交失败，请联系管理员，eg：自定义接口 SupplierApproval20230816 缺少参数：lb");
            }

            rst.executeUpdate("update uf_st_CustomerInfo set formmodeid=?,modedatacreater=?,modedatacreatedate=?,modedatacreatetime=?,MODEUUID=? where id=?",
                    843,result.get("sqr"),today,time, UUID.randomUUID().toString(),id);

            rst.commit();
            return Action.SUCCESS;
        }catch (Exception e){
            rst.rollback();
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}