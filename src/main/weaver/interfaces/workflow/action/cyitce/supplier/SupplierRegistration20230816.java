package weaver.interfaces.workflow.action.cyitce.supplier;

import com.api.formmode.page.util.Util;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 数藤云计算-供应商登记流程检查供应商是否注册接口
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-16  16:04
 * @Version: 1.0
 */
public class SupplierRegistration20230816 extends BaseBean implements Action {
    public String lb="";

    public String execute(RequestInfo request) {
        RecordSet rs = new RecordSet();
        Map<String, String> result = new HashMap();
        Property[] properties = request.getMainTableInfo().getProperty();

        try {
            int i;
            String name;
            for(i = 0; i < properties.length; ++i) {
                name = properties[i].getName();
                String value = Util.null2String(properties[i].getValue());
                result.put(name, value);
            }

            this.writeLog("SupplierRegistration执行");

            if("0".equals(lb)){
                rs.executeQuery("select gysbh from uf_st_gysdj where gysmc='?'",result.get("gysmc"));
                if(rs.next()){
                    throw new Exception("供应商已存在！");
                }
            }else if("1".equals(lb)){
                rs.executeQuery("select khbh from uf_styjs_khdj where khmc='?'",result.get("khmc"));
                if(rs.next()){
                    throw new Exception("客户已存在！");
                }
            }else {
                throw new Exception("提交失败，请联系管理员，eg：自定义接口 SupplierRegistration20230816 缺少参数：lb");
            }

            writeLog("执行成功");
            return Action.SUCCESS;
        }catch (Exception e){
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}