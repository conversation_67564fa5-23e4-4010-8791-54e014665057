package weaver.interfaces.workflow.action.cyitce.supplier;

import com.api.formmode.page.util.Util;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: SupplierRegistration
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-06-19  11:36
 * @Version: 1.0
 */
public class SupplierRegistration extends BaseBean implements Action {
    public SupplierRegistration() {
    }

    public String execute(RequestInfo request) {
        RecordSet rs = new RecordSet();
        Map<String, String> result = new HashMap();
        Property[] properties = request.getMainTableInfo().getProperty();

        try {
            int i;
            String name;
            for(i = 0; i < properties.length; ++i) {
                name = properties[i].getName();
                String value = Util.null2String(properties[i].getValue());
                result.put(name, value);
            }

            this.writeLog("SupplierRegistration执行");

            if (result.get("gyszx").equals("0")) {
                rs.execute("select gysbh from uf_gysdjbdjm where tyshxydm='"+result.get("tyshxydm")+"'");
                if(rs.next()){
                    throw new Exception("身份证号码已存在！");
                }
            }else {
                rs.execute("select gysbh from uf_gysdjbdjm where gysmc='"+ result.get("gysmc")+ "'");
                if(rs.next()){
                    throw new Exception("供应商已存在！");
                }
            }

            writeLog("执行成功");
            return Action.SUCCESS;
        }catch (Exception e){
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}