package weaver.interfaces.workflow.action.cyitce.supplier;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.FileDownload;
import weaver.interfaces.workflow.action.cyitce.util.HttpUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: 供应商门户采购变更入库
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-19  14:05
 * @Version: 1.0
 */
public class PurchaseChangeInsert extends BaseBean implements Action  {
    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar now = Calendar.getInstance();

        RecordSet rs = new RecordSet();
        Map<String,Object> body = new HashMap<>();

        try {
            writeLog("===================PurchaseChangeInsert=========== requestid:"+requestInfo.getRequestid());
            Map<String,String> tbProcureRequest = new HashMap<>();
            rs.executeQuery("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid=?",new Object[]{requestInfo.getRequestid()});
            rs.next();
            String option = rs.getString("bgnrdx");
            if(option.length()==0){
                throw new Exception("没有选择变更内容");
            }

            Set<String> set = Arrays.stream(option.split(",")).collect(Collectors.toSet());

            tbProcureRequest.put("changeNo", Util.null2String(rs.getString("cgxqbgbh")));
            if(set.contains("2")){
                tbProcureRequest.put("changeTitle", Util.null2String(rs.getString("bgcgxqbt")));
            }
            if(set.contains("8")||set.contains("9")||set.contains("10")||set.contains("11")){
                String area = JoinFieldManage.getProvinceName(rs.getString("sf"));
                tbProcureRequest.put("area", area);
                String harvestAddress = toAddress(rs.getString("sf"),rs.getString("cs"),rs.getString("qx"),rs.getString("bgxxdz"));
                tbProcureRequest.put("harvestAddress",harvestAddress);
            }
            if(set.contains("12")||set.contains("13")){
                String harvestTime = rs.getString("bgzwshrq") +" "+rs.getString("bgzwshsj")+":00";
                tbProcureRequest.put("harvestTime",harvestTime);
            }
            if(set.contains("14")||set.contains("15")){
                String deadlineTime = rs.getString("bgbmjzrq") +" "+rs.getString("bgbmjzsj")+":00";
                tbProcureRequest.put("deadlineTime",deadlineTime);
            }
            if(set.contains("16")){
                tbProcureRequest.put("contacts",JoinFieldManage.getPersonName(rs.getString("bgcglxr")));
            }
            if(set.contains("17")){
                tbProcureRequest.put("telephone",Util.null2String(rs.getString("bgcglxrdh")));
            }

            //状态
            Date deadlineTime = dateFormat.parse(rs.getString("bgbmjzrq") +" "+rs.getString("bgbmjzsj")+"00");
            String status;
            if(deadlineTime.compareTo(now.getTime())==-1){
                status = "2";
            }else {
                status = "0";
            }
            if(set.contains("19")){
                status=rs.getString("bgcgzt");
            }
            tbProcureRequest.put("status",status);

            String releaseTime = dateFormat.format(now.getTime());
            writeLog("===================PurchaseChangeInsert=========== releaseTime:"+releaseTime);
            tbProcureRequest.put("releaseTime",releaseTime);
//            String companyName = JoinFieldManage.getCompanyName(String.valueOf(requestInfo.getRequestManager().getUser().getUserSubCompany1()),"subcompanyname");
//            tbProcureRequest.put("purchaser",companyName);

            String cgqxbgid = rs.getString("cgbgid");
            if(cgqxbgid.isEmpty()){
                throw new Exception("【采购需求变更ID】反写失败");
            }
            tbProcureRequest.put("oaChangeId",cgqxbgid);
            body.put("procureChange",tbProcureRequest);

            List<Map> tbProcureRequestChangeRelsList = new ArrayList();
            Map<String,String> tbProcureRequestChangeRels = new HashMap<>();
            tbProcureRequestChangeRels.put("oaRequestId",rs.getString("cgxqxz"));
            tbProcureRequestChangeRels.put("oaChangeId",cgqxbgid);
            tbProcureRequestChangeRelsList.add(tbProcureRequestChangeRels);
            body.put("procureChangeRequestRels",tbProcureRequestChangeRelsList);

            try {
                List<Map> tbProcureRequestListRelsList = new ArrayList();
                if(!rs.getString("cgqdfj").isEmpty()){
                    Map<String,String> tbProcureRequestListRels = new HashMap<>();

                    Map<String,String> fileInfo = FileDownload.getFileDatas(rs.getString("cgqdfj"),user);
                    tbProcureRequestListRels.put("fileName",fileInfo.get("imagefilename"));
                    tbProcureRequestListRels.put("type","变更");

                    tbProcureRequestListRels.put("releaseTime",releaseTime+":00");
                    tbProcureRequestListRels.put("fileUrl",fileInfo.get("path"));
                    tbProcureRequestListRels.put("oaChangeId",cgqxbgid);

                    tbProcureRequestListRelsList.add(tbProcureRequestListRels);
                }
                body.put("procureChangeListRels",tbProcureRequestListRelsList);
            }catch (Exception e){
                throw new Exception("文件信息错误");
            }

            Map<String, Object> headers = new HashMap<>();
            headers.put("Cookie",requestInfo.getRequestManager().getRequest().getHeader("Cookie"));

            ReturnData rd = HttpUtil.httpPost(SupplierURL.PUR_CHANGE_INS_URL,body,headers);
            if(!rd.getSuccess()){
                throw new Exception("请求发送失败，http："+rd.getCode());
            }
            JSONObject obj = JSON.parseObject(rd.getData().toString());
            if(!obj.getBoolean("success")){
                throw new Exception(obj.getString("msg"));
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    public String toAddress(String sf,String cs,String qx,String address) throws Exception {
        if(sf.isEmpty()){
            throw new Exception("省份不能为空");
        }else if (cs.isEmpty()){
            throw new Exception("城市不能为空");
        }else if(qx.isEmpty()){
            throw new Exception("区县不能为空");
        }

        RecordSet rs = new RecordSet();

        rs.executeQuery("select provincename from HrmProvince where id=?",sf);
        rs.next();
        sf = rs.getString(1);
        rs.executeQuery("select cityname from HrmCity where id=?",cs);
        rs.next();
        cs = rs.getString(1);
        rs.executeQuery("select cityname from hrmcitytwo where id=?",qx);
        rs.next();
        qx = rs.getString(1);

        if(sf.equals(cs)){
            return sf+qx+address;
        }else {
            return sf+cs+qx+address;
        }
    }
}