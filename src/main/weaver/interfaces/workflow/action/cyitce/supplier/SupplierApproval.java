package weaver.interfaces.workflow.action.cyitce.supplier;

/**
 * @ClassName: SupplierApproval
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-06-19  13:47
 * @Version: 1.0
 */
import com.api.formmode.page.util.Util;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

public class SupplierApproval extends BaseBean implements Action {
    public SupplierApproval() {
    }
    public String execute(RequestInfo request) {
        writeLog("SupplierApproval执行");

        RecordSet rs = new RecordSet();
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        String crmcode = "";
        String id = "";
        DateFormat dateFormat_jyrq = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String today = dateFormat_jyrq.format(date);
        int billid = request.getRequestManager().getBillid();
        String table = request.getRequestManager().getBillTableName();
        Map<String, String> result = new HashMap();
        Property[] properties = request.getMainTableInfo().getProperty();

        User user = request.getRequestManager().getUser();
        try {
            int i;
            String name;
            for(i = 0; i < properties.length; ++i) {
                name = properties[i].getName();
                String value = Util.null2String(properties[i].getValue());
                result.put(name, value);
            }

            if (result.get("gyszx").equals("0")) {
                rs.execute("SELECT crmcode,id FROM CRM_CustomerInfo WHERE tyshxydm='" + result.get("tyshxydm") + "'");
            }else {
                rs.execute("SELECT crmcode,id FROM CRM_CustomerInfo WHERE name='" + result.get("gysmc") + "'");
            }

            if (rs.next()) {
                if(result.get("gyszx").equals("0")){
                    rst.execute("select gysbh from uf_gysdjbdjm where tyshxydm='"+ result.get("tyshxydm")+ "'");
                }else {
                    rst.execute("select gysbh from uf_gysdjbdjm where gysmc='"+ result.get("gysmc")+ "'");
                }

                if(rst.next()){
                    throw new Exception("供应商已存在！");
                }else {
                    crmcode = rs.getString(1);
                    id = rs.getString(2);
                }
            }else {
                rs.execute("INSERT INTO CRM_CustomerInfo(name,[language],engname,address1,country,province,manager,department,fincode,currency,contractlevel,creditlevel,creditoffset,discount,invoiceacount,deliverytype,paymentterm,paymentway,saleconfirm,type,typebegin,status,rating,deleted,subcompanyid1,createdate,Sex,tyshxydm) VALUES('" + result.get("gysmc") + "','7','" + result.get("gysmc") + "','','0','0','"+user.getUID()+"','"+user.getUserDepartment()+"','0','0','0','0','.0000','100','0','0','0','0','0','1','" + today + "','2','0'," + "'0','"+user.getUserSubCompany1()+"','" + today + "','0','"+result.get("tyshxydm")+"')");
                rs.execute("SELECT crmcode,id FROM CRM_CustomerInfo WHERE name='" + result.get("gysmc") + "'");
                if(rs.next()){
                    crmcode = rs.getString(1);
                    id = rs.getString(2);
                }else {
                    throw new Exception("供应商注册失败！");
                }
            }

            writeLog("供应商名称："+result.get("gysmc"));
            writeLog("流程表单名称："+table);
            rst.execute("UPDATE "+table+" SET gysbh='" + crmcode + "',gysxx='"+id+"' WHERE id=" + billid);
            writeLog("编号为" + crmcode);

            rst.commit();
            return Action.SUCCESS;
        }catch (Exception e){
            rst.rollback();
            request.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}
