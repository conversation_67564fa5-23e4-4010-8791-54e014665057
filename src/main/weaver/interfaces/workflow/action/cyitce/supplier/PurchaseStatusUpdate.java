package weaver.interfaces.workflow.action.cyitce.supplier;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.HttpUtil;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;


/**
 * @ClassName: 供应商门户采购需求状态变更
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-19  16:01
 * @Version: 1.0
 */
public class PurchaseStatusUpdate extends BaseBean implements Action {
    public String status;
    public String oaRequestId;

    @Override
    public String execute(RequestInfo requestInfo) {
        RecordSet rs = new RecordSet();
        User user = requestInfo.getRequestManager().getUser();

        try {
            Map<String,Object> body = new HashMap<>();
            try {
                rs.executeQuery("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid=?",new Object[]{requestInfo.getRequestid()});
                rs.next();
                body.put("status",status);
                body.put("oaRequestId",rs.getString(oaRequestId));
            }catch (Exception e){
                throw new Exception("获取表单不存在采购需求ID字段："+oaRequestId);
            }

            Map<String, Object> headers = new HashMap<>();
            headers.put("Cookie",requestInfo.getRequestManager().getRequest().getHeader("Cookie"));

            ReturnData rd = HttpUtil.httpPost(SupplierURL.CHANGE_STATUS_URL,body,headers);
            if(!rd.getSuccess()){
                throw new Exception("请求发送失败，http："+rd.getCode());
            }
            JSONObject obj = JSON.parseObject(rd.getData().toString());
            if(!obj.getBoolean("success")){
                throw new Exception(obj.getString("msg"));
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }
}