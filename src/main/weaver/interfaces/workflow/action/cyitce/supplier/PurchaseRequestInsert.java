package weaver.interfaces.workflow.action.cyitce.supplier;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.util.FileDownload;
import weaver.interfaces.workflow.action.cyitce.util.HttpUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: 供应商门户采购需求入库
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-12  11:02
 * @Version: 1.0
 */
public class PurchaseRequestInsert extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();

        RecordSet rs = new RecordSet();
        Map<String,Object> body = new HashMap<>();

        try {
            writeLog("===================PurchaseRequestInsert=========== requestid:"+requestInfo.getRequestid());
            Map<String,String> tbProcureRequest = new HashMap<>();
            rs.executeQuery("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid=?",new Object[]{requestInfo.getRequestid()});
            rs.next();
            tbProcureRequest.put("requestTitle", Util.null2String(rs.getString("cgxqbt")));
            tbProcureRequest.put("requestNo", Util.null2String(rs.getString("cgxqbh")));

            String area = JoinFieldManage.getProvinceName(rs.getString("sf"));
            tbProcureRequest.put("area", area);
            tbProcureRequest.put("status","0");

            String releaseTime = dateFormat.format(calendar.getTime());
            writeLog("===================PurchaseRequestInsert=========== releaseTime:"+releaseTime);
            tbProcureRequest.put("releaseTime",releaseTime);
            String deadlineTime = rs.getString("bmjzrq")+" "+rs.getString("bmjzsj")+":00";
            tbProcureRequest.put("deadlineTime",deadlineTime);
            String companyName = JoinFieldManage.getCompanyName(String.valueOf(requestInfo.getRequestManager().getUser().getUserSubCompany1()),"subcompanyname");
            tbProcureRequest.put("purchaser",companyName);
            tbProcureRequest.put("contacts",JoinFieldManage.getPersonName(rs.getString("cglxr")));
            tbProcureRequest.put("telephone",Util.null2String(rs.getString("cglxrdh")));

            String harvestAddress = toAddress(rs.getString("sf"),rs.getString("cs"),rs.getString("qx"),rs.getString("xxdz"));
            tbProcureRequest.put("harvestAddress",harvestAddress);
            String harvestTime = rs.getString("zwshrq") +" "+rs.getString("zwshsj")+":00";

            tbProcureRequest.put("harvestTime",harvestTime);
            String cgqxid = rs.getString("cgxqid");
            if(cgqxid.isEmpty()){
                throw new Exception("【采购需求ID】反写失败");
            }
            tbProcureRequest.put("oaRequestId",cgqxid);
            tbProcureRequest.put("isChanged","0");
            body.put("tbProcureRequest",tbProcureRequest);

            try {
                List<Map> tbProcureRequestListRelsList = new ArrayList();
                if(!rs.getString("cgqdfj").isEmpty()){

                    Map<String,String> tbProcureRequestListRels = new HashMap<>();
                    Map<String,String> fileInfo = FileDownload.getFileDatas(rs.getString("cgqdfj"),user);

                    tbProcureRequestListRels.put("fileName",fileInfo.get("imagefilename"));
                    tbProcureRequestListRels.put("type","");

                    tbProcureRequestListRels.put("releaseTime",releaseTime+":00");
                    tbProcureRequestListRels.put("fileUrl",fileInfo.get("path"));
                    tbProcureRequestListRels.put("oaRequestId",cgqxid);

                    tbProcureRequestListRelsList.add(tbProcureRequestListRels);
                }
                body.put("tbProcureRequestListRels",tbProcureRequestListRelsList);
            }catch (Exception e){
                throw new Exception("文件信息错误");
            }

            Map<String, Object> headers = new HashMap<>();
            headers.put("Cookie",requestInfo.getRequestManager().getRequest().getHeader("Cookie"));

            ReturnData rd = HttpUtil.httpPost(SupplierURL.PUR_REQ_INS_URL,body,headers);
            if(!rd.getSuccess()){
                throw new Exception("请求发送失败，http："+rd.getCode());
            }
            JSONObject obj = JSON.parseObject(rd.getData().toString());
            if(!obj.getBoolean("success")){
                throw new Exception(obj.getString("msg"));
            }

            return Action.SUCCESS;
        }catch (Exception e){
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    public String toAddress(String sf,String cs,String qx,String address) throws Exception {
        if(sf.isEmpty()){
            throw new Exception("省份不能为空");
        }else if (cs.isEmpty()){
            throw new Exception("城市不能为空");
        }else if(qx.isEmpty()){
            throw new Exception("区县不能为空");
        }

        RecordSet rs = new RecordSet();

        rs.executeQuery("select provincename from HrmProvince where id=?",sf);
        rs.next();
        sf = rs.getString(1);
        rs.executeQuery("select cityname from HrmCity where id=?",cs);
        rs.next();
        cs = rs.getString(1);
        rs.executeQuery("select cityname from hrmcitytwo where id=?",qx);
        rs.next();
        qx = rs.getString(1);

        if(sf.equals(cs)){
            return sf+qx+address;
        }else {
            return sf+cs+qx+address;
        }

    }
}