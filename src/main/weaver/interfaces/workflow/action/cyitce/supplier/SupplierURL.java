package weaver.interfaces.workflow.action.cyitce.supplier;

/**
 * @ClassName: SupplierURL
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-12  15:21
 * @Version: 1.0
 */
public class SupplierURL {
    //IP
    public static final String API = "http://***************:82";
    //采购需求入库
    public static final String PUR_REQ_INS_URL = API + "/supplier-system/purchase-order/purchaseRequestInsert";
    //采购变更入库
    public static final String PUR_CHANGE_INS_URL = API + "/supplier-system/purchase-order/purchaseChangeInsert";
    //采购需求状态变更
    public static final String CHANGE_STATUS_URL = API + "/supplier-system/purchase-order/change-status";
}