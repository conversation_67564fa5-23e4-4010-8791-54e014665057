package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 无关联人员认证声明书
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  13:47
 * @Version: 1.0
 */
public class Mode6 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;

    public Mode6() {
        setPath("lwwx/fj3/fj3d2");
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    @Override
    public String toString() {
        return "Mode6{" +
                "gysmc='" + gysmc + '\'' +
                '}';
    }
}