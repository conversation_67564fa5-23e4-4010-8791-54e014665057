package weaver.interfaces.workflow.action.cyitce.doc.Action;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.Mode8;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 车辆租赁合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-10-12  09:45
 * @Version: 1.0
 */
public class CLZPHTToElectronDocAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        RecordSet rs = null;
        String table = requestInfo.getRequestManager().getBillTableName();
        ConverToChinesePart cp = null;
        ImageInfoManage im = null;
        Mode8 m = null;
        String tableName = null;
        Row[] rList = null;
        Cell[] cList = null;
        String name = "车辆租赁合同";
        ModeToWord mwt = null;
        String requestid = null;
        RecordSetTrans rst = null;
        try {
            tableName = requestInfo.getRequestManager().getBillTableName();
            im = new ImageInfoManage();
            m = new Mode8();
            im.init();

            mwt = new ModeToWord();
            rst = new RecordSetTrans();
            rst.setAutoCommit(false);

            rs = new RecordSet();
            rs.execute("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid="+requestInfo.getRequestid());
            rs.next();

            //合同模板字段赋值处理
            m.setHtbh(Util.null2String(rs.getString("htbh")));
            
            m.setFb(JoinFieldManage.getCompanyName(rs.getString("fb"),"subcompanyname"));
            
            m.setTyshxydmczf(Util.null2String(rs.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(rs.getString("zsdjf")));
            m.setDzyxjf(Util.null2String(rs.getString("dzyxjf")));
            m.setShrdh(Util.null2String(rs.getString("shrdh")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(rs.getString("gysmc")));
            m.setSfzhmtyshxydmczf(Util.null2String(rs.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(rs.getString("lxdzyf")));
            m.setFzryxdz(Util.null2String(rs.getString("fzryxdz")));
            m.setFzrlxdh(Util.null2String(rs.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(rs.getString("wxhyf")));
            m.setZlqxdwy(Util.null2String(rs.getString("zlqxdwy")));
            writeLog("合同开始日期："+rs.getString("htkssj"));
            m.setHtkssj(JoinFieldManage.dateStringToString(rs.getString("htkssj")));
            writeLog("合同结束日期："+rs.getString("htzzsj"));
            m.setHtzzsj(JoinFieldManage.dateStringToString(rs.getString("htzzsj")));
            m.setZlmsjzffs(String.valueOf(Integer.parseInt(rs.getString("zlmsjzffs"))+1));
            m.setSfdjsy(JoinFieldManage.getSelectName("sfdjsy",tableName,rs.getString("sfdjsy")));
            m.setBgms(String.valueOf(rs.getString("bgms")));
            m.setBgje(Util.null2String(rs.getString("bgje")));
            m.setBgsfhs(JoinFieldManage.getSelectName("bgsfhs",tableName,rs.getString("bgsfhs"))+"税价");
            m.setBgjhssl(JoinFieldManage.getSelectName("bgjhssl",tableName,rs.getString("bgjhssl")));
            m.setBbgms(Util.null2String(rs.getString("bbgms")));
            m.setBbgmsjbzj(Util.null2String(rs.getString("bbgmsjbzj")));
            m.setJsyrgf(Util.null2String(rs.getString("jsyrgf")));
            m.setJsyfwfms(Util.null2String(rs.getString("jsyfwfms")));
            m.setBbgmsqtfycdr(JoinFieldManage.getSelectName("bbgmsqtfycdr",tableName,rs.getString("bbgmsqtfycdr")).replace("方",""));
            m.setBbgmsqtfyzyfjjfs(Util.null2String(rs.getString("bbgmsqtfyzyfjjfs")));
            m.setBbgsfhs(JoinFieldManage.getSelectName("bbgsfhs",tableName,rs.getString("bbgsfhs"))+"税价");
            m.setBbgjhssl(JoinFieldManage.getSelectName("bbgjhssl",tableName,rs.getString("bbgjhssl")));
            m.setYfkjfplx(JoinFieldManage.getSelectName("yfkjfplx",tableName,rs.getString("yfkjfplx")).replace("发票",""));
            m.setYfkjfpsl(JoinFieldManage.getSelectName("yfkjfpsl",tableName,rs.getString("yfkjfpsl")));
            m.setZcyj(Util.null2String(rs.getString("zcyj")));
            m.setYjzfsj(Util.null2String(rs.getString("yjzfsj")));
            m.setTqtzts(Util.null2String(rs.getString("tqtzts")));
            m.setZhmc(Util.null2String(rs.getString("zhmc")));
            m.setYxkh(Util.null2String(rs.getString("yxkh")));
            m.setKhyx(Util.null2String(rs.getString("khyx")));

            writeLog("获取明细表二");
            DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
            rList = detailTable[1].getRow();
            for (int i = 0;i<rList.length;++i){
                Map<String,Object> map = new HashMap<>();
                cList = rList[i].getCell();
                map.put("id",String.valueOf(i+1));
                for (int j = 0;j<cList.length;++j){
                    if("zp".equals(cList[j].getName())){
                        String[] zpId =  cList[j].getValue().split(",");
                        List<ImageInfo> val1 = new ArrayList<>();
                        for(String s:zpId){
                            ImageInfo imageInfo = im.getImageInfo(s);
                            val1.add(imageInfo);
                            m.getImageInfoList().add(imageInfo);
                        }
                        map.put(cList[j].getName(),val1);
                    }else {
                        map.put(cList[j].getName(),cList[j].getValue());
                    }
                }
                m.getDetail().add(map);
            }
            
            writeLog("获取明细表三");
            detailTable = requestInfo.getDetailTableInfo().getDetailTable();
            rList = detailTable[2].getRow();
            for (int i = 0;i<rList.length;++i){
                Map<String,String> map = new HashMap<>();
                cList = rList[i].getCell();
                map.put("id",String.valueOf(i+1));
                for (int j = 0;j<cList.length;++j){
                    if("sfns".equals(cList[j].getName())){
                        map.put(cList[j].getName(),JoinFieldManage.getSelectName(cList[j].getName(),tableName,cList[j].getValue()));
                    }else {
                        map.put(cList[j].getName(),cList[j].getValue());
                    }
                }
                m.getDetail2().add(map);
            }

            //附件
            String[] str = null;

            writeLog("获取附件二 字节码");
            String fjValue = Util.null2String(rs.getString("czrsfzyyzz"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getCzrsfzyyzz().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            writeLog("获取附件三 二进制字符串");
            fjValue = Util.null2String(rs.getString("fjs"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getFjs().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            writeLog("获取附件五 二进制字符串");
            fjValue = Util.null2String(rs.getString("fjw"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getFjw().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }
            writeLog("赋值完成");

            //甲乙方 盖章
            int len = 12;

            if(m.getFb().length()>len){
                m.setFbLeft(Util.null2String(m.getFb().substring(0,len)));
                m.setFbRight(Util.null2String(m.getFb().substring(len)));
            }else {
                m.setFbLeft(Util.null2String(m.getFb()));
            }

            if(m.getGysmc().length()>len){
                m.setGysmcLeft(Util.null2String(m.getGysmc().substring(0,len)));
                m.setGysmcRight(Util.null2String(m.getGysmc().substring(len)));
            }else {
                m.setGysmcLeft(Util.null2String(m.getGysmc()));
            }

            //mode转docx
            String docPath = mwt.toDocx(m,name);
            String fid = FileUtil.uploadToOA(name+".docx",docPath,requestid);
            rst.executeUpdate("update "+table+" set gzfj=? where requestid=?",fid,requestid);

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            e.printStackTrace();
            writeLog("--------------------："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }finally {
            //删除临时附件
            im.deleteFile();
        }

        return Action.SUCCESS;
    }
}