package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 有关联人员认证声明书
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  11:33
 * @Version: 1.0
 */
public class Mode5 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;

    public Mode5() {
        setPath("lwwx/fj3/fj3d1");
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    @Override
    public String toString() {
        return "Mode5{" +
                "gysmc='" + gysmc + '\'' +
                '}';
    }
}