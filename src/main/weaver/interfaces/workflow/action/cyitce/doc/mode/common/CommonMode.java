package weaver.interfaces.workflow.action.cyitce.doc.mode.common;

import weaver.interfaces.workflow.action.cyitce.doc.config.DocConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: TemplateMode
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-02  11:16
 * @Version: 1.0
 */
public class CommonMode {
    //模板路径
    private String templatePath;
    //附件路径集合
    private List<String> annexList;

    public CommonMode(){
        templatePath = DocConfig.TEMPLATE_PATH;
        annexList = new ArrayList<>();
    }

    public String setPath(String templateName){
        int preIndex = templateName.indexOf("/");
        if(preIndex!=0){
            templateName = "/"+templateName;
        }
        templatePath = templatePath + templateName;
        return templatePath;
    }

    public String getTemplatePath(){
        return templatePath;
    }

    public void setAnnex(List<String> annexs){
        this.annexList = annexs;
    }

    public List<String> getAnnexList(){
        return annexList;
    }
}