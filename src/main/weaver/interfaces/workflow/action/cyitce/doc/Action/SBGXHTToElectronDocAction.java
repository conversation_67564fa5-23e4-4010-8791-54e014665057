package weaver.interfaces.workflow.action.cyitce.doc.Action;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.Mode11;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 设备购销合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-01-26  14:00
 * @Version: 1.0
 */
public class SBGXHTToElectronDocAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String table = requestInfo.getRequestManager().getBillTableName();
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
        ConverToChinesePart cp = null;

        String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();
        RecordSet rs = null;

        Mode11 m = null;
        ImageInfoManage im = null;
        Row[] r = null;
        Cell[] c = null;
        String name = "设备购销合同";
        ModeToWord mwt = null;
        String requestid = null;
        RecordSetTrans rst = null;
        try {
            requestid = requestInfo.getRequestid();
            im = new ImageInfoManage();
            cp = new ConverToChinesePart();
            m = new Mode11();
            mwt = new ModeToWord();
            rst = new RecordSetTrans();
            rst.setAutoCommit(false);

            im.init();
            rs = new RecordSet();
            rs.execute(sql);
            if(!rs.next()){
                writeLog("sql:  select * from "+table+" where requestid="+requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent("获取合同数据失败！");
                throw new Exception("contract 没有查询到合同数据！");
            }

            m.setHtbh(Util.null2String(rs.getString("htbh")));
            m.setFb(JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")),"subcompanyname"));
            m.setTyshxydmczf(Util.null2String(rs.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(rs.getString("zsdjf")));
            m.setLxrjf(JoinFieldManage.getPersonName(rs.getString("lxrjf")));
            m.setJflxrzw(Util.null2String(rs.getString("jflxrzw")));
            m.setLxdhjf(Util.null2String(rs.getString("lxdhjf")));
            m.setWxhjf(Util.null2String(rs.getString("wxhjf")));
            m.setDzyxjf(Util.null2String(rs.getString("dzyxjf")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(rs.getString("gysmc")));
            m.setSfzhmtyshxydmczf(Util.null2String(rs.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(rs.getString("lxdzyf")));
            m.setYwqyfzr(Util.null2String(rs.getString("ywqyfzr")));
            m.setGyslxrzw(Util.null2String(rs.getString("gyslxrzw")));
            m.setFzrlxdh(Util.null2String(rs.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(rs.getString("wxhyf")));
            m.setFzryxdz(Util.null2String(rs.getString("fzryxdz")));

            m.setBhsjg(Util.null2String(rs.getString("bhsjg")));
            m.setBhsjgDX(cp.convertToChinese(Util.null2String(rs.getString("bhsjg"),"0")));
            m.setSldx(JoinFieldManage.getSelectName("sldx",table,rs.getString("sldx")));
            m.setHsjg(Util.null2String(rs.getString("hsjg")));
            m.setHsjgDX(cp.convertToChinese(Util.null2String(rs.getString("hsjg"),"0")));
            m.setFkfslw(Util.null2String(rs.getString("fkfslw")));
            //付款方式
            m.setZfhtjkfs("".equals(Util.null2String(rs.getString("zfhtjkfs")))?"":String.valueOf(rs.getInt("zfhtjkfs")+1));
            //方式一
            m.setFsy(Util.null2String(rs.getString("fsy")));
            //方式二
            m.setZfdjqx(Util.null2String(rs.getString("zfdjqx")));
            m.setDjje(Util.null2String(rs.getString("djje")));
            m.setDjjeDX(cp.convertToChinese(Util.null2String(rs.getString("djje"),"0")));
            m.setHgyszfqx(Util.null2String(rs.getString("hgyszfqx")));
            m.setHtzjkzfbl(CommonUtil.IntToPercentage(rs.getString("htzjkzfbl")));
            m.setZfje(Util.null2String(rs.getString("zfje")));
            m.setZfjeDX(cp.convertToChinese(Util.null2String(rs.getString("zfje"),"0")));
            m.setYlhtzjkbfb(CommonUtil.IntToPercentage(rs.getString("ylhtzjkbfb")));
            m.setZbjje(Util.null2String(rs.getString("zbjje")));
            m.setZbjjeDX(cp.convertToChinese(Util.null2String(rs.getString("zbjje"),"0")));
            //方式三
            m.setZfdjqx2(Util.null2String(rs.getString("zfdjqx2")));
            m.setDjje2(Util.null2String(rs.getString("djje2")));
            m.setDjje2DX(cp.convertToChinese(Util.null2String(rs.getString("djje2"),"0")));
            m.setHgyszfqx2(Util.null2String(rs.getString("hgyszfqx2")));
            m.setHtzjkzfbl2(CommonUtil.IntToPercentage(rs.getString("htzjkzfbl2")));
            m.setZfje2(Util.null2String(rs.getString("zfje2")));
            m.setZfje2DX(cp.convertToChinese(Util.null2String(rs.getString("zfje2"),"0")));
            m.setJfysrn(Util.null2String(rs.getString("jfysrn")));
            m.setHtzjkzfbl3(CommonUtil.IntToPercentage(rs.getString("htzjkzfbl3")));
            m.setZfje3(Util.null2String(rs.getString("zfje3")));
            m.setZfje3DX(cp.convertToChinese(Util.null2String(rs.getString("zfje3"),"0")));
            m.setJfysrn2(Util.null2String(rs.getString("jfysrn2")));
            m.setHtzjkzfbl4(CommonUtil.IntToPercentage(rs.getString("htzjkzfbl4")));
            m.setZfje4(Util.null2String(rs.getString("zfje4")));
            m.setZfje4DX(cp.convertToChinese(Util.null2String(rs.getString("zfje4"),"0")));

            m.setYlhtzjkbfb2(CommonUtil.IntToPercentage(rs.getString("ylhtzjkbfb2")));
            m.setZbjje2(Util.null2String(rs.getString("zbjje2")));
            m.setZbjje2DX(cp.convertToChinese(Util.null2String(rs.getString("zbjje2"),"0")));
            //方式四
            m.setQtfkfs(CommonUtil.ReplaceSpeciaiToBlank(rs.getString("qtfkfs")));
            m.setYffkfs("".equals(Util.null2String(rs.getString("yffkfs")))?"":String.valueOf(rs.getInt("yffkfs")+1));

            m.setZhmc(Util.null2String(rs.getString("zhmc")));
            m.setGsnsrsbh(Util.null2String(rs.getString("gsnsrsbh")));
            m.setYxkh(Util.null2String(rs.getString("yxkh")));
            m.setKhyx(Util.null2String(rs.getString("khyx")));

            m.setYfzfsj(Util.null2String(rs.getString("yfzfsj")));
            m.setShrxxdz(Util.null2String(rs.getString("shrxxdz")));
            m.setShr(JoinFieldManage.getPersonName(rs.getString("shr")));
            m.setShrdh(Util.null2String(rs.getString("shrdh")));
            m.setJhfs("".equals(Util.null2String(rs.getString("jhfs")))?"":String.valueOf(rs.getInt("jhfs")+1));
            m.setZthdz(Util.null2String(rs.getString("zthdz")));
            m.setZthlxr(Util.null2String(rs.getString("zthlxr")));
            m.setYsrxm(Util.null2String(rs.getString("ysrxm")));
            m.setWyfyxsyfzfwy(Util.null2String(rs.getString("wyfyxsyfzfwy")));

            r = detailTable[5].getRow();
            for (int i = 0;i<r.length;++i){
                Map<String,String> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id",String.valueOf(i+1));
                for (int j = 0;j<c.length;++j){
                    writeLog(c[j].getName()+"---"+c[j].getValue());
                    map.put(c[j].getName(),c[j].getValue());
                }
                m.getDetail().add(map);
            }

            //附件处理
            String[] str = null;

            writeLog("获取附件一 字节码");
            String fjValue = Util.null2String(rs.getString("zlfwcqz"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getZlfwcqz().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            writeLog("获取附件二");
            fjValue = Util.null2String(rs.getString("czrsfzyyzz"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getCzrsfzyyzz().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            //甲乙方盖章
            int len = 14;

            if(m.getFb().length()>len){
                m.setFbLeft(Util.null2String(m.getFb().substring(0,len)));
                m.setFbRight(Util.null2String(m.getFb().substring(len)));
            }else {
                m.setFbLeft(Util.null2String(m.getFb()));
            }

            if(m.getGysmc().length()>len){
                m.setGysmcLeft(Util.null2String(m.getGysmc().substring(0,len)));
                m.setGysmcRight(Util.null2String(m.getGysmc().substring(len)));
            }else {
                m.setGysmcLeft(Util.null2String(m.getGysmc()));
            }

            //mode转docx
            String docPath = mwt.toDocx(m,name);
            String fid = FileUtil.uploadToOA(name+".docx",docPath,requestid);
            rst.executeUpdate("update "+table+" set gzfj=? where requestid=?",fid,requestid);

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            e.printStackTrace();
            writeLog("----------："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }finally {
            //删除临时附件
            im.deleteFile();
        }

        return Action.SUCCESS;
    }
}