package weaver.interfaces.workflow.action.cyitce.doc.Action.update;

import com.engine.core.exception.ECException;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.config.DocConfig;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.*;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName: 劳务外协框架合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-08  15:57
 * @Version: 1.0
 */
public class UpdateLWWXToElectronDocAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String table = requestInfo.getRequestManager().getBillTableName();
        String otTable = "uf_cghtspbd";
        String name = "劳务外协框架合同";

        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        RecordSet mainObje = new RecordSet();
        RecordSet otherObje =new RecordSet();

        Mode1 m = null;
        ConverToChinesePart cp = null;
        ModeToWord mwt = null;
        //需合并的文档路径（按顺序依次合并）
        List<String> mergDocxPathList;

        //合同
        try {
            cp = new ConverToChinesePart();

            m = new Mode1();
            mwt = new ModeToWord();

            //主数据来源
            mainObje.executeQuery("select * from "+table+" where requestid=?",requestid);
            mainObje.next();
            //其它数据来源
            if("".equals(Util.null2String(mainObje.getString("cghtmc")))){
                throw new Exception("缺失采购合同！");
            }

            otherObje.executeQuery("select * from "+otTable+" where id=?",mainObje.getString("cghtmc"));
            otherObje.next();
            //主文档路径
            mergDocxPathList = new ArrayList<>();
            mergDocxPathList.add(mwt.getMerg_file_path()+name+".docx");
            //其它数据来源封装合同信息
            m.setHtbh(Util.null2String(otherObje.getString("htbh")));
            writeLog("合同编号 ："+Util.null2String(otherObje.getString("htbh")));
            m.setFb(JoinFieldManage.getCompanyName(Util.null2String(otherObje.getString("fb")),"subcompanyname"));
            m.setTyshxydmczf(Util.null2String(otherObje.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(otherObje.getString("zsdjf")));
            m.setWxjfjl(JoinFieldManage.getPersonName(otherObje.getString("wxjfjl")));
            m.setWxjfjldh(Util.null2String(otherObje.getString("wxjfjldh")));
            m.setWxhjf(Util.null2String(otherObje.getString("wxhjf")));
            m.setWxjfjlyx(Util.null2String(otherObje.getString("wxjfjlyx")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(otherObje.getString("gysmc")));
            m.setSfzhmtyshxydmczf(Util.null2String(otherObje.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(otherObje.getString("lxdzyf")));
            m.setYwqyfzr(Util.null2String(otherObje.getString("ywqyfzr")));
            m.setFzrlxdh(Util.null2String(otherObje.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(otherObje.getString("wxhyf")));
            m.setFzryxdz(Util.null2String(otherObje.getString("fzryxdz")));
            //资质证书（明细7）
            rst.executeQuery("select * from "+otTable+"_dt7 where mainid=?",otherObje.getString("id"));
            while (rst.next()){
                String rs[] = rst.getColumnName();
                Map<String,String> m2 = new HashMap<>();
                for (String s:rs){
                    String value2 = CommonUtil.nullToStr(rst.getString(s),"/");
                    if(!"/".equals(value2)&&("yxqkssj".equals(s)||"yxqjssj".equals(s))){
                        value2 = JoinFieldManage.dateStringToString(value2);
                    }
                    m2.put(s,value2);
                }

                if("安全生成许可证".equals(m2.get("zzmcwb"))){
                    m.setZzzsbh1(m2.get("zzzsbh"));
                    m.setZzzyjdj1(m2.get("zzzyjdj"));
                    m.setFzjg1(m2.get("fzjg"));
                    m.setYxqstart1(m2.get("yxqkssj"));
                    m.setYxqend1(m2.get("yxqjssj"));
                    m.setXkfw(m2.get("xkfw"));
                }else if("劳务资质".equals(m2.get("zzmcwb"))){
                    m.setZzzsbh2(m2.get("zzzsbh"));
                    m.setZzzyjdj2(m2.get("zzzyjdj"));
                    m.setFzjg2(m2.get("fzjg"));
                    m.setYxqstart2(m2.get("yxqkssj"));
                    m.setYxqend2(m2.get("yxqjssj"));
                }else if("".equals(Util.null2String(m2.get("zzmcwb")))){
                    m.setZsmc(m2.get("zzmcwb"));
                    m.setZzzsbh3(m2.get("zzzsbh"));
                    m.setZzzyjdj3(m2.get("zzzyjdj"));
                    m.setFzjg3(m2.get("fzjg"));
                    m.setYxqstart3(m2.get("yxqkssj"));
                    m.setYxqend3(m2.get("yxqjssj"));
                }else if ("".equals(Util.null2String(m2.get("zzmcwb")))){
                    m.setZsmc2(m2.get("zzmcwb"));
                    m.setZzzsbh4(m2.get("zzzsbh"));
                    m.setZzzyjdj4(m2.get("zzzyjdj"));
                    m.setFzjg4(m2.get("fzjg"));
                    m.setYxqstart4(m2.get("yxqkssj"));
                    m.setYxqend4(m2.get("yxqjssj"));
                }
            }

            Map<String,String> m1 = new HashMap<>();
            m1.put("zzmcwb","/");
            m1.put("zzzsbh","/");
            m1.put("zzzyjdj","/");
            m1.put("fzjg","/");
            m1.put("yxqkssj","/");
            m1.put("yxqjssj","/");
            m1.put("xkfw","/");

            if("".equals(m.getZzzsbh1())){
                m.setZzzsbh1(m1.get("zzzsbh"));
                m.setZzzyjdj1(m1.get("zzzyjdj"));
                m.setFzjg1(m1.get("fzjg"));
                m.setYxqstart1(m1.get("yxqkssj"));
                m.setYxqend1(m1.get("yxqjssj"));
                m.setXkfw(m1.get("xkfw"));
            }
            if("".equals(m.getZzzsbh2())){
                m.setZzzsbh2(m1.get("zzzsbh"));
                m.setZzzyjdj2(m1.get("zzzyjdj"));
                m.setFzjg2(m1.get("fzjg"));
                m.setYxqstart2(m1.get("yxqkssj"));
                m.setYxqend2(m1.get("yxqjssj"));
            }
            if("".equals(Util.null2String(m.getZzzsbh3()))){
                m.setZsmc(m1.get("zzmcwb"));
                m.setZzzsbh3(m1.get("zzzsbh"));
                m.setZzzyjdj3(m1.get("zzzyjdj"));
                m.setFzjg3(m1.get("fzjg"));
                m.setYxqstart3(m1.get("yxqkssj"));
                m.setYxqend3(m1.get("yxqjssj"));
            }
            if ("".equals(Util.null2String(m.getZzzsbh4()))){
                m.setZsmc2(m1.get("zzmcwb"));
                m.setZzzsbh4(m1.get("zzzsbh"));
                m.setZzzyjdj4(m1.get("zzzyjdj"));
                m.setFzjg4(m1.get("fzjg"));
                m.setYxqstart4(m1.get("yxqkssj"));
                m.setYxqend4(m1.get("yxqjssj"));
            }

            //--------------------------二 start----------------------------------------------
            m.setYjxmmc(CommonUtil.nullToStr(JoinFieldManage.getProjectName(otherObje.getString("yjxmmc")),"/"));
            m.setXmszsf(CommonUtil.nullToStr(JoinFieldManage.getProvinceName(otherObje.getString("xmszsf")),"/"));
            m.setFbnr(CommonUtil.nullToStr(idToStringLWWX(otherObje.getString("wxlwwx"),otherObje.getString("fbnr")),"/"));
            //--------------------------二 end----------------------------------------------

            //-----------------三 start -----------------------------
            m.setHtkssj(CommonUtil.nullToStr(JoinFieldManage.dateStringToString(otherObje.getString("htkssj")),"/"));
            m.setHtzzsj(CommonUtil.nullToStr(JoinFieldManage.dateStringToString(otherObje.getString("htzzsj")),"/"));
            //-----------------三 end -----------------------------

            //-------------------五 start ------------------------------------
            m.setBhsjg(CommonUtil.nullToStr(otherObje.getString("bhsjg"),"/"));
            m.setBhsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(otherObje.getString("bhsjg"),"0")),"/"));
            String slxzVals = Util.null2String(otherObje.getString("sldx"));
            String slxzValsStr = "";
            if(!"".equals(slxzVals)){
                String[] slxzs = slxzVals.split(",");
                for (String s:slxzs){
                    slxzValsStr += JoinFieldManage.getSelectName("sldx",otTable,s)+" ";
                }
            }
            m.setSlxz(slxzValsStr);

            m.setHsjg(CommonUtil.nullToStr(otherObje.getString("hsjg"),"/"));
            m.setHsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(otherObje.getString("hsjg"),"0")),"/"));
            if("0".equals(Util.null2String(otherObje.getString("nwbcjs")))){
                m.setNwbcjs("一");
            }else if("1".equals(Util.null2String(otherObje.getString("nwbcjs")))){
                m.setNwbcjs("二");
            }else if("2".equals(Util.null2String(otherObje.getString("nwbcjs")))){
                m.setNwbcjs("四");
            }else if("3".equals(Util.null2String(otherObje.getString("nwbcjs")))){
                m.setNwbcjs("三");
            }
            m.setNwbcgj(Util.null2String(otherObje.getString("nwbcgj")));
            m.setNwbcgjDX(CommonUtil.nullToStr(cp.convertToChinese(otherObje.getString("nwbcgj")),"/"));
            m.setFkfs(Util.null2String(otherObje.getString("fkfs")));
            m.setQtfkfs1(Util.null2String(otherObje.getString("qtfkfs1")));
            //特别约定
            m.setSkhtkhmc(CommonUtil.nullToStr(JoinFieldManage.getCustomerName(otherObje.getString("skhtkhmc")),"/"));
            m.setSkhtbh(CommonUtil.nullToStr(otherObje.getString("skhtbh"),"/"));
            m.setSkhtmc(CommonUtil.nullToStr(JoinFieldManage.getContractName(otherObje.getString("skhtmc")),"/"));

            String fpzzslVals = Util.null2String(otherObje.getString("fpzzsl"));
            String fpzzslValsStr = "";
            if(!"".equals(fpzzslVals)){
                String[] fpzzsls = fpzzslVals.split(",");
                for (String s:fpzzsls){
                    fpzzslValsStr += JoinFieldManage.getSelectName("fpzzsl",otTable,s)+" ";
                }
            }
            m.setFpzzsl(fpzzslValsStr);

            m.setFplx1(JoinFieldManage.getSelectName("fplx1",otTable,otherObje.getString("fplx1")));
            m.setLybzjjnsj(Util.null2String(otherObje.getString("lybzjjnsj")));
            m.setLybzj(Util.null2String(otherObje.getString("lybzj")));
            m.setLybzjDX(cp.convertToChinese(Util.null2String(otherObje.getString("lybzj"),"0")));
            //-------------------五 end ------------------------------------

            //------------------六 start----------------------------
            m.setZhmc(Util.null2String(otherObje.getString("zhmc")));
            m.setYxkh(Util.null2String(otherObje.getString("yxkh")));
            m.setKhyx(Util.null2String(otherObje.getString("khyx")));
            //------------------六 end----------------------------

            //------------------八 施工与设计变更 start----------------------------
            m.setYflwwxdz(Util.null2String(otherObje.getString("yflwwxdz")));
            m.setYfdzdh(Util.null2String(otherObje.getString("yfdzdh")));
            m.setYfdzsfz(Util.null2String(otherObje.getString("yfdzsfz")));
            //------------------八 施工与设计变更 end----------------------------

            //--------------九 工程质量管理及验收 start----------------
            m.setGCZLgcmc(CommonUtil.nullToStr(JoinFieldManage.getCustomerName(otherObje.getString("skhtkhmc")),"/"));
            m.setGCZLhtbh(CommonUtil.nullToStr(otherObje.getString("skhtbh"),"/"));
            m.setGCZLhtmc(CommonUtil.nullToStr(JoinFieldManage.getContractName(otherObje.getString("skhtmc")),"/"));
            //--------------九 工程质量管理及验收 end---------------

            //---------------------------十一 start--------------------------------
            m.setGczlbxqx(Util.null2String(otherObje.getString("gczlbxqx")));
            //---------------------------十一 end--------------------------------

            //---------------------------十二 start--------------------------------
            m.setQcfy(Util.null2String(otherObje.getString("qcfy")));
            m.setLkgdqx(Util.null2String(otherObje.getString("lkgdqx")));
            //---------------------------十二 end--------------------------------

            //---------------------------十三 start--------------------------------
            m.setWjbl(CommonUtil.saveSmallNum(Util.null2String(otherObje.getString("wjbl"))));
            //---------------------------十三 end--------------------------------

            m.setWxsftjfj11(Util.null2String(otherObje.getString("wxsftjfj11")));

            //明细表4
            rst.executeQuery("select * from "+otTable+"_dt4 where mainid=?",otherObje.getString("id"));
            List<Map<String,String>> detail = new ArrayList<>();
            int count = 0;
            while (rst.next()){
                Map<String,String> map = new HashMap<>();
                String rs[] = rst.getColumnName();
                map.put("id",String.valueOf(count+1));
                for (String s:rs){
                    String value = Util.null2String(rst.getString(s));
                    writeLog(s+"---"+value);
                    map.put(s,value);
                }
                detail.add(map);
            }
            if(detail.size()==0){
                writeLog("mx4 length == 0");
                Map<String,String> map = new HashMap<>();
                map.put("id","/");
                map.put("gzqy","/");
                map.put("bl","/");
                map.put("cnsl","/");
                map.put("bz","/");
                detail.add(map);
            }
            m.setDetail(detail);

            //明细表8
            rst.executeQuery("select * from "+otTable+"_dt8 where mainid=?",otherObje.getString("id"));
            List<Map<String,String>> detail2 = new ArrayList<>();
            count = 0;
            while (rst.next()){
                Map<String,String> map = new HashMap<>();
                String rs[] = rst.getColumnName();
                map.put("id",String.valueOf(count+1));
                for (String s:rs){
                    String value = Util.null2String(rst.getString(s));
                    writeLog(s+"---"+value);
                    map.put(s,value);
                }
                detail2.add(map);
            }
            if(detail2.size()==0){
                Map<String,String> map = new HashMap<>();
                map.put("id","/");
                map.put("sx","/");
                map.put("hsdjy","/");
                map.put("sl","/");
                map.put("yghj","/");
                detail2.add(map);
            }
            m.setDetail2(detail2);

            //明细表9
            rst.executeQuery("select * from "+otTable+"_dt9 where mainid=?",otherObje.getString("id"));
            List<Map<String,String>> detail3 = new ArrayList<>();
            count = 0;
            while (rst.next()){
                Map<String,String> map = new HashMap<>();
                String rs[] = rst.getColumnName();
                map.put("id",String.valueOf(count+1));
                for (String s:rs){
                    String value = Util.null2String(rst.getString(s));
                    writeLog(s+"---"+value);
                    map.put(s,value);
                }
                detail3.add(map);
            }
            if(detail3.size()==0){
                Map<String,String> map = new HashMap<>();
                map.put("id","/");
                map.put("tmmc","/");
                map.put("hsdjy","/");
                map.put("yggzl","/");
                map.put("yghj","/");
                detail3.add(map);
            }
            m.setDetail3(detail3);

            //明细表10
            rst.executeQuery("select * from "+otTable+"_dt10 where mainid=?",otherObje.getString("id"));
            List<Map<String,String>> detail4 = new ArrayList<>();
            count = 0;
            while (rst.next()){
                Map<String,String> map = new HashMap<>();
                String rs[] = rst.getColumnName();
                map.put("id",String.valueOf(count+1));
                for (String s:rs){
                    String value = Util.null2String(rst.getString(s));
                    writeLog(s+"---"+value);
                    map.put(s,value);
                }
                detail4.add(map);
            }
            if(detail4.size()==0){
                Map<String,String> map = new HashMap<>();
                map.put("id","/");
                map.put("gzmc","/");
                map.put("gzbl","/");
                detail4.add(map);
            }
            m.setDetail4(detail4);

            //主数据来源更新合同信息
            Set<String> bgnrs = Arrays.stream(Util.null2String(mainObje.getString("bgnr")).split(",")).collect(Collectors.toSet());
            if(bgnrs.contains("0")){
                m.setHtkssj(mainObje.getString("startdate"));
                m.setHtzzsj(mainObje.getString("enddate"));
            }
            if(bgnrs.contains("1")){
                m.setBhsjg(CommonUtil.nullToStr(mainObje.getString("bghcgjebhs"),"/"));
                m.setBhsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(mainObje.getString("bghcgjebhs"),"0")),"/"));
                m.setHsjg(CommonUtil.nullToStr(mainObje.getString("bghcgjehs"),"/"));
                m.setHsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(mainObje.getString("bghcgjehs"),"0")),"/"));
            }
            if(bgnrs.contains("2")){
                slxzVals = Util.null2String(mainObje.getString("bghsl"));
                slxzValsStr = "";
                if(!"".equals(slxzVals)){
                    String[] slxzs = slxzVals.split(",");
                    for (String s:slxzs){
                        slxzValsStr += JoinFieldManage.getSelectName("bghsl",table,s)+" ";
                    }
                }
                m.setSlxz(slxzValsStr);
            }
            if(bgnrs.contains("3")){
                String fkfs = Util.null2String(mainObje.getString("bghfkfs"));
                Set<String> fkfsSet = Arrays.stream(fkfs.split(",")).collect(Collectors.toSet());

                m.setFkfs(fkfs);
                if(fkfsSet.contains("3")){
                    m.setQtfkfs1(Util.null2String(mainObje.getString("bghqtfkfs")));
                }else {
                    m.setQtfkfs1("");
                }
            }
            if(bgnrs.contains("4")){
                if("3".equals(Util.null2String(mainObje.getString("cgdl")))){
                    if("0".equals(Util.null2String(mainObje.getString("bghlwbc")))){
                        m.setNwbcjs("一");
                        //明细表
                        rst.executeQuery("select * from "+table+"_dt2 where mainid=?",mainObje.getString("id"));
                        List<Map<String,String>> detail22 = new ArrayList<>();
                        count = 0;
                        while (rst.next()){
                            Map<String,String> map = new HashMap<>();
                            String rs[] = rst.getColumnName();
                            map.put("id",String.valueOf(count+1));
                            for (String s:rs){
                                String value = Util.null2String(rst.getString(s));
                                writeLog(s+"---"+value);
                                map.put(s,value);
                            }
                            detail22.add(map);
                        }
                        if(detail22.size()==0){
                            Map<String,String> map = new HashMap<>();
                            map.put("id","/");
                            map.put("sx","/");
                            map.put("hsdjy","/");
                            map.put("sl","/");
                            map.put("yghj","/");
                            detail22.add(map);
                        }
                        m.setDetail2(detail22);
                    }else if("1".equals(Util.null2String(mainObje.getString("bghlwbc")))){
                        m.setNwbcjs("二");
                        //明细表
                        rst.executeQuery("select * from "+table+"_dt3 where mainid=?",mainObje.getString("id"));
                        List<Map<String,String>> detail22 = new ArrayList<>();
                        count = 0;
                        while (rst.next()){
                            Map<String,String> map = new HashMap<>();
                            String rs[] = rst.getColumnName();
                            map.put("id",String.valueOf(count+1));
                            for (String s:rs){
                                String value = Util.null2String(rst.getString(s));
                                writeLog(s+"---"+value);
                                map.put(s,value);
                            }
                            detail22.add(map);
                        }
                        if(detail22.size()==0){
                            Map<String,String> map = new HashMap<>();
                            map.put("id","/");
                            map.put("tmmc","/");
                            map.put("hsdjy","/");
                            map.put("yggzl","/");
                            map.put("yghj","/");
                            detail22.add(map);
                        }
                        m.setDetail3(detail22);
                    }else if("2".equals(Util.null2String(mainObje.getString("bghlwbc")))){
                        m.setNwbcjs("四");
                        //明细表
                        rst.executeQuery("select * from "+table+"_dt5 where mainid=?",mainObje.getString("id"));
                        List<Map<String,String>> detail22 = new ArrayList<>();
                        count = 0;
                        while (rst.next()){
                            Map<String,String> map = new HashMap<>();
                            String rs[] = rst.getColumnName();
                            map.put("id",String.valueOf(count+1));
                            for (String s:rs){
                                String value = Util.null2String(rst.getString(s));
                                writeLog(s+"---"+value);
                                map.put(s,value);
                            }
                            detail22.add(map);
                        }
                        if(detail22.size()==0){
                            Map<String,String> map = new HashMap<>();
                            map.put("id","/");
                            map.put("gzqy","/");
                            map.put("bl","/");
                            map.put("cnsl","/");
                            map.put("bz","/");
                            detail22.add(map);
                        }
                        m.setDetail4(detail22);
                    }else if("3".equals(Util.null2String(mainObje.getString("bghlwbc")))){
                        m.setNwbcjs("三");
                        //明细表
                        rst.executeQuery("select * from "+table+"_dt4 where mainid=?",mainObje.getString("id"));
                        List<Map<String,String>> detail22 = new ArrayList<>();
                        count = 0;
                        while (rst.next()){
                            Map<String,String> map = new HashMap<>();
                            String rs[] = rst.getColumnName();
                            map.put("id",String.valueOf(count+1));
                            for (String s:rs){
                                String value = Util.null2String(rst.getString(s));
                                writeLog(s+"---"+value);
                                map.put(s,value);
                            }
                            detail22.add(map);
                        }
                        if(detail22.size()==0){
                            Map<String,String> map = new HashMap<>();
                            map.put("id","/");
                            map.put("gzmc","/");
                            map.put("gzbl","/");
                            detail22.add(map);
                        }
                        m.setDetail4(detail22);
                    }
                }
            }
            if(bgnrs.contains("6")){
                m.setZhmc(Util.null2String(mainObje.getString("gghhm")));
                m.setYxkh(Util.null2String(mainObje.getString("gghzh")));
                m.setKhyx(Util.null2String(mainObje.getString("gghkhx")));
                m.setLxdzyf(Util.null2String(mainObje.getString("gghdz")));
                m.setFzrlxdh(Util.null2String(mainObje.getString("gghlxdh")));
            }

            String str = "";
            String fid = "";

            //附件1 关于规范发票行为及收款账户的不可撤销承诺函
            Mode2 m2 = new Mode2();
            try {
                m2.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));
                m2.setCreditCode(m.getSfzhmtyshxydmczf());
                m2.setLxdzyf(m.getLxdzyf());
                m2.setFzrlxdh(m.getFzrlxdh());
                m2.setZhmc(m.getZhmc());
                m2.setYxkh(m.getYxkh());
                m2.setKhyx(m.getKhyx());
                m2.setYwqyfzr(m.getYwqyfzr());

                m2.setFb(JoinFieldManage.getCompanyName(m.getFb(),"subcompanyname"));
                m2.setLxrjf(JoinFieldManage.getPersonName(otherObje.getString("lxrjf")));
                m2.setLxdhjf(Util.null2String(otherObje.getString("lxdhjf")));

                writeLog("关于规范发票行为及收款账户的不可撤销承诺函 Mode2:"+m2.toString());
                //创建docx文档
                str = mwt.toDocx(m2,"关于规范发票行为及收款账户的不可撤销承诺函");
                writeLog("关于规范发票行为及收款账户的不可撤销承诺函 save:"+str);
                mergDocxPathList.add(str);
            }catch (Exception e){
                throw new ECException("《关于规范发票行为及收款账户的不可撤销承诺函》字段映射错误！",e);
            }

            //附件2 项目安全生产与文明施工管理承诺书
            Mode3 m3 = new Mode3();
            try {
                m3.setCreditCode(m.getSfzhmtyshxydmczf());
                m3.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));
                m3.setYjxmmc(JoinFieldManage.getProjectName(m.getYjxmmc()));

                //创建docx文档
                str = mwt.toDocx(m3,"项目安全生产与文明施工管理承诺书");
                mergDocxPathList.add(str);
            }catch (Exception e){
                throw new ECException("《项目安全生产与文明施工管理承诺书》字段映射错误！",e);
            }

            //附件3 廉洁诚信承诺书
            //附件3.1 有关联人员认证声明书
            //附件3.2 无关联人员认证声明书
            Mode4 m4 = new Mode4();
            Mode5 m5 = new Mode5();
            Mode6 m6 = new Mode6();

            try {
                m4.setHtmc(Util.null2String(otherObje.getString("htmc")));
                m4.setHtbh(Util.null2String(otherObje.getString("htbh")));
                m4.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));
                m4.setYwqyfzr(m.getYwqyfzr());

                String fids = "";
                //创建docx文档
                str = mwt.toDocx(m4,"廉洁诚信承诺书");
                mergDocxPathList.add(str);

                if("0".equals(otherObje.getString("ywglryrzsms"))){
                    m5.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));

                    str = mwt.toDocx(m5,"有关联人员认证声明书");
                    mergDocxPathList.add(str);
                }else {
                    m6.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));

                    str = mwt.toDocx(m6,"无关联人员认证声明书");
                    mergDocxPathList.add(str);
                }
            }catch (Exception e){
                throw new ECException("《廉洁诚信承诺书》字段映射错误！",e);
            }

            //附件4 不拖欠民工工资承诺书
            Mode7 m7 = new Mode7();
            try {
                m7.setGysmc(JoinFieldManage.getVendorAccountName(m.getGysmc()));
                m7.setYjxmmc(JoinFieldManage.getProjectName(m.getYjxmmc()));
                m7.setFkrq(Util.null2String(otherObje.getString("fkrq")));

                str = mwt.toDocx(m7,"不拖欠民工工资承诺书");
                mergDocxPathList.add(str);
            }catch (Exception e){
                throw new ECException("《不拖欠民工工资承诺书》字段映射错误！",e);
            }

            //附件5-10
//            try {
//                if(!"".equals(Util.null2String(otherObje.getString("fjw")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件5",otherObje.getString("fjw")));
//                }
//                if(!"".equals(Util.null2String(otherObje.getString("fjl")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件6",otherObje.getString("fjl")));
//                }
//                if(!"".equals(Util.null2String(otherObje.getString("fjq")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件7",otherObje.getString("fjq")));
//                }
//                if(!"".equals(Util.null2String(otherObje.getString("fjb")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件8",otherObje.getString("fjb")));
//                }
//                if(!"".equals(Util.null2String(otherObje.getString("fjj")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件9",otherObje.getString("fjj")));
//                }
//                if(!"".equals(Util.null2String(otherObje.getString("fjshi")))){
//                    mergDocxPathList.add(mwt.mergToWord("附件10",otherObje.getString("fjshi")));
//                }
//            }catch (Exception e){
//                throw new ECException("OA附件下载失败！",e);
//            }

            //附件11
            mergDocxPathList.add(DocConfig.TEMPLATE_PATH + "/lwwx/fj11.docx");

            //附件12
            try {
                mergDocxPathList.add(mwt.createDocxByImage("附件12",DocConfig.TEMPLATE_PATH + "/lwwx/fj12/order.jpg"));
            }catch (Exception e){
                throw new ECException("附件12转换docx失败！",e);
            }

            //将需要合并的文档路径放入主模板中
            m.setAnnex(mergDocxPathList);

            //mode转docx
            str = mwt.toDocx(m,name);
            fid = FileUtil.uploadToOA(name+".docx",str,requestid);
            rst.executeUpdate("update "+table+" set bcxydzd=? where requestid=?",fid,requestid);

            mwt.delete();
            rst.commit();
        }catch (Exception e){
            rst.rollback();
            throw new ECException("电子合同生成失败！,"+e.getMessage());
        }finally {
            try {
                mwt.delete();
            }catch (Exception e1){
                throw new ECException("删除临时文件夹失败！，"+e1.getMessage());
            }
        }

        return Action.SUCCESS;
    }

    private String idToStringLWWX(String ids,String qt){
        if("".equals(Util.null2String(ids))){
            return "";
        }
        StringBuffer content = new StringBuffer();

        String str[] = ids.split(",");
        RecordSet rs = new RecordSet();
        for (int i=0;i<str.length;++i){
            rs.execute("select nr from uf_wxlwwxnr where id="+str[i]);
            if(rs.next()){
                if("17".equals(str[i])){
                    content.append(Util.null2String(qt));
                }else {
                    content.append(Util.null2String(rs.getString(1)));
                }

                if(i!=(str.length-1)){
                    content.append("、");
                }
            }
        }
        return content.toString();
    }
}