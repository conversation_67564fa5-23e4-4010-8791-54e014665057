package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 劳务外协框架合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-02  15:08
 * @Version: 1.0
 */
public class Mode1 extends CommonMode {



    /**
     * 增值税金
     */
    private String zzsj;

    /**
     * 大写增值税金
     */
    private String dxzzsj;
    /**
     * htbh 采购合同编号
     */
    private String htbh;
    /**
     * 甲方（分包方） fb
     */
    private String fb;
    /**
     * 统一社会信用代码 tyshxydmczf
     */
    private String tyshxydmczf;
    /**
     * 住所地 zsdjf
     */
    private String zsdjf;
    /**
     * 联系人 wxjfjl
     */
    private String wxjfjl;
    /**
     * 电话 wxjfjldh
     */
    private String wxjfjldh;
    /**
     * 微信号 wxhjf
     */
    private String wxhjf;
    /**
     * 电子邮箱 wxjfjlyx
     */
    private String wxjfjlyx;
    /**
     * 乙方（承包方）gysmc
     */
    private String gysmc;
    /**
     * 统一社会信用代码 sfzhmtyshxydmczf
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地 lxdzyf
     */
    private String lxdzyf;
    /**
     * 联系人 ywqyfzr
     */
    private String ywqyfzr;
    /**
     * 电话 fzrlxdh
     */
    private String fzrlxdh;
    /**
     * 微信号 wxhyf
     */
    private String wxhyf;
    /**
     * 电子邮箱（乙方）fzryxdz
     */
    private String fzryxdz;
    /**
     * 资质证书编号（安全生产许可证）zzzsbh1
     */
    private String zzzsbh1;
    /**
     * 资质专业及等级（安全生产许可证）zzzyjdj1
     */
    private String zzzyjdj1;
    /**
     * 发证机关（安全生产许可证）fzjg1
     */
    private String fzjg1;
    /**
     * 有效期（安全生产许可证）yxqstart1，yxqend1
     */
    private String yxqstart1;
    private String yxqend1;
    /**
     * 许可范围（安全生产许可证）xkfw
     */
    private String xkfw;
    /**
     * 资质证书编号（劳务资质）zzzsbh2
     */
    private String zzzsbh2;
    /**
     * 资质专业及等级（劳务资质）zzzyjdj2
     */
    private String zzzyjdj2;
    /**
     * fzjg2	发证机关
     */
    private String fzjg2;
    /**
     * 有效期（劳务资质）yxqstart2，yxqend2
     */
    private String yxqstart2;
    private String yxqend2;
    /**
     * zsmc	证书名称（其他资质）
     */
    private String zsmc;
    /**
     * zzzsbh3	资质证书编号（其他资质）
     */
    private String zzzsbh3;
    /**
     * zzzyjdj3	资质专业及等级（其他资质）
     */
    private String zzzyjdj3;
    /**
     * fzjg3	发证机关（其他资质）
     */
    private String fzjg3;
    /**
     * yxqstart3，yxqend2	有效期（其他资质）
     */
    private String yxqstart3;
    private String yxqend3;
    /**
     * zsmc	证书名称2（其他资质）
     */
    private String zsmc2;
    /**
     * zzzsbh3	资质证书编号4（其他资质）
     */
    private String zzzsbh4;
    /**
     * zzzyjdj3	资质专业及等级4（其他资质）
     */
    private String zzzyjdj4;
    /**
     * fzjg3	发证机关4（其他资质）
     */
    private String fzjg4;
    /**
     * yxqstart4，yxqend4	有效期4（其他资质）
     */
    private String yxqstart4;
    private String yxqend4;
    /**
     * yjxmmc	项目名称
     */
    private String yjxmmc;
    /**
     * xmszsf	项目地点
     */
    private String xmszsf;
    /**
     * fbnr	劳务外协内容
     */
    private String fbnr;
    /**
     * htkssj	合同开始日期
     */
    private String htkssj;
    /**
     * htzzsj	合同终止日期
     */
    private String htzzsj;
    /**
     * bhsjg	合同不含税价预估金额
     * 虚拟字段 bhsjgDX
     */
    private String bhsjg;
    private String bhsjgDX;
    /**
     * slxz	合同签署日的法定税率,%
     */
    private String slxz;
    /**
     * hsjg	合同含税价预估总金额
     * 虚拟字段 hsjgDX
     */
    private String hsjg;
    private String hsjgDX;
    /**
     * nwbcjs	劳务报酬结算方式
     */
    private String nwbcjs;
    /** lwbcssl 劳务报酬方式四-是否乘（1+承诺税率）
     *
     */
    private String lwbcssl;
    /**
     * nwbcgj	固定总价
     * nwbcgjDX 大写
     */
    private String nwbcgj;
    private String nwbcgjDX;
    /**
     * fkfs	付款方式
     */
    private String fkfs;
    /**
     * qtfkfs1 其他付款方式
     */
    private String qtfkfs1;
    /**
     * skhtkhmc	业主方名称
     */
    private String skhtkhmc;
    /**
     * skhtbh	合同编号
     */
    private String skhtbh;
    /**
     * skhtmc	合同名称
     */
    private String skhtmc;
    /**
     * fpzzsl	发票增值税率
     */
    private String fpzzsl;
    /**
     * fplx1	发票类型
     */
    private String fplx1;
    /**
     * lybzjjnsj	履约保证金缴纳时间
     */
    private String lybzjjnsj;
    /**
     * lybzj	履约保证金
     * 虚拟字段 lybzjDX
     */
    private String lybzj;
    private String lybzjDX;
    /**
     * zhmc	户名
     */
    private String zhmc;
    /**
     * yxkh	账号
     */
    private String yxkh;
    /**
     * khyx	开户行
     */
    private String khyx;
    /**
     * yflwwxdz	乙方劳务外协施工队长
     */
    private String yflwwxdz;
    /**
     * yfdzdh	乙方施工队长联系电话
     */
    private String yfdzdh;
    /**
     * yfdzsfz	乙方施工队长身份证号码
     */
    private String yfdzsfz;

    /**--------------九 工程质量管理及验收 start----------------
     /**
     * GCZLgcmc	业主方
     */
    private String GCZLgcmc;
    /**
     * GCZLhtbh	合同编号
     */
    private String GCZLhtbh;
    /**
     * GCZLhtmc	合同名称
     */
    private String GCZLhtmc;
    /**--------------九 工程质量管理及验收 end---------------
     *
     */

    /**
     * gczlbxqx	工程质量保修期限
     */
    private String gczlbxqx;
    /**
     * qcfy	甲方代乙方清场费用
     */
    private String qcfy;
    /**
     * lkgdqx	乙方离开甲方工地期限
     */
    private String lkgdqx;
    /**
     * wjbl	逾期未通过验收违约金比例（千分号）
     */
    private String wjbl;

    /**
     * wxsftjfj11	是否添加附件11
     */
    private String wxsftjfj11;
    /**
     * 明细表四
     */
    private List<Map<String,String>> detail;
    /**
     * 明细表8
     */
    private List<Map<String,String>> detail2;
    /**
     * 明细表9
     */
    private List<Map<String,String>> detail3;
    /**
     * 明细表10
     */
    private List<Map<String,String>> detail4;

    public Mode1() {
        this.zzsj="";
        this.dxzzsj = "";
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.wxjfjl = "";
        this.wxjfjldh = "";
        this.wxhjf = "";
        this.wxjfjlyx = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.ywqyfzr = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.fzryxdz = "";
        this.zzzsbh1 = "";
        this.zzzyjdj1 = "";
        this.fzjg1 = "";
        this.yxqstart1 = "";
        this.yxqend1 = "";
        this.xkfw = "";
        this.zzzsbh2 = "";
        this.zzzyjdj2 = "";
        this.fzjg2 = "";
        this.yxqstart2 = "";
        this.yxqend2 = "";
        this.zsmc = "";
        this.zzzsbh3 = "";
        this.zzzyjdj3 = "";
        this.fzjg3 = "";
        this.yxqstart3 = "";
        this.yxqend3 = "";
        this.zsmc2 = "";
        this.zzzsbh4 = "";
        this.zzzyjdj4 = "";
        this.fzjg4 = "";
        this.yxqstart4 = "";
        this.yxqend4 = "";
        this.yjxmmc = "";
        this.xmszsf = "";
        this.fbnr = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.bhsjg = "";
        this.bhsjgDX = "";
        this.slxz = "";
        this.hsjg = "";
        this.hsjgDX = "";
        this.nwbcjs = "";
        this.nwbcgj = "";
        this.fkfs = "";
        this.qtfkfs1 = "";
        this.skhtkhmc = "";
        this.skhtbh = "";
        this.skhtmc = "";
        this.fpzzsl = "";
        this.fplx1 = "";
        this.lybzjjnsj = "";
        this.lybzj = "";
        this.lybzjDX = "";
        this.zhmc = "";
        this.yxkh = "";
        this.khyx = "";
        this.yflwwxdz = "";
        this.yfdzdh = "";
        this.yfdzsfz = "";
        this.gczlbxqx = "";
        this.qcfy = "";
        this.lkgdqx = "";
        this.wxsftjfj11 = "0";
        this.wjbl = "";
        this.lwbcssl = "1";
        this.detail = new ArrayList<>();
        this.detail2 = new ArrayList<>();
        this.detail3 = new ArrayList<>();
        this.detail4 = new ArrayList<>();

        setPath("lwwx");
    }
    public String getZzsj() {
        return zzsj;
    }

    public void setZzsj(String zzsj) {
        this.zzsj = zzsj;
    }

    public String getDxzzsj() {
        return dxzzsj;
    }

    public void setDxzzsj(String dxzzsj) {
        this.dxzzsj = dxzzsj;
    }
    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getWxjfjl() {
        return wxjfjl;
    }

    public void setWxjfjl(String wxjfjl) {
        this.wxjfjl = wxjfjl;
    }

    public String getWxjfjldh() {
        return wxjfjldh;
    }

    public void setWxjfjldh(String wxjfjldh) {
        this.wxjfjldh = wxjfjldh;
    }

    public String getWxhjf() {
        return wxhjf;
    }

    public void setWxhjf(String wxhjf) {
        this.wxhjf = wxhjf;
    }

    public String getWxjfjlyx() {
        return wxjfjlyx;
    }

    public void setWxjfjlyx(String wxjfjlyx) {
        this.wxjfjlyx = wxjfjlyx;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getZzzsbh1() {
        return zzzsbh1;
    }

    public void setZzzsbh1(String zzzsbh1) {
        this.zzzsbh1 = zzzsbh1;
    }

    public String getZzzyjdj1() {
        return zzzyjdj1;
    }

    public void setZzzyjdj1(String zzzyjdj1) {
        this.zzzyjdj1 = zzzyjdj1;
    }

    public String getFzjg1() {
        return fzjg1;
    }

    public void setFzjg1(String fzjg1) {
        this.fzjg1 = fzjg1;
    }

    public String getYxqstart1() {
        return yxqstart1;
    }

    public void setYxqstart1(String yxqstart1) {
        this.yxqstart1 = yxqstart1;
    }

    public String getYxqend1() {
        return yxqend1;
    }

    public void setYxqend1(String yxqend1) {
        this.yxqend1 = yxqend1;
    }

    public String getXkfw() {
        return xkfw;
    }

    public void setXkfw(String xkfw) {
        this.xkfw = xkfw;
    }

    public String getZzzsbh2() {
        return zzzsbh2;
    }

    public void setZzzsbh2(String zzzsbh2) {
        this.zzzsbh2 = zzzsbh2;
    }

    public String getZzzyjdj2() {
        return zzzyjdj2;
    }

    public void setZzzyjdj2(String zzzyjdj2) {
        this.zzzyjdj2 = zzzyjdj2;
    }

    public String getFzjg2() {
        return fzjg2;
    }

    public void setFzjg2(String fzjg2) {
        this.fzjg2 = fzjg2;
    }

    public String getYxqstart2() {
        return yxqstart2;
    }

    public void setYxqstart2(String yxqstart2) {
        this.yxqstart2 = yxqstart2;
    }

    public String getYxqend2() {
        return yxqend2;
    }

    public void setYxqend2(String yxqend2) {
        this.yxqend2 = yxqend2;
    }

    public String getZsmc() {
        return zsmc;
    }

    public void setZsmc(String zsmc) {
        this.zsmc = zsmc;
    }

    public String getZzzsbh3() {
        return zzzsbh3;
    }

    public void setZzzsbh3(String zzzsbh3) {
        this.zzzsbh3 = zzzsbh3;
    }

    public String getZzzyjdj3() {
        return zzzyjdj3;
    }

    public void setZzzyjdj3(String zzzyjdj3) {
        this.zzzyjdj3 = zzzyjdj3;
    }

    public String getFzjg3() {
        return fzjg3;
    }

    public void setFzjg3(String fzjg3) {
        this.fzjg3 = fzjg3;
    }

    public String getYxqstart3() {
        return yxqstart3;
    }

    public void setYxqstart3(String yxqstart3) {
        this.yxqstart3 = yxqstart3;
    }

    public String getYxqend3() {
        return yxqend3;
    }

    public void setYxqend3(String yxqend3) {
        this.yxqend3 = yxqend3;
    }

    public String getZsmc2() {
        return zsmc2;
    }

    public void setZsmc2(String zsmc2) {
        this.zsmc2 = zsmc2;
    }

    public String getZzzsbh4() {
        return zzzsbh4;
    }

    public void setZzzsbh4(String zzzsbh4) {
        this.zzzsbh4 = zzzsbh4;
    }

    public String getZzzyjdj4() {
        return zzzyjdj4;
    }

    public void setZzzyjdj4(String zzzyjdj4) {
        this.zzzyjdj4 = zzzyjdj4;
    }

    public String getFzjg4() {
        return fzjg4;
    }

    public void setFzjg4(String fzjg4) {
        this.fzjg4 = fzjg4;
    }

    public String getYxqstart4() {
        return yxqstart4;
    }

    public void setYxqstart4(String yxqstart4) {
        this.yxqstart4 = yxqstart4;
    }

    public String getYxqend4() {
        return yxqend4;
    }

    public void setYxqend4(String yxqend4) {
        this.yxqend4 = yxqend4;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getXmszsf() {
        return xmszsf;
    }

    public void setXmszsf(String xmszsf) {
        this.xmszsf = xmszsf;
    }

    public String getFbnr() {
        return fbnr;
    }

    public void setFbnr(String fbnr) {
        this.fbnr = fbnr;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getBhsjg() {
        return bhsjg;
    }

    public void setBhsjg(String bhsjg) {
        this.bhsjg = bhsjg;
    }

    public String getBhsjgDX() {
        return bhsjgDX;
    }

    public void setBhsjgDX(String bhsjgDX) {
        this.bhsjgDX = bhsjgDX;
    }

    public String getSlxz() {
        return slxz;
    }

    public void setSlxz(String slxz) {
        this.slxz = slxz;
    }

    public String getHsjg() {
        return hsjg;
    }

    public void setHsjg(String hsjg) {
        this.hsjg = hsjg;
    }

    public String getHsjgDX() {
        return hsjgDX;
    }

    public void setHsjgDX(String hsjgDX) {
        this.hsjgDX = hsjgDX;
    }

    public String getNwbcjs() {
        return nwbcjs;
    }

    public void setNwbcjs(String nwbcjs) {
        this.nwbcjs = nwbcjs;
    }

    public String getNwbcgj() {
        return nwbcgj;
    }

    public void setNwbcgj(String nwbcgj) {
        this.nwbcgj = nwbcgj;
    }

    public String getNwbcgjDX() {
        return nwbcgjDX;
    }

    public void setNwbcgjDX(String nwbcgjDX) {
        this.nwbcgjDX = nwbcgjDX;
    }

    public String getFkfs() {
        return fkfs;
    }

    public void setFkfs(String fkfs) {
        this.fkfs = fkfs;
    }

    public String getQtfkfs1() {
        return qtfkfs1;
    }

    public void setQtfkfs1(String qtfkfs1) {
        this.qtfkfs1 = qtfkfs1;
    }

    public String getSkhtkhmc() {
        return skhtkhmc;
    }

    public void setSkhtkhmc(String skhtkhmc) {
        this.skhtkhmc = skhtkhmc;
    }

    public String getSkhtbh() {
        return skhtbh;
    }

    public void setSkhtbh(String skhtbh) {
        this.skhtbh = skhtbh;
    }

    public String getSkhtmc() {
        return skhtmc;
    }

    public void setSkhtmc(String skhtmc) {
        this.skhtmc = skhtmc;
    }

    public String getFpzzsl() {
        return fpzzsl;
    }

    public void setFpzzsl(String fpzzsl) {
        this.fpzzsl = fpzzsl;
    }

    public String getFplx1() {
        return fplx1;
    }

    public void setFplx1(String fplx1) {
        this.fplx1 = fplx1;
    }

    public String getLybzjjnsj() {
        return lybzjjnsj;
    }

    public void setLybzjjnsj(String lybzjjnsj) {
        this.lybzjjnsj = lybzjjnsj;
    }

    public String getLybzj() {
        return lybzj;
    }

    public void setLybzj(String lybzj) {
        this.lybzj = lybzj;
    }

    public String getLybzjDX() {
        return lybzjDX;
    }

    public void setLybzjDX(String lybzjDX) {
        this.lybzjDX = lybzjDX;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYflwwxdz() {
        return yflwwxdz;
    }

    public void setYflwwxdz(String yflwwxdz) {
        this.yflwwxdz = yflwwxdz;
    }

    public String getYfdzdh() {
        return yfdzdh;
    }

    public void setYfdzdh(String yfdzdh) {
        this.yfdzdh = yfdzdh;
    }

    public String getYfdzsfz() {
        return yfdzsfz;
    }

    public void setYfdzsfz(String yfdzsfz) {
        this.yfdzsfz = yfdzsfz;
    }

    public String getGCZLgcmc() {
        return GCZLgcmc;
    }

    public void setGCZLgcmc(String GCZLgcmc) {
        this.GCZLgcmc = GCZLgcmc;
    }

    public String getGCZLhtbh() {
        return GCZLhtbh;
    }

    public void setGCZLhtbh(String GCZLhtbh) {
        this.GCZLhtbh = GCZLhtbh;
    }

    public String getGCZLhtmc() {
        return GCZLhtmc;
    }

    public void setGCZLhtmc(String GCZLhtmc) {
        this.GCZLhtmc = GCZLhtmc;
    }

    public String getGczlbxqx() {
        return gczlbxqx;
    }

    public void setGczlbxqx(String gczlbxqx) {
        this.gczlbxqx = gczlbxqx;
    }

    public String getQcfy() {
        return qcfy;
    }

    public void setQcfy(String qcfy) {
        this.qcfy = qcfy;
    }

    public String getLkgdqx() {
        return lkgdqx;
    }

    public void setLkgdqx(String lkgdqx) {
        this.lkgdqx = lkgdqx;
    }

    public String getWjbl() {
        return wjbl;
    }

    public void setWjbl(String wjbl) {
        this.wjbl = wjbl;
    }

    public String getWxsftjfj11() {
        return wxsftjfj11;
    }

    public void setWxsftjfj11(String wxsftjfj11) {
        this.wxsftjfj11 = wxsftjfj11;
    }

    public List<Map<String, String>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, String>> detail) {
        this.detail = detail;
    }

    public List<Map<String, String>> getDetail2() {
        return detail2;
    }

    public void setDetail2(List<Map<String, String>> detail2) {
        this.detail2 = detail2;
    }

    public List<Map<String, String>> getDetail3() {
        return detail3;
    }

    public void setDetail3(List<Map<String, String>> detail3) {
        this.detail3 = detail3;
    }

    public List<Map<String, String>> getDetail4() {
        return detail4;
    }

    public void setDetail4(List<Map<String, String>> detail4) {
        this.detail4 = detail4;
    }

    public String getLwbcssl() {
        return lwbcssl;
    }

    public void setLwbcssl(String lwbcssl) {
        this.lwbcssl = lwbcssl;
    }
}