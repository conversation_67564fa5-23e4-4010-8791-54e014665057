package weaver.interfaces.workflow.action.cyitce.doc.Action;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.Mode12;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 技术项目服务框架合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-01-26  13:39
 * @Version: 1.0
 */
public class JSXMFWToElectronDocAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String table = requestInfo.getRequestManager().getBillTableName();
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
        ConverToChinesePart cp = null;
        Mode12 m = null;
        String name = "技术项目服务框架合同";
        RecordSet rs = null;
        RecordSetTrans rst = null;
        Row[] r = null;
        Cell[] c = null;
        ModeToWord mwt = null;

        try {
            cp = new ConverToChinesePart();
            m = new Mode12();
            rs = new RecordSet();
            rst = new RecordSetTrans();
            mwt = new ModeToWord();

            String sql = "select * from "+table+" where requestid="+requestInfo.getRequestid();
            rs.execute(sql);
            rs.next();

            m.setHtbh(Util.null2String(rs.getString("htbh")));
            m.setRjxmmc(Util.null2String(rs.getString("rjxmmc")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(rs.getString("gysmc")));
            m.setQdsj(JoinFieldManage.dateStringToString(rs.getString("qdsj")));
            m.setFb(JoinFieldManage.getCompanyName(Util.null2String(rs.getString("fb")),"subcompanyname"));
            m.setTyshxydmczf(Util.null2String(rs.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(rs.getString("zsdjf")));
            m.setDzyxjf(Util.null2String(rs.getString("dzyxjf")));
            m.setLxdhjf(Util.null2String(rs.getString("lxdhjf")));
            m.setSfzhmtyshxydmczf(Util.null2String(rs.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(rs.getString("lxdzyf")));
            m.setFzryxdz(Util.null2String(rs.getString("fzryxdz")));
            m.setFzrlxdh(Util.null2String(rs.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(rs.getString("wxhyf")));
            m.setYjxmmc(JoinFieldManage.getProjectName(rs.getString("yjxmmc")));
            m.setXmszsf(JoinFieldManage.getProvinceName(rs.getString("xmszsf")));
            m.setMx5hszj(Util.null2String(rs.getString("mx5hszj")));
            m.setMx5hszjDX(cp.convertToChinese(rs.getString("mx5hszj")));
            m.setJsfwnrxx(Util.null2String(rs.getString("jsfwnrxx")));
            m.setXqwxn(Util.null2String(rs.getString("xqwxn")));
            m.setHtkssj(JoinFieldManage.dateStringToString(rs.getString("htkssj")));
            m.setHtzzsj(JoinFieldManage.dateStringToString(rs.getString("htzzsj")));
            m.setYfsmqrje(Util.null2String(rs.getString("yfsmqrje")));
            m.setYfdjezf(Util.null2String(rs.getString("yfdjezf")));
            m.setYfzxdje(Util.null2String(rs.getString("yfzxdje")));
            m.setFkfsrj(Util.null2String(rs.getString("fkfsrj")));
            m.setQtfkfsjsfw(Util.null2String(rs.getString("qtfkfsjsfw")));
            m.setDsfmc(Util.null2String(rs.getString("dsfmc")));
            m.setGsnsrsbh(Util.null2String(rs.getString("gsnsrsbh")));
            m.setZhmc(Util.null2String(rs.getString("zhmc")));
            m.setGsnsrsbh(Util.null2String(rs.getString("gsnsrsbh")));
            m.setYxkh(Util.null2String(rs.getString("yxkh")));
            m.setKhyx(Util.null2String(rs.getString("khyx")));
            m.setRjlybzj(Util.null2String(rs.getString("rjlybzj")));
            m.setRjlybzjDX(cp.convertToChinese(rs.getString("rjlybzj")));
            m.setSldx(JoinFieldManage.getSelectName("sldx",table,rs.getString("sldx")));
            m.setFplx(JoinFieldManage.getSelectName("fplx",table,rs.getString("fplx")));

            r = detailTable[4].getRow();
            writeLog("99999 "+r.toString());
            for (int i = 0;i<r.length;++i){
                Map<String,String> map = new HashMap<>();
                c = r[i].getCell();
                map.put("id",String.valueOf(i+1));
                for (int j = 0;j<c.length;++j){
                    writeLog(c[j].getName()+"---"+c[j].getValue());
                    if("sl".equals(c[j].getName())){
                        map.put(c[j].getName(),JoinFieldManage.getSelectName(c[j].getName(),table,c[j].getValue()));
                    }else {
                        map.put(c[j].getName(),c[j].getValue());
                    }
                }
                m.getDetail().add(map);
            }

            //甲乙方盖章
            int len = 10;

            if(m.getFb().length()>len){
                m.setFbLeft(Util.null2String(m.getFb().substring(0,len)));
                m.setFbRight(Util.null2String(m.getFb().substring(len)));
            }else {
                m.setFbLeft(Util.null2String(m.getFb()));
            }

            len = 8;
            if(m.getGysmc().length()>len){
                m.setGysmcLeft(Util.null2String(m.getGysmc().substring(0,len)));
                m.setGysmcRight(Util.null2String(m.getGysmc().substring(len)));
            }else {
                m.setGysmcLeft(Util.null2String(m.getGysmc()));
            }

            //mode转docx
            String docPath = mwt.toDocx(m,name);
            String fid = FileUtil.uploadToOA(name+".docx",docPath,requestid);
            rst.executeUpdate("update "+table+" set gzfj=? where requestid=?",fid,requestid);

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            writeLog("=====----=====:"+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }
}