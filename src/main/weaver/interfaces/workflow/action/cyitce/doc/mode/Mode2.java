package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 关于规范发票行为及收款账户的不可撤销承诺函
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  11:25
 * @Version: 1.0
 */
public class Mode2 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * 统一社会信用代码 creditCode
     */
    public String creditCode;
    /**
     * 住所地 lxdzyf
     */
    public String lxdzyf;
    /**
     * 电话 fzrlxdh
     */
    public String fzrlxdh;
    /**
     * zhmc	账户名称
     */
    public String zhmc;
    /**
     * yxkh	账号
     */
    public String yxkh;
    /**
     * khyx	开户行
     */
    public String khyx;
    /**
     * ywqyfzr 乙方联系人
     */
    public String ywqyfzr;

    //-------------------------甲方---------------------------------
    /**
     * 甲方（分包方） fb
     */
    public String fb;
    /**
     * 联系人 lxrjf
     */
    public String lxrjf;
    /**
     * 电话 shrdh
     */
    public String lxdhjf;

    public Mode2() {
        setPath("lwwx/fj1");
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getLxrjf() {
        return lxrjf;
    }

    public void setLxrjf(String lxrjf) {
        this.lxrjf = lxrjf;
    }

    public String getLxdhjf() {
        return lxdhjf;
    }

    public void setLxdhjf(String lxdhjf) {
        this.lxdhjf = lxdhjf;
    }

    @Override
    public String toString() {
        return "Mode2{" +
                "gysmc='" + gysmc + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                ", fb='" + fb + '\'' +
                ", lxrjf='" + lxrjf + '\'' +
                ", lxdhjf='" + lxdhjf + '\'' +
                '}';
    }
}