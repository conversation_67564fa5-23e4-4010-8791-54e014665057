package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 安全生产管理协议
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-09  16:05
 * @Version: 1.0
 */
public class Mode13 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * yjxmmc	项目名称
     */
    public String yjxmmc;
    /**
     * 劳务外协-安全生产费-劳务外协合同价款的百分比 securemoneylabor
     */
    public String securemoneylabor;
    /**
     * 劳务外协-安全生产费-乙方安全生产费的百分比 secumoneysupplier
     */
    public String secumoneysupplier;
    /**
     * 劳务外协-补充条款 lwwxbctk
     */
    public String lwwxbctk;
    /**
     * 乙方住所地 lxdzyf
     */
    public String lxdzyf;
    /**
     * 年月日
     */
    public String date;


    public Mode13() {
        setPath("lwwx/fj2/v2");
    }

    @Override
    public String toString() {
        return "Mode13{" +
                "gysmc='" + gysmc + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", securemoneylabor='" + securemoneylabor + '\'' +
                ", secumoneysupplier='" + secumoneysupplier + '\'' +
                ", lwwxbctk='" + lwwxbctk + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", date='" + date + '\'' +
                '}';
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getSecuremoneylabor() {
        return securemoneylabor;
    }

    public void setSecuremoneylabor(String securemoneylabor) {
        this.securemoneylabor = securemoneylabor;
    }

    public String getSecumoneysupplier() {
        return secumoneysupplier;
    }

    public void setSecumoneysupplier(String secumoneysupplier) {
        this.secumoneysupplier = secumoneysupplier;
    }

    public String getLwwxbctk() {
        return lwwxbctk;
    }

    public void setLwwxbctk(String lwwxbctk) {
        this.lwwxbctk = lwwxbctk;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}