package weaver.interfaces.workflow.action.cyitce.doc.export;


import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;
import weaver.interfaces.workflow.action.cyitce.service.buyContract.ContractFileMerg;
import weaver.interfaces.workflow.action.cyitce.util.MathUtils;
import weaver.interfaces.workflow.action.cyitce.util.WordEditUtils;
import weaver.interfaces.workflow.action.cyitce.util.freemarker.FreemarkerCreateDocx;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * @ClassName: 转docx文档
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-07-28  16:34
 */
public class ModeToWord extends ContractFileMerg {
    //文档路径
    private String savePath;

    public ModeToWord() {
        super();
        this.savePath = getMerg_file_path();
    }

    //临时文件路径
    public String getTempPath(){
        return getMerg_file_path()+getName()+"_"+getCount()+".docx";
    }

    /**
     * @description:Mode转文档
     * @author: lijianpan
     * @date: 2023/7/31
     * @param: mode 模板对象
     * @param: name 导出文档名称
     * @return: java.lang.String
     **/
    public String toDocx(CommonMode mode, String name) throws IOException, ExecutionException, InterruptedException {
        String templatePath = mode.getTemplatePath();
        String zipName = templatePath.substring(templatePath.lastIndexOf("/")+1)+".zip";
        String docxPath = savePath + name+".docx";
        writeLog("docxPath:"+docxPath);
        FreemarkerCreateDocx.createDocx(mode,new FileOutputStream(new File(docxPath)),templatePath,zipName);

        List<String> annexs = mode.getAnnexList();

        if(annexs.size()>0){
//            WordEditUtils.MergWordList(docxPath,annexs);
            executirMergDocx(annexs,docxPath);
        }

        return docxPath;
    }

    /**
     * @description:多文件合并为docx
     * @author: lijianpan
     * @date: 2023/9/6
     * @param: [title,ids]
     * @return: java.lang.String
     **/
    public String mergToWord(String title,String ids) throws Exception {
        if("".equals(ids)){
            writeLog("ids 是空值");
            return "";
        }

        String[] idList = ids.split(",");
        List<String> appendixImgPath = new ArrayList<>();
        List<String> appendixWordPath = new ArrayList<>();

        String path = "";
        String dex = "";
        for (String id:idList){
            path = getFilePathById(id);
            dex = path.substring(path.lastIndexOf("."));
            writeLog("文件路径：" + path);

            if(".png;.jpg;.jpge".indexOf(dex)!=-1){
                writeLog("图片");
                appendixImgPath.add(path);
            }else if(".docx;.doc".indexOf(dex)!=-1) {
                writeLog("DOCX");
                appendixWordPath.add(path);
            }else if(".pdf".indexOf(dex)!=-1){
                writeLog("PDF");
//                appendixWordPath.add(pdfToWord(path));
            }
        }

        writeLog("docx合并");
        String path1 = "";
        List<String> paths = new ArrayList<>();
        if(appendixWordPath.size()>1){
            path1 = tempMergFileByPath(appendixWordPath);
            paths.add(path1);
        }else if (appendixWordPath.size()==1){
            paths.add(appendixWordPath.get(0));
        }

        writeLog("图片合并转换为docx");
        String path2 = "";
        if(appendixImgPath.size()>0){
            path2 = imagesMergeToDocx(appendixImgPath);
            paths.add(path2);
        }

        writeLog("最终合并为附件");
        String path0 = "";
        if(paths.size()>1){
            path0 = tempMergFileByPath(paths);
        }else if(paths.size()==1){
            path0 = paths.get(0);
        }else {
            throw new Exception("OA附件下载失败!");
        }

        path0 = WordEditUtils.wordFirstParagraphAddContent(title,path0);

        return path0;
    }

    /**
     * @description:多个图片合并为word
     * @author: lijianpan
     * @date: 2023/9/7
     * @param: [imgPath]
     * @return: java.lang.String
     **/
    public String imagesMergeToDocx(List<String> formPaths) throws IOException {
        String path = WordEditUtils.createDocxMergImage(formPaths,getOut_path()+getName()+"_"+getCount()+".docx");
        countNext();
        return path;
    }

    /**
     * @description:多线程合并docx
     * @author: lijianpan
     * @date: 2023/9/15
     * @param: [paths, outDocx]
     * @return: java.lang.String
     **/
    public String executirMergDocx(List<String> paths,String outDocx) throws ExecutionException, InterruptedException, IOException {
        //线程数
        int threadNumMax = 4;
        int threadNum = paths.size()/2;
        if(threadNum > threadNumMax){
            threadNum = threadNumMax;
        }
        // 创建一个线程池，包含threadNum个线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadNum);
        // 分割数组成threadNum个子数组
        List<List<String>> lists = MathUtils.divideNumbersIntoGroups(paths,threadNum);
        // 创建threadNum个任务，每个任务计算一个子数组的合计
        List<Future<String>> futureList = new ArrayList<>();
        for (int i = 0; i < lists.size(); i++) {
            List<String> groupPaths = lists.get(i);

            DocxMerg task = new DocxMerg(groupPaths,savePath);
            System.out.println("thread "+(i+1));
            futureList.add(executorService.submit(task));
        }

        // 等待所有任务完成并获取它们的计算结果
        List<String> docxList2 = new ArrayList<>();
        for (int j = 0;j < futureList.size(); j++){
            docxList2.add(futureList.get(j).get());
        }

        WordEditUtils.MergWordList(outDocx,docxList2);

        return outDocx;
    }
}

