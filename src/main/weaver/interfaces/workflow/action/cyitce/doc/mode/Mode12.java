package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 技术项目服务框架合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-01-26  13:42
 * @Version: 1.0
 */
public class Mode12 extends CommonMode {
    /**
     * htbh 合同编号
     */
    private String htbh;
    /**
     * rjxmmc	___XX_项目技术服务框架合同
     */
    private String rjxmmc;
    /**
     * gysmc	乙方名称
     */
    private String gysmc;
    /**
     * qdsj	签订时间
     */
    private String qdsj;
    /**
     * fb	甲   方
     */
    private String fb;
    /**
     * tyshxydmczf	统一社会信用代码
     */
    private String tyshxydmczf;
    /**
     * zsdjf	住所地
     */
    private String zsdjf;
    /**
     * dzyxjf	电子邮箱
     */
    private String dzyxjf;
    /**
     * lxdhjf	联系电话
     */
    private String lxdhjf;
    /**
     * sfzhmtyshxydmczf	身份证号码/统一社会信用代码
     */
    private String sfzhmtyshxydmczf;
    /**
     *lxdzyf	住所地
     */
    private String lxdzyf;
    /**
     * fzryxdz	电子邮箱
     */
    private String fzryxdz;
    /**
     * fzrlxdh	联系电话
     */
    private String fzrlxdh;
    /**
     * wxhyf	微信号
     */
    private String wxhyf;
    /**
     * yjxmmc	项目名称
     */
    private String yjxmmc;
    /**
     * xmszsf	项目地点
     */
    private String xmszsf;
    /**
     * mx5hszj	含税总计
     */
    private String mx5hszj;
    /**
     * mx5hszjDX 含税总计大写
     */
    private String mx5hszjDX;
    /**
     * jsfwnrxx	技术服务内容__XX__
     */
    private String jsfwnrxx;
    /**
     * xqwxn	本合同协议下服务成果保修期为  X   年
     */
    private String xqwxn;
    /**
     * htkssj	本协议有效日期开始时间
     */
    private String htkssj;
    /**
     * htzzsj	本协议有效日期结束时间
     */
    private String htzzsj;
    /**
     * yfsmqrje	乙方服务工作完工并经甲方书面确认后按照__X__的金额支付50%
     */
    private String yfsmqrje;
    /**
     * yfdjezf	乙方服务成果通过业主方及甲方初验后按照__X__的金额支付30%
     */
    private String yfdjezf;
    /**
     * yfzxdje	乙方服务成果通过业主方及甲方选定的审计机构审计后按照__X__的金额支付20%
     */
    private String yfzxdje;
    /**
     * fkfsrj	付款方式
     */
    private String fkfsrj;
    /**
     * qtfkfsjsfw 其他付款方式
     */
    private String qtfkfsjsfw;
    /**
     * dsfmc 第三方名称
     */
    private String dsfmc;
    /**
     * gsnsrsbh	国税纳税人识别号
     */
    private String gsnsrsbh;
    /**
     * zhmc	开户名
     */
    private String zhmc;
    /**
     * khyx	开户行
     */
    private String khyx;
    /**
     * yxkh	账号
     */
    private String yxkh;
    /**
     * rjlybzj	履约保证金
     */
    private String rjlybzj;
    /**
     * rjlybzjDX	履约保证金大写
     */
    private String rjlybzjDX;
    /**
     * sldx 税率
     */
    private String sldx;
    /**
     * fplx  发票类型
     */
    private String fplx;
    /**
     * 明细表五
     */
    private List<Map<String,String>> detail;

    /**
     * 甲方（签章）名称前半段
     */
    private String fbLeft;
    /**
     * 甲方（签章）名称后半段
     */
    private String fbRight;
    /**
     * 乙方（签章）名称前半段
     */
    private String gysmcLeft;
    /**
     * 乙方（签章）名称后半段
     */
    private String gysmcRight;

    //附件集合
    private List<ImageInfo> imageInfoList;

    public Mode12() {
        this.htbh = "";
        this.rjxmmc = "";
        this.gysmc = "";
        this.qdsj = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.dzyxjf = "";
        this.lxdhjf = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.fzryxdz = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.yjxmmc = "";
        this.xmszsf = "";
        this.mx5hszj = "";
        this.mx5hszjDX = "";
        this.jsfwnrxx = "";
        this.xqwxn = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.yfsmqrje = "";
        this.yfdjezf = "";
        this.yfzxdje = "";
        this.fkfsrj = "";
        this.qtfkfsjsfw = "";
        this.dsfmc = "";
        this.gsnsrsbh = "";
        this.zhmc = "";
        this.khyx = "";
        this.yxkh = "";
        this.rjlybzj = "";
        this.rjlybzjDX = "";
        this.sldx = "";
        this.fplx = "";
        this.detail = new ArrayList<>();
        this.fbLeft = "";
        this.fbRight = "";
        this.gysmcLeft = "";
        this.gysmcRight = "";
        this.imageInfoList = new ArrayList<>();

        setPath("jsxmfwkjht");
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getRjxmmc() {
        return rjxmmc;
    }

    public void setRjxmmc(String rjxmmc) {
        this.rjxmmc = rjxmmc;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getQdsj() {
        return qdsj;
    }

    public void setQdsj(String qdsj) {
        this.qdsj = qdsj;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getLxdhjf() {
        return lxdhjf;
    }

    public void setLxdhjf(String lxdhjf) {
        this.lxdhjf = lxdhjf;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getXmszsf() {
        return xmszsf;
    }

    public void setXmszsf(String xmszsf) {
        this.xmszsf = xmszsf;
    }

    public String getMx5hszj() {
        return mx5hszj;
    }

    public void setMx5hszj(String mx5hszj) {
        this.mx5hszj = mx5hszj;
    }

    public String getMx5hszjDX() {
        return mx5hszjDX;
    }

    public void setMx5hszjDX(String mx5hszjDX) {
        this.mx5hszjDX = mx5hszjDX;
    }

    public String getJsfwnrxx() {
        return jsfwnrxx;
    }

    public void setJsfwnrxx(String jsfwnrxx) {
        this.jsfwnrxx = jsfwnrxx;
    }

    public String getXqwxn() {
        return xqwxn;
    }

    public void setXqwxn(String xqwxn) {
        this.xqwxn = xqwxn;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getYfsmqrje() {
        return yfsmqrje;
    }

    public void setYfsmqrje(String yfsmqrje) {
        this.yfsmqrje = yfsmqrje;
    }

    public String getYfdjezf() {
        return yfdjezf;
    }

    public void setYfdjezf(String yfdjezf) {
        this.yfdjezf = yfdjezf;
    }

    public String getYfzxdje() {
        return yfzxdje;
    }

    public void setYfzxdje(String yfzxdje) {
        this.yfzxdje = yfzxdje;
    }

    public String getFkfsrj() {
        return fkfsrj;
    }

    public void setFkfsrj(String fkfsrj) {
        this.fkfsrj = fkfsrj;
    }

    public String getQtfkfsjsfw() {
        return qtfkfsjsfw;
    }

    public void setQtfkfsjsfw(String qtfkfsjsfw) {
        this.qtfkfsjsfw = qtfkfsjsfw;
    }

    public String getDsfmc() {
        return dsfmc;
    }

    public void setDsfmc(String dsfmc) {
        this.dsfmc = dsfmc;
    }

    public String getGsnsrsbh() {
        return gsnsrsbh;
    }

    public void setGsnsrsbh(String gsnsrsbh) {
        this.gsnsrsbh = gsnsrsbh;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getRjlybzj() {
        return rjlybzj;
    }

    public void setRjlybzj(String rjlybzj) {
        this.rjlybzj = rjlybzj;
    }

    public String getRjlybzjDX() {
        return rjlybzjDX;
    }

    public void setRjlybzjDX(String rjlybzjDX) {
        this.rjlybzjDX = rjlybzjDX;
    }

    public String getSldx() {
        return sldx;
    }

    public void setSldx(String sldx) {
        this.sldx = sldx;
    }

    public String getFplx() {
        return fplx;
    }

    public void setFplx(String fplx) {
        this.fplx = fplx;
    }

    public List<Map<String, String>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, String>> detail) {
        this.detail = detail;
    }

    public String getFbLeft() {
        return fbLeft;
    }

    public void setFbLeft(String fbLeft) {
        this.fbLeft = fbLeft;
    }

    public String getFbRight() {
        return fbRight;
    }

    public void setFbRight(String fbRight) {
        this.fbRight = fbRight;
    }

    public String getGysmcLeft() {
        return gysmcLeft;
    }

    public void setGysmcLeft(String gysmcLeft) {
        this.gysmcLeft = gysmcLeft;
    }

    public String getGysmcRight() {
        return gysmcRight;
    }

    public void setGysmcRight(String gysmcRight) {
        this.gysmcRight = gysmcRight;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    @Override
    public String toString() {
        return "Mode12{" +
                "htbh='" + htbh + '\'' +
                ", rjxmmc='" + rjxmmc + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", qdsj='" + qdsj + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", lxdhjf='" + lxdhjf + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", xmszsf='" + xmszsf + '\'' +
                ", mx5hszj='" + mx5hszj + '\'' +
                ", mx5hszjDX='" + mx5hszjDX + '\'' +
                ", jsfwnrxx='" + jsfwnrxx + '\'' +
                ", xqwxn='" + xqwxn + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", yfsmqrje='" + yfsmqrje + '\'' +
                ", yfdjezf='" + yfdjezf + '\'' +
                ", yfzxdje='" + yfzxdje + '\'' +
                ", fkfsrj='" + fkfsrj + '\'' +
                ", qtfkfsjsfw='" + qtfkfsjsfw + '\'' +
                ", dsfmc='" + dsfmc + '\'' +
                ", gsnsrsbh='" + gsnsrsbh + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", khyx='" + khyx + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", rjlybzj='" + rjlybzj + '\'' +
                ", rjlybzjDX='" + rjlybzjDX + '\'' +
                ", sldx='" + sldx + '\'' +
                ", fplx='" + fplx + '\'' +
                ", detail=" + detail +
                ", fbLeft='" + fbLeft + '\'' +
                ", fbRight='" + fbRight + '\'' +
                ", gysmcLeft='" + gysmcLeft + '\'' +
                ", gysmcRight='" + gysmcRight + '\'' +
                ", imageInfoList=" + imageInfoList +
                '}';
    }
}