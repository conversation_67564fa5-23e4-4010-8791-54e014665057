package weaver.interfaces.workflow.action.cyitce.doc.export;

import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.cyitce.util.WordEditUtils;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.Callable;

/**
 * @ClassName: 多文档合并docx（多线程）
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-09-15  15:44
 * @Version: 1.0
 */
public class DocxMerg extends BaseBean implements Callable<String> {
    private static String resource;
    private final List<String> array;

    public DocxMerg(List<String> array,String resource) {
        this.resource = resource;
        this.array = array;
    }

    @Override
    public String call() {
        String out = resource + UUID.randomUUID() + ".docx";

        try {
            System.out.println("docx开始合并");
            WordEditUtils.MergWordList(out,array);
        }catch (Exception e){
            e.printStackTrace();
            writeLog(e.getMessage());
            System.out.println(e.getMessage());
            System.out.println("图片转docx失败！");
        }

        return out;
    }
}