package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 廉洁诚信承诺书
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  11:32
 * @Version: 1.0
 */
public class Mode4 extends CommonMode {
    /**
     * htmc	采购合同名称
     */
    public String htmc;
    /**
     * htbh 采购合同编号
     */
    public String htbh;


    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * 联系人（乙方） ywqyfzr
     */
    public String ywqyfzr;

    public Mode4() {
        setPath("lwwx/fj3");
    }

    public String getHtmc() {
        return htmc;
    }

    public void setHtmc(String htmc) {
        this.htmc = htmc;
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getYwqyfzr() {
        return ywqyfzr;
    }

    public void setYwqyfzr(String ywqyfzr) {
        this.ywqyfzr = ywqyfzr;
    }

    @Override
    public String toString() {
        return "Mode4{" +
                "htmc='" + htmc + '\'' +
                ", htbh='" + htbh + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", ywqyfzr='" + ywqyfzr + '\'' +
                '}';
    }
}