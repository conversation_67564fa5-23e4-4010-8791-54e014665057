package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: 房屋租赁合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-10-12  14:18
 * @Version: 1.0
 */
public class Mode9 extends CommonMode {
    /**
     * 采购合同编号
     */
    private String htbh;
    /**
     * 承租方（甲方）--分部
     */
    private String fb;
    /**
     * 统一社会信用代码（甲方）
     */
    private String tyshxydmczf;
    /**
     * 住所地(甲方)
     */
    private String zsdjf;
    /**
     * 电子邮箱（甲方）
     */
    private String dzyxjf;
    /**
     * 联系电话（甲方）---收货人电话
     */
    private String shrdh;
    /**
     * 出租方（乙方）---供应商名称
     */
    private String gysmc;
    /**
     * 身份证号码/统一社会信用代码
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地（乙方）
     */
    private String lxdzyf;
    /**
     * 电子邮箱----供应商邮箱地址
     */
    private String fzryxdz;
    /**
     * 联系电话（乙方）
     */
    private String fzrlxdh;
    /**
     * 微信号（乙方）
     */
    private String wxhyf;
    /**
     * 租赁物地址
     */
    private String zldz;
    /**
     * 建筑面积
     */
    private String jzmj;
    /**
     * 使用性质
     */
    private String fwyt;
    /**
     * 租赁房屋产权证号
     */
    private String zlfwcqzh;
    /**
     * 门窗情况
     */
    private String mcqk;
    /**
     * 地板及墙壁装修情况
     */
    private String dbjqbzxqk;
    /**
     * 水电、消防设施情况
     */
    private String sdxfssqk;
    /**
     * 已有可移动设备情况
     */
    private String yykydsbqk;
    /**
     * 其他
     */
    private String qt;
    /**
     * 租赁期限（单位：月）
     */
    private String zlqxdwy;
    /**
     * 租赁开始日期---合同开始日期
     */
    private String htkssj;
    /**
     * 租赁结束日期----合同终止日期
     */
    private String htzzsj;
    /**
     * 总租金金额----含税价格
     */
    private String hsjg;
    /**
     * 总租金金额（大写）
     */
    private String hsjgdx;
    /**
     * 含税月租金
     */
    private String hsyzj;
    /**
     * 含税月租金（大写）
     */
    private String hsyzjdx;
    /**
     * 免租期（单位：月）
     */
    private String mzqdwy;
    /**
     * 每期租赁时限
     */
    private String zjzffs;
    /**
     * 第一期支付期限
     */
    private String dyqzfqx;
    /**
     * 续租支付要求
     */
    private String xzzfyq;
    /**
     * 续租提前支付天数
     */
    private String xztqzftsdwr;
    /**
     * 物业管理费承担方
     */
    private String wyglfcdf;
    /**
     * 甲方向乙方支付押金时限
     */
    private String jfxyfzfyjsx;
    /**
     * 甲方向乙方支付押金
     */
    private String jfxyfzfyj;
    /**
     * 退还押金期限
     */
    private String thyjqx;
    /**
     * 账户名称（乙方）
     */
    private String zhmc;
    /**
     * 开户银行（乙方）
     */
    private String khyx;
    /**
     * 银行卡号（乙方）
     */
    private String yxkh;
    /**
     * 账户名称（甲方）
     */
    private String zhmcjf;
    /**
     * 开户银行（甲方）
     */
    private String khyxjf;
    /**
     * 银行卡号（甲方）
     */
    private String yxkhjf;
    /**
     * 房屋交付期限
     */
    private String fwjfqxdwr;
    /**
     * 甲方（签章）名称前半段
     */
    private String fbLeft;
    /**
     * 甲方（签章）名称后半段
     */
    private String fbRight;
    /**
     * 乙方（签章）名称前半段
     */
    private String gysmcLeft;
    /**
     * 乙方（签章）名称后半段
     */
    private String gysmcRight;

    /*--------------附件----------------------*/
    /**
     * 附件一-------租赁房屋产权证、出租人身份证/营业执照、产权人授权的相关证明材料
     */
    private List<ImageInfo> zlfwcqz;
    /**
     * 附件二-------租赁房屋内外部照片
     */
    private List<ImageInfo> czrsfzyyzz;
    /**
     * 附件三----------租赁房屋移交清单
     */
    private List<ImageInfo> fjs;

    /*-------------附件集合------------------------*/
    private List<ImageInfo> imageInfoList;

    public Mode9() {
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.dzyxjf = "";
        this.shrdh = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.fzryxdz = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.zldz = "";
        this.jzmj = "";
        this.fwyt = "";
        this.zlfwcqzh = "";
        this.mcqk = "";
        this.dbjqbzxqk = "";
        this.sdxfssqk = "";
        this.yykydsbqk = "";
        this.qt = "";
        this.zlqxdwy = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.hsjg = "";
        this.hsjgdx = "";
        this.hsyzj = "";
        this.hsyzjdx = "";
        this.mzqdwy = "";
        this.zjzffs = "";
        this.dyqzfqx = "";
        this.xzzfyq = "";
        this.xztqzftsdwr = "";
        this.wyglfcdf = "";
        this.jfxyfzfyjsx = "";
        this.jfxyfzfyj = "";
        this.thyjqx = "";
        this.zhmc = "";
        this.khyx = "";
        this.yxkh = "";
        this.zhmcjf = "";
        this.khyxjf = "";
        this.yxkhjf = "";
        this.fwjfqxdwr = "";
        this.fbLeft = "";
        this.fbRight = "";
        this.gysmcLeft = "";
        this.gysmcRight = "";
        this.zlfwcqz = new ArrayList<>();
        this.czrsfzyyzz = new ArrayList<>();
        this.fjs = new ArrayList<>();
        this.imageInfoList = new ArrayList<>();

        setPath("fwzpht");
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getShrdh() {
        return shrdh;
    }

    public void setShrdh(String shrdh) {
        this.shrdh = shrdh;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getZldz() {
        return zldz;
    }

    public void setZldz(String zldz) {
        this.zldz = zldz;
    }

    public String getJzmj() {
        return jzmj;
    }

    public void setJzmj(String jzmj) {
        this.jzmj = jzmj;
    }

    public String getFwyt() {
        return fwyt;
    }

    public void setFwyt(String fwyt) {
        this.fwyt = fwyt;
    }

    public String getZlfwcqzh() {
        return zlfwcqzh;
    }

    public void setZlfwcqzh(String zlfwcqzh) {
        this.zlfwcqzh = zlfwcqzh;
    }

    public String getMcqk() {
        return mcqk;
    }

    public void setMcqk(String mcqk) {
        this.mcqk = mcqk;
    }

    public String getDbjqbzxqk() {
        return dbjqbzxqk;
    }

    public void setDbjqbzxqk(String dbjqbzxqk) {
        this.dbjqbzxqk = dbjqbzxqk;
    }

    public String getSdxfssqk() {
        return sdxfssqk;
    }

    public void setSdxfssqk(String sdxfssqk) {
        this.sdxfssqk = sdxfssqk;
    }

    public String getYykydsbqk() {
        return yykydsbqk;
    }

    public void setYykydsbqk(String yykydsbqk) {
        this.yykydsbqk = yykydsbqk;
    }

    public String getQt() {
        return qt;
    }

    public void setQt(String qt) {
        this.qt = qt;
    }

    public String getZlqxdwy() {
        return zlqxdwy;
    }

    public void setZlqxdwy(String zlqxdwy) {
        this.zlqxdwy = zlqxdwy;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getHsjg() {
        return hsjg;
    }

    public void setHsjg(String hsjg) {
        this.hsjg = hsjg;
    }

    public String getHsjgdx() {
        return hsjgdx;
    }

    public void setHsjgdx(String hsjgdx) {
        this.hsjgdx = hsjgdx;
    }

    public String getHsyzj() {
        return hsyzj;
    }

    public void setHsyzj(String hsyzj) {
        this.hsyzj = hsyzj;
    }

    public String getHsyzjdx() {
        return hsyzjdx;
    }

    public void setHsyzjdx(String hsyzjdx) {
        this.hsyzjdx = hsyzjdx;
    }

    public String getMzqdwy() {
        return mzqdwy;
    }

    public void setMzqdwy(String mzqdwy) {
        this.mzqdwy = mzqdwy;
    }

    public String getZjzffs() {
        return zjzffs;
    }

    public void setZjzffs(String zjzffs) {
        this.zjzffs = zjzffs;
    }

    public String getDyqzfqx() {
        return dyqzfqx;
    }

    public void setDyqzfqx(String dyqzfqx) {
        this.dyqzfqx = dyqzfqx;
    }

    public String getXzzfyq() {
        return xzzfyq;
    }

    public void setXzzfyq(String xzzfyq) {
        this.xzzfyq = xzzfyq;
    }

    public String getXztqzftsdwr() {
        return xztqzftsdwr;
    }

    public void setXztqzftsdwr(String xztqzftsdwr) {
        this.xztqzftsdwr = xztqzftsdwr;
    }

    public String getWyglfcdf() {
        return wyglfcdf;
    }

    public void setWyglfcdf(String wyglfcdf) {
        this.wyglfcdf = wyglfcdf;
    }

    public String getJfxyfzfyjsx() {
        return jfxyfzfyjsx;
    }

    public void setJfxyfzfyjsx(String jfxyfzfyjsx) {
        this.jfxyfzfyjsx = jfxyfzfyjsx;
    }

    public String getJfxyfzfyj() {
        return jfxyfzfyj;
    }

    public void setJfxyfzfyj(String jfxyfzfyj) {
        this.jfxyfzfyj = jfxyfzfyj;
    }

    public String getThyjqx() {
        return thyjqx;
    }

    public void setThyjqx(String thyjqx) {
        this.thyjqx = thyjqx;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getZhmcjf() {
        return zhmcjf;
    }

    public void setZhmcjf(String zhmcjf) {
        this.zhmcjf = zhmcjf;
    }

    public String getKhyxjf() {
        return khyxjf;
    }

    public void setKhyxjf(String khyxjf) {
        this.khyxjf = khyxjf;
    }

    public String getYxkhjf() {
        return yxkhjf;
    }

    public void setYxkhjf(String yxkhjf) {
        this.yxkhjf = yxkhjf;
    }

    public String getFwjfqxdwr() {
        return fwjfqxdwr;
    }

    public void setFwjfqxdwr(String fwjfqxdwr) {
        this.fwjfqxdwr = fwjfqxdwr;
    }

    public String getFbLeft() {
        return fbLeft;
    }

    public void setFbLeft(String fbLeft) {
        this.fbLeft = fbLeft;
    }

    public String getFbRight() {
        return fbRight;
    }

    public void setFbRight(String fbRight) {
        this.fbRight = fbRight;
    }

    public String getGysmcLeft() {
        return gysmcLeft;
    }

    public void setGysmcLeft(String gysmcLeft) {
        this.gysmcLeft = gysmcLeft;
    }

    public String getGysmcRight() {
        return gysmcRight;
    }

    public void setGysmcRight(String gysmcRight) {
        this.gysmcRight = gysmcRight;
    }

    public List<ImageInfo> getZlfwcqz() {
        return zlfwcqz;
    }

    public void setZlfwcqz(List<ImageInfo> zlfwcqz) {
        this.zlfwcqz = zlfwcqz;
    }

    public List<ImageInfo> getCzrsfzyyzz() {
        return czrsfzyyzz;
    }

    public void setCzrsfzyyzz(List<ImageInfo> czrsfzyyzz) {
        this.czrsfzyyzz = czrsfzyyzz;
    }

    public List<ImageInfo> getFjs() {
        return fjs;
    }

    public void setFjs(List<ImageInfo> fjs) {
        this.fjs = fjs;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    @Override
    public String toString() {
        return "Mode9{" +
                "htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", shrdh='" + shrdh + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", zldz='" + zldz + '\'' +
                ", jzmj='" + jzmj + '\'' +
                ", fwyt='" + fwyt + '\'' +
                ", zlfwcqzh='" + zlfwcqzh + '\'' +
                ", mcqk='" + mcqk + '\'' +
                ", dbjqbzxqk='" + dbjqbzxqk + '\'' +
                ", sdxfssqk='" + sdxfssqk + '\'' +
                ", yykydsbqk='" + yykydsbqk + '\'' +
                ", qt='" + qt + '\'' +
                ", zlqxdwy='" + zlqxdwy + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", hsjg='" + hsjg + '\'' +
                ", hsjgdx='" + hsjgdx + '\'' +
                ", hsyzj='" + hsyzj + '\'' +
                ", hsyzjdx='" + hsyzjdx + '\'' +
                ", mzqdwy='" + mzqdwy + '\'' +
                ", zjzffs='" + zjzffs + '\'' +
                ", dyqzfqx='" + dyqzfqx + '\'' +
                ", xzzfyq='" + xzzfyq + '\'' +
                ", xztqzftsdwr='" + xztqzftsdwr + '\'' +
                ", wyglfcdf='" + wyglfcdf + '\'' +
                ", jfxyfzfyjsx='" + jfxyfzfyjsx + '\'' +
                ", jfxyfzfyj='" + jfxyfzfyj + '\'' +
                ", thyjqx='" + thyjqx + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", khyx='" + khyx + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", zhmcjf='" + zhmcjf + '\'' +
                ", khyxjf='" + khyxjf + '\'' +
                ", yxkhjf='" + yxkhjf + '\'' +
                ", fwjfqxdwr='" + fwjfqxdwr + '\'' +
                ", fbLeft='" + fbLeft + '\'' +
                ", fbRight='" + fbRight + '\'' +
                ", gysmcLeft='" + gysmcLeft + '\'' +
                ", gysmcRight='" + gysmcRight + '\'' +
                ", zlfwcqz=" + zlfwcqz +
                ", czrsfzyyzz=" + czrsfzyyzz +
                ", fjs=" + fjs +
                ", imageInfoList=" + imageInfoList +
                '}';
    }
}