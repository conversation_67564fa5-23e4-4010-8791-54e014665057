package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 项目安全生产与文明施工管理承诺书
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  11:30
 * @Version: 1.0
 */
public class Mode3 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * 统一社会信用代码 creditCode
     */
    public String creditCode;
    /**
     * yjxmmc	项目名称
     */
    public String yjxmmc;

    public Mode3() {
        setPath("lwwx/fj2");
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    @Override
    public String toString() {
        return "Mode3{" +
                "gysmc='" + gysmc + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                '}';
    }
}