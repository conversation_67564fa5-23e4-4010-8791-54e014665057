package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 车辆租赁合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  13:50
 * @Version: 1.0
 */
public class Mode8 extends CommonMode {
    /**
     * 合同编号
     */
    private String htbh;
    /**
     * 承租方（甲方）
     *
     */
    private String fb;
    /**
     * 统一社会信用代码
     */
    private String tyshxydmczf;
    /**
     * 住所地（甲方）
     */
    private String zsdjf;
    /**
     * 电子邮箱（甲方）
     */
    private String dzyxjf;
    /**
     * 联系电话（甲方）----收货人电话（shrdh）
     */
    private String shrdh;
    /**
     * 出租方（乙方） 供应商
     */
    private String gysmc;
    /**
     * 身份证号码/统一社会信用代码
     */
    private String sfzhmtyshxydmczf;
    /**
     * 住所地（乙方）
     */
    private String lxdzyf;
    /**
     * 电子邮箱（乙方）-----供应商邮箱地址
     */
    private String fzryxdz;
    /**
     * 联系电话（乙方）------供应商联系电话（）
     */
    private String fzrlxdh;
    /**
     * 微信号（乙方）
     */
    private String wxhyf;
    /**
     * 租赁期限（单位：月）
     */
    private String zlqxdwy;
    /**
     * 租赁开始日期
     * 格式：年月日
     * 对应流程字段：合同开始时间
     */
    private String htkssj;
    /**
     * 租赁结束日期
     * 格式：年月日
     * 对应流程字段：合同终止时间
     */
    private String htzzsj;
    /**
     * 租赁模式及支付方式
     */
    private String zlmsjzffs;
    /**
     * 是否带驾驶员
     */
    private String sfdjsy;
    /**
     * 包干模式
     */
    private String bgms;
    /**
     * 包干金额
     */
    private String bgje;
    /**
     * 包干是否含税
     */
    private String bgsfhs;
    /**
     * 包干价含税税率
     */
    private String bgjhssl;
    /**
     * 不包干模式
     */
    private String bbgms;
    /**
     * 不包干模式基本租金
     */
    private String bbgmsjbzj;
    /**
     * 驾驶员服务费模式
     */
    private String jsyfwfms;
    /**
     * 驾驶员服务费
     */
    private String jsyrgf;
    /**
     * 不包干模式其他费用承担方
     */
    private String bbgmsqtfycdr;
    /**
     * 油费计价方式
     */
    private String bbgmsqtfyzyfjjfs;
    /**
     * 不包干是否含税
     */
    private String bbgsfhs;
    /**
     * 不包干价含税税率
     */
    private String bbgjhssl;
    /**
     * 乙方开具发票类型
     */
    private String yfkjfplx;
    /**
     * 乙方开具发票税率
     */
    private String yfkjfpsl;
    /**
     * 租车押金
     */
    private String zcyj;
    /**
     * 押金支付在【】工作日内
     */
    private String yjzfsj;
    /**
     * 甲方延长或缩短租赁，提前【】日通知乙方
     */
    private String tqtzts;
    /**
     * 账户名称（乙方）
     */
    private String zhmc;
    /**
     * 银行卡号（乙方）
     */
    private String yxkh;
    /**
     * 开户银行（乙方）
     */
    private String khyx;
    /**
     * 甲方（签章）名称前半段
     */
    private String fbLeft;
    /**
     * 甲方（签章）名称后半段
     */
    private String fbRight;
    /**
     * 乙方（签章）名称前半段
     */
    private String gysmcLeft;
    /**
     * 乙方（签章）名称后半段
     */
    private String gysmcRight;
    /**
     * 明细二
     * 租赁车辆清单
     */
    private List<Map<String,Object>> detail;

    /**
     * 明细三
     * 驾驶员信息登记表
     */
    private List<Map<String,String>> detail2;

    /*---------------附件--------------------*/
    /**
     * 附件二
     */
    private List<ImageInfo> czrsfzyyzz;
    /**
     * 附件三
     */
    private List<ImageInfo> fjs;
    /**
     * 附件五
     */
    private List<ImageInfo> fjw;


    /*----------- 附件集合 ----------------------*/
    private List<ImageInfo> imageInfoList;

    public Mode8() {
        this.htbh = "";
        this.fb = "";
        this.tyshxydmczf = "";
        this.zsdjf = "";
        this.dzyxjf = "";
        this.shrdh = "";
        this.gysmc = "";
        this.sfzhmtyshxydmczf = "";
        this.lxdzyf = "";
        this.fzryxdz = "";
        this.fzrlxdh = "";
        this.wxhyf = "";
        this.zlqxdwy = "";
        this.htkssj = "";
        this.htzzsj = "";
        this.zlmsjzffs = "";
        this.sfdjsy = "";
        this.bgms = "";
        this.bgje = "";
        this.bgsfhs = "";
        this.bgjhssl = "";
        this.bbgms = "";
        this.bbgmsjbzj = "";
        this.jsyfwfms = "";
        this.jsyrgf = "";
        this.bbgmsqtfycdr = "";
        this.bbgmsqtfyzyfjjfs = "";
        this.bbgsfhs = "";
        this.bbgjhssl = "";
        this.yfkjfplx = "";
        this.yfkjfpsl = "";
        this.zcyj = "";
        this.yjzfsj = "";
        this.tqtzts = "";
        this.zhmc = "";
        this.yxkh = "";
        this.khyx = "";
        this.fbLeft = "";
        this.fbRight = "";
        this.gysmcLeft = "";
        this.gysmcRight = "";
        this.detail = new ArrayList<>();
        this.detail2 = new ArrayList<>();
        this.czrsfzyyzz = new ArrayList<>();
        this.fjs = new ArrayList<>();
        this.fjw = new ArrayList<>();
        this.imageInfoList = new ArrayList<>();

        setPath("clzpht");
    }

    public String getHtbh() {
        return htbh;
    }

    public void setHtbh(String htbh) {
        this.htbh = htbh;
    }

    public String getFb() {
        return fb;
    }

    public void setFb(String fb) {
        this.fb = fb;
    }

    public String getTyshxydmczf() {
        return tyshxydmczf;
    }

    public void setTyshxydmczf(String tyshxydmczf) {
        this.tyshxydmczf = tyshxydmczf;
    }

    public String getZsdjf() {
        return zsdjf;
    }

    public void setZsdjf(String zsdjf) {
        this.zsdjf = zsdjf;
    }

    public String getDzyxjf() {
        return dzyxjf;
    }

    public void setDzyxjf(String dzyxjf) {
        this.dzyxjf = dzyxjf;
    }

    public String getShrdh() {
        return shrdh;
    }

    public void setShrdh(String shrdh) {
        this.shrdh = shrdh;
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getSfzhmtyshxydmczf() {
        return sfzhmtyshxydmczf;
    }

    public void setSfzhmtyshxydmczf(String sfzhmtyshxydmczf) {
        this.sfzhmtyshxydmczf = sfzhmtyshxydmczf;
    }

    public String getLxdzyf() {
        return lxdzyf;
    }

    public void setLxdzyf(String lxdzyf) {
        this.lxdzyf = lxdzyf;
    }

    public String getFzryxdz() {
        return fzryxdz;
    }

    public void setFzryxdz(String fzryxdz) {
        this.fzryxdz = fzryxdz;
    }

    public String getFzrlxdh() {
        return fzrlxdh;
    }

    public void setFzrlxdh(String fzrlxdh) {
        this.fzrlxdh = fzrlxdh;
    }

    public String getWxhyf() {
        return wxhyf;
    }

    public void setWxhyf(String wxhyf) {
        this.wxhyf = wxhyf;
    }

    public String getZlqxdwy() {
        return zlqxdwy;
    }

    public void setZlqxdwy(String zlqxdwy) {
        this.zlqxdwy = zlqxdwy;
    }

    public String getHtkssj() {
        return htkssj;
    }

    public void setHtkssj(String htkssj) {
        this.htkssj = htkssj;
    }

    public String getHtzzsj() {
        return htzzsj;
    }

    public void setHtzzsj(String htzzsj) {
        this.htzzsj = htzzsj;
    }

    public String getZlmsjzffs() {
        return zlmsjzffs;
    }

    public void setZlmsjzffs(String zlmsjzffs) {
        this.zlmsjzffs = zlmsjzffs;
    }

    public String getSfdjsy() {
        return sfdjsy;
    }

    public void setSfdjsy(String sfdjsy) {
        this.sfdjsy = sfdjsy;
    }

    public String getBgms() {
        return bgms;
    }

    public void setBgms(String bgms) {
        this.bgms = bgms;
    }

    public String getBgje() {
        return bgje;
    }

    public void setBgje(String bgje) {
        this.bgje = bgje;
    }

    public String getBgsfhs() {
        return bgsfhs;
    }

    public void setBgsfhs(String bgsfhs) {
        this.bgsfhs = bgsfhs;
    }

    public String getBgjhssl() {
        return bgjhssl;
    }

    public void setBgjhssl(String bgjhssl) {
        this.bgjhssl = bgjhssl;
    }

    public String getBbgms() {
        return bbgms;
    }

    public void setBbgms(String bbgms) {
        this.bbgms = bbgms;
    }

    public String getBbgmsjbzj() {
        return bbgmsjbzj;
    }

    public void setBbgmsjbzj(String bbgmsjbzj) {
        this.bbgmsjbzj = bbgmsjbzj;
    }

    public String getJsyfwfms() {
        return jsyfwfms;
    }

    public void setJsyfwfms(String jsyfwfms) {
        this.jsyfwfms = jsyfwfms;
    }

    public String getJsyrgf() {
        return jsyrgf;
    }

    public void setJsyrgf(String jsyrgf) {
        this.jsyrgf = jsyrgf;
    }

    public String getBbgmsqtfycdr() {
        return bbgmsqtfycdr;
    }

    public void setBbgmsqtfycdr(String bbgmsqtfycdr) {
        this.bbgmsqtfycdr = bbgmsqtfycdr;
    }

    public String getBbgmsqtfyzyfjjfs() {
        return bbgmsqtfyzyfjjfs;
    }

    public void setBbgmsqtfyzyfjjfs(String bbgmsqtfyzyfjjfs) {
        this.bbgmsqtfyzyfjjfs = bbgmsqtfyzyfjjfs;
    }

    public String getBbgsfhs() {
        return bbgsfhs;
    }

    public void setBbgsfhs(String bbgsfhs) {
        this.bbgsfhs = bbgsfhs;
    }

    public String getBbgjhssl() {
        return bbgjhssl;
    }

    public void setBbgjhssl(String bbgjhssl) {
        this.bbgjhssl = bbgjhssl;
    }

    public String getYfkjfplx() {
        return yfkjfplx;
    }

    public void setYfkjfplx(String yfkjfplx) {
        this.yfkjfplx = yfkjfplx;
    }

    public String getYfkjfpsl() {
        return yfkjfpsl;
    }

    public void setYfkjfpsl(String yfkjfpsl) {
        this.yfkjfpsl = yfkjfpsl;
    }

    public String getZcyj() {
        return zcyj;
    }

    public void setZcyj(String zcyj) {
        this.zcyj = zcyj;
    }

    public String getYjzfsj() {
        return yjzfsj;
    }

    public void setYjzfsj(String yjzfsj) {
        this.yjzfsj = yjzfsj;
    }

    public String getTqtzts() {
        return tqtzts;
    }

    public void setTqtzts(String tqtzts) {
        this.tqtzts = tqtzts;
    }

    public String getZhmc() {
        return zhmc;
    }

    public void setZhmc(String zhmc) {
        this.zhmc = zhmc;
    }

    public String getYxkh() {
        return yxkh;
    }

    public void setYxkh(String yxkh) {
        this.yxkh = yxkh;
    }

    public String getKhyx() {
        return khyx;
    }

    public void setKhyx(String khyx) {
        this.khyx = khyx;
    }

    public String getFbLeft() {
        return fbLeft;
    }

    public void setFbLeft(String fbLeft) {
        this.fbLeft = fbLeft;
    }

    public String getFbRight() {
        return fbRight;
    }

    public void setFbRight(String fbRight) {
        this.fbRight = fbRight;
    }

    public String getGysmcLeft() {
        return gysmcLeft;
    }

    public void setGysmcLeft(String gysmcLeft) {
        this.gysmcLeft = gysmcLeft;
    }

    public String getGysmcRight() {
        return gysmcRight;
    }

    public void setGysmcRight(String gysmcRight) {
        this.gysmcRight = gysmcRight;
    }

    public List<Map<String, Object>> getDetail() {
        return detail;
    }

    public void setDetail(List<Map<String, Object>> detail) {
        this.detail = detail;
    }

    public List<Map<String, String>> getDetail2() {
        return detail2;
    }

    public void setDetail2(List<Map<String, String>> detail2) {
        this.detail2 = detail2;
    }

    public List<ImageInfo> getCzrsfzyyzz() {
        return czrsfzyyzz;
    }

    public void setCzrsfzyyzz(List<ImageInfo> czrsfzyyzz) {
        this.czrsfzyyzz = czrsfzyyzz;
    }

    public List<ImageInfo> getFjs() {
        return fjs;
    }

    public void setFjs(List<ImageInfo> fjs) {
        this.fjs = fjs;
    }

    public List<ImageInfo> getFjw() {
        return fjw;
    }

    public void setFjw(List<ImageInfo> fjw) {
        this.fjw = fjw;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    @Override
    public String toString() {
        return "Mode8{" +
                "htbh='" + htbh + '\'' +
                ", fb='" + fb + '\'' +
                ", tyshxydmczf='" + tyshxydmczf + '\'' +
                ", zsdjf='" + zsdjf + '\'' +
                ", dzyxjf='" + dzyxjf + '\'' +
                ", shrdh='" + shrdh + '\'' +
                ", gysmc='" + gysmc + '\'' +
                ", sfzhmtyshxydmczf='" + sfzhmtyshxydmczf + '\'' +
                ", lxdzyf='" + lxdzyf + '\'' +
                ", fzryxdz='" + fzryxdz + '\'' +
                ", fzrlxdh='" + fzrlxdh + '\'' +
                ", wxhyf='" + wxhyf + '\'' +
                ", zlqxdwy='" + zlqxdwy + '\'' +
                ", htkssj='" + htkssj + '\'' +
                ", htzzsj='" + htzzsj + '\'' +
                ", zlmsjzffs='" + zlmsjzffs + '\'' +
                ", sfdjsy='" + sfdjsy + '\'' +
                ", bgms='" + bgms + '\'' +
                ", bgje='" + bgje + '\'' +
                ", bgsfhs='" + bgsfhs + '\'' +
                ", bgjhssl='" + bgjhssl + '\'' +
                ", bbgms='" + bbgms + '\'' +
                ", bbgmsjbzj='" + bbgmsjbzj + '\'' +
                ", jsyfwfms='" + jsyfwfms + '\'' +
                ", jsyrgf='" + jsyrgf + '\'' +
                ", bbgmsqtfycdr='" + bbgmsqtfycdr + '\'' +
                ", bbgmsqtfyzyfjjfs='" + bbgmsqtfyzyfjjfs + '\'' +
                ", bbgsfhs='" + bbgsfhs + '\'' +
                ", bbgjhssl='" + bbgjhssl + '\'' +
                ", yfkjfplx='" + yfkjfplx + '\'' +
                ", yfkjfpsl='" + yfkjfpsl + '\'' +
                ", zcyj='" + zcyj + '\'' +
                ", yjzfsj='" + yjzfsj + '\'' +
                ", tqtzts='" + tqtzts + '\'' +
                ", zhmc='" + zhmc + '\'' +
                ", yxkh='" + yxkh + '\'' +
                ", khyx='" + khyx + '\'' +
                ", fbLeft='" + fbLeft + '\'' +
                ", fbRight='" + fbRight + '\'' +
                ", gysmcLeft='" + gysmcLeft + '\'' +
                ", gysmcRight='" + gysmcRight + '\'' +
                ", detail=" + detail +
                ", detail2=" + detail2 +
                ", czrsfzyyzz=" + czrsfzyyzz +
                ", fjs=" + fjs +
                ", fjw=" + fjw +
                ", imageInfoList=" + imageInfoList +
                '}';
    }
}