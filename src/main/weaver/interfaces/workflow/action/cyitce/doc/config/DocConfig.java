package weaver.interfaces.workflow.action.cyitce.doc.config;

import weaver.interfaces.workflow.action.cyitce.config.BuyContract;

/**
 * @ClassName: doc操作配置
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-01  15:15
 * @Version: 1.0
 */
public class DocConfig {
    //合并转docx文件保存路径
    public static String MERG_SAVE_PATH = BuyContract.outUrl+"/tempDoc/merg";
    //下载文件保存路径
    public static String DOWN_SAVE_PATH = BuyContract.outUrl+"/tempDoc/temp";
    //模板路径
    public static String TEMPLATE_PATH = BuyContract.outUrl+"/template/v3";
}