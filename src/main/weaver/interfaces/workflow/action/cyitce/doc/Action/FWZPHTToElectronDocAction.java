package weaver.interfaces.workflow.action.cyitce.doc.Action;

import com.engine.core.exception.ECException;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.Mode9;
import weaver.interfaces.workflow.action.cyitce.entity.ImageInfo;
import weaver.interfaces.workflow.action.cyitce.service.ImageInfoManage;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: 房屋租赁合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-09-26  16:41
 * @VemainObjeion: 1.0
 */
public class FWZPHTToElectronDocAction extends BaseBean implements Action{
    @Override
    public String execute(RequestInfo requestInfo) {
        RecordSetTrans mainObje = null;
        String table = requestInfo.getRequestManager().getBillTableName();
        ConverToChinesePart cp = null;
        ImageInfoManage im = null;
        ModeToWord mwt = null;
        //需合并的文档路径（按顺序依次合并）
        List<String> mergDocxPathList;

        String tableName = requestInfo.getRequestManager().getBillTableName();
        Mode9 m = null;
        String name = "房屋租赁合同";
        String requestid = null;
        RecordSetTrans rst = null;
        try {
            im = new ImageInfoManage();
            cp = new ConverToChinesePart();
            mwt = new ModeToWord();
            mergDocxPathList = new ArrayList<>();
            m = new Mode9();

            im.init();
            mainObje = new RecordSetTrans();
            mainObje.execute("select * from "+requestInfo.getRequestManager().getBillTableName()+" where requestid="+requestInfo.getRequestid());
            mainObje.next();

            m.setHtbh(Util.null2String(mainObje.getString("htbh")));
            m.setFb(JoinFieldManage.getCompanyName(mainObje.getString("fb"),"subcompanyname"));
            m.setTyshxydmczf(Util.null2String(mainObje.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(mainObje.getString("zsdjf")));
            m.setDzyxjf(Util.null2String(mainObje.getString("dzyxjf")));
            m.setShrdh(Util.null2String(mainObje.getString("shrdh")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
            m.setSfzhmtyshxydmczf(Util.null2String(mainObje.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(mainObje.getString("lxdzyf")));

            m.setFzryxdz(Util.null2String(mainObje.getString("fzryxdz")));
            m.setFzrlxdh(Util.null2String(mainObje.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(mainObje.getString("wxhyf")));
            m.setZldz(Util.null2String(mainObje.getString("zldz")));
            m.setJzmj(Util.null2String(mainObje.getString("jzmj")));
            m.setFwyt(JoinFieldManage.getSelectName("fwyt",tableName,mainObje.getString("fwyt")));
            m.setZlfwcqzh(Util.null2String(mainObje.getString("zlfwcqzh")));
            m.setMcqk(Util.null2String(mainObje.getString("mcqk")));
            m.setDbjqbzxqk(Util.null2String(mainObje.getString("dbjqbzxqk")));
            m.setSdxfssqk(Util.null2String(mainObje.getString("sdxfssqk")));
            m.setYykydsbqk(Util.null2String(mainObje.getString("yykydsbqk")));
            m.setQt(Util.null2String(mainObje.getString("qt")));
            m.setZlqxdwy(Util.null2String(mainObje.getString("zlqxdwy")));
            m.setHtkssj(JoinFieldManage.dateStringToString(mainObje.getString("htkssj")));
            m.setHtzzsj(JoinFieldManage.dateStringToString(mainObje.getString("htzzsj")));
            m.setHsjg(Util.null2String(mainObje.getString("hsjg")));
            writeLog("5555555");
            writeLog("金额转换 1");
            m.setHsjgdx(cp.convertToChinese(mainObje.getString("hsjg")));
            writeLog("金额完成 1");
            m.setHsyzj(Util.null2String(mainObje.getString("hsyzj")));
            writeLog("金额转换 2");
            m.setHsyzjdx(cp.convertToChinese(mainObje.getString("hsyzj")));
            writeLog("金额完成 2");
            m.setMzqdwy(Util.null2String(mainObje.getString("mzqdwy")));
            writeLog("55551111");
            m.setZjzffs(JoinFieldManage.getSelectName("zjzffs",tableName,mainObje.getString("zjzffs")));
            m.setDyqzfqx(Util.null2String(mainObje.getString("dyqzfqx")));
            writeLog("55552222");
            m.setXzzfyq(JoinFieldManage.getSelectName("xzzfyq",tableName,mainObje.getString("xzzfyq")));
            m.setXztqzftsdwr(Util.null2String(mainObje.getString("xztqzftsdwr")));
            m.setWyglfcdf(JoinFieldManage.getSelectName("wyglfcdf",tableName,mainObje.getString("wyglfcdf")).replace("方",""));
            writeLog("55553333");
            m.setJfxyfzfyjsx(Util.null2String(mainObje.getString("jfxyfzfyjsx")));
            m.setJfxyfzfyj(Util.null2String(mainObje.getString("jfxyfzfyj")));
            m.setThyjqx(Util.null2String(mainObje.getString("thyjqx")));
            m.setZhmc(Util.null2String(mainObje.getString("zhmc")));
            m.setKhyx(Util.null2String(mainObje.getString("khyx")));
            m.setYxkh(Util.null2String(mainObje.getString("yxkh")));
            m.setZhmcjf(Util.null2String(mainObje.getString("zhmcjf")));
            m.setKhyxjf(Util.null2String(mainObje.getString("khyxjf")));
            m.setYxkhjf(Util.null2String(mainObje.getString("yxkhjf")));
            m.setFwjfqxdwr(Util.null2String(mainObje.getString("fwjfqxdwr")));

            //附件
            String[] str = null;

            writeLog("获取附件一 字节码");
            String fjValue = Util.null2String(mainObje.getString("zlfwcqz"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getZlfwcqz().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            writeLog("获取附件二 二进制字符串");
            fjValue = Util.null2String(mainObje.getString("czmainObjefzyyzz"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getCzrsfzyyzz().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }

            writeLog("获取附件三 二进制字符串");
            fjValue = Util.null2String(mainObje.getString("fjs"));
            if(!"".equals(fjValue)){
                str = fjValue.split(",");
            }else {
                str = new String[]{};
            }
            for (int i = 0;i<str.length;++i){
                ImageInfo imageInfo = im.getImageInfo(str[i]);
                m.getFjs().add(imageInfo);
                m.getImageInfoList().add(imageInfo);
            }
            writeLog("赋值完成");


            //甲乙方 盖章
            int len = 9;
            if(m.getFb().length()>len){
                m.setFbLeft(Util.null2String(m.getFb().substring(0,len)));
                m.setFbRight(Util.null2String(m.getFb().substring(len)));
            }else {
                m.setFbLeft(Util.null2String(m.getFb()));
            }

            if(m.getGysmc().length()>len){
                m.setGysmcLeft(Util.null2String(m.getGysmc().substring(0,len)));
                m.setGysmcRight(Util.null2String(m.getGysmc().substring(len)));
            }else {
                m.setGysmcLeft(Util.null2String(m.getGysmc()));
            }

            //附件5-10
            try {
                if(!"".equals(Util.null2String(mainObje.getString("fjw")))){
                    mergDocxPathList.add(mwt.mergToWord("附件5",mainObje.getString("fjw")));
                }
                if(!"".equals(Util.null2String(mainObje.getString("fjl")))){
                    mergDocxPathList.add(mwt.mergToWord("附件6",mainObje.getString("fjl")));
                }
                if(!"".equals(Util.null2String(mainObje.getString("fjq")))){
                    mergDocxPathList.add(mwt.mergToWord("附件7",mainObje.getString("fjq")));
                }
                if(!"".equals(Util.null2String(mainObje.getString("fjb")))){
                    mergDocxPathList.add(mwt.mergToWord("附件8",mainObje.getString("fjb")));
                }
                if(!"".equals(Util.null2String(mainObje.getString("fjj")))){
                    mergDocxPathList.add(mwt.mergToWord("附件9",mainObje.getString("fjj")));
                }
                if(!"".equals(Util.null2String(mainObje.getString("fjshi")))){
                    mergDocxPathList.add(mwt.mergToWord("附件10",mainObje.getString("fjshi")));
                }
            }catch (Exception e){
                throw new ECException("OA附件下载失败！",e);
            }

            //mode转docx
            String docPath = mwt.toDocx(m,name);
            String fid = FileUtil.uploadToOA(name+".docx",docPath,requestid);
            rst.executeUpdate("update "+table+" set gzfj=? where requestid=?",fid,requestid);

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            e.printStackTrace();
            writeLog("------------------："+e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }finally {
            //删除临时附件
            im.deleteFile();
        }

        return Action.SUCCESS;
    }
}