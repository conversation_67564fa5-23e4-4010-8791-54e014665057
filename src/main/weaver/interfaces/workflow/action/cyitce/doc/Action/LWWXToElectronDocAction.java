package weaver.interfaces.workflow.action.cyitce.doc.Action;

import com.engine.core.exception.ECException;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.interfaces.workflow.action.cyitce.doc.config.DocConfig;
import weaver.interfaces.workflow.action.cyitce.doc.export.ModeToWord;
import weaver.interfaces.workflow.action.cyitce.doc.mode.*;
import weaver.interfaces.workflow.action.cyitce.util.CommonUtil;
import weaver.interfaces.workflow.action.cyitce.util.ConverToChinesePart;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;
import weaver.soa.workflow.request.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static weaver.interfaces.workflow.action.cyitce.po.contract.CreateBuyContractWordActionTest2.calculateDifference;
import static weaver.interfaces.workflow.action.cyitce.po.contract.CreateBuyContractWordActionTest2.processContract;


/**
 * @ClassName: 劳务外协框架合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-08  15:57
 * @Version: 1.0
 */
public class LWWXToElectronDocAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String lcbh = "";
        String xmmc = "";
        Property[] property = requestInfo.getMainTableInfo().getProperty();
        for (Property p : property) {
            if ("htbh".equals(p.getName())) {
                lcbh = p.getValue();
            }else if("yjxmmc".equals(p.getName())){
                xmmc = JoinFieldManage.getProjectName(p.getValue());
            }
        }
        ConverToChinesePart cp = null;
        RecordSetTrans rst = null;
        Mode1 m = null;
        String name = xmmc + "-" + lcbh + "-" + "劳务外协框架合同";
        String requestid = null;
        RecordSetTrans mainObje = null;
        // 表1
        String table = null;
        Row[] r = null;
        Cell[] c = null;
        DetailTable[] detailTables = null;
        ModeToWord mwt = null;
        // 需合并的文档路径（按顺序依次合并）
        List<String> mergDocxPathList;

        // 合同
        try {
            requestid = requestInfo.getRequestid();
            cp = new ConverToChinesePart();
            rst = new RecordSetTrans();
            m = new Mode1();
            table = requestInfo.getRequestManager().getBillTableName();
            mwt = new ModeToWord();
            mainObje = new RecordSetTrans();
            detailTables = requestInfo.getDetailTableInfo().getDetailTable();
            mergDocxPathList = new ArrayList<>();
            // 当前流程对象
            mainObje.executeQuery("select * from " + requestInfo.getRequestManager().getBillTableName() + " where requestid=?", requestInfo.getRequestid());
            mainObje.next();
            // 主文档路径
            mergDocxPathList.add(mwt.getMerg_file_path() + name + ".docx");
            // 合同信息
            m.setHtbh(Util.null2String(mainObje.getString("htbh")));
            writeLog("合同编号 ：" + Util.null2String(mainObje.getString("htbh")));
            m.setFb(JoinFieldManage.getCompanyName(Util.null2String(mainObje.getString("fb")), "subcompanyname"));
            m.setTyshxydmczf(Util.null2String(mainObje.getString("tyshxydmczf")));
            m.setZsdjf(Util.null2String(mainObje.getString("zsdjf")));
            m.setWxjfjl(JoinFieldManage.getPersonName(mainObje.getString("wxjfjl")));
            m.setWxjfjldh(Util.null2String(mainObje.getString("wxjfjldh")));
            m.setWxhjf(Util.null2String(mainObje.getString("wxhjf")));
            m.setWxjfjlyx(Util.null2String(mainObje.getString("wxjfjlyx")));
            m.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
            m.setSfzhmtyshxydmczf(Util.null2String(mainObje.getString("sfzhmtyshxydmczf")));
            m.setLxdzyf(Util.null2String(mainObje.getString("lxdzyf")));
            m.setYwqyfzr(Util.null2String(mainObje.getString("ywqyfzr")));
            m.setFzrlxdh(Util.null2String(mainObje.getString("fzrlxdh")));
            m.setWxhyf(Util.null2String(mainObje.getString("wxhyf")));
            m.setFzryxdz(Util.null2String(mainObje.getString("fzryxdz")));
            m.setLwbcssl(Util.null2String(mainObje.getString("lwbcssl")));


            // 资质证书（明细7）
            r = detailTables[6].getRow();
            for (int i = 0; i < r.length; i++) {
                Cell[] cs = r[i].getCell();
                Map<String, String> m2 = new HashMap<>();
                for (Cell c1 : cs) {
                    String name2 = c1.getName();
                    String value2 = CommonUtil.nullToStr(c1.getValue(), "/");
                    if (!"/".equals(value2) && ("yxqkssj".equals(name2) || "yxqjssj".equals(name2))) {
                        value2 = JoinFieldManage.dateStringToString(value2);
                    }
                    m2.put(name2, value2);
                }

                if ("安全生产许可证".equals(m2.get("zzmcwb"))) {
                    m.setZzzsbh1(m2.get("zzzsbh"));
                    m.setZzzyjdj1(m2.get("zzzyjdj"));
                    m.setFzjg1(m2.get("fzjg"));
                    m.setYxqstart1(m2.get("yxqkssj"));
                    m.setYxqend1(m2.get("yxqjssj"));
                    m.setXkfw(m2.get("xkfw"));
                } else if ("劳务资质".equals(m2.get("zzmcwb"))) {
                    m.setZzzsbh2(m2.get("zzzsbh"));
                    m.setZzzyjdj2(m2.get("zzzyjdj"));
                    m.setFzjg2(m2.get("fzjg"));
                    m.setYxqstart2(m2.get("yxqkssj"));
                    m.setYxqend2(m2.get("yxqjssj"));
                } else if ("".equals(Util.null2String(m.getZsmc()))) {
                    m.setZsmc(m2.get("zzmcwb"));
                    m.setZzzsbh3(m2.get("zzzsbh"));
                    m.setZzzyjdj3(m2.get("zzzyjdj"));
                    m.setFzjg3(m2.get("fzjg"));
                    m.setYxqstart3(m2.get("yxqkssj"));
                    m.setYxqend3(m2.get("yxqjssj"));
                } else if ("".equals(Util.null2String(m.getZsmc2()))) {
                    m.setZsmc2(m2.get("zzmcwb"));
                    m.setZzzsbh4(m2.get("zzzsbh"));
                    m.setZzzyjdj4(m2.get("zzzyjdj"));
                    m.setFzjg4(m2.get("fzjg"));
                    m.setYxqstart4(m2.get("yxqkssj"));
                    m.setYxqend4(m2.get("yxqjssj"));
                }
            }

            Map<String, String> m1 = new HashMap<>();
            m1.put("zzmcwb", "/");
            m1.put("zzzsbh", "/");
            m1.put("zzzyjdj", "/");
            m1.put("fzjg", "/");
            m1.put("yxqkssj", "/");
            m1.put("yxqjssj", "/");
            m1.put("xkfw", "/");

            if ("".equals(m.getZzzsbh1())) {
                m.setZzzsbh1(m1.get("zzzsbh"));
                m.setZzzyjdj1(m1.get("zzzyjdj"));
                m.setFzjg1(m1.get("fzjg"));
                m.setYxqstart1(m1.get("yxqkssj"));
                m.setYxqend1(m1.get("yxqjssj"));
                m.setXkfw(m1.get("xkfw"));
            }
            if ("".equals(m.getZzzsbh2())) {
                m.setZzzsbh2(m1.get("zzzsbh"));
                m.setZzzyjdj2(m1.get("zzzyjdj"));
                m.setFzjg2(m1.get("fzjg"));
                m.setYxqstart2(m1.get("yxqkssj"));
                m.setYxqend2(m1.get("yxqjssj"));
            }
            if ("".equals(Util.null2String(m.getZzzsbh3()))) {
                m.setZsmc(m1.get("zzmcwb"));
                m.setZzzsbh3(m1.get("zzzsbh"));
                m.setZzzyjdj3(m1.get("zzzyjdj"));
                m.setFzjg3(m1.get("fzjg"));
                m.setYxqstart3(m1.get("yxqkssj"));
                m.setYxqend3(m1.get("yxqjssj"));
            }
            if ("".equals(Util.null2String(m.getZzzsbh4()))) {
                m.setZsmc2(m1.get("zzmcwb"));
                m.setZzzsbh4(m1.get("zzzsbh"));
                m.setZzzyjdj4(m1.get("zzzyjdj"));
                m.setFzjg4(m1.get("fzjg"));
                m.setYxqstart4(m1.get("yxqkssj"));
                m.setYxqend4(m1.get("yxqjssj"));
            }

            //--------------------------二 start----------------------------------------------
            m.setYjxmmc(CommonUtil.nullToStr(JoinFieldManage.getProjectName(mainObje.getString("yjxmmc")), "/"));
            m.setXmszsf(CommonUtil.nullToStr(JoinFieldManage.getProvinceName(mainObje.getString("xmszsf")), "/"));
            m.setFbnr(CommonUtil.nullToStr(idToStringLWWX(mainObje.getString("wxlwwx"), mainObje.getString("fbnr")), "/"));
            //--------------------------二 end----------------------------------------------

            //-----------------三 start -----------------------------
            m.setHtkssj(CommonUtil.nullToStr(JoinFieldManage.dateStringToString(mainObje.getString("htkssj")), "/"));
            m.setHtzzsj(CommonUtil.nullToStr(JoinFieldManage.dateStringToString(mainObje.getString("htzzsj")), "/"));
            //-----------------三 end -----------------------------

            //-------------------五 start ------------------------------------
            m.setBhsjg(CommonUtil.nullToStr(mainObje.getString("bhsjg"), "/"));
            m.setBhsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(mainObje.getString("bhsjg"), "0")), "/"));
            String slxzVals = Util.null2String(mainObje.getString("sldx"));
            String slxzValsStr = "";
            if (!"".equals(slxzVals)) {
                String[] slxzs = slxzVals.split(",");
                for (String s : slxzs) {
                    slxzValsStr += JoinFieldManage.getSelectName("sldx", table, s) + " ";
                }
            }
            m.setSlxz(slxzValsStr);
            String v6 = calculateDifference(mainObje.getString("hsjg"), m.getBhsjg());
            m.setZzsj(v6);
            m.setDxzzsj(new ConverToChinesePart().convertToChinese(v6));

            // writeLog("bhsjg: " + mainObje.getString("bhsjg") + "   sldx : " + mainObje.getString("slsjz"));
            // // 计算税金
            // if (slxzValsStr.contains("多税率") || slxzValsStr.contains("0%")) {
            //     m.setZzsj("0.00");
            //     m.setDxzzsj("零元整");
            // } else {
            //     String trim = slxzValsStr.trim();
            //     String[] split = slxzValsStr.split(" ");
            //     if (split.length >= 1) {
            //         // 去除百分号
            //         String numberStr = split[0].replace("%", "");
            //         // 将字符串转换为 double 类型
            //         double decimal = Double.parseDouble(numberStr) / 100;
            //         // this.slxz= Util.null2String(rs.getString("slsjz"));
            //         Map<String, String> map5 = processContract(mainObje.getString("bhsjg"), String.valueOf(decimal));
            //         m.setZzsj(map5.get("zzsj"));
            //         m.setDxzzsj(map5.get("dxzzsj"));
            //     } else {
            //         m.setZzsj("0.00");
            //         m.setDxzzsj("零元整");
            //     }
            // }


            m.setHsjg(CommonUtil.nullToStr(mainObje.getString("hsjg"), "/"));
            m.setHsjgDX(CommonUtil.nullToStr(cp.convertToChinese(Util.null2String(mainObje.getString("hsjg"), "0")), "/"));
            if ("0".equals(Util.null2String(mainObje.getString("nwbcjs")))) {
                m.setNwbcjs("一");
            } else if ("1".equals(Util.null2String(mainObje.getString("nwbcjs")))) {
                m.setNwbcjs("二");
            } else if ("2".equals(Util.null2String(mainObje.getString("nwbcjs")))) {
                m.setNwbcjs("四");
            } else if ("3".equals(Util.null2String(mainObje.getString("nwbcjs")))) {
                m.setNwbcjs("三");
            }
            m.setNwbcgj(Util.null2String(mainObje.getString("nwbcgj")));
            m.setNwbcgjDX(CommonUtil.nullToStr(cp.convertToChinese(mainObje.getString("nwbcgj")), "/"));
            m.setFkfs(Util.null2String(mainObje.getString("fkfs")));
            m.setQtfkfs1(Util.null2String(mainObje.getString("qtfkfs1")));
            // 特别约定
            m.setSkhtkhmc(CommonUtil.nullToStr(JoinFieldManage.getCustomerName(mainObje.getString("skhtkhmc")), "/"));
            m.setSkhtbh(CommonUtil.nullToStr(mainObje.getString("skhtbh"), "/"));
            m.setSkhtmc(CommonUtil.nullToStr(JoinFieldManage.getContractName(mainObje.getString("skhtmc")), "/"));

            String fpzzslVals = Util.null2String(mainObje.getString("fpzzsl"));
            String fpzzslValsStr = "";
            if (!"".equals(fpzzslVals)) {
                String[] fpzzsls = fpzzslVals.split(",");
                for (String s : fpzzsls) {
                    fpzzslValsStr += JoinFieldManage.getSelectName("fpzzsl", table, s) + " ";
                }
            }
            m.setFpzzsl(fpzzslValsStr);

            m.setFplx1(JoinFieldManage.getSelectName("fplx1", table, mainObje.getString("fplx1")));
            m.setLybzjjnsj(Util.null2String(mainObje.getString("lybzjjnsj")));
            m.setLybzj(Util.null2String(mainObje.getString("lybzj")));
            m.setLybzjDX(cp.convertToChinese(Util.null2String(mainObje.getString("lybzj"), "0")));
            //-------------------五 end ------------------------------------

            //------------------六 start----------------------------
            m.setZhmc(Util.null2String(mainObje.getString("zhmc")));
            m.setYxkh(Util.null2String(mainObje.getString("yxkh")));
            m.setKhyx(Util.null2String(mainObje.getString("khyx")));
            //------------------六 end----------------------------

            //------------------八 施工与设计变更 start----------------------------
            m.setYflwwxdz(Util.null2String(mainObje.getString("yflwwxdz")));
            m.setYfdzdh(Util.null2String(mainObje.getString("yfdzdh")));
            m.setYfdzsfz(Util.null2String(mainObje.getString("yfdzsfz")));
            //------------------八 施工与设计变更 end----------------------------

            //--------------九 工程质量管理及验收 start----------------
            m.setGCZLgcmc(CommonUtil.nullToStr(JoinFieldManage.getCustomerName(mainObje.getString("skhtkhmc")), "/"));
            m.setGCZLhtbh(CommonUtil.nullToStr(mainObje.getString("skhtbh"), "/"));
            m.setGCZLhtmc(CommonUtil.nullToStr(JoinFieldManage.getContractName(mainObje.getString("skhtmc")), "/"));
            //--------------九 工程质量管理及验收 end---------------

            //---------------------------十一 start--------------------------------
            m.setGczlbxqx(Util.null2String(mainObje.getString("gczlbxqx")));
            //---------------------------十一 end--------------------------------

            //---------------------------十二 start--------------------------------
            m.setQcfy(Util.null2String(mainObje.getString("qcfy")));
            m.setLkgdqx(Util.null2String(mainObje.getString("lkgdqx")));
            //---------------------------十二 end--------------------------------

            //---------------------------十三 start--------------------------------
            m.setWjbl(CommonUtil.saveSmallNum(Util.null2String(mainObje.getString("wjbl"))));
            //---------------------------十三 end--------------------------------

            m.setWxsftjfj11(Util.null2String(mainObje.getString("wxsftjfj11")));

            // 明细表4
            r = detailTables[3].getRow();
            List<Map<String, String>> detail = new ArrayList<>();
            if (r.length == 0) {
                writeLog("mx4 length == 0");
                Map<String, String> map = new HashMap<>();
                map.put("id", "/");
                map.put("gzqy", "/");
                map.put("bl", "/");
                map.put("cnsl", "/");
                map.put("bz", "/");
                detail.add(map);
            } else {
                for (int i = 0; i < r.length; ++i) {
                    Map<String, String> map = new HashMap<>();
                    c = r[i].getCell();
                    map.put("id", String.valueOf(i + 1));
                    for (int j = 0; j < c.length; ++j) {
                        writeLog(c[j].getName() + "---" + c[j].getValue());

                        String name1 = c[j].getName();
                        String value = Util.null2String(c[j].getValue());

                        try {
                            if ("bl".equals(name1)) {
                                value = "".equals(value) ? "0" : String.format("%.2f", Double.parseDouble(value) * 100);
                            } else if ("cnsl".equals(name1)) {
                                value = "".equals(value) ? "0" : String.format("%.1f", Double.parseDouble(value) * 100);
                            }
                        } catch (Exception e) {
                            value = "";
                        }

                        map.put(name1, value);
                    }
                    detail.add(map);
                }
            }
            m.setDetail(detail);

            // 明细表8
            r = detailTables[7].getRow();
            List<Map<String, String>> detail2 = new ArrayList<>();
            if (r.length == 0) {
                Map<String, String> map = new HashMap<>();
                map.put("id", "/");
                map.put("sx", "/");
                map.put("hsdjy", "/");
                map.put("sl", "/");
                map.put("yghj", "/");
                detail2.add(map);
            } else {
                for (int i = 0; i < r.length; ++i) {
                    Map<String, String> map = new HashMap<>();
                    c = r[i].getCell();
                    map.put("id", String.valueOf(i + 1));
                    for (int j = 0; j < c.length; ++j) {
                        writeLog(c[j].getName() + "---" + c[j].getValue());
                        map.put(c[j].getName(), Util.null2String(c[j].getValue()));
                    }
                    detail2.add(map);
                }
            }
            m.setDetail2(detail2);

            // 明细表9
            r = detailTables[8].getRow();
            List<Map<String, String>> detail3 = new ArrayList<>();
            if (r.length == 0) {
                Map<String, String> map = new HashMap<>();
                map.put("id", "/");
                map.put("tmmc", "/");
                map.put("hsdjy", "/");
                map.put("yggzl", "/");
                map.put("yghj", "/");
                detail3.add(map);
            } else {
                for (int i = 0; i < r.length; ++i) {
                    Map<String, String> map = new HashMap<>();
                    c = r[i].getCell();
                    map.put("id", String.valueOf(i + 1));
                    for (int j = 0; j < c.length; ++j) {
                        writeLog(c[j].getName() + "---" + c[j].getValue());
                        map.put(c[j].getName(), Util.null2String(c[j].getValue()));
                    }
                    detail3.add(map);
                }
            }
            m.setDetail3(detail3);

            // 明细表10
            r = detailTables[9].getRow();
            List<Map<String, String>> detail4 = new ArrayList<>();
            if (r.length == 0) {
                Map<String, String> map = new HashMap<>();
                map.put("id", "/");
                map.put("gz", "/");
                map.put("gzmc", "/");
                map.put("gzbl", "/");
                map.put("gzblxs", "/");
                detail4.add(map);
            } else {
                for (int i = 0; i < r.length; ++i) {
                    Map<String, String> map = new HashMap<>();
                    c = r[i].getCell();
                    map.put("id", String.valueOf(i + 1));
                    for (int j = 0; j < c.length; ++j) {
                        writeLog(c[j].getName() + "---" + c[j].getValue());
                        String fname = c[j].getName();
                        String value = Util.null2String(c[j].getValue());

                        if ("gzblxs".equals(fname)) {
                            if (!"".equals(value)) {
                                value = Double.parseDouble(value) * 100 + "%";
                            }
                        }
                        map.put(fname, value);
                    }
                    detail4.add(map);
                }
            }
            m.setDetail4(detail4);

            String str = "";
            String fid = "";

            // 附件1 关于规范发票行为及收款账户的不可撤销承诺函
            Mode2 m2 = new Mode2();
            try {
                m2.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
                m2.setCreditCode(Util.null2String(mainObje.getString("sfzhmtyshxydmczf")));
                m2.setLxdzyf(Util.null2String(mainObje.getString("lxdzyf")));
                m2.setFzrlxdh(Util.null2String(mainObje.getString("fzrlxdh")));
                m2.setZhmc(Util.null2String(mainObje.getString("zhmc")));
                m2.setYxkh(Util.null2String(mainObje.getString("yxkh")));
                m2.setKhyx(Util.null2String(mainObje.getString("khyx")));
                m2.setYwqyfzr(Util.null2String(mainObje.getString("ywqyfzr")));

                m2.setFb(JoinFieldManage.getCompanyName(Util.null2String(mainObje.getString("fb")), "subcompanyname"));
                m2.setLxrjf(JoinFieldManage.getPersonName(mainObje.getString("lxrjf")));
                m2.setLxdhjf(Util.null2String(mainObje.getString("lxdhjf")));

                writeLog("关于规范发票行为及收款账户的不可撤销承诺函 Mode2:" + m2.toString());
                // 创建docx文档
                str = mwt.toDocx(m2, "关于规范发票行为及收款账户的不可撤销承诺函");
                writeLog("关于规范发票行为及收款账户的不可撤销承诺函 save:" + str);
                // 上传至OA
                fid = FileUtil.uploadToOA("关于规范发票行为及收款账户的不可撤销承诺函.docx", str, requestid);
                rst.executeUpdate("update " + table + " set zlfwcqz=? where requestid=?", fid, requestid);
                mergDocxPathList.add(str);
            } catch (Exception e) {
                throw new ECException("《关于规范发票行为及收款账户的不可撤销承诺函》字段映射错误！", e);
            }

            // 附件2 项目安全生产与文明施工管理承诺书
            Mode13 m3 = new Mode13();
            try {
                m3.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
                m3.setYjxmmc(JoinFieldManage.getProjectName(mainObje.getString("yjxmmc")));
                String dec = Util.null2String(mainObje.getString("securemoneylabor"));
                if (dec != "") {
                    BigDecimal decimalValue = new BigDecimal(dec.trim());
                    dec = decimalValue.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString();
                } else {
                    dec = "0";
                }
                m3.setSecuremoneylabor(dec);

                dec = Util.null2String(mainObje.getString("secumoneysupplier"));
                if (dec != "") {
                    BigDecimal decimalValue = new BigDecimal(dec.trim());
                    dec = decimalValue.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString();
                } else {
                    dec = "0";
                }
                m3.setSecumoneysupplier(dec);

                m3.setLwwxbctk(Util.null2String(mainObje.getString("lwwxbctk")).replace("&nbsp;", "").replace("<br>", ""));
                m3.setLxdzyf(Util.null2String(mainObje.getString("lxdzyf")));

                m3.setDate(JoinFieldManage.dateStringToString(mainObje.getString("htkssj")));

                // 创建docx文档
                str = mwt.toDocx(m3, "安全生产管理协议");
                // 上传至OA
                fid = FileUtil.uploadToOA("安全生产管理协议.docx", str, requestid);
                rst.executeUpdate("update " + table + " set czrsfzyyzz=? where requestid=?", fid, requestid);
                mergDocxPathList.add(str);
            } catch (Exception e) {
                throw new ECException("《安全生产管理协议》字段映射错误！", e);
            }

            // 附件3 廉洁诚信承诺书
            // 附件3.1 有关联人员认证声明书
            // 附件3.2 无关联人员认证声明书
            Mode4 m4 = new Mode4();
            Mode5 m5 = new Mode5();
            Mode6 m6 = new Mode6();

            try {
                m4.setHtmc(Util.null2String(mainObje.getString("htmc")));
                m4.setHtbh(Util.null2String(mainObje.getString("htbh")));
                m4.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
                m4.setYwqyfzr(Util.null2String(mainObje.getString("ywqyfzr")));

                String fids = "";
                // 创建docx文档
                str = mwt.toDocx(m4, "廉洁诚信承诺书");
                // 上传至OA
                fid = FileUtil.uploadToOA("廉洁诚信承诺书.docx", str, requestid);
                fids = fid;
                mergDocxPathList.add(str);

                if ("0".equals(mainObje.getString("ywglryrzsms"))) {
                    m5.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));

                    str = mwt.toDocx(m5, "有关联人员认证声明书");
                    fid = FileUtil.uploadToOA("有关联人员认证声明书.docx", str, requestid);
                    fids = fids + "," + fid;
                    mergDocxPathList.add(str);
                } else {
                    m6.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));

                    str = mwt.toDocx(m6, "无关联人员认证声明书");
                    fid = FileUtil.uploadToOA("无关联人员认证声明书.docx", str, requestid);
                    fids = fids + "," + fid;
                    mergDocxPathList.add(str);
                }

                rst.executeUpdate("update " + table + " set fjs=? where requestid=?", fids, requestid);
            } catch (Exception e) {
                throw new ECException("《廉洁诚信承诺书》字段映射错误！", e);
            }

            // 附件4 不拖欠民工工资承诺书
            Mode7 m7 = new Mode7();
            try {
                m7.setGysmc(JoinFieldManage.getVendorAccountName(mainObje.getString("gysmc")));
                m7.setYjxmmc(JoinFieldManage.getProjectName(mainObje.getString("yjxmmc")));
                m7.setFkrq(Util.null2String(mainObje.getString("fkrq")));

                str = mwt.toDocx(m7, "不拖欠民工工资承诺书");
                fid = FileUtil.uploadToOA("不拖欠民工工资承诺书.docx", str, requestid);
                rst.executeUpdate("update " + table + " set fjsi=? where requestid=?", fid, requestid);
                mergDocxPathList.add(str);
            } catch (Exception e) {
                throw new ECException("《不拖欠民工工资承诺书》字段映射错误！", e);
            }

            // 附件5-10
            try {
                if (!"".equals(Util.null2String(mainObje.getString("fjw")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件5", mainObje.getString("fjw")));
                }
                if (!"".equals(Util.null2String(mainObje.getString("fjl")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件6", mainObje.getString("fjl")));
                }
                if (!"".equals(Util.null2String(mainObje.getString("fjq")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件7", mainObje.getString("fjq")));
                }
                if (!"".equals(Util.null2String(mainObje.getString("fjb")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件8", mainObje.getString("fjb")));
                }
                if (!"".equals(Util.null2String(mainObje.getString("fjj")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件9", mainObje.getString("fjj")));
                }
                if (!"".equals(Util.null2String(mainObje.getString("fjshi")))) {
                    mergDocxPathList.add(mwt.mergToWord("附件10", mainObje.getString("fjshi")));
                }
            } catch (Exception e) {
                throw new ECException("OA附件下载失败！", e);
            }

            // 附件11
            mergDocxPathList.add(DocConfig.TEMPLATE_PATH + "/lwwx/fj11.docx");

            // 附件12
            try {
                mergDocxPathList.add(mwt.createDocxByImage("附件12", DocConfig.TEMPLATE_PATH + "/lwwx/fj12/order.jpg"));
            } catch (Exception e) {
                throw new ECException("附件12转换docx失败！", e);
            }

            // 将需要合并的文档路径放入主模板中
            m.setAnnex(mergDocxPathList);

            // mode转docx
            str = mwt.toDocx(m, name);
            fid = FileUtil.uploadToOA(name + ".docx", str, requestid);
            rst.executeUpdate("update " + table + " set gzfj=? where requestid=?", fid, requestid);

            mwt.delete();
            return Action.SUCCESS;
        } catch (Exception e) {
            try {
                mwt.delete();
            } catch (Exception e1) {
                throw new ECException("删除临时文件夹失败！", e1);
            }
            requestInfo.getRequestManager().setMessageid("202309");
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());

            return Action.FAILURE_AND_CONTINUE;
        }
    }

    private String idToStringLWWX(String ids, String qt) {
        if ("".equals(Util.null2String(ids))) {
            return "";
        }
        StringBuffer content = new StringBuffer();

        String str[] = ids.split(",");
        RecordSet rs = new RecordSet();
        for (int i = 0; i < str.length; ++i) {
            rs.execute("select nr from uf_wxlwwxnr where id=" + str[i]);
            if (rs.next()) {
                if ("17".equals(str[i])) {
                    content.append(Util.null2String(qt));
                } else {
                    content.append(Util.null2String(rs.getString(1)));
                }

                if (i != (str.length - 1)) {
                    content.append("、");
                }
            }
        }
        return content.toString();
    }
}