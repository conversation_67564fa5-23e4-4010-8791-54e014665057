package weaver.interfaces.workflow.action.cyitce.doc.mode;

import weaver.interfaces.workflow.action.cyitce.doc.mode.common.CommonMode;

/**
 * @ClassName: 不拖欠民工工资承诺书
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-08-09  13:49
 * @Version: 1.0
 */
public class Mode7 extends CommonMode {
    /**
     * 乙方（承包方）gysmc
     */
    public String gysmc;
    /**
     * yjxmmc	项目名称
     */
    public String yjxmmc;

    /**
     * fkrq 农民工工资付款日期
     */
    public String fkrq;

    public Mode7() {
        setPath("lwwx/fj4");
    }

    public String getGysmc() {
        return gysmc;
    }

    public void setGysmc(String gysmc) {
        this.gysmc = gysmc;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getFkrq() {
        return fkrq;
    }

    public void setFkrq(String fkrq) {
        this.fkrq = fkrq;
    }

    @Override
    public String toString() {
        return "Mode7{" +
                "gysmc='" + gysmc + '\'' +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", fkrq='" + fkrq + '\'' +
                '}';
    }
}