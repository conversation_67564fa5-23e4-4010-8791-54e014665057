package weaver.interfaces.workflow.action.api;

import com.api.formmode.page.util.Util;
import com.cloudstore.dev.api.bean.MessageBean;
import com.cloudstore.dev.api.bean.MessageType;
import com.cloudstore.dev.api.util.Util_Message;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class EjProjectinform4 extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        Map<String, String> result = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; i++) {
            String name = properties[i].getName();
            String value = Util.null2String(properties[i].getValue());
            result.put(name, value);
        }
        String sqr = result.get("sqr");
        writeLog("申请人："+sqr);
        String xmmc = result.get("ejxmnbmc");
        writeLog("项目名称："+xmmc);
        String 	yjsfsggxm = result.get("yjsfsggxm");
        if(yjsfsggxm.equals("1")){
            MessageType messageType = MessageType.newInstance(546);
            Set<String> userIdList = new HashSet<>();
            userIdList.add(sqr);
            writeLog("遍历："+userIdList);
            String title = "请及时在预算系统做预算";
            String context = "二级项目名称："+xmmc+"立项已归档，请及时提交项目预算，否则，项目的所有支付和报销均不能支付";
            writeLog("通知："+context);
            String linkUrl = "/spa/coms/message/index-message.html#/message/entrance?typeId=3HzISRVx";
            String linkMobileUrl = "/spa/coms/static4mobileMessage/index.html#/?typeId=3HzISRVx";
            try {
                MessageBean messageBean = Util_Message.createMessage(messageType, userIdList, title, context, linkUrl, linkMobileUrl);
                messageBean.setCreater(1);
                Util_Message.store(messageBean);
                return Action.SUCCESS;
            } catch (IOException e) {
                writeLog("失败！",e);
                return "消息发送失败";
            }
        }
        return Action.SUCCESS;
    }

}
