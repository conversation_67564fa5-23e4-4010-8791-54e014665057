package weaver.formmode.customjavacode.cyitce.customsearch;

import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractCustomSqlConditionJavaCode;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * @ClassName: 数藤云计算公司台账通用自定义查询
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-05-21  15:56
 * @Version: 1.0
 */
public class STYJSCustomSearchCommon extends AbstractCustomSqlConditionJavaCode {
    BaseBean log = new BaseBean();

    private void writeLog(String s){
        log.writeLog("====STYJSCustomSearchCommon===== :"+s);
    }
    /**
     * 生成SQL查询限制条件
     * @param param
     *  param包含(但不限于)以下数据
     *  user 当前用户
     *
     * @return
     *  返回的查询限制条件的格式举例为: t1.a = '1' and t1.b = '3' and t1.c like '%22%'
     *  其中t1为表单主表表名的别名
     */
    @Override
    public String generateSqlCondition(Map<String, Object> param) {
        User user = (User)param.get("user");
        String customid = (String)param.get("customid");
        writeLog("param："+param);
        String sqlCondition=" (t1.modedatacreater="+user.getUID();

        String selectFields;
        String companyid;
        String pro_val_field;
        String spro_val_field;

        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        RecordSet rs3 = new RecordSet();

        String currMainTable;
        String currDetailTable;
        rs.executeQuery("SELECT b.tablename,a.detailtable FROM mode_customsearch a\n" +
                "inner join workflow_bill b on a.formid=b.id\n" +
                "where a.id=?",new Object[]{customid});
        if(rs.next()){
            currMainTable = Util.null2String(rs.getString(1));
            currDetailTable = Util.null2String(rs.getString(2));
            writeLog("currMainTable:"+currMainTable);
            writeLog("currDetailTable:"+currDetailTable);
        }else {
            sqlCondition+=")";
            return sqlCondition;
        }

        String[] selectFieldListF;
        String[] selectFieldListS;
        Set<Integer> proIds = new HashSet<>();
        Set<Integer> sproIds = new HashSet<>();
        rs.executeQuery("select companyid,pro_val_field,spro_val_field,xmrlzyzd from uf_TableFieldMatrix where customid=?",new Object[]{customid});
        while (rs.next()){
            companyid = Util.null2String(rs.getString(1));
            pro_val_field = Util.null2String(rs.getString(2));
            spro_val_field = Util.null2String(rs.getString(3));
            selectFields = Util.null2String(rs.getString(4));

            selectFieldListF = Arrays.stream(selectFields.trim().split(","))
                    .filter(s -> !s.isEmpty())
                    .filter(s -> s.contains("f."))
                    .map(s -> s.substring(s.indexOf(".")+1))
                    .toArray(String[]::new);

            selectFieldListS = Arrays.stream(selectFields.trim().split(","))
                    .filter(s -> !s.isEmpty())
                    .filter(s -> s.contains("s."))
                    .map(s -> s.substring(s.indexOf(".")+1))
                    .toArray(String[]::new);

            rs2.executeQuery("select type,formid from uf_companyWorkflowMapping where type in (2,3) and companyid=?",new Object[]{companyid});
            while (rs2.next()){
                String type = Util.null2String(rs2.getString(1));
                String proTab = "";
                String sproTab = "";
                if("2".equals(type)){
                    proTab = Util.null2String(rs2.getString(2));

                    for(int i=0;i<selectFieldListF.length;++i){
                        rs3.executeQuery("select id from "+proTab+" where "+selectFieldListF[i]+" like '%"+user.getUID()+"%'");
                        while (rs3.next()){
                            proIds.add(Integer.valueOf(rs3.getString(1)));
                        }
                    }
                }else if("3".equals(type)){
                    sproTab = Util.null2String(rs2.getString(2));

                    for(int i=0;i<selectFieldListS.length;++i){
                        rs3.executeQuery("select id from "+sproTab+" where "+selectFieldListS[i]+" like '%"+user.getUID()+"%'");
                        while (rs3.next()){
                            sproIds.add(Integer.valueOf(rs3.getString(1)));
                        }
                    }
                }
            }

            if(!"".equals(pro_val_field)&&proIds.size()>0){
                sqlCondition+=" or "+pro_val_field + " in " + "("+String.join(",",proIds.stream().map(String::valueOf).toArray(String[]::new))+")";
            }else if(!"".equals(spro_val_field)&&sproIds.size()>0){
                sqlCondition+=" or "+spro_val_field + " in " + "("+String.join(",",sproIds.stream().map(String::valueOf).toArray(String[]::new))+")";
            }
        }

        sqlCondition+=roleAut(1,"274,87,127,282",user);

        rs.executeQuery("select * from Matrixtable_19 where zjl like '%"+user.getUID()+"%' or gg like '%"+user.getUID()+"%' or yjjl like '%"+user.getUID()+"%'");
        if(rs.next()){
            sqlCondition+=" or 1=1";
        }
        sqlCondition+=")";
        return sqlCondition;
    }

    /**
     * @Description: 根据角色设置权限
     * @param type 权限范围，1：查看所有
     * @param roleId 角色id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/8/31 11:18
     */
    private String roleAut(int type, String roleId, User user){
        String sqlCondition = "";
        String sqlWhere = "";
        String sql = "select resourceid,resourcetype,seclevelfrom,seclevelto,jobtitlelevel,subdepid from HrmRoleMembers WHERE roleid in ("+roleId+")";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        if(type==1){
            sqlWhere=" or 1=1";
        }

        while (rs.next()){
            if(rs.getInt("resourcetype")==1&&rs.getInt("resourceid")==user.getUID()){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==2&&rs.getInt("resourceid")==user.getUserSubCompany1()&&isSeclevel(user.getSeclevel(),rs)){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==3&&rs.getInt("resourceid")==user.getUserDepartment()&&isSeclevel(user.getSeclevel(),rs)){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==5){
                if(rs.getInt("subdepid")==user.getUserSubCompany1()&&rs.getString("resourceid").equals(user.getJobtitle())&&rs.getInt("jobtitlelevel")==2){
                    sqlCondition+=sqlWhere;
                    break;
                }else if(rs.getInt("subdepid")==user.getUserSubCompany1()&&rs.getString("resourceid").equals(user.getJobtitle())&&rs.getInt("jobtitlelevel")==3){
                    sqlCondition+=sqlWhere;
                    break;
                }
            }
        }
        return sqlCondition;
    }

    private boolean isSeclevel(String seclevel,RecordSet rs){
        return Integer.valueOf(seclevel)>=rs.getInt("seclevelfrom")&&Integer.valueOf(seclevel)<=rs.getInt("seclevelto");
    }
}