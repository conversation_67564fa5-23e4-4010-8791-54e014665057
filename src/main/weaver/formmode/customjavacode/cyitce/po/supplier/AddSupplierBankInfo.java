package weaver.formmode.customjavacode.cyitce.po.supplier;

import org.apache.log4j.Logger;
import weaver.conn.RecordSetTrans;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.hrm.User;
import weaver.soa.workflow.request.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * @ClassName: 供应商银行信息插入供应商账户信息表
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-05-24  16:00
 * @Version: 1.0
 */
public class AddSupplierBankInfo extends AbstractModeExpandJavaCodeNew {

    private static Logger log = Logger.getLogger(AddSupplierBankInfo.class);

    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();

        RecordSetTrans rst = null;
        String sql = null;

        Date date = null;
        SimpleDateFormat format = null;
        String createDate = null;
        String createTime = null;
        String table_dt1 = null;

        try {
            rst = new RecordSetTrans();
            rst.setAutoCommit(false);

            date = new Date();
            format = new SimpleDateFormat("yyyy-MM-dd");
            createDate = format.format(date);
            format = new SimpleDateFormat("HH:mm:ss");
            createTime = format.format(date);

            RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
            User user = (User) param.get("user");
            Property[] properties = requestInfo.getMainTableInfo().getProperty();
            DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();

            Map<String,String> mObj = new HashMap<>();
            for (int i=0;i<properties.length;++i){
                mObj.put(properties[i].getName(),properties[i].getValue());
            }

            sql = "select id from CRM_CustomerInfo where crmcode=?";
            rst.executeQuery(sql,mObj.get("gysbh"));
            if(rst.next()){
                mObj.put("crmid",rst.getString(1));
            }

            DetailTable dt1 = detailTable[0];// 指定明细表1,供应商资质信息
            table_dt1 = dt1.getTableDBName();//明细表名称
            log.info("明细1名称："+table_dt1);
            Row[] rs = dt1.getRow();// 当前明细表的所有数据,按行存储
            boolean boo = false;
            for (int j = 0; j < rs.length; j++) {
                Row r = rs[j];// 指定行
                Cell[] cs = r.getCell();// 每行数据再按列存储
                Map<String,String> dObj = new HashMap<>();
                for (int k = 0; k < cs.length; k++) {
                    Cell c1 = cs[k];// 指定列
                    dObj.put(c1.getName(), c1.getValue());
                }

                if("".equals(dObj.get("bcode"))||dObj.get("bcode")==null){
                    throw new Exception("bcod为空，提交失败，请联系管理员处理！");
                }

                sql = "select gysbh from uf_VendorAccountInfo where bcode=?";
                rst.executeQuery(sql,dObj.get("bcode"));
                String bcode = null;
                if(!rst.next()){
                    bcode = "Gys"+dObj.get("id");
                    log.info("insert bcode:"+bcode);
                    sql = "update "+table_dt1+" set bcode=? where id=?";
                    rst.executeUpdate(sql,bcode,dObj.get("id"));
                    sql = "insert into uf_VendorAccountInfo(gysxx,zhmc,yxmc,yxzx,khxxxjzh,gysmc,gysbh,nsrsbh,khdz,formmodeid,modedatacreater,gysgz,modedatacreatertype,modedatacreatedate,modedatacreatetime,MODEUUID,bcode) " +
                            "values('"+mObj.get("crmid")+"','"+dObj.get("gysmc")+"','"+dObj.get("khxmc")+"','"+dObj.get("khxzh")+"','"+dObj.get("khxdz")+"','"+mObj.get("gysmc")+ "','" +
                            mObj.get("gysbh")+"','"+mObj.get("tyshxydm")+"','"+mObj.get("gyszcdz")+"',238,'"+user.getUID()+"','"+dObj.get("gysgz")+"',0,'"+createDate+"','"+createTime+"','"+UUID.randomUUID()+"','"+bcode+"')";
                    boo = rst.execute(sql);
                    if (!boo){
                        log.info("sql执行失败："+rst.getMsg());
                        throw new Exception("sql执行失败：");
                    }
                }else {
                    log.info("update bcode:"+dObj.get("bcode"));
                    sql = "update uf_VendorAccountInfo set gysxx=?,zhmc=?,yxmc=?,khxxxjzh=?,gysmc=?,nsrsbh=?,khdz=?,gysgz=? where bcode=?";
                    boo = rst.executeUpdate(sql,mObj.get("gysxx"),dObj.get("gysmc"),dObj.get("khxmc"),dObj.get("khxdz"),mObj.get("gysmc"),mObj.get("tyshxydm"),mObj.get("gyszcdz"),dObj.get("gysgz"),dObj.get("bcode"));
                    if (!boo){
                        log.info("sql执行失败："+rst.getMsg());
                        throw new Exception("sql执行失败：");
                    }
                }
            }

            rst.commit();
        } catch (Exception e) {
            e.printStackTrace();
            rst.rollback();

            result.put("errmsg",e.getMessage());
            result.put("flag", "false");
        }

        return result;
    }
}