//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package weaver.formmode.customjavacode;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.icbc.HttpRequest;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.Zlbxlh;
import weaver.soa.workflow.request.RequestInfo;

public class PayCLBX extends AbstractModeExpandJavaCode {
    public PayCLBX() {
    }

    private void writeLog(Object obj) {
        this.writeLog(this.getClass().getName(), obj);
    }

    private void writeLog(String classname, Object obj) {
        Log log = LogFactory.getLog(classname);
        if (obj instanceof Exception) {
            log.error(classname, (Exception)obj);
        } else {
            log.error(obj);
        }

    }

    public void doModeExpand(Map<String, Object> param) throws Exception {
        User user = (User)param.get("user");
        String name = user.getUsername();
        RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
        this.writeLog(requestInfo != null);
        if (requestInfo != null) {
            int billid = Util.getIntValue(requestInfo.getRequestid());
            int modeid = Util.getIntValue(requestInfo.getWorkflowid());
            if (billid > 0 && modeid > 0) {
                RecordSet rs = new RecordSet();
                this.writeLog("业务逻辑代码开始执行 ");
                String jydm = "PAYENT";
                String jtCIS = "310090001615893";
                String gsyhbh = "102";
                String zsID = "xktx02.y.3100";
                String jyrq = "";
                String jysj = "";
                String zlbxu = Zlbxlh.getCLBXPay();
                this.writeLog("指令包序列号为" + zlbxu);
                String rzfs = "0";
                String zbs = "1";
                String zje = "";
                String qmsj = "";
                String zlsxh = "1";
                String jzclfs = "2";
                String xtnwbz = "";
                String bz = "001";
                String zfje = "";
                String yxzh = "";
                String zhmc = "";
                String tcydbz = "";
                String dgdsbz = "";
                String skfszcsmc = "";
                String dfxxh = "";
                String jydfyxmc = "";
                String ytzwms = "";
                String zy = "";
                String ERPNo = "";
                String cwpzh = "";
                String gsyxzhm = "重庆信科通信工程有限公司";
                String gsyhzh = "3100027619200023318";
                rs.execute("select rzfs,jzclfs,xtnwbz,tcydbz,dgdsbz,skfszcsmc,dfxxh,ytzwms,zfjey,khx,yxzh,zhm,zy,lcbh,cwpzh from uf_clbxzfbd where id=" + billid);
                rs.next();
                xtnwbz = rs.getString(3);
                tcydbz = rs.getString(4);
                dgdsbz = rs.getString(5);
                skfszcsmc = rs.getString(6);
                dfxxh = rs.getString(7);
                ytzwms = rs.getString(8);
                Boolean sendflag = false;
                float zfjefloat = 0.0F;
                zfjefloat = (float)Math.round(rs.getDouble(9) * 1000.0D) / 10.0F;
                if (zfjefloat <= 2000000.0F) {
                    sendflag = true;
                }

                zfje = String.valueOf(zfjefloat);
                zfje = zfje.substring(0, zfje.length() - 2);
                this.writeLog("支付金额:" + zfje);
                jydfyxmc = rs.getString(10);
                yxzh = rs.getString(11);
                zhmc = rs.getString(12);
                zy = rs.getString(13);
                ERPNo = rs.getString(14);
                cwpzh = rs.getString(15);
                DateFormat dateFormat_jyrq = new SimpleDateFormat("yyyyMMdd");
                DateFormat dateFormat_jysj = new SimpleDateFormat("HHmmssSSS");
                DateFormat dateFormat_qmsj = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                Date date = new Date();
                jyrq = dateFormat_jyrq.format(date);
                jysj = dateFormat_jysj.format(date);
                qmsj = dateFormat_qmsj.format(date);
                this.writeLog(name + "差旅报销支付了:jydm:" + jydm + " jydm:" + jydm + " gsyhbh:" + gsyhbh + " zsID:" + zsID + " zlbxu:" + zlbxu + " rzfs:" + rzfs + " zbs:" + zbs + " zje:" + zfje + " zlsxh:" + zlsxh + " jzclfs:" + jzclfs + " xtnwbz:" + xtnwbz + " bz:" + bz + " zfje:" + zfje + " yxzh:" + yxzh + " " + jysj + " " + jyrq + " " + qmsj + " " + zhmc + " " + tcydbz + " " + dgdsbz + " " + skfszcsmc + " dfxxh:" + dfxxh + " jydfyxmc:" + jydfyxmc + " ytzwms:" + ytzwms + " zy:" + zy + " ERPNo:" + ERPNo + " cwpzh:" + cwpzh);
                rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='提交成功，等待系统处理',zfrq = '" + jyrq + "',zlbxlh='" + zlbxu + "' where id = " + billid);
                if (sendflag) {
                    String sContentSign = HttpRequest.getRequestString(jydm, jtCIS, gsyhbh, zsID, jyrq, jysj, zlbxu, "1", rzfs, zbs, zfje, qmsj, zlsxh, jzclfs, gsyhzh, gsyxzhm, yxzh, xtnwbz, bz, zfje, zhmc, tcydbz, dgdsbz, skfszcsmc, dfxxh, jydfyxmc, ytzwms, zy, ERPNo, cwpzh);
                    this.writeLog("差旅报销报文：" + sContentSign);
                    this.writeLog("差旅报销发送xml--------------------");
                    List resultList = HttpRequest.sendRequest(true, zlbxu, "PAYENT", sContentSign);
                    this.writeLog("差旅报销已发送--------------------");
                    Map search = (Map)resultList.get(0);
                    this.writeLog("差旅报销回文： " + search.toString());
                    String errer = (String)search.get("errer");
                    if (!"".equals(errer) && errer != null) {
                        this.writeLog("差旅报销error： " + errer);
                        rs.execute("update uf_clbxzfbd set sfzfwc=2,ztsm='" + errer + "' where id = " + billid);
                    } else {
                        String iRetCode = (String)search.get("iRetCode");
                        String iRetMsg = (String)search.get("iRetMsg");
                        String Result = (String)search.get("Result");
                        String instrRetCode = (String)search.get("instrRetCode");
                        String instrRetMsg = (String)search.get("instrRetMsg");
                        if (Result.equals("7")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=0,ztsm='支付成功' where id = " + billid);
                        } else if (Result.equals("0")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='提交成功，等待银行处理 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("1")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='授权成功，等待银行处理  " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("2")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='等待授权 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("3")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='等待二次授权 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("4")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='等待银行回复 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("5")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='主机返回待处理 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("6")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=2,ztsm='被银行拒接 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("11")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='预约取消 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("8")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=2,ztsm='指令拒绝授权 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("9")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='银行正在处理 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("10")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='预约指令 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("20")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='待提交人确认转账 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("86")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='等待电话核实 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("95")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='待核查 " + iRetMsg + "' where id = " + billid);
                        } else if (Result.equals("98")) {
                            rs.execute("update uf_clbxzfbd set sfzfwc=1,ztsm='区域中心通讯可疑 " + iRetMsg + "' where id = " + billid);
                        }
                    }
                } else {
                    rs.execute("update uf_clbxzfbd set sfzfwc=2,ztsm='支付金额大于2万 ' where id = " + billid);
                }
            }
        }

    }
}
