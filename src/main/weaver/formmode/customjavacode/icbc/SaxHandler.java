package weaver.formmode.customjavacode.icbc;

import java.util.Map;
import java.util.Properties;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

public class SaxHandler extends DefaultHandler {
    int layer = 0;
    String curSectionName = "";
    String curKey = "";
    String curValue = "";
    XmlPacket pktData;
    Map mpRecord;

    public SaxHandler(XmlPacket data) {
        this.pktData = data;
        this.mpRecord = new Properties();
    }

    public void startElement(String uri, String localName, String qName, Attributes attributes) throws SAXException {
        ++this.layer;
        if (this.layer == 4) {
            this.curSectionName = qName;
        } else if (this.layer == 5) {
            this.curKey = qName;
        }

    }

    public void endElement(String uri, String localName, String qName) throws SAXException {
        if (this.layer == 4) {
            this.pktData.putProperty(this.curSectionName, this.mpRecord);
            this.mpRecord = new Properties();
        } else if (this.layer == 5) {
            this.mpRecord.put(this.curKey, this.curValue);
            if (this.curSectionName.equals("rd")) {
                if (this.curKey.equals("ERPSqn")) {
                    this.pktData.setERPSqn(this.curValue);
                } else if (this.curKey.equals("instrRetMsg")) {
                    this.pktData.setInstrRetMsg(this.curValue);
                } else if (this.curKey.equals("instrRetCode")) {
                    this.pktData.setInstrRetCode(this.curValue);
                } else if (this.curKey.equals("Result")) {
                    this.pktData.setResult(this.curValue);
                } else if (this.curKey.equals("iRetCode")) {
                    this.pktData.setiRetCode(this.curValue);
                } else if (this.curKey.equals("iRetMsg")) {
                    this.pktData.setiRetMsg(this.curValue);
                } else if (this.curKey.equals("Toutfo")) {
                    this.pktData.setToutfo(this.curValue);
                } else if (this.curKey.equals("TInfoNew")) {
                    this.pktData.setTInfoNew(this.curValue);
                }
            }
        }

        this.curValue = "";
        --this.layer;
    }

    public void characters(char[] ch, int start, int length) throws SAXException {
        if (this.layer == 5) {
            String value = new String(ch, start, length);
            if (ch.equals("\n")) {
                this.curValue = this.curValue + "\r\n";
            } else {
                this.curValue = this.curValue + value;
            }
        }

    }
}
