package weaver.formmode.customjavacode.icbc;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import org.xml.sax.SAXException;

public class XmlPacket {
    protected String TransCode;
    protected String CIS;
    protected String BankCode;
    protected String ID;
    protected String TranDate;
    protected String TranTime;
    protected String fSeqno;
    protected String SerialNo;
    protected String RetCode;
    protected String RetMsg;
    protected String ERPSqn;
    protected String Result;
    protected String iRetCode;
    protected String iRetMsg;
    protected String instrRetCode;
    protected String instrRetMsg;
    protected String Toutfo;
    protected String TInfoNew;
    protected String EbillSerialno;
    protected String PayAccount;
    protected String PayAcctName;
    protected String PayBankName;
    protected String RecAccount;
    protected String RecAcctName;
    protected String RecBankName;
    protected String PayAmt;
    protected String CurrType;
    protected String Summary;
    protected String BusType;
    protected String UseCN;
    protected String TranSerialNo;
    protected String TimeStamp;
    protected String TransNetCode;
    protected String TransTellno;
    protected String TransDate;
    protected String NextTag;
    protected Map data;

    public XmlPacket() {
        this.data = new Properties();
    }

    public XmlPacket(String transCode, String cIS, String bankCode, String iD, String tranDate, String tranTime, String fSeqno) {
        this.TransCode = transCode;
        this.CIS = cIS;
        this.BankCode = bankCode;
        this.ID = iD;
        this.TranDate = tranDate;
        this.TranTime = tranTime;
        this.fSeqno = fSeqno;
        this.data = new Properties();
    }

    public XmlPacket(String eRPSqn, String result, String iRetCode, String iRetMsg, String instrRetCode, String instrRetMsg) {
        this.ERPSqn = eRPSqn;
        this.Result = result;
        this.iRetCode = iRetCode;
        this.iRetMsg = iRetMsg;
        this.instrRetCode = instrRetCode;
        this.instrRetMsg = instrRetMsg;
        this.data = new Properties();
    }

    public String getNextTag() {
        return this.NextTag;
    }

    public void setNextTag(String nextTag) {
        this.NextTag = nextTag;
    }

    public String getEbillSerialno() {
        return this.EbillSerialno;
    }

    public void setEbillSerialno(String ebillSerialno) {
        this.EbillSerialno = ebillSerialno;
    }

    public String getPayAccount() {
        return this.PayAccount;
    }

    public void setPayAccount(String payAccount) {
        this.PayAccount = payAccount;
    }

    public String getPayAcctName() {
        return this.PayAcctName;
    }

    public void setPayAcctName(String payAcctName) {
        this.PayAcctName = payAcctName;
    }

    public String getPayBankName() {
        return this.PayBankName;
    }

    public void setPayBankName(String payBankName) {
        this.PayBankName = payBankName;
    }

    public String getRecAccount() {
        return this.RecAccount;
    }

    public void setRecAccount(String recAccount) {
        this.RecAccount = recAccount;
    }

    public String getRecAcctName() {
        return this.RecAcctName;
    }

    public void setRecAcctName(String recAcctName) {
        this.RecAcctName = recAcctName;
    }

    public String getRecBankName() {
        return this.RecBankName;
    }

    public void setRecBankName(String recBankName) {
        this.RecBankName = recBankName;
    }

    public String getPayAmt() {
        return this.PayAmt;
    }

    public void setPayAmt(String payAmt) {
        this.PayAmt = payAmt;
    }

    public String getCurrType() {
        return this.CurrType;
    }

    public void setCurrType(String currType) {
        this.CurrType = currType;
    }

    public String getSummary() {
        return this.Summary;
    }

    public void setSummary(String summary) {
        this.Summary = summary;
    }

    public String getBusType() {
        return this.BusType;
    }

    public void setBusType(String busType) {
        this.BusType = busType;
    }

    public String getUseCN() {
        return this.UseCN;
    }

    public void setUseCN(String useCN) {
        this.UseCN = useCN;
    }

    public String getTranSerialNo() {
        return this.TranSerialNo;
    }

    public void setTranSerialNo(String tranSerialNo) {
        this.TranSerialNo = tranSerialNo;
    }

    public String getTimeStamp() {
        return this.TimeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.TimeStamp = timeStamp;
    }

    public String getTransNetCode() {
        return this.TransNetCode;
    }

    public void setTransNetCode(String transNetCode) {
        this.TransNetCode = transNetCode;
    }

    public String getTransTellno() {
        return this.TransTellno;
    }

    public void setTransTellno(String transTellno) {
        this.TransTellno = transTellno;
    }

    public String getTransDate() {
        return this.TransDate;
    }

    public void setTransDate(String transDate) {
        this.TransDate = transDate;
    }

    public String getTInfoNew() {
        return this.TInfoNew;
    }

    public void setTInfoNew(String tInfoNew) {
        this.TInfoNew = tInfoNew;
    }

    public String getToutfo() {
        return this.Toutfo;
    }

    public void setToutfo(String toutfo) {
        this.Toutfo = toutfo;
    }

    public String getERPSqn() {
        return this.ERPSqn;
    }

    public void setERPSqn(String eRPSqn) {
        this.ERPSqn = eRPSqn;
    }

    public String getResult() {
        return this.Result;
    }

    public void setResult(String result) {
        this.Result = result;
    }

    public String getiRetCode() {
        return this.iRetCode;
    }

    public void setiRetCode(String iRetCode) {
        this.iRetCode = iRetCode;
    }

    public String getiRetMsg() {
        return this.iRetMsg;
    }

    public void setiRetMsg(String iRetMsg) {
        this.iRetMsg = iRetMsg;
    }

    public String getInstrRetCode() {
        return this.instrRetCode;
    }

    public void setInstrRetCode(String instrRetCode) {
        this.instrRetCode = instrRetCode;
    }

    public String getInstrRetMsg() {
        return this.instrRetMsg;
    }

    public void setInstrRetMsg(String instrRetMsg) {
        this.instrRetMsg = instrRetMsg;
    }

    public String getTransCode() {
        return this.TransCode;
    }

    public void setTransCode(String transCode) {
        this.TransCode = transCode;
    }

    public String getCIS() {
        return this.CIS;
    }

    public void setCIS(String cIS) {
        this.CIS = cIS;
    }

    public String getBankCode() {
        return this.BankCode;
    }

    public void setBankCode(String bankCode) {
        this.BankCode = bankCode;
    }

    public String getID() {
        return this.ID;
    }

    public void setID(String iD) {
        this.ID = iD;
    }

    public String getTranDate() {
        return this.TranDate;
    }

    public void setTranDate(String tranDate) {
        this.TranDate = tranDate;
    }

    public String getTranTime() {
        return this.TranTime;
    }

    public void setTranTime(String tranTime) {
        this.TranTime = tranTime;
    }

    public String getfSeqno() {
        return this.fSeqno;
    }

    public void setfSeqno(String fSeqno) {
        this.fSeqno = fSeqno;
    }

    public Map getData() {
        return this.data;
    }

    public void setData(Map data) {
        this.data = data;
    }

    public String getSerialNo() {
        return this.SerialNo;
    }

    public void setSerialNo(String serialNo) {
        this.SerialNo = serialNo;
    }

    public String getRetCode() {
        return this.RetCode;
    }

    public void setRetCode(String retCode) {
        this.RetCode = retCode;
    }

    public String getRetMsg() {
        return this.RetMsg;
    }

    public void setRetMsg(String retMsg) {
        this.RetMsg = retMsg;
    }

    public boolean isError() {
        return !"0".equals(this.RetCode);
    }

    public void putProperty(String sSectionName, Map mpData) {
        Vector vt;
        if (this.data.containsKey(sSectionName)) {
            vt = (Vector)this.data.get(sSectionName);
            vt.add(mpData);
        } else {
            vt = new Vector();
            vt.add(mpData);
            this.data.put(sSectionName, vt);
        }

    }

    public Map getProperty(String sSectionName, int index) {
        return this.data.containsKey(sSectionName) ? (Map)((Vector)this.data.get(sSectionName)).get(index) : null;
    }

    public int getSectionSize(String sSectionName) {
        if (this.data.containsKey(sSectionName)) {
            Vector sec = (Vector)this.data.get(sSectionName);
            return sec.size();
        } else {
            return 0;
        }
    }

    public String toXmlString() {
        StringBuffer sfData = new StringBuffer("<?xml version=\"1.0\" encoding=\"GB2312\"?>");
        sfData.append("<CMS>");
        sfData.append("<eb>");
        sfData.append("<pub><TransCode>" + this.TransCode + "</TransCode><CIS>" + this.CIS + "</CIS><BankCode>" + this.BankCode + "</BankCode><ID>" + this.ID + "</ID><TranDate>" + this.TranDate + "</TranDate><TranTime>" + this.TranTime + "</TranTime><fSeqno>" + this.fSeqno + "</fSeqno></pub>");
        int secSize = this.data.size();
        Iterator itr = this.data.keySet().iterator();

        while(itr.hasNext()) {
            String secName = (String)itr.next();
            Vector vt = (Vector)this.data.get(secName);

            for(int i = 0; i < vt.size(); ++i) {
                Map record = (Map)vt.get(i);
                Iterator itr2 = record.keySet().iterator();
                sfData.append("<" + secName + ">");
                Object map = new HashMap();

                String datakey1;
                while(itr2.hasNext()) {
                    String datakey = (String)itr2.next();
                    if (datakey.equals("rd")) {
                        map = (Map)record.get(datakey);
                    } else {
                        sfData.append("<" + datakey + ">");
                        datakey1 = (String)record.get(datakey);
                        sfData.append(datakey1);
                        sfData.append("</" + datakey + ">");
                    }
                }

                sfData.append("<rd>");
                Iterator itr3 = ((Map)map).keySet().iterator();

                while(itr3.hasNext()) {
                    datakey1 = (String)itr3.next();
                    sfData.append("<" + datakey1 + ">");
                    String dataValue1 = (String)((Map)map).get(datakey1);
                    sfData.append(dataValue1);
                    sfData.append("</" + datakey1 + ">");
                }

                sfData.append("</rd>");
                sfData.append("</" + secName + ">");
            }
        }

        sfData.append("</eb>");
        sfData.append("</CMS>");
        return sfData.toString();
    }

    public static XmlPacket valueOf(String sTransCode, String message) {
        SAXParserFactory saxfac = SAXParserFactory.newInstance();

        try {
            SAXParser saxparser;
            ByteArrayInputStream is;
            if ("DownEBill".equals(sTransCode)) {
                saxparser = saxfac.newSAXParser();
                is = new ByteArrayInputStream(message.getBytes());
                XmlPacket xmlPkt = new XmlPacket();
                saxparser.parse(is, new SearchSaxHandler(xmlPkt));
                is.close();
                return xmlPkt;
            }

            saxparser = saxfac.newSAXParser();
            is = new ByteArrayInputStream(message.getBytes());
            ByteArrayInputStream is2 = new ByteArrayInputStream(message.getBytes());
            XmlPacket xmlPkt = new XmlPacket();
            saxparser.parse(is, new SearchSaxHandler(xmlPkt));
            saxparser.parse(is2, new SaxHandler(xmlPkt));
            is.close();
            return xmlPkt;
        } catch (ParserConfigurationException var7) {
            var7.printStackTrace();
        } catch (SAXException var8) {
            var8.printStackTrace();
        } catch (IOException var9) {
            var9.printStackTrace();
        }

        return null;
    }
}
