//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package weaver.formmode.customjavacode.icbc;

import cn.com.infosec.icbc.ReturnValue;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import sun.misc.BASE64Decoder;

public class HttpRequest {
    static SocketRequest socket = new SocketRequest();

    public HttpRequest() {
    }

    public static void main(String[] args) throws Exception {
        String sContentSign = getRequestString("QPAYENT", "***************", "102", "xktx.y.3100", "", "", "fseqno20200817154456208", "fseqno20200817154456208", "");
        System.out.println(sContentSign);

        try {
            List<Map> result = sendRequest(false, "fseqno20200817154456208", "QPAYENT", sContentSign);

            for(int i = 0; i < result.size(); ++i) {
                Map map = (Map)result.get(i);
                if (i == 0) {
                    String NextTag = (String)map.get("NextTag");
                    System.out.println(NextTag);
                    System.out.println("".equals(NextTag) || NextTag == null);
                }

                System.out.println(map.get("errer") != null);
                System.out.println(map.toString());
            }

            System.out.println(result.size());
        } catch (Exception var6) {
            System.out.println(var6.getMessage());
        }

    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String OnlBatF, String SettleMode, String TotalNum, String TotalAmt, String SignTime, String iSeqno, String PayType, String PayAccNo, String PayAccNameCN, String RecAccNo, String SysIOFlg, String CurrType, String PayAmt, String RecAccNameCN, String IsSameCity, String Prop, String RecCityName, String RecBankNo, String RecBankName, String UseCN, String Summary, String ERPSqn, String CrvouhNo) {
        String sContentSign = "<?xml version=\"1.0\" encoding=\"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate>" + TranDate + "</TranDate><TranTime>" + TranTime + "</TranTime><fSeqno>" + fSeqno + "</fSeqno></pub><in><OnlBatF>" + OnlBatF + "</OnlBatF><SettleMode>" + SettleMode + "</SettleMode><TotalNum>" + TotalNum + "</TotalNum><TotalAmt>" + TotalAmt + "</TotalAmt><SignTime>" + SignTime + "</SignTime><ReqReserved1></ReqReserved1><ReqReserved2></ReqReserved2><rd><iSeqno>" + iSeqno + "</iSeqno><ReimburseNo></ReimburseNo><ReimburseNum></ReimburseNum><StartDate></StartDate><StartTime></StartTime><PayType>" + PayType + "</PayType><PayAccNo>" + PayAccNo + "</PayAccNo><PayAccNameCN>" + PayAccNameCN + "</PayAccNameCN><PayAccNameEN></PayAccNameEN><RecAccNo>" + RecAccNo + "</RecAccNo><RecAccNameCN>" + RecAccNameCN + "</RecAccNameCN><RecAccNameEN></RecAccNameEN><SysIOFlg>" + SysIOFlg + "</SysIOFlg><IsSameCity>" + IsSameCity + "</IsSameCity><Prop>" + Prop + "</Prop><RecICBCCode></RecICBCCode><RecCityName>" + RecCityName + "</RecCityName><RecBankNo>" + RecBankNo + "</RecBankNo><RecBankName>" + RecBankName + "</RecBankName><CurrType>" + CurrType + "</CurrType><PayAmt>" + PayAmt + "</PayAmt><UseCode></UseCode><UseCN>" + UseCN + "</UseCN><EnSummary></EnSummary><PostScript></PostScript><Summary>" + Summary + "</Summary><Ref>" + ERPSqn + "</Ref><Oref></Oref><ERPSqn>" + ERPSqn + "</ERPSqn><BusCode></BusCode><ERPcheckno></ERPcheckno><CrvouhType></CrvouhType><CrvouhName></CrvouhName><CrvouhNo>" + CrvouhNo + "</CrvouhNo><BankType></BankType><FileNames></FileNames><Indexs></Indexs><PaySubNo></PaySubNo><RecSubNo></RecSubNo><MCardNo></MCardNo><MCardName></MCardName></rd></in></eb></CMS>";
        return sContentSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String OnlBatF, String SettleMode, String TotalNum, String TotalAmt, String SignTime, String ReqReserved1, String ReqReserved2, String mark, String kay) {
        String sContentSign = "<?xml version=\"1.0\" encoding=\"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate>" + TranDate + "</TranDate><TranTime>" + TranTime + "</TranTime><fSeqno>" + fSeqno + "</fSeqno></pub><in><OnlBatF>" + OnlBatF + "</OnlBatF><SettleMode>" + SettleMode + "</SettleMode><TotalNum>" + TotalNum + "</TotalNum><TotalAmt>" + TotalAmt + "</TotalAmt><SignTime>" + SignTime + "</SignTime><ReqReserved1></ReqReserved1><ReqReserved2></ReqReserved2>";
        return sContentSign;
    }

    public static String getRequestString(String iSeqno, String PayType, String PayAccNo, String PayAccNameCN, String RecAccNo, String SysIOFlg, String CurrType, String PayAmt, String RecAccNameCN, String IsSameCity, String Prop, String RecCityName, String RecBankNo, String RecBankName, String UseCN, String Summary, String ERPSqn, String CrvouhNo) {
        String sContentSign = "<rd><iSeqno>" + iSeqno + "</iSeqno><ReimburseNo></ReimburseNo><ReimburseNum></ReimburseNum><StartDate></StartDate><StartTime></StartTime><PayType>" + PayType + "</PayType><PayAccNo>" + PayAccNo + "</PayAccNo><PayAccNameCN>" + PayAccNameCN + "</PayAccNameCN><PayAccNameEN></PayAccNameEN><RecAccNo>" + RecAccNo + "</RecAccNo><RecAccNameCN>" + RecAccNameCN + "</RecAccNameCN><RecAccNameEN></RecAccNameEN><SysIOFlg>" + SysIOFlg + "</SysIOFlg><IsSameCity>" + IsSameCity + "</IsSameCity><Prop>" + Prop + "</Prop><RecICBCCode></RecICBCCode><RecCityName>" + RecCityName + "</RecCityName><RecBankNo>" + RecBankNo + "</RecBankNo><RecBankName>" + RecBankName + "</RecBankName><CurrType>" + CurrType + "</CurrType><PayAmt>" + PayAmt + "</PayAmt><UseCode></UseCode><UseCN>" + UseCN + "</UseCN><EnSummary></EnSummary><PostScript></PostScript><Summary>" + Summary + "</Summary><Ref>" + ERPSqn + "</Ref><Oref></Oref><ERPSqn>" + ERPSqn + "</ERPSqn><BusCode></BusCode><ERPcheckno></ERPcheckno><CrvouhType></CrvouhType><CrvouhName></CrvouhName><CrvouhNo>" + CrvouhNo + "</CrvouhNo><BankType></BankType><FileNames></FileNames><Indexs></Indexs><PaySubNo></PaySubNo><RecSubNo></RecSubNo><MCardNo></MCardNo><MCardName></MCardName></rd>";
        return sContentSign;
    }

    public static String getRequestString() {
        String sContentSign = "</in></eb></CMS>";
        return sContentSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String QryfSeqno, String QrySerialNo) {
        String sContenSign = "<?xml version=\"1.0\" encoding = \"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate>" + TranDate + "</TranDate><TranTime>" + TranTime + "</TranTime><fSeqno>" + fSeqno + "</fSeqno></pub><in><QryfSeqno>" + QryfSeqno + "</QryfSeqno><QrySerialNo>" + QrySerialNo + "</QrySerialNo></in></eb></CMS>";
        return sContenSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String AreaCode, String NetCode, String TellerNo, String WorkDate, String TranSerialNo, String AcctNo, String CurrencyType, String SignTime) {
        String sContenSign = "<?xml version=\"1.0\" encoding=\"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate /><TranTime /><fSeqno>" + fSeqno + "</fSeqno></pub><in><AreaCode>" + AreaCode + "</AreaCode><NetCode>" + NetCode + "</NetCode><TellerNo>" + TellerNo + "</TellerNo><WorkDate>" + WorkDate + "</WorkDate><TranSerialNo>" + TranSerialNo + "</TranSerialNo><AcctNo>" + AcctNo + "</AcctNo><CurrencyType>" + CurrencyType + "</CurrencyType><SignTime>" + SignTime + "</SignTime><ReqReserved1></ReqReserved1><ReqReserved2></ReqReserved2><ReqReserved3></ReqReserved3><ReqReserved4></ReqReserved4><AcctSeq></AcctSeq></in></eb></CMS>";
        return sContenSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String AccNo, String BeginDate, String EndDate, String MinAmt, String MaxAmt) {
        String sContenSign = "<?xml version=\"1.0\" encoding=\"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate /><TranTime /><fSeqno>" + fSeqno + "</fSeqno></pub><in><AccNo>" + AccNo + "</AccNo><BeginDate>" + BeginDate + "</BeginDate><EndDate>" + EndDate + "</EndDate><MinAmt>" + MinAmt + "</MinAmt><MaxAmt>" + MaxAmt + "</MaxAmt><BankType></BankType><NextTag></NextTag><CurrType></CurrType><DueBillNo></DueBillNo><AcctSeq></AcctSeq><ComplementFlag></ComplementFlag><CardAccNoDef></CardAccNoDef><DesByTime>1</DesByTime></in></eb></CMS>";
        return sContenSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String AccNo, String BeginDate, String EndDate, String MinAmt, String MaxAmt, String NextTag, String CurrType) {
        String sContenSign = "<?xml version=\"1.0\" encoding=\"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate /><TranTime /><fSeqno>" + fSeqno + "</fSeqno></pub><in><AccNo>" + AccNo + "</AccNo><BeginDate>" + BeginDate + "</BeginDate><EndDate>" + EndDate + "</EndDate><MinAmt>" + MinAmt + "</MinAmt><MaxAmt>" + MaxAmt + "</MaxAmt><BankType></BankType><NextTag>" + NextTag + "</NextTag><CurrType></CurrType><DueBillNo></DueBillNo><AcctSeq></AcctSeq><ComplementFlag></ComplementFlag><CardAccNoDef></CardAccNoDef><DesByTime>1</DesByTime></in></eb></CMS>";
        return sContenSign;
    }

    public static String getRequestString(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String TotalNum, String BLFlag, String SynFlag, String iSeqno, String AccNo, String CurrType) {
        String sContenSign = "<?xml version=\"1.0\" encoding = \"GBK\"?><CMS><eb><pub><TransCode>" + TransCode + "</TransCode><CIS>" + CIS + "</CIS><BankCode>" + BankCode + "</BankCode><ID>" + ID + "</ID><TranDate>" + TranDate + "</TranDate><TranTime>" + TranTime + "</TranTime><fSeqno>" + fSeqno + "</fSeqno></pub><in><TotalNum>" + TotalNum + "</TotalNum><BLFlag>" + BLFlag + "</BLFlag><SynFlag>" + SynFlag + "</SynFlag><rd><iSeqno>" + iSeqno + "</iSeqno><AccNo>" + AccNo + "</AccNo><CurrType>" + CurrType + "</CurrType><ReqReserved3></ReqReserved3><AcctSeq></AcctSeq><MainAcctNo></MainAcctNo></rd></in></eb></CMS>";
        return sContenSign;
    }

    public static List sendRequest(boolean signatureflgs, String sPackageIDs, String sTransCodes, String sContentSigns) {
        String NCIp = "*************";
        String NCPort = "8030";
        String NCPort2 = "8031";
        boolean signatureflg = signatureflgs;
        String retcertPath = "E:\\admin.crt";
        List<Map> reli = new ArrayList();
        HashMap rem = new HashMap();

        try {
            String sCoding = "GBK";
            String cmpVersion = "*******";
            String sZip = "0";
            String sLanguage = "zh_CN";
            String sTransCode = sTransCodes;
            String sBankCode = "102";
            String sGroupCIS = "***************";
            String sID = "xktx02.y.3100";
            int responseCode;
            String readLine;
            if (signatureflg) {
                URL aURL = new URL("http://" + NCIp + ":" + NCPort2);
                HttpURLConnection urlConnection = (HttpURLConnection)aURL.openConnection();
                urlConnection.setRequestMethod("POST");
                urlConnection.setDoInput(true);
                urlConnection.setDoOutput(true);
                urlConnection.setUseCaches(false);
                urlConnection.setRequestProperty("Content-Length", String.valueOf(sContentSigns.getBytes(sCoding).length));
                urlConnection.setRequestProperty("Content-Type", "INFOSEC_SIGN/1.0");
                BufferedOutputStream out = new BufferedOutputStream(urlConnection.getOutputStream());
                out.write(sContentSigns.getBytes(sCoding));
                out.flush();
                out.close();
                responseCode = urlConnection.getResponseCode();
                if (responseCode != 200) {
                    System.out.println("NC签名失败");
                }

                String resM = urlConnection.getResponseMessage();
                StringBuffer repContent = new StringBuffer("");
                InputStreamReader in = new InputStreamReader(urlConnection.getInputStream());
                BufferedReader bufferedReader = new BufferedReader(in);
                readLine = null;

                while((readLine = bufferedReader.readLine()) != null) {
                    repContent.append(readLine);
                }

                in.close();
                urlConnection.disconnect();
                int beginSign = 0;
                int endSign = 0;

                try {
                    beginSign = repContent.indexOf("<sign>") + 6;
                    endSign = repContent.indexOf("</sign>");
                    System.out.println("repIndx=" + beginSign + "&" + endSign);
                } catch (Exception var56) {
                    System.out.println("！！！！！！！！！！接收签名数据失败，请检查nc设置！！！！！！！！！！");
                }

                String repSignContent = repContent.substring(beginSign, endSign);
                System.out.println(repSignContent);
                Calendar rightNow = Calendar.getInstance();
                int year = rightNow.get(1);
                int month = rightNow.get(2) + 1;
                int date = rightNow.get(5);
                int hour = rightNow.get(11);
                int minute = rightNow.get(12);
                int second = rightNow.get(13);
                String sendTime = String.valueOf(year) + String.valueOf(month) + date + hour + minute + second;
                String urlStr1 = "http://" + NCIp + ":" + NCPort + "/servlet/ICBCCMPAPIReqServlet?PackageID=" + sPackageIDs + "&SendTime=" + sendTime;
                HttpClient myclient = new HttpClient();
                PostMethod mypost = new PostMethod(urlStr1);
                mypost.setRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=GBK");
                mypost.addParameter("Version", cmpVersion);
                mypost.addParameter("TransCode", sTransCodes);
                mypost.addParameter("BankCode", sBankCode);
                mypost.addParameter("GroupCIS", sGroupCIS);
                mypost.addParameter("ID", sID);
                mypost.addParameter("PackageID", sPackageIDs);
                mypost.addParameter("Cert", "");
                mypost.addParameter("Language", sLanguage);
                mypost.addParameter("zipFlag", sZip);
                mypost.addParameter("reqData", repSignContent);
                System.out.println("开始发送到加密端..." + System.currentTimeMillis());
                int returnFlag = myclient.executeMethod(mypost);
                System.out.println(returnFlag);
                System.out.println("已返回数据...");

                try {
                    String postResult = mypost.getResponseBodyAsString();
                    String mark = postResult;
                    if (postResult.startsWith("reqData=")) {
                        postResult = postResult.substring(8);
                    } else if (postResult.startsWith("errorCode=")) {
                        postResult = postResult.substring(10);
                    }

                    System.out.println("******************************NC返回******************************\n");
                    System.out.println(new String(postResult));
                    byte[] decodeResult = getFromBASE64(postResult);
                    String sysout = new String(decodeResult, sCoding);
                    int retsign = sysout.indexOf("ICBCCMP");
                    if (retsign != -1) {
                        int mLength = Integer.parseInt(sysout.substring(0, 10).trim());
                        String message = sysout.substring(10, 10 + mLength);
                        String seperator = sysout.substring(10 + mLength, 10 + mLength + 7);
                        if (seperator == null || !seperator.equals("ICBCCMP")) {
                            System.out.println("返回包分隔符错误");
                        }

                        byte[] crymessage = getFromBASE64(sysout.substring(10 + mLength + 7));
                        int retvserfysign = verifySign(message, crymessage, mLength, retcertPath);
                        if (retvserfysign != 0) {
                            System.out.println("返回包验签失败" + retvserfysign);
                        } else {
                            System.out.println("返回包验签成功");
                        }
                    }

                    System.out.println("******************************银企互联返回数据******************************\n");
                    System.out.println(sysout);
                    List<Map> result = new ArrayList();
                    if (sysout != null && !mark.startsWith("errorCode=")) {
                        Map map = socket.processResult(sTransCode, sysout);
                        if (map == null) {
                            map.put("errer", "System error");
                            result.add(map);
                        } else {
                            result.add(map);
                        }

                        return result;
                    }
                } catch (Exception var57) {
                    var57.printStackTrace();
                    Map map = new HashMap();
                    map.put("errer", var57.getMessage());
                    List<Map> result = new ArrayList();
                    result.add(map);
                    return result;
                }

                mypost.releaseConnection();
            } else {
                Calendar rightNow = Calendar.getInstance();
                int year = rightNow.get(1);
                int month = rightNow.get(2) + 1;
                responseCode = rightNow.get(5);
                int hour = rightNow.get(11);
                int minute = rightNow.get(12);
                int second = rightNow.get(13);
                String sendTime = String.valueOf(year) + String.valueOf(month) + responseCode + hour + minute + second;
                readLine = "http://" + NCIp + ":" + NCPort + "/servlet/ICBCCMPAPIReqServlet?PackageID=" + sPackageIDs + "&SendTime=" + sendTime;
                System.out.println("url==" + readLine);
                HttpClient client = new HttpClient();
                PostMethod post = new PostMethod(readLine);
                post.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=GBK");
                post.addParameter("Version", cmpVersion);
                post.addParameter("TransCode", sTransCodes);
                post.addParameter("BankCode", sBankCode);
                post.addParameter("GroupCIS", sGroupCIS);
                post.addParameter("ID", sID);
                post.addParameter("PackageID", sPackageIDs);
                post.addParameter("Cert", "");
                post.addParameter("Language", sLanguage);
                post.addParameter("reqData", sContentSigns);
                System.out.println(sContentSigns);
                System.out.println("开始发送。。。");
                client.executeMethod(post);
                System.out.println("发送成功。。。");

                try {
                    String postResult = post.getResponseBodyAsString();
                    System.out.println("******************************NC返回******************************\n");
                    System.out.println(new String(postResult));
                    if (postResult.startsWith("reqData=")) {
                        postResult = postResult.substring(8);
                        System.out.println("retMessage==" + new String(postResult));
                        byte[] decodeResult = getFromBASE64(postResult);
                        String sysout = new String(decodeResult, sCoding);
                        System.out.println("******************************银企互联返回数据******************************\n");
                        System.out.println(sysout);
                        if (!"QHISD".equals(sTransCode)) {
                            Map result = socket.processResult(sTransCode, sysout);
                            List<Map> result1 = new ArrayList();
                            result1.add(result);
                            return result1;
                        }

                        List<Map> result1 = socket.searchprocessResult(sTransCode, sysout);
                        return result1;
                    }

                    System.out.println("******************************银企互联返回数据******************************\n");
                    String mark = postResult;
                    if (postResult.startsWith("errorCode=")) {
                        postResult = postResult.substring(10);
                    }

                    byte[] decodeResult = getFromBASE64(postResult);
                    String sysout = new String(decodeResult, sCoding);
                    System.out.println(sysout);
                    if (new String(postResult) != null && !mark.startsWith("errorCode=")) {
                        if (!"QHISD".equals(sTransCode)) {
                            Map result = socket.processResult(sTransCode, sysout);
                            List<Map> result1 = new ArrayList();
                            result1.add(result);
                            return result1;
                        }

                        List<Map> result1 = socket.searchprocessResult(sTransCode, sysout);
                        return result1;
                    }
                } catch (Exception var58) {
                    var58.printStackTrace();
                    rem.put("errer", var58.getMessage());
                    reli.add(rem);
                    return reli;
                }

                post.releaseConnection();
            }

            return null;
        } catch (IOException var59) {
            var59.printStackTrace();
            var59.printStackTrace(System.out);
            if ("Connection refused: connect".equals(var59.getMessage())) {
                rem.put("errer", "连接前置机失败");
            } else {
                rem.put("errer", var59.getMessage());
            }

            reli.add(rem);
            return reli;
        } catch (Exception var60) {
            var60.printStackTrace();
            var60.printStackTrace(System.out);
            System.err.println("error:" + var60.getMessage());
            rem.put("errer", var60.getMessage());
            reli.add(rem);
            return reli;
        }
    }

    public static byte[] getFromBASE64(String s) {
        if (s == null) {
            return null;
        } else {
            BASE64Decoder decoder = new BASE64Decoder();

            try {
                return decoder.decodeBuffer(s);
            } catch (Exception var3) {
                return null;
            }
        }
    }

    public static int verifySign(String message, byte[] signinfo, int mlength, String certpath) {
        try {
            FileInputStream fii = new FileInputStream(new File(certpath));
            byte[] bPubCert = new byte[fii.available()];
            fii.read(bPubCert);
            return ReturnValue.verifySign(message.getBytes(), mlength, bPubCert, ReturnValue.base64dec(signinfo));
        } catch (InvalidKeyException var6) {
            System.out.println("InvalidKeyException:" + var6);
            var6.printStackTrace();
        } catch (NoSuchAlgorithmException var7) {
            System.out.println("NoSuchAlgorithmException:" + var7);
            var7.printStackTrace();
        } catch (SignatureException var8) {
            System.out.println("SignatureException:" + var8);
            var8.printStackTrace();
        } catch (IOException var9) {
            System.out.println("IOException:" + var9);
            var9.printStackTrace();
        }

        return -1;
    }
}
