//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package weaver.formmode.customjavacode.icbc;

import java.util.Map;
import java.util.Properties;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

public class SearchSaxHandler extends DefaultHandler {
    int layer = 0;
    String curSectionName = "";
    String curKey = "";
    String curValue = "";
    XmlPacket pktData;
    Map mpRecord;

    public SearchSaxHandler(XmlPacket data) {
        this.pktData = data;
        this.mpRecord = new Properties();
    }

    public void startElement(String uri, String localName, String qName, Attributes attributes) throws SAXException {
        ++this.layer;
        if (this.layer == 3) {
            this.curSectionName = qName;
        } else if (this.layer == 4) {
            this.curKey = qName;
        }

    }

    public void endElement(String uri, String localName, String qName) throws SAXException {
        if (this.layer == 3) {
            this.pktData.putProperty(this.curSectionName, this.mpRecord);
            this.mpRecord = new Properties();
        } else if (this.layer == 4) {
            this.mpRecord.put(this.curKey, this.curValue);
            if (this.curSectionName.equals("out")) {
                if (this.curKey.equals("EbillSerialno")) {
                    this.pktData.setEbillSerialno(this.curValue);
                } else if (this.curKey.equals("PayAccount")) {
                    this.pktData.setPayAccount(this.curValue);
                } else if (this.curKey.equals("PayAcctName")) {
                    this.pktData.setPayAcctName(this.curValue);
                } else if (this.curKey.equals("PayBankName")) {
                    this.pktData.setPayBankName(this.curValue);
                } else if (this.curKey.equals("RecAccount")) {
                    this.pktData.setRecAccount(this.curValue);
                } else if (this.curKey.equals("RecAcctName")) {
                    this.pktData.setRecAcctName(this.curValue);
                } else if (this.curKey.equals("RecBankName")) {
                    this.pktData.setRecBankName(this.curValue);
                } else if (this.curKey.equals("PayAmt")) {
                    this.pktData.setPayAmt(this.curValue);
                } else if (this.curKey.equals("CurrType")) {
                    this.pktData.setCurrType(this.curValue);
                } else if (this.curKey.equals("Summary")) {
                    this.pktData.setSummary(this.curValue);
                } else if (this.curKey.equals("BusType")) {
                    this.pktData.setBusType(this.curValue);
                } else if (this.curKey.equals("UseCN")) {
                    this.pktData.setUseCN(this.curValue);
                } else if (this.curKey.equals("TranSerialNo")) {
                    this.pktData.setTranSerialNo(this.curValue);
                } else if (this.curKey.equals("TransNetCode")) {
                    this.pktData.setTransCode(this.curValue);
                } else if (this.curKey.equals("TransTellno")) {
                    this.pktData.setTransTellno(this.curValue);
                } else if (this.curKey.equals("TransDate")) {
                    this.pktData.setTransDate(this.curValue);
                } else if (this.curKey.equals("NextTag")) {
                    this.pktData.setNextTag(this.curValue);
                }
            } else if (this.curSectionName.equals("pub")) {
                if (this.curKey.equals("RetCode")) {
                    this.pktData.setRetCode(this.curValue);
                } else if (this.curKey.equals("RetMsg")) {
                    this.pktData.setRetMsg(this.curValue);
                }
            }
        }

        this.curValue = "";
        --this.layer;
    }

    public void characters(char[] ch, int start, int length) throws SAXException {
        if (this.layer == 4) {
            String value = new String(ch, start, length);
            if (ch.equals("\n")) {
                this.curValue = this.curValue + "\r\n";
            } else {
                this.curValue = this.curValue + value;
            }
        }

    }
}
