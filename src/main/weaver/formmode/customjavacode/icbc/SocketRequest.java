package weaver.formmode.customjavacode.icbc;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompilpackage weaver.formmode.customjavacode.icbc;

import com.caucho.db.sql.Data;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class SocketRequest {
    public SocketRequest() {
    }

    public static void main(String[] args) {
        try {
            new SocketRequest();
        } catch (Exception var2) {
            System.out.println(var2.getMessage());
        }

    }

    public String getRequestStr(String TransCode, String CIS, String BankCode, String ID, String TranDate, String TranTime, String fSeqno, String OnlBatF, String SettleMode, String TotalNum, String TotalAmt, String SignTime, String iSeqno, String PayType, String RecAccNo, String SysIOFlg, String CurrType, String PayAmt, String RecAccNameCN, String IsSameCity, String Prop, String RecCityName, String RecBankNo, String RecBankName, String UseCN, String Summary, String ERPSqn, String CrvouhNo) {
        XmlPacket xmlPkt = new XmlPacket(TransCode, CIS, BankCode, ID, TranDate, TranTime, fSeqno);
        Map mpAccInfo = new Properties();
        Map mpAccInfo1 = new Properties();
        mpAccInfo.put("SettleMode", SettleMode);
        mpAccInfo.put("TotalNum", TotalNum);
        mpAccInfo.put("TotalAmt", TotalAmt);
        mpAccInfo.put("SignTime", SignTime);
        mpAccInfo1.put("iSeqno", iSeqno);
        mpAccInfo1.put("PayType", PayType);
        mpAccInfo.put("RecAccNo", RecAccNo);
        mpAccInfo.put("RecAccNameCN", RecAccNameCN);
        mpAccInfo1.put("SysIOFlg", SysIOFlg);
        mpAccInfo1.put("CurrType", CurrType);
        mpAccInfo1.put("PayAmt", PayAmt);
        mpAccInfo1.put("RecAccNameCN", RecAccNameCN);
        mpAccInfo1.put("IsSameCity", IsSameCity);
        mpAccInfo1.put("Prop", Prop);
        mpAccInfo1.put("RecCityName", RecCityName);
        mpAccInfo1.put("RecBankNo", RecBankNo);
        mpAccInfo1.put("RecBankName", RecBankName);
        mpAccInfo1.put("UseCN", UseCN);
        mpAccInfo1.put("Summary", Summary);
        mpAccInfo1.put("ERPSqn", ERPSqn);
        mpAccInfo1.put("CrvouhNo", CrvouhNo);
        mpAccInfo.put("rd", mpAccInfo1);
        xmlPkt.putProperty("in", mpAccInfo);
        return xmlPkt.toXmlString();
    }

    public String getRequestStr(String TransCode, String CIS, String BankCode, String ID, String fSeqno, String QryfSeqno, String QrySerialNo) {
        XmlPacket xmlPkt = new XmlPacket(TransCode, CIS, BankCode, ID, (new Data()).toString(), (new Data()).toString(), fSeqno);
        Map mpAccInfo = new Properties();
        mpAccInfo.put("QryfSeqno", QryfSeqno);
        mpAccInfo.put("QrySerialNo", QrySerialNo);
        xmlPkt.putProperty("in", mpAccInfo);
        return xmlPkt.toXmlString();
    }

    public String getRequestStr(String TransCode, String CIS, String BankCode, String ID, String fSeqno, String AreaCode, String NetCode, String TellerNo, String WorkDate, String TranSerialNo, String AcctNo, String CurrencyType, String SignTime) {
        XmlPacket xmlPkt = new XmlPacket(TransCode, CIS, BankCode, ID, (new Data()).toString(), (new Data()).toString(), fSeqno);
        Map mpAccInfo = new Properties();
        mpAccInfo.put("AreaCode", AreaCode);
        mpAccInfo.put("NetCode", NetCode);
        mpAccInfo.put("TellerNo", TellerNo);
        mpAccInfo.put("WorkDate", WorkDate);
        mpAccInfo.put("TranSerialNo", TranSerialNo);
        mpAccInfo.put("AcctNo", AcctNo);
        mpAccInfo.put("CurrencyType", CurrencyType);
        mpAccInfo.put("SignTime", SignTime);
        xmlPkt.putProperty("in", mpAccInfo);
        return xmlPkt.toXmlString();
    }

    public String sendRequest(String data) {
        String result = "";

        try {
            URL url = new URL("http://localhost:8080");
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            OutputStream os = conn.getOutputStream();
            os.write(data.toString().getBytes("gbk"));
            os.close();

            BufferedReader br;
            String line;
            for(br = new BufferedReader(new InputStreamReader(conn.getInputStream())); (line = br.readLine()) != null; result = result + line) {
            }

            System.out.println(result);
            br.close();
        } catch (MalformedURLException var8) {
            var8.printStackTrace();
        } catch (UnsupportedEncodingException var9) {
            var9.printStackTrace();
        } catch (ProtocolException var10) {
            var10.printStackTrace();
        } catch (IOException var11) {
            var11.printStackTrace();
        }

        return result;
    }

    public Map processResult(String sTransCode, String result) {
        Map re = new HashMap();
        if (result != null && result.length() > 0) {
            XmlPacket pktRsp = XmlPacket.valueOf(sTransCode, result);
            if (pktRsp != null) {
                if (pktRsp.isError()) {
                    System.out.println("取账户信息失败：" + pktRsp.getRetMsg());
                    re.put("errer", pktRsp.getRetMsg());
                    return re;
                } else {
                    Map propAcc;
                    if ("DownEBill".equals(sTransCode)) {
                        propAcc = pktRsp.getProperty("out", 0);
                        return propAcc;
                    } else {
                        propAcc = pktRsp.getProperty("rd", 0);
                        return propAcc;
                    }
                }
            } else {
                System.out.println("响应报文解析失败");
                re.put("errer", "响应报文解析失败");
                return re;
            }
        } else {
            return null;
        }
    }

    public List<Map> searchprocessResult(String sTransCode, String result) {
        Map re = new HashMap();
        List<Map> li = new ArrayList();
        if (result != null && result.length() > 0) {
            XmlPacket pktRsp = XmlPacket.valueOf(sTransCode, result);
            if (pktRsp != null) {
                if (pktRsp.isError()) {
                    System.out.println("取信息失败" + pktRsp.getRetMsg());
                    re.put("errer", pktRsp.getRetMsg());
                    li.add(re);
                    return li;
                } else {
                    int size = pktRsp.getSectionSize("rd");

                    for(int i = 0; i < size; ++i) {
                        Map propAcc = pktRsp.getProperty("rd", i);
                        if (i == 0) {
                            propAcc.put("NextTag", pktRsp.getNextTag());
                        }

                        li.add(propAcc);
                    }

                    Map propAcc = pktRsp.getProperty("rd", 0);
                    System.out.println("娴佺▼瀹炰緥鍙�:" + propAcc.get("REQNBR") + ",涓氬姟缂栫爜锛�" + propAcc.get("BUSCOD") + ",涓氬姟璇锋眰鐘舵��:" + propAcc.get("REQSTS") + ",涓氬姟澶勭悊缁撴灉  :" + propAcc.get("RTNFLG"));
                    return li;
                }
            } else {
                System.out.println("响应报文解析失败");
                re.put("errer", "响应报文解析失败");
                li.add(re);
                return li;
            }
        } else {
            return null;
        }
    }
}
