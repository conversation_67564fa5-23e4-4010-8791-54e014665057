package com.api.cyitce.seal2.cmd.thrid;

import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;

public class InsertThridDataCmd extends AbstractCommonCommand<Boolean> {
    private int type;
    private String oaId;
    private String thridId;
    private ThirdMappConfig config;

    public InsertThridDataCmd(int type, String oaId, String thridId, ThirdMappConfig config) {
        this.type = type;
        this.oaId = oaId;
        this.thridId = thridId;
        this.config = config;
    }

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        String sql = "insert into " + config.getThirdTable() + "(type,oaid,sealid) values(" + type + ",'" + oaId + "','" + thridId + "')";
        writeLog("三方数据插入sql : " + sql);
        rs.execute(sql);
        return rs.next();
    }
}
