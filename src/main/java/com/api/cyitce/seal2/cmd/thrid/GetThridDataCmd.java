package com.api.cyitce.seal2.cmd.thrid;

import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

public class GetThridDataCmd extends AbstractCommonCommand<String> {
    private String oaId;
    private int type;
    private ThirdMappConfig config;

    public GetThridDataCmd(String oaId, int type, ThirdMappConfig config) {
        this.oaId = oaId;
        this.type = type;
        this.config = config;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        String sql = "select sealid from  " + config.getThirdTable() + "  where type=" + type + " and oaid='" + oaId + "'";
        new BaseBean().writeLog("GetThridDataCmd-------------------------> 执行sql " + sql);
        rs.executeQuery(sql);
        new BaseBean().writeLog("GetThridDataCmd-------------------------> 执行sql " + rs.next() + "  " + rs.getString("sealid"));
        // if (rs.next()) {
        //     return Util.null2String(rs.getString("sealid"));
        // }
        //
        // // return rs.next() ? Util.null2String(rs.getString("sealid")) : "";
        return rs.getString("sealid");
    }
}
