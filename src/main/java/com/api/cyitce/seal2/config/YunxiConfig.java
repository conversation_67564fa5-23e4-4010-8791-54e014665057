package com.api.cyitce.seal2.config;


public class YunxiConfig extends BaseConfig {
    String baseUrl = props("yunxi.baseurl"); //
    String appKey = props("yunxi.appKey"); //
    String appSecret = props("yunxi.appSecret"); //
    String tenant = props("yunxi.tenant"); //
    String topId = props("yunxi.topId"); //
    String ipUrl = props("yunxi.ipUrl"); //

    public String getIpUrl() {
        return ipUrl;
    }

    public void setIpUrl(String ipUrl) {
        this.ipUrl = ipUrl;
    }

    public String getTopId() {
        return topId;
    }

    public void setTopId(String topId) {
        this.topId = topId;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    @Override
    public String toString() {
        return "YunxiConfig{" +
                "baseUrl='" + baseUrl + '\'' +
                ", appKey='" + appKey + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", tenant='" + tenant + '\'' +
                ", topId='" + topId + '\'' +
                ", ipUrl='" + ipUrl + '\'' +
                '}';
    }
}