package com.api.cyitce.seal2.config.eSeal;

import com.api.cyitce.seal2.config.BaseConfig;

/**
 * @ClassName: StampConfig
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  11:21
 * @Version: 1.0
 */
public class ESealConfig extends BaseConfig {
    String url = props("itruscloud.eSeal.api.url");

    String appId = props("itruscloud.appId");

    String secretKey = props("itruscloud.secretKey");

    String serviceCode = props("itruscloud.eSeal.api.serviceCode");

    String companyUUID = props("itruscloud.eSeal.api.companyUUID");

    String savePath = props("itruscloud.eSeal.api.savePath");

    public String getUrl() {
        return url;
    }

    public String getAppId() {
        return appId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public String getServiceCode() { return serviceCode; }

    public String getCompanyUUID() { return companyUUID; }

    public String getSavePath() {
        return savePath;
    }
}