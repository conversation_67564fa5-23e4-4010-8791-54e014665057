package com.api.cyitce.seal2.config.yunxi;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.YunxiConfig;
import com.api.cyitce.seal2.util.AesUtils;

public class ApiClient extends BaseLog {
    private final String baseUrl;
    private final String appKey;
    private final String tenant;
    private final String apiSecret;
    private final boolean encryptionEnabled; //

    /**
     * API客户端
     *
     * @param baseUrl           API基础地址,
     * @param appKey            由平台提供的应用标识
     * @param tenant            由平台提供的租户标识
     * @param apiSecret         密钥
     * @param encryptionEnabled 是否启用加密
     */
    public ApiClient(String baseUrl, String appKey, String tenant, String apiSecret, boolean encryptionEnabled) {
        YunxiConfig yunxiConfig = new YunxiConfig();
        // 如果外部传入的 baseUrl 不为空，则使用外部的；否则，使用配置类中的。
        this.baseUrl = StrUtil.isNotBlank(baseUrl) ? baseUrl : yunxiConfig.getBaseUrl();

        // 对 appKey 应用同样的逻辑
        this.appKey = StrUtil.isNotBlank(appKey) ? appKey : yunxiConfig.getAppKey();

        // 对 tenant 应用同样的逻辑
        this.tenant = StrUtil.isNotBlank(tenant) ? tenant : yunxiConfig.getTenant();

        // 对 apiSecret 应用同样的逻辑
        this.apiSecret = StrUtil.isNotBlank(apiSecret) ? apiSecret : yunxiConfig.getAppSecret();

        // encryptionEnabled 是布尔值，直接使用即可
        this.encryptionEnabled = encryptionEnabled;

    }

    /**
     * 通用的API执行方法
     *
     * @param apiKey       具体业务的接口标识
     * @param params       业务参数对象 (任何可以被Hutool序列化的POJO)
     * @param responseType 期望的响应数据类型 (data字段反序列化后的类型)
     * @param <T>          响应数据类型的泛型
     * @return 成功时返回反序列化后的业务数据对象
     * @throws ApiException     如果API返回业务错误 (code != 0)
     * @throws RuntimeException 如果发生网络或JSON解析错误
     */
    public <T> T execute(String apiKey, Object params, Class<T> responseType) throws ApiException {
        String paramsStr = (params != null) ? JSONUtil.toJsonStr(params) : "{}";

        if (this.encryptionEnabled) {
            try {
                // *** 核心修改：调用提供的 AesUtils.encrypt 方法 ***
                paramsStr = AesUtils.encrypt(paramsStr, this.apiSecret);
            } catch (Exception e) {
                throw new RuntimeException("参数加密失败: " + e.getMessage(), e);
            }
        }

        ApiRequest requestPayload = new ApiRequest();
        requestPayload.setAppKey(this.appKey);
        requestPayload.setApiKey(apiKey);
        requestPayload.setBizId(IdUtil.randomUUID());
        requestPayload.setTimestamp(System.currentTimeMillis());
        requestPayload.setTenant(this.tenant);
        requestPayload.setParams(paramsStr);

        String requestBody = JSONUtil.toJsonStr(requestPayload);

        try {
            String endpoint = this.baseUrl + "/business/docking/execute";
            String responseBody = HttpRequest.post(endpoint)
                    .header("Content-Type", "application/json")
                    .timeout(20000)
                    .body(requestBody)
                    .execute()
                    .body();

            ApiResponse apiResponse = JSONUtil.toBean(responseBody, ApiResponse.class);

            if (apiResponse.getCode() != 0) {
                throw new ApiException(apiResponse.getCode(), apiResponse.getMessage(), apiResponse.getBizId());
            }

            String data = apiResponse.getData();
            if (data != null && !data.isEmpty() && responseType != null && responseType != Void.class) {
                return JSONUtil.toBean(data, responseType);
            }
            return null;
        } catch (Exception e) {
            if (e instanceof ApiException) throw (ApiException) e;
            throw new RuntimeException("API请求执行失败: " + e.getMessage(), e);
        }
    }


    // public <T> T execute(String apiKey, Object params, Class<T> responseType) throws ApiException {
    //     // 1. 使用 Hutool 将业务参数 params 序列化为JSON字符串
    //     String paramsStr = (params != null) ? JSONUtil.toJsonStr(params) : "{}";
    //     info("云玺------------------> 接受到的参数: " + paramsStr);
    //     // 2. 组装完整的请求体
    //     ApiRequest requestPayload = new ApiRequest();
    //     requestPayload.setAppKey(this.appKey);
    //     requestPayload.setApiKey(apiKey);
    //     // 使用 Hutool 生成 UUID
    //     requestPayload.setBizId(IdUtil.randomUUID());
    //     requestPayload.setTimestamp(System.currentTimeMillis());
    //     requestPayload.setTenant(this.tenant);
    //     requestPayload.setParams(paramsStr);
    //
    //     // 使用 Hutool 将整个请求体序列化
    //     String requestBody = JSONUtil.toJsonStr(requestPayload);
    //     info("云玺------------------> 组装后接受到的参数: " + requestBody);
    //     try {
    //         // 3. 使用 Hutool 发送HTTP POST请求
    //         String endpoint = this.baseUrl + "/business/docking/execute";
    //         String responseBody = HttpRequest.post(endpoint)
    //                 .header("Content-Type", "application/json")
    //                 .timeout(10000) // 设置超时，单位毫秒
    //                 .body(requestBody)
    //                 .execute()
    //                 .body();
    //
    //         // 4. 使用 Hutool 解析响应体
    //         ApiResponse apiResponse = JSONUtil.toBean(responseBody, ApiResponse.class);
    //         info("云玺------------------> 请求响应数据: " + JSONUtil.toJsonStr(apiResponse));
    //         // 5. 检查业务状态码
    //         if (apiResponse.getCode() != 0) {
    //             throw new ApiException(apiResponse.getCode(), apiResponse.getMessage(), apiResponse.getBizId());
    //         }
    //
    //         // 6. 如果成功，且调用者需要结果，则解析data字段
    //         String data = apiResponse.getData();
    //
    //         if (data != null && !data.isEmpty() && responseType != null && responseType != Void.class) {
    //             return JSONUtil.toBean(data, responseType);
    //         }
    //
    //         return null;
    //
    //     } catch (Exception e) {
    //         // 将Hutool的异常或其他运行时异常统一包装后抛出
    //         throw new RuntimeException("API请求执行失败: " + e.getMessage(), e);
    //     }
    // }
}