package com.api.cyitce.seal2.config;

import weaver.general.BaseBean;

/**
 * @Description: 通用日志
 * @Author: lijianpan
 */
public class BaseLog extends BaseBean {

    private static final String LOG_PRE = "电子印章集成模块 seal：";
    private String className = null;

    public BaseLog() {
    }

    public BaseLog(Class classs) {
        this.className = classs.getName();
    }

    private static StringBuilder log(String val){
        return new StringBuilder(LOG_PRE).append(val);
    }

    public void writeLog(Object val) {
        super.writeLog(null==this.className?this.getClass().getName():this.className,val);
    }

    public void info(String val,String ...Parameters) {
        StringBuilder result = log(val);
        int index = 0;
        int start;
        while ((start=result.indexOf("{?}"))!= -1 && index < Parameters.length) {
            // 替换占位符为 Parameters 中的元素
            if (index < Parameters.length) {
                result.replace(start, start + 3, Parameters[index]);
                index++;
            } else {
                result.replace(start, start + 3, "");
            }
        }

        writeLog(result);
    }

    public void error(String val,Object ...Parameters) {
        StringBuilder result = log(val);
        int index = 0;
        int start = result.indexOf("{?}");
        while (start!= -1 && index < Parameters.length) {
            // 替换占位符为 Parameters 中的元素
            if (index < Parameters.length) {
                if (Parameters[index] instanceof Exception) {
                    writeLog(result);
                    writeLog(Parameters[index]);
                    break;
                }
                result.replace(start, start + 3, String.valueOf(Parameters[index]));
                index++;
            } else {
                result.replace(start, start + 3, "");
            }
        }

        writeLog(result);
    }
}