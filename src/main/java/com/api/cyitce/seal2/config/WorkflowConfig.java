package com.api.cyitce.seal2.config;

import weaver.conn.RecordSet;
import weaver.general.Util;

public class WorkflowConfig {
    private String createrfieldname;
    private Integer datasource;
    private String fsealType;
    private String ffile;
    private String ftitle;
    private String fsigner;
    private int signer;
    private String yzlx;
    private String sqfs;
    private String yqgs;
    private String gszd;
    private String mbzd;
    private String sfczmb;
    private String sfxyyy;
    private String dzyypt;
    private String yysp;
    private String jbrbm;
    private String jbr;
    private String sfkqstyz;
    private String mbsx;
    private String lcbh;
    private String gdyz;
    private String wdmx;
    private String wdtfj;
    private String wdsqr;

    public String getWdsqr() {
        return wdsqr;
    }

    public void setWdsqr(String wdsqr) {
        this.wdsqr = wdsqr;
    }


    public String getWdtfj() {
        return wdtfj;
    }

    public void setWdtfj(String wdtfj) {
        this.wdtfj = wdtfj;
    }

    public String getWdmx() {
        return wdmx;
    }

    public void setWdmx(String wdmx) {
        this.wdmx = wdmx;
    }

    public String getGdyz() {
        return gdyz;
    }

    public void setGdyz(String gdyz) {
        this.gdyz = gdyz;
    }


    public String getLcbh() {
        return lcbh;
    }

    public void setLcbh(String lcbh) {
        this.lcbh = lcbh;
    }

    public String getGzr() {
        return gzr;
    }

    public void setGzr(String gzr) {
        this.gzr = gzr;
    }

    private String gzr;

    public String getMbsx() {
        return mbsx;
    }

    public void setMbsx(String mbsx) {
        this.mbsx = mbsx;
    }


    public String getSfkqstyz() {
        return sfkqstyz;
    }

    public void setSfkqstyz(String sfkqstyz) {
        this.sfkqstyz = sfkqstyz;
    }


    public String getJbr() {
        return jbr;
    }

    public void setJbr(String jbr) {
        this.jbr = jbr;
    }


    public String getJbrbm() {
        return jbrbm;
    }

    public void setJbrbm(String jbrbm) {
        this.jbrbm = jbrbm;
    }

    public String getYysp() {
        return yysp;
    }

    public void setYysp(String yysp) {
        this.yysp = yysp;
    }


    public String getDzyypt() {
        return dzyypt;
    }

    public void setDzyypt(String dzyypt) {
        this.dzyypt = dzyypt;
    }


    public String getSfxyyy() {
        return sfxyyy;
    }

    public void setSfxyyy(String sfxyyy) {
        this.sfxyyy = sfxyyy;
    }


    public String getSfczmb() {
        return sfczmb;
    }

    public void setSfczmb(String sfczmb) {
        this.sfczmb = sfczmb;
    }


    public String getMbzd() {
        return mbzd;
    }

    public void setMbzd(String mbzd) {
        this.mbzd = mbzd;
    }


    public String getGszd() {
        return gszd;
    }

    public void setGszd(String gszd) {
        this.gszd = gszd;
    }


    public String getYqgs() {
        return yqgs;
    }

    public void setYqgs(String yqgs) {
        this.yqgs = yqgs;
    }


    public String getSqfs() {
        return sqfs;
    }

    public void setSqfs(String sqfs) {
        this.sqfs = sqfs;
    }


    public String getYzlx() {
        return yzlx;
    }

    public void setYzlx(String yzlx) {
        this.yzlx = yzlx;
    }


    public String getCreaterfieldname() {
        return createrfieldname;
    }

    public Integer getDatasource() {
        return datasource;
    }

    public boolean isDetail() {
        return !"0".equals(datasource);
    }

    public String getFsealType() {
        return fsealType;
    }

    public String getFfile() {
        return ffile;
    }

    public String getFtitle() {
        return ftitle;
    }

    public String getFsigner() {
        return fsigner;
    }

    public int getSigner() {
        return signer;
    }

    public boolean isSigner() {
        return signer != -1;
    }

    public WorkflowConfig(int configId) {
        RecordSet rs = new RecordSet();

        rs.executeQuery("select * from uf_wf_seal_config where id = ?", configId);

        if (rs.next()) {
            createrfieldname = Util.null2String(rs.getString("createrfieldname"));
            datasource = rs.getInt("datasource");
            fsealType = Util.null2String(rs.getString("fsealType"));
            ffile = Util.null2String(rs.getString("ffile"));
            ftitle = Util.null2String(rs.getString("ftitle"));
            fsigner = Util.null2String(rs.getString("fsigner"));
            signer = rs.getInt("signer");
            sqfs = Util.null2String(rs.getString("sqfs"));
            yzlx = Util.null2String(rs.getString("yzlx")); // 印章类型
            yqgs = Util.null2String(rs.getString("yqgs")); // 要求公司
            gszd = Util.null2String(rs.getString("gszd")); // 公司字段
            mbzd = Util.null2String(rs.getString("mbzd")); // 模板字段
            sfczmb = Util.null2String(rs.getString("sfczmb")); // 是否存在模板
            sfxyyy = Util.null2String(rs.getString("sfxyyy")); // 是否需要用印
            dzyypt = Util.null2String(rs.getString("dzyypt")); // 是否需要用印
            yysp = Util.null2String(rs.getString("yysp")); // 是否需要用印
            jbrbm = Util.null2String(rs.getString("jbrbm")); // 是否需要用印
            jbr = Util.null2String(rs.getString("jbr")); // 是否需要用印
            sfkqstyz = Util.null2String(rs.getString("sfkqstyz")); // 是否开启实体印章
            mbsx = Util.null2String(rs.getString("mbsx")); //  模板事项
            gzr = Util.null2String(rs.getString("gzr")); // 盖章人
            lcbh = Util.null2String(rs.getString("lcbh")); // 流程编号
            gdyz = Util.null2String(rs.getString("gdyz")); // 固定印章
            wdmx = Util.null2String(rs.getString("wdmx")); // 外带明细
            wdtfj = Util.null2String(rs.getString("wdtfj")); // 外带附件
            wdsqr = Util.null2String(rs.getString("wdsqr")); // 外带附件
        }

    }

    @Override
    public String toString() {
        return "WorkflowConfig{" +
                "createrfieldname='" + createrfieldname + '\'' +
                ", datasource=" + datasource +
                ", fsealType='" + fsealType + '\'' +
                ", ffile='" + ffile + '\'' +
                ", ftitle='" + ftitle + '\'' +
                ", fsigner='" + fsigner + '\'' +
                ", signer=" + signer +
                ", yzlx='" + yzlx + '\'' +
                ", sqfs='" + sqfs + '\'' +
                ", yqgs='" + yqgs + '\'' +
                ", gszd='" + gszd + '\'' +
                ", mbzd='" + mbzd + '\'' +
                ", sfczmb='" + sfczmb + '\'' +
                ", sfxyyy='" + sfxyyy + '\'' +
                ", dzyypt='" + dzyypt + '\'' +
                ", yysp='" + yysp + '\'' +
                ", jbrbm='" + jbrbm + '\'' +
                ", jbr='" + jbr + '\'' +
                '}';
    }
}
