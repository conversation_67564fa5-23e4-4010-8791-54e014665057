package com.api.cyitce.seal2.config.realauth;

import com.api.cyitce.seal2.config.BaseConfig;


/**
 * @Description: 天威云基础配置
 * @Author:lijianpan
 **/
public class RealAuthConfig extends BaseConfig {
    /**
     * 请求地址
     */
    String url = props("itruscloud.realAuth.api.url");
    /**
     * 天威云 appId
     */
    String appId = props("itruscloud.appId");
    /**
     * 天威云 SecretKey
     */
    String secretKey = props("itruscloud.secretKey");

    public String getUrl() {
        return url;
    }

    public String getAppId() {
        return appId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    @Override
    public String toString() {
        return "RealAuthConfig{" +
                "url='" + url + '\'' +
                ", appId='" + appId + '\'' +
                ", secretKey='" + secretKey + '\'' +
                '}';
    }
}
