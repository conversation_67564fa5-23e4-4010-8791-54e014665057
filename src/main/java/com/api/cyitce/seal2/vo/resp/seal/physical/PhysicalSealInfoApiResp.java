package com.api.cyitce.seal2.vo.resp.seal.physical;

/**
 * @ClassName: PhysicalSealInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  17:49
 * @Version: 1.0
 */
public class PhysicalSealInfoApiResp {
    /**
     * 印章 ID 必填
     **/
    private Long Id;

    /**
     * 印章编号 必填
     **/
    private String sealCode;

    /**
     * 印章名称 必填
     **/
    private String sealName;

    /**
     * 印章类型 非必填
     **/
    private String sealType;

    /**
     * 印章状态 0：停用 1：启用 缺省为启用
     **/
    private String sealState;

    /**
     * 印章宽度 非必填
     **/
    private Integer sealWidth;

    /**
     * 印章高度 非必填
     **/
    private Integer sealHeight;

    /**
     * 是否蘸印泥 必填 0：无需蘸印泥 1：需要蘸印泥 默认是1
     **/
    private String inkpadType;

    /**
     * 当前所在印控仪编号 非必填
     **/
    private String instrumentCode;

    /**
     * 当前所在印控仪名称 非必填
     **/
    private String instrumentName;

    /**
     * 印章在印控仪中的位置 非必填
     **/
    private Integer slotNum;

    /**
     * 印章所属单位id 非必填
     **/
    private Integer departmentID;

    /**
     * 印章所属单位名称 非必填
     **/
    private String departmentName;

    /**
     * 印章保管人 非必填，多值时以“；”分割
     **/
    private String sealKeeperLoginName;

    /**
     * 印章保管人名称 非必填，多值时以“；”分割
     **/
    private String sealKeeperName;

    public Long getId() {
        return Id;
    }

    public void setId(Long id) {
        Id = id;
    }

    public String getSealCode() {
        return sealCode;
    }

    public void setSealCode(String sealCode) {
        this.sealCode = sealCode;
    }

    public String getSealName() {
        return sealName;
    }

    public void setSealName(String sealName) {
        this.sealName = sealName;
    }

    public String getSealType() {
        return sealType;
    }

    public void setSealType(String sealType) {
        this.sealType = sealType;
    }

    public String getSealState() {
        return sealState;
    }

    public void setSealState(String sealState) {
        this.sealState = sealState;
    }

    public Integer getSealWidth() {
        return sealWidth;
    }

    public void setSealWidth(Integer sealWidth) {
        this.sealWidth = sealWidth;
    }

    public Integer getSealHeight() {
        return sealHeight;
    }

    public void setSealHeight(Integer sealHeight) {
        this.sealHeight = sealHeight;
    }

    public String getInkpadType() {
        return inkpadType;
    }

    public void setInkpadType(String inkpadType) {
        this.inkpadType = inkpadType;
    }

    public String getInstrumentCode() {
        return instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode) {
        this.instrumentCode = instrumentCode;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public Integer getSlotNum() {
        return slotNum;
    }

    public void setSlotNum(Integer slotNum) {
        this.slotNum = slotNum;
    }

    public Integer getDepartmentID() {
        return departmentID;
    }

    public void setDepartmentID(Integer departmentID) {
        this.departmentID = departmentID;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getSealKeeperLoginName() {
        return sealKeeperLoginName;
    }

    public void setSealKeeperLoginName(String sealKeeperLoginName) {
        this.sealKeeperLoginName = sealKeeperLoginName;
    }

    public String getSealKeeperName() {
        return sealKeeperName;
    }

    public void setSealKeeperName(String sealKeeperName) {
        this.sealKeeperName = sealKeeperName;
    }
}