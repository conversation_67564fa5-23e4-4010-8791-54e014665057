package com.api.cyitce.seal2.vo.req.yunxi;

/**
 * 申请单附件实体类 (Base64方式)
 * (用于 YXYG03_25 创建申请单接口的嵌套对象)
 */
public class YxApplicationFileBase64 {
    /**
     * 合同附件名称
     */
    private String fileName;

    /**
     * 合同附件的Base64编码字符串
     */
    private String fileBase64;

    public YxApplicationFileBase64() {}

    public YxApplicationFileBase64(String fileName, String fileBase64) {
        this.fileName = fileName;
        this.fileBase64 = fileBase64;
    }

    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    public String getFileBase64() { return fileBase64; }
    public void setFileBase64(String fileBase64) { this.fileBase64 = fileBase64; }
}