package com.api.cyitce.seal2.vo.req.yunxi;

public class YxUser {
    /**
     * 用户id (必填)
     */
    private String id;

    /**
     * 组织id, 为空时用户归属到根组织下
     */
    private String deptId;

    /**
     * 用户名 (必填)
     */
    private String name;

    /**
     * 登录名, 为空时该用户无法登录印管平台
     */
    private String login;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户状态: 1 禁用；0 正常(默认)
     */
    private Integer status;

    // 空构造函数
    public YxUser() {
    }

    // 构造函数也需要更新
    public YxUser(String id, String name, String deptId, String login, String phone, Integer status) {
        this.id = id;
        this.name = name;
        this.deptId = deptId;
        this.login = login;
        this.phone = phone;
        this.status = status;
    }

    // Getters and Setters... (内容不变)
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getDeptId() { return deptId; }
    public void setDeptId(String deptId) { this.deptId = deptId; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getLogin() { return login; }
    public void setLogin(String login) { this.login = login; }
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
}