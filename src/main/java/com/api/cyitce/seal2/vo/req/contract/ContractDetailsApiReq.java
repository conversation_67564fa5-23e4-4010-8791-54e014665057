package com.api.cyitce.seal2.vo.req.contract;

public class ContractDetailsApiReq {
    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 查询详情是否返回合同文件数据，默认false不返回
     */
    private Boolean responseContractFile;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Boolean getResponseContractFile() {
        return responseContractFile;
    }

    public void setResponseContractFile(Boolean responseContractFile) {
        this.responseContractFile = responseContractFile;
    }

    @Override
    public String toString() {
        return "ContractDetailsApiReq{" +
                "contractId=" + contractId +
                ", responseContractFile=" + responseContractFile +
                '}';
    }
}
