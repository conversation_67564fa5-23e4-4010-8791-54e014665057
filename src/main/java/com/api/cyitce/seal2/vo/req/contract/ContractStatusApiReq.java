package com.api.cyitce.seal2.vo.req.contract;

public class ContractStatusApiReq {

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同文件id
     */
    private Long docId;

    /**
     * 操作类型：4=发起，5=撤回，6=结束
     */
    private Integer operationType;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    @Override
    public String toString() {
        return "ContractStatusApiReq{" +
                "contractId=" + contractId +
                ", docId=" + docId +
                ", operationType=" + operationType +
                '}';
    }
}
