package com.api.cyitce.seal2.vo.req.updatefile;

public class Document {

    // 合同ID（非必填）
    private Long contractId;

    // 文档名称（非必填）
    private String name;

    // 模板文档ID（非必填）
    private Long templateDocId;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTemplateDocId() {
        return templateDocId;
    }

    public void setTemplateDocId(Long templateDocId) {
        this.templateDocId = templateDocId;
    }

    public Long getSrcFileId() {
        return srcFileId;
    }

    public void setSrcFileId(Long srcFileId) {
        this.srcFileId = srcFileId;
    }

    public Long getPdfFileId() {
        return pdfFileId;
    }

    public void setPdfFileId(Long pdfFileId) {
        this.pdfFileId = pdfFileId;
    }

    public Long getLastFileId() {
        return lastFileId;
    }

    public void setLastFileId(Long lastFileId) {
        this.lastFileId = lastFileId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getPages() {
        return pages;
    }

    public void setPages(String pages) {
        this.pages = pages;
    }

    public Integer getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(Integer signStatus) {
        this.signStatus = signStatus;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getPdfUrl() {
        return pdfUrl;
    }

    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    // 原文件存储ID（非必填）
    private Long srcFileId;

    // PDF存储ID（非必填）
    private Long pdfFileId;

    // 最终文件存储ID（非必填）
    private Long lastFileId;

    // 文档序号（非必填）
    private Integer sequence;

    // 合同文档页数（非必填），可能是一个字符串表示的数字或范围
    private String pages;

    // 签署状态（非必填）
    private Integer signStatus;

    // 文件名（非必填）
    private String fileName;

    // 合同文件归档路径（非必填）
    private String filePath;

    // PDF文件URL（非必填）
    private String pdfUrl;

    // 文件类型（非必填）
    private Integer fileType;

    // 创建人（非必填）
    private String creator;

    // 注意：这里似乎有一个参数被截断了，标记为"d"，可能需要您补充完整
    // 如果这是一个误操作，并且没有其他参数，那么可以忽略这一行注释。

    // Getter和Setter方法（通常使用IDE自动生成）
    // ...

    // 示例：生成getter和setter方法（通常这部分代码由IDE自动生成）
    // public Long getContractId() {
    //     return contractId;
    // }
    //
    // public void setContractId(Long contractId) {
    //     this.contractId = contractId;
    // }
    //
    // ... (其他字段的getter和setter方法类似)
}