package com.api.cyitce.seal2.vo.req.yunxi;

public class YxOrganization {
    /**
     * 组织机构id (必填)
     */
    private String id;

    /**
     * 组织机构名称 (必填)
     */
    private String name;

    /**
     * 父级组织id, 为空时作为根组织
     */
    private String parentId;

    /**
     * 组织机构类型 (必填), 0:部门 1:公司
     */
    private int type;


    // 空构造函数
    public YxOrganization() {
    }
    
    // 构造函数也需要更新
    public YxOrganization(String id, String name, String parentId, int type) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.type = type;
    }

    // Getters and Setters... (内容不变)
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
    public int getType() { return type; }
    public void setType(int type) { this.type = type; }
}