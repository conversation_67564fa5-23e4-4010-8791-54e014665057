package com.api.cyitce.seal2.vo.req.seal.physical;

/**
 * @ClassName: FileDataVo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  09:46
 * @Version: 1.0
 */
public class FileDataVo {

    /**
     * 文件名 必填
     **/
    private String fileName;

    /**
     * 文件访问路径 必填
     **/
    private String filePath;
    
    /**
     * 申请份数 必填
     **/
    private Integer totalCount;

    @Override
    public String toString() {
        return "FileDataVo{" +
                "fileName='" + fileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", totalCount=" + totalCount +
                ", classified='" + classified + '\'' +
                ", printType='" + printType + '\'' +
                ", useSealType='" + useSealType + '\'' +
                ", positionType='" + positionType + '\'' +
                ", scanType='" + scanType + '\'' +
                '}';
    }

    public String getClassified() {
        return classified;
    }

    public void setClassified(String classified) {
        this.classified = classified;
    }

    /**
     * 是否保密文件：0 否， 1 是
     **/
    private String classified="0";

    /**
     * 打印方式 非必填
     * 0单面,1双面
     * 默认单面
     **/
    private String printType="0";

    /**
     * 用印模式 非必填
     * 0打印盖章, 1扫描盖章,2仅盖章,3扫描识别盖章,4批量盖章
     * 默认打印盖章
     **/
    private String useSealType="1";

    /**
     * 定位方式 非必填
     * 0人工定位,1关键字定位
     * 默认人工定位
     **/
    private String positionType="0";

    /**
     * 扫描方式 非必填
     * 0单面扫描,1双面扫描
     * 默认单面扫描
     **/
    private String scanType="0";

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getPrintType() {
        return printType;
    }

    public void setPrintType(String printType) {
        this.printType = printType;
    }

    public String getUseSealType() {
        return useSealType;
    }

    public void setUseSealType(String useSealType) {
        this.useSealType = useSealType;
    }

    public String getPositionType() {
        return positionType;
    }

    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }

    public String getScanType() {
        return scanType;
    }

    public void setScanType(String scanType) {
        this.scanType = scanType;
    }
}