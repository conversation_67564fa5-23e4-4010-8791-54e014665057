package com.api.cyitce.seal2.vo.req.seal.physical;

public class SealUsePickTaskVO {

    /**
     * 取放章人登录名 必填
     **/
    private String staffLoginName;

    /**
     * 印章编码 必填
     **/
    private String sealCode;

    /**
     * 印章名称 必填
     **/
    private String sealName;

    /**
     * 取还是放 必填，0：取；1：放
     **/
    private String pickType;

    /**
     * 印控仪编号 非必填，印控仪唯一编号
     **/
    private String instrumentCode;

    /**
     * 印控仪名称 非必填
     **/
    private String instrumentName;

    /**
     * 所属机构 ID 非必填，用印机构 ID
     **/
    private Long departmentId;

    /**
     * 所属机构名称 非必填，用印机构名称
     **/
    private String departmentName;

    public String getStaffLoginName() {
        return staffLoginName;
    }

    public void setStaffLoginName(String staffLoginName) {
        this.staffLoginName = staffLoginName;
    }

    public String getSealCode() {
        return sealCode;
    }

    public void setSealCode(String sealCode) {
        this.sealCode = sealCode;
    }

    public String getSealName() {
        return sealName;
    }

    public void setSealName(String sealName) {
        this.sealName = sealName;
    }

    public String getPickType() {
        return pickType;
    }

    public void setPickType(String pickType) {
        this.pickType = pickType;
    }

    public String getInstrumentCode() {
        return instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode) {
        this.instrumentCode = instrumentCode;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }
}
