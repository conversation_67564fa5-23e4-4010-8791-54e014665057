package com.api.cyitce.seal2.vo.req;

// 印控仪信息类，用于存储印控仪的相关信息
class InstrumentInfo {
    // 印控仪编号，类型为 String，必填项
    private String instrumentCode;
    // 印控仪名称，类型为 String，必填项
    private String instrumentName;
    // 印控仪类型，类型为 String，必填项
    // 取值说明：
    // 0：封闭式印控仪；
    // 1：开放式印控仪；
    // 2：一体机印控仪；
    // 3：便携式印控仪；
    // 4：手工；
    // 7：封闭式一体机
    private String instrumentType;
    // 印控仪所属机构 id，类型为 Long，非必填项
    private Long departmentID;
    // 印控仪所属机构名称，类型为 String，非必填项
    private String departmentName;
    // 印控仪保管人登录名，类型为 String，非必填项
    // 多值时以“；”分割
    private String sealKeeperLoginName;
    // 印控仪保管人姓名，类型为 String，非必填项
    // 多值时以“；”分割
    private String sealKeeperName;
    // 印章数量，类型为 Integer，必填项
    private Integer sealCount;
    // 是否锁定，类型为 String，非必填项
    // 取值说明：
    // 0：正常；
    // 1：锁定；
    // 缺省为正常
    private String state;

    // 获取印控仪编号
    public String getInstrumentCode() {
        return instrumentCode;
    }

    // 设置印控仪编号
    public void setInstrumentCode(String instrumentCode) {
        this.instrumentCode = instrumentCode;
    }

    // 获取印控仪名称
    public String getInstrumentName() {
        return instrumentName;
    }

    // 设置印控仪名称
    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    // 获取印控仪类型
    public String getInstrumentType() {
        return instrumentType;
    }

    // 设置印控仪类型
    public void setInstrumentType(String instrumentType) {
        this.instrumentType = instrumentType;
    }

    // 获取印控仪所属机构 id
    public Long getDepartmentID() {
        return departmentID;
    }

    // 设置印控仪所属机构 id
    public void setDepartmentID(Long departmentID) {
        this.departmentID = departmentID;
    }

    // 获取印控仪所属机构名称
    public String getDepartmentName() {
        return departmentName;
    }

    // 设置印控仪所属机构名称
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    // 获取印控仪保管人登录名
    public String getSealKeeperLoginName() {
        return sealKeeperLoginName;
    }

    // 设置印控仪保管人登录名
    public void setSealKeeperLoginName(String sealKeeperLoginName) {
        this.sealKeeperLoginName = sealKeeperLoginName;
    }

    // 获取印控仪保管人姓名
    public String getSealKeeperName() {
        return sealKeeperName;
    }

    // 设置印控仪保管人姓名
    public void setSealKeeperName(String sealKeeperName) {
        this.sealKeeperName = sealKeeperName;
    }

    // 获取印章数量
    public Integer getSealCount() {
        return sealCount;
    }

    // 设置印章数量
    public void setSealCount(Integer sealCount) {
        this.sealCount = sealCount;
    }

    // 获取是否锁定状态
    public String getState() {
        return state;
    }

    // 设置是否锁定状态
    public void setState(String state) {
        this.state = state;
    }
}