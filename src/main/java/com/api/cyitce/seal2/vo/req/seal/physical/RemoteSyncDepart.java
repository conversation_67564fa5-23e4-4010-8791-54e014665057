package com.api.cyitce.seal2.vo.req.seal.physical;

// 远程同步部门信息类
public class RemoteSyncDepart {
    // 部门编码，必填项
    private String code;
    // 上级部门编码，非必填，不传默认一级
    private String parentCode;
    // 部门名称，必填项
    private String departmentName;
    // 部门序号，必填项
    private Integer departmentOrder;
    // 删除状态，0 未删除，1 已删除，新增数据默认未删除
    private String deletedState;
    // 状态，必填，0 停用，1 启用，默认启用
    private String state;

    // 构造函数，用于初始化部门信息
    public RemoteSyncDepart(String code, String departmentName, Integer departmentOrder, String state) {
        this.code = code;
        this.departmentName = departmentName;
        this.departmentOrder = departmentOrder;
        this.deletedState = "0"; // 新增数据默认未删除
        this.state = state;
    }

    // 获取部门编码
    public String getCode() {
        return code;
    }

    // 设置部门编码
    public void setCode(String code) {
        this.code = code;
    }

    // 获取上级部门编码
    public String getParentCode() {
        return parentCode;
    }

    // 设置上级部门编码
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    // 获取部门名称
    public String getDepartmentName() {
        return departmentName;
    }

    // 设置部门名称
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    // 获取部门序号
    public Integer getDepartmentOrder() {
        return departmentOrder;
    }

    // 设置部门序号
    public void setDepartmentOrder(Integer departmentOrder) {
        this.departmentOrder = departmentOrder;
    }

    // 获取删除状态
    public String getDeletedState() {
        return deletedState;
    }

    // 设置删除状态
    public void setDeletedState(String deletedState) {
        this.deletedState = deletedState;
    }

    // 获取状态
    public String getState() {
        return state;
    }

    // 设置状态
    public void setState(String state) {
        this.state = state;
    }
}
