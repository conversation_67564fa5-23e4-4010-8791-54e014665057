package com.api.cyitce.seal2.vo.req.seal.physical;

import java.util.List;

/**
 * @ClassName: PrintSealUseTaskApiReq
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  09:41
 * @Version: 1.0
 */
public class PrintSealUseTaskApiReq {

    /**
     * 申请ID 必填，用印申请单ID
     **/
    private Long applyId;

    /**
     * 申请人登录名 必填
     **/
    private String applyStaffLoginName;

    /**
     * 用印人登录名 必填(指定的盖章人登录名数组)
     **/
    private List<String> sealUseStaffLoginNames;

    /**
     * 申请时间 必填
     **/
    private String applyTime;

    /**
     * 申请事由 必填
     **/
    private String applyReason;

    /**
     * 申请标题 必填
     **/
    private String applyTitle;


    /**
     * 业务类型 非必填，默认是“用印业务”
     **/
    private String businessType = "用印业务";

    // /**
    //  * 限时盖章开始时间 非必填
    //  **/
    // private String startTIme;
    //
    // /**
    //  * 限时盖章结束时间 非必填
    //  **/
    // private String endTime;

    public List<FileDataVo> getPushSealUseAioFileData() {
        return pushSealUseAioFileData;
    }

    public void setPushSealUseAioFileData(List<FileDataVo> pushSealUseAioFileData) {
        this.pushSealUseAioFileData = pushSealUseAioFileData;
    }

    /**
     * 附件信息 必填
     **/
    private List<FileDataVo> pushSealUseAioFileData;

    /**
     * 印章信息 批量的印章信息
     **/
    private List<PrintSealUseTaskVo> data;


    public String getApplyStaffLoginName() {
        return applyStaffLoginName;
    }

    public void setApplyStaffLoginName(String applyStaffLoginName) {
        this.applyStaffLoginName = applyStaffLoginName;
    }

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public List<String> getSealUseStaffLoginNames() {
        return sealUseStaffLoginNames;
    }

    public void setSealUseStaffLoginNames(List<String> sealUseStaffLoginNames) {
        this.sealUseStaffLoginNames = sealUseStaffLoginNames;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getApplyTitle() {
        return applyTitle;
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }


    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    // public String getStartTIme() {
    //     return startTIme;
    // }
    //
    // public void setStartTIme(String startTIme) {
    //     this.startTIme = startTIme;
    // }
    //
    // public String getEndTime() {
    //     return endTime;
    // }
    //
    // public void setEndTime(String endTime) {
    //     this.endTime = endTime;
    // }

    public List<PrintSealUseTaskVo> getData() {
        return data;
    }

    public void setData(List<PrintSealUseTaskVo> data) {
        this.data = data;
    }


    @Override
    public String toString() {
        return "PrintSealUseTaskApiReq{" +
                "applyId=" + applyId +
                ", applyStaffLoginName='" + applyStaffLoginName + '\'' +
                ", sealUseStaffLoginNames=" + sealUseStaffLoginNames +
                ", applyTime='" + applyTime + '\'' +
                ", applyReason='" + applyReason + '\'' +
                ", applyTitle='" + applyTitle + '\'' +
                ", businessType='" + businessType + '\'' +
                ", pushSealUseAioFileData=" + pushSealUseAioFileData +
                ", data=" + data +
                '}';
    }
}