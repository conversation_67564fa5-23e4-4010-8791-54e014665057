package com.api.cyitce.seal2.vo.req.updatefile;

import java.util.List;

/**
 * 合同回调接口的主类。
 */
public class ContractCallback {

    /**
     * 合同ID。
     */
    private Long contractId;

    /**
     * 合同名称。
     */
    private String name;

    /**
     * 合同编号。
     */
    private String code;

    /**
     * 业务类型。
     */
    private String bizCode;

    /**
     * 签署人数量。
     */
    private Integer signCount;

    /**
     * 模板ID。
     */
    private Long templateId;

    /**
     * 待签署有效天数。
     */
    private Integer waitSignValidDays;

    /**
     * 合同状态。
     */
    private Integer status;

    /**
     * 是否顺序签署。
     */
    private Boolean signSortable;

    /**
     * 企业ID。
     */
    private String enterpriseId;

    /**
     * 三方企业ID。
     */
    private String thirdEnterpriseId;

    /**
     * 企业名称。
     */
    private String enterpriseName;

    /**
     * 发起人ID。
     */
    private String creator;

    /**
     * 发起人三方ID。
     */
    private String thirdCreator;

    /**
     * 发起人姓名。
     */
    private String createName;

    /**
     * 页面同步通知地址。
     */
    private String syncUrl;

    /**
     * 后台异步通知地址。
     */
    private String asyncUrl;

    /**
     * 是否需要审批。
     */
    private Boolean approved;

    /**
     * 合同发起审批状态。
     */
    private Integer approveStatus;

    /**
     * 审批工作流实例ID。
     */
    private String processInstanceId;

    /**
     * 解约状态。
     */
    private Integer applyNullify;

    /**
     * 是否存在附件。
     */
    private Boolean existAttached;

    /**
     * 归档状态。
     */
    private Boolean archivedStatus;

    /**
     * 是否自动签。
     */
    private Boolean autoSign;

    /**
     * 文件类型。
     */
    private Integer fileType;

    /**
     * 数据来源。
     */
    private Integer dataSource;

    /**
     * 生效日期。
     */
    private String effectDate;

    /**
     * 发送日期。
     */
    private String sendTime;

    /**
     * 最后签署时间。
     */
    private String lastSignedTime;

    /**
     * 过期时间。
     */
    private String expiredTime;

    /**
     * 完成时间。
     */
    private String finishedTime;

    /**
     * 合同信息对象。
     */
    private Contract contract;

    /**
     * 合同文档列表。
     */
    private List<Document> documentList;

    /**
     * 签署人列表。
     */
    private List<Signer> signerList;

    /**
     * 签署人文档列表。
     */
    private List<SignerDocument> signerDocumentList;

    /**
     * 签署人信息列表。
     */
    private List<SignerInfo> signerBasicInfoRespList;

    /**
     * 操作人列表。
     */
    private List<ContractCc> contractCcList;

    /**
     * 解约纪录列表。
     */
    private List<NullifyLog> nullifyLogList;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public Integer getSignCount() {
        return signCount;
    }

    public void setSignCount(Integer signCount) {
        this.signCount = signCount;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getWaitSignValidDays() {
        return waitSignValidDays;
    }

    public void setWaitSignValidDays(Integer waitSignValidDays) {
        this.waitSignValidDays = waitSignValidDays;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getSignSortable() {
        return signSortable;
    }

    public void setSignSortable(Boolean signSortable) {
        this.signSortable = signSortable;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getThirdCreator() {
        return thirdCreator;
    }

    public void setThirdCreator(String thirdCreator) {
        this.thirdCreator = thirdCreator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getSyncUrl() {
        return syncUrl;
    }

    public void setSyncUrl(String syncUrl) {
        this.syncUrl = syncUrl;
    }

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }

    public Boolean getApproved() {
        return approved;
    }

    public void setApproved(Boolean approved) {
        this.approved = approved;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public Integer getApplyNullify() {
        return applyNullify;
    }

    public void setApplyNullify(Integer applyNullify) {
        this.applyNullify = applyNullify;
    }

    public Boolean getExistAttached() {
        return existAttached;
    }

    public void setExistAttached(Boolean existAttached) {
        this.existAttached = existAttached;
    }

    public Boolean getArchivedStatus() {
        return archivedStatus;
    }

    public void setArchivedStatus(Boolean archivedStatus) {
        this.archivedStatus = archivedStatus;
    }

    public Boolean getAutoSign() {
        return autoSign;
    }

    public void setAutoSign(Boolean autoSign) {
        this.autoSign = autoSign;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public String getEffectDate() {
        return effectDate;
    }

    public void setEffectDate(String effectDate) {
        this.effectDate = effectDate;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getLastSignedTime() {
        return lastSignedTime;
    }

    public void setLastSignedTime(String lastSignedTime) {
        this.lastSignedTime = lastSignedTime;
    }

    public String getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(String expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(String finishedTime) {
        this.finishedTime = finishedTime;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public List<Document> getDocumentList() {
        return documentList;
    }

    public void setDocumentList(List<Document> documentList) {
        this.documentList = documentList;
    }

    public List<Signer> getSignerList() {
        return signerList;
    }

    public void setSignerList(List<Signer> signerList) {
        this.signerList = signerList;
    }

    public List<SignerDocument> getSignerDocumentList() {
        return signerDocumentList;
    }

    public void setSignerDocumentList(List<SignerDocument> signerDocumentList) {
        this.signerDocumentList = signerDocumentList;
    }

    public List<SignerInfo> getSignerBasicInfoRespList() {
        return signerBasicInfoRespList;
    }

    public void setSignerBasicInfoRespList(List<SignerInfo> signerBasicInfoRespList) {
        this.signerBasicInfoRespList = signerBasicInfoRespList;
    }

    public List<ContractCc> getContractCcList() {
        return contractCcList;
    }

    public void setContractCcList(List<ContractCc> contractCcList) {
        this.contractCcList = contractCcList;
    }

    public List<NullifyLog> getNullifyLogList() {
        return nullifyLogList;
    }

    public void setNullifyLogList(List<NullifyLog> nullifyLogList) {
        this.nullifyLogList = nullifyLogList;
    }

    public List<Attachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<Attachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    /**
     * 合同附件列表。
     */
    private List<Attachment> attachmentList;

    // Getters and Setters 省略，为了简洁


}