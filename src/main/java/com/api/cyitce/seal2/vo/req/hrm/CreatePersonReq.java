package com.api.cyitce.seal2.vo.req.hrm;

import com.api.cyitce.seal2.enums.account.AuthSource;
import com.api.cyitce.seal2.enums.account.CertificateTypeEnum;
import com.api.cyitce.seal2.enums.account.ESealUserType;

public class CreatePersonReq {
    /**
     * 用户名称，用户名称长度需要在1-32之间，当authentication为true时，为必填
     */
    private String displayName;

    /**
     * 证件类型，（当输入idCardNum，此项必填），当authentication为true时支持多种证件类型，当authentication 为false时只支持身份证号
     */
    private String idCardType = CertificateTypeEnum.IDENTITY_CARD.getCode();

    /**
     * 证件号码 ，当authentication为true时，idCardNum为必填
     */
    private String idCardNum;

    /**
     * 其他证件类型名称
     */
    private String otherCardName;

    /**
     * 账号类型:1手机号(默认),2账号
     */
    private Integer type = ESealUserType.MOBILE.getCode();

    /**
     * 手机号(手机号与账号二选一)  type为1时手机号必填
     */
    private String phone;

    /**
     * 账号  长度1-64位(手机号与账号二选一)  type为2时账号必填
     */
    private String account;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String passwd = "Yg123456";

    /**
     * 实名认证来源(客户认证true,天威认证false)
     */
    private Boolean authentication = AuthSource.CUSTOMER.getCode();

    /**
     * 用户标签(需在诚信签-前置web页面配置)
     */
    private String userLabel;

    /**
     * 用户三方id 必填
     */
    private String thirdDataId;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(String idCardType) {
        this.idCardType = idCardType;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public String getOtherCardName() {
        return otherCardName;
    }

    public void setOtherCardName(String otherCardName) {
        this.otherCardName = otherCardName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public Boolean getAuthentication() {
        return authentication;
    }

    public void setAuthentication(Boolean authentication) {
        this.authentication = authentication;
    }

    public String getUserLabel() {
        return userLabel;
    }

    public void setUserLabel(String userLabel) {
        this.userLabel = userLabel;
    }

    public String getThirdDataId() {
        return thirdDataId;
    }

    public void setThirdDataId(String thirdDataId) {
        this.thirdDataId = thirdDataId;
    }
}
