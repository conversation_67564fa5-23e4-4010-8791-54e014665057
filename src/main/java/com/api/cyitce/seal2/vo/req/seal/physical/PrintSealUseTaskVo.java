package com.api.cyitce.seal2.vo.req.seal.physical;

/**
 * @ClassName: PrintSealUseTaskVo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  09:50
 * @Version: 1.0
 */
public class PrintSealUseTaskVo {

    /**
     * 文件名 必填 (该印章所要盖章文件的文件名称,与FileDataVo的fileName字段数值保证一致)
     **/
    private String fileName;

    /**
     * 印章编码 必填
     **/
    private String sealCode;

    /**
     * 盖章次数 必填
     **/
    private Integer useCount;

    /**
     * 是否加盖骑缝章 非必填
     * 0否,1是
     * 默认 不加盖
     **/
    private String isCheckMark="0";

    /**
     * 骑缝章类型 非必填 0普通,1打印
     * 默认 普通
     **/
    private String checkMarkType="0";



    /**
     * 骑缝章数量 非必填
     **/
    private String checkMarkUseCount;
    public String getCheckMarkUseCount() {
        return checkMarkUseCount;
    }

    public void setCheckMarkUseCount(String checkMarkUseCount) {
        this.checkMarkUseCount = checkMarkUseCount;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSealCode() {
        return sealCode;
    }

    public void setSealCode(String sealCode) {
        this.sealCode = sealCode;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    public String getIsCheckMark() {
        return isCheckMark;
    }

    public void setIsCheckMark(String isCheckMark) {
        this.isCheckMark = isCheckMark;
    }

    public String getCheckMarkType() {
        return checkMarkType;
    }

    @Override
    public String toString() {
        return "PrintSealUseTaskVo{" +
                "fileName='" + fileName + '\'' +
                ", sealCode='" + sealCode + '\'' +
                ", useCount=" + useCount +
                ", isCheckMark='" + isCheckMark + '\'' +
                ", checkMarkType='" + checkMarkType + '\'' +
                ", checkMarkUseCount='" + checkMarkUseCount + '\'' +
                '}';
    }

    public void setCheckMarkType(String checkMarkType) {
        this.checkMarkType = checkMarkType;
    }


}