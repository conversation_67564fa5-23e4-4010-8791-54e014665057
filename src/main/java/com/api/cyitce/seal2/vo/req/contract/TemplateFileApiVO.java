package com.api.cyitce.seal2.vo.req.contract;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class TemplateFileApiVO {

    /**
     * 模板合同文件id
     */
    private Long templateDocId;

    /**
     * 合同文件名称
     */

    private String docName;

    /**
     * 模板合同补充变量
     */
    private Map<String,String> params;

    /**
     * 文本控件字体属性
     */
    private List<FontApiVO> fonts;

    public Long getTemplateDocId() {
        return templateDocId;
    }

    public void setTemplateDocId(Long templateDocId) {
        this.templateDocId = templateDocId;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public List<FontApiVO> getFonts() {
        return fonts;
    }

    public void setFonts(List<FontApiVO> fonts) {
        this.fonts = fonts;
    }
}
