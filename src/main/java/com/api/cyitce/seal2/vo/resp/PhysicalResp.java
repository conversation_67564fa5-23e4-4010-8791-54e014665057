package com.api.cyitce.seal2.vo.resp;

public class PhysicalResp<T> extends BaseResp<T> {
    Integer statusCode;

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;

        if(statusCode==200){
            setStatus(1);
        }else {
            setStatus(0);
        }
    }

    public void statusSync() {
        if(this.statusCode==200){
            setStatus(1);
        }else {
            setStatus(0);
        }
    }

    public BaseResp<T> baseResp() {
        if(this.statusCode==200){
            setStatus(1);
        }else {
            setStatus(0);
        }

        return this;
    }
}
