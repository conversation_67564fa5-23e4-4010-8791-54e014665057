package com.api.cyitce.seal2.vo.req.yunxi;

import java.util.List;
/**
 * 获取智能印章列表接口的响应数据实体类 (YXYG04_01 的 data 字段)
 */
public class YxDeviceListResponseData {
    /**
     * 页码
     */
    private int pageNum;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<YxDevice> getList() {
        return list;
    }

    public void setList(List<YxDevice> list) {
        this.list = list;
    }

    /**
     * 条数
     */
    private int pageSize;
    
    /**
     * 总数
     */
    private long total;
    
    /**
     * 设备信息列表
     */
    private List<YxDevice> list;
    
    // Getters & Setters...
}