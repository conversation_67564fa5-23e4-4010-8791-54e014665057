package com.api.cyitce.seal2.vo.req.yunxi;

import java.util.List;

/**
 * 用印记录实体类 (用于 YXYG04_02 回调接口的 params)
 */
public class YxSealUsageRecord {
    /**
     * 用印记录id, 印管系统内部的用印记录标识 (必填)
     */
    private Integer id;

    /**
     * 租户标识 (必填)
     */
    private String tenant;

    /**
     * 组织id (必填)
     */
    private String deptId;

    /**
     * 组织名称 (必填)
     */
    private String deptName;

    /**
     * 用印时间 (必填)
     */
    private String useTime;

    /**
     * 用印人id (-2代表密码管章人解锁用印)
     */
    private String userId;

    /**
     * 用印人名称
     */
    private String userName;

    /**
     * 设备唯一标识 (必填)
     */
    private String uuid;

    /**
     * 设备名称 (必填)
     */
    private String deviceName;

    /**
     * 申请单id
     */
    private String applicationId;

    /**
     * 用印次序, 从1开始递增 (必填)
     */
    private Integer useCount;

    /**
     * 异常状态, -1:异常 0:正常 (必填)
     */
    private Integer error;
    
    /**
     * 异常描述
     */
    private String errorDesc;

    /**
     * 用印模式 (必填)
     * 示例中是字符串"审批模式", 但文档中是int。为兼容示例，使用Object。
     * -1:未知 1:审批 2:指纹 3:锁定 4:装章 5:密码 6:OTA 7:休眠 8:产测 10:临时 11:远程
     */
    private Object model; 

    /**
     * 摄像头开关 (必填)
     */
    private Boolean camera;

    /**
     * 用印地址
     */
    private String location;

    /**
     * 用印视频url (m3u8格式)
     */
    private String videoFileUrl;

    /**
     * 人脸图片url
     */
    private String faceFileUrl;

    /**
     * 用印图片url
     */
    private String useFileUrl;

    /**
     * 审计图片url列表
     */
    private List<String> auditFileUrls;

    /**
     * 超时按压图片url列表
     */
    private List<String> timeoutFileUrls;

    public YxSealUsageRecord() {
    }

    // Getters and Setters... (此处省略，实际代码中应完整)
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getTenant() { return tenant; }
    public void setTenant(String tenant) { this.tenant = tenant; }
    public String getDeptId() { return deptId; }
    public void setDeptId(String deptId) { this.deptId = deptId; }
    public String getDeptName() { return deptName; }
    public void setDeptName(String deptName) { this.deptName = deptName; }
    public String getUseTime() { return useTime; }
    public void setUseTime(String useTime) { this.useTime = useTime; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public String getUserName() { return userName; }
    public void setUserName(String userName) { this.userName = userName; }
    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }
    public String getDeviceName() { return deviceName; }
    public void setDeviceName(String deviceName) { this.deviceName = deviceName; }
    public String getApplicationId() { return applicationId; }
    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }
    public Integer getUseCount() { return useCount; }
    public void setUseCount(Integer useCount) { this.useCount = useCount; }
    public Integer getError() { return error; }
    public void setError(Integer error) { this.error = error; }
    public String getErrorDesc() { return errorDesc; }
    public void setErrorDesc(String errorDesc) { this.errorDesc = errorDesc; }
    public Object getModel() { return model; }
    public void setModel(Object model) { this.model = model; }
    public Boolean getCamera() { return camera; }
    public void setCamera(Boolean camera) { this.camera = camera; }
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    public String getVideoFileUrl() { return videoFileUrl; }
    public void setVideoFileUrl(String videoFileUrl) { this.videoFileUrl = videoFileUrl; }
    public String getFaceFileUrl() { return faceFileUrl; }
    public void setFaceFileUrl(String faceFileUrl) { this.faceFileUrl = faceFileUrl; }
    public String getUseFileUrl() { return useFileUrl; }
    public void setUseFileUrl(String useFileUrl) { this.useFileUrl = useFileUrl; }
    public List<String> getAuditFileUrls() { return auditFileUrls; }
    public void setAuditFileUrls(List<String> auditFileUrls) { this.auditFileUrls = auditFileUrls; }
    public List<String> getTimeoutFileUrls() { return timeoutFileUrls; }
    public void setTimeoutFileUrls(List<String> timeoutFileUrls) { this.timeoutFileUrls = timeoutFileUrls; }
}