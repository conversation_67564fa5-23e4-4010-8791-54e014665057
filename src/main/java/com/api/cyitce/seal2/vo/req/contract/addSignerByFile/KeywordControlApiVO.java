package com.api.cyitce.seal2.vo.req.contract.addSignerByFile;

public class KeywordControlApiVO extends ControlApiVO {

    /**
     * 签署页
     */
    private String pageNum;

    /**
     * 签署关键字
     */
    private String keyword;

    /**
     * 水平偏移距离(单位cm)
     */
    private Float offsetX = 0f;

    /**
     * 垂直偏移距离(单位cm)
     */
    private Float offsetY = 0f;

    private String type;


    public KeywordControlApiVO() {
    }

    public KeywordControlApiVO(Integer id, String type, String pageNum, String keyword, Float offsetX, Float offsetY) {
        super.setId(id);
        this.pageNum = pageNum;
        this.keyword = keyword;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
        this.type = type;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Float getOffsetX() {
        return offsetX;
    }

    public void setOffsetX(Float offsetX) {
        this.offsetX = offsetX;
    }

    public Float getOffsetY() {
        return offsetY;
    }

    public void setOffsetY(Float offsetY) {
        this.offsetY = offsetY;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }
}