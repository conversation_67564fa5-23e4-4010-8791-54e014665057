package com.api.cyitce.seal2.vo.req.yunxi;

/**
 * 获取授权码接口的响应数据实体类 (YXYG03_04 的 data 字段)
 */
public class YxAuthCodeResponseData {
    /**
     * 申请单id
     */
    private String applicationId;

    /**
     * 返回的授权码
     */
    private String authorizationCode;

    /**
     * 授权码的过期时间, 格式：yyyy-MM-dd HH:mm:ss
     */
    private String expireTime;

    /**
     * 设备唯一标识
     */
    private String uuid;

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    public String getAuthorizationCode() {
        return authorizationCode;
    }
    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }
    public String getExpireTime() {
        return expireTime;
    }
    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }
    public String getUuid() {
        return uuid;
    }
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public String toString() {
        return "YxAuthCodeResponseData{" +
                "applicationId='" + applicationId + '\'' +
                ", authorizationCode='" + authorizationCode + '\'' +
                ", expireTime='" + expireTime + '\'' +
                ", uuid='" + uuid + '\'' +
                '}';
    }
}