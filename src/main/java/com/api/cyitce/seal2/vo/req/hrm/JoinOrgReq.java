package com.api.cyitce.seal2.vo.req.hrm;

/**
 * @ClassName: JoinOrgReq
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-22  16:06
 * @Version: 1.0
 */
public class JoinOrgReq {
    /**
     * 邀请人用户唯一标识 必填
     */
    private String userId;

    /**
     * 受邀人用户唯一标识 必填
     */
    private String inviteUserId;

    /**
     * 邀请加入企业唯一标识 非必填
     */
    private String enterpriseId;

    /**
     * 企业邮箱 非必填
     */
    private String employeeEmail;

    /**
     * 员工工号 非必填
     */
    private String employeeCode;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getInviteUserId() {
        return inviteUserId;
    }

    public void setInviteUserId(String inviteUserId) {
        this.inviteUserId = inviteUserId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    @Override
    public String toString() {
        return "JoinOrgReq{" +
                "userId='" + userId + '\'' +
                ", inviteUserId='" + inviteUserId + '\'' +
                ", enterpriseId='" + enterpriseId + '\'' +
                ", employeeEmail='" + employeeEmail + '\'' +
                ", employeeCode='" + employeeCode + '\'' +
                '}';
    }
}