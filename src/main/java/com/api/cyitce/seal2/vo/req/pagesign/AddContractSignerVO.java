package com.api.cyitce.seal2.vo.req.pagesign;

import com.api.cyitce.seal2.enums.account.UserTypeEnum;
import com.api.cyitce.seal2.vo.req.contract.addSignerByFile.SignFileApiVO;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AddContractSignerVO {
    /**
     * 签署人类型 必填  1：个人 2：企业
     */
    private Integer signerType = UserTypeEnum.PERSON.getIndex();

    /**
     * 签署人用户id或third 必填 签署人可以未实名
     */
    private String userId;

    /**
     * 签署人公司ID（signerType=2时必填）
     */
    private String enterpriseId;

    /**
     * 是否要做意愿认证 非必填
     */
    private Boolean isUserWishes = true;


    public Boolean getUserWishes() {
        return isUserWishes;
    }

    public void setUserWishes(Boolean userWishes) {
        isUserWishes = userWishes;
    }

    public Integer[] getUserWishesWay() {
        return userWishesWay;
    }

    public void setUserWishesWay(Integer[] userWishesWay) {
        this.userWishesWay = userWishesWay;
    }

    private Integer[] userWishesWay = {1, 2};
    /**
     * 签署人顺序（签署顺序1-100之间）
     */
    private Integer sequence = 1;

    /**
     * 是否发送短信通知到诚信签-前置系统签署,false不发送(默认)、true发送
     */
    private Boolean sendMsg = false;

    /**
     * 是否指定签署位置,设置签署控件
     */
    private Boolean setting = false;


    /**
     * 不设置控件,签署时的控件类型范围;当setting为false时,该参数为必填
     * 当 签署人类型 为 个人，必须设置一个 签名控件 ControlTypeEnum.AUTOGRAPH.getCode()
     * 当 签署人类型 为 公司，必须设置一个 印章控件 ControlTypeEnum.SIGNET.getCode()
     */
    private Set<String> controlsType = new HashSet<>();

    /**
     * 签署人要签署的合同文件
     *
     * @required
     */
    private List<SignFileApiVO> signFiles;

    public Integer getSignerType() {
        return signerType;
    }

    public void setSignerType(Integer signerType) {
        this.signerType = signerType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Boolean getIsUserWishes() {
        return isUserWishes;
    }

    public void setIsUserWishes(Boolean userWishes) {
        isUserWishes = userWishes;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Boolean getSendMsg() {
        return sendMsg;
    }

    public void setSendMsg(Boolean sendMsg) {
        this.sendMsg = sendMsg;
    }

    public Boolean getSetting() {
        return setting;
    }

    public void setSetting(Boolean setting) {
        this.setting = setting;
    }

    public Set<String> getControlsType() {
        return controlsType;
    }

    public void setControlsType(Set<String> controlsType) {
        this.controlsType = controlsType;
    }

    public List<SignFileApiVO> getSignFiles() {
        return signFiles;
    }

    public void setSignFiles(List<SignFileApiVO> signFiles) {
        this.signFiles = signFiles;
    }

    @Override
    public String toString() {
        return "AddContractSignerVO{" +
                "signerType=" + signerType +
                ", userId='" + userId + '\'' +
                ", enterpriseId='" + enterpriseId + '\'' +
                ", isUserWishes=" + isUserWishes +
                ", sequence=" + sequence +
                ", sendMsg=" + sendMsg +
                ", setting=" + setting +
                ", controlsType=" + controlsType +
                ", signFiles=" + signFiles +
                '}';
    }
}
