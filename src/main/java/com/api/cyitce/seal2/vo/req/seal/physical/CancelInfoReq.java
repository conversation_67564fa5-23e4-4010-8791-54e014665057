package com.api.cyitce.seal2.vo.req.seal.physical;

public class CancelInfoReq {
    private String applyId;
    private String loginName;

    // 无参构造方法
    public CancelInfoReq() {
    }

    // 有参构造方法
    public CancelInfoReq(String applyId, String loginName) {
        this.applyId = applyId;
        this.loginName = loginName;
    }

    // 获取 applyId
    public String getApplyId() {
        return applyId;
    }

    // 设置 applyId
    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    // 获取 loginName
    public String getLoginName() {
        return loginName;
    }

    // 设置 loginName
    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "applyId='" + applyId + '\'' +
                ", loginName='" + loginName + '\'' +
                '}';
    }
}    