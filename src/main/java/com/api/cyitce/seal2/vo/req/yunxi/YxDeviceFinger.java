package com.api.cyitce.seal2.vo.req.yunxi;

/**
 * 设备关联的指纹信息实体 (YxDevice的嵌套对象)
 */
public class YxDeviceFinger {
    /**
     * 指纹用户id
     */
    private String userId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    /**
     * 指纹用户名称
     */
    private String userName;

    /**
     * 指纹录入时间
     */
    private String time;
    
    // Getters & Setters...
}