package com.api.cyitce.seal2.vo.req;



import java.util.List;

/**
 * <AUTHOR>
 */

public class SignSignerApiVO {

    /**
     * 签署人ID（添加签署人接口返回）
     */
    private Long signerId;

    /**
     * 是否要做意愿认证;false 否 用户自己做意愿认证, true 是 需要天威诚信做意愿认证;
     */
    private Boolean isUserWishes;

    /**
     * 认证编号
     */
    private String intentionId;

    /**
     * 签署人合同文件参数不能为空
     */
    private List<SignSignerFileApiVO> signFiles;

    /**
     * 签章备注
     */
    private String remark;

    public Long getSignerId() {
        return signerId;
    }

    public void setSignerId(Long signerId) {
        this.signerId = signerId;
    }

    public Boolean getUserWishes() {
        return isUserWishes;
    }

    public void setUserWishes(Boolean userWishes) {
        isUserWishes = userWishes;
    }

    public String getIntentionId() {
        return intentionId;
    }

    public void setIntentionId(String intentionId) {
        this.intentionId = intentionId;
    }

    public List<SignSignerFileApiVO> getSignFiles() {
        return signFiles;
    }

    public void setSignFiles(List<SignSignerFileApiVO> signFiles) {
        this.signFiles = signFiles;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
