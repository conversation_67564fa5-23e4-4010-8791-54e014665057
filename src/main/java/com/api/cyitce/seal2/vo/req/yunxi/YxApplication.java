package com.api.cyitce.seal2.vo.req.yunxi;

import java.util.List;

// "创建申请单" 接口的 params 对象模型
public class YxApplication {
    /**
     * 申请单id, 第三方对接系统的申请单id (必填)
     */
    private String id;

    /**
     * 申请标题 (必填)
     */
    private String title;

    /**
     * 申请内容
     */
    private String content;

    /**
     * 智能印章设备唯一标识 (必填)
     */
    private String uuid;

    /**
     * 合同份数 (必填)
     */
    private Integer contractCopiesNum;

    /**
     * 每份用印次数 (必填)
     */
    private Integer contractCopiesCount;

    /**
     * 骑缝章用印次数
     */
    private Integer stitchSealCount;

    /**
     * 申请文件类型id, 与typeCode任选其一
     */
    private Integer typeId;

    /**
     * 申请文件类型标识, 与typeId任选其一 (推荐)
     */
    private String typeCode;

    /**
     * 申请人id (必填)
     */
    private String applicantId;

    /**
     * 用印人id, 建议必填
     */
    private String usePeopleId;

    /**
     * 电子围栏信息列表
     */
    private List<YxApplicationEleFence> ApplicationEleFences;

    /**
     * 合同附件列表
     */
    private List<YxApplicationFile> files;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUuid() {
        return uuid;
    }

    public YxApplication() {
    }

    public YxApplication(String id, String title, String content, String uuid, Integer contractCopiesNum, Integer contractCopiesCount, Integer stitchSealCount, String typeCode, String applicantId, String usePeopleId) {
        this.id = id;
        this.title = title;
        this.content = content;
        this.uuid = uuid;
        this.contractCopiesNum = contractCopiesNum;
        this.contractCopiesCount = contractCopiesCount;
        this.stitchSealCount = stitchSealCount;
        this.typeCode = typeCode;
        this.applicantId = applicantId;
        this.usePeopleId = usePeopleId;

    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getContractCopiesNum() {
        return contractCopiesNum;
    }

    public void setContractCopiesNum(Integer contractCopiesNum) {
        this.contractCopiesNum = contractCopiesNum;
    }

    public Integer getContractCopiesCount() {
        return contractCopiesCount;
    }

    public void setContractCopiesCount(Integer contractCopiesCount) {
        this.contractCopiesCount = contractCopiesCount;
    }

    public Integer getStitchSealCount() {
        return stitchSealCount;
    }

    public void setStitchSealCount(Integer stitchSealCount) {
        this.stitchSealCount = stitchSealCount;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getUsePeopleId() {
        return usePeopleId;
    }

    public void setUsePeopleId(String usePeopleId) {
        this.usePeopleId = usePeopleId;
    }

    public List<YxApplicationEleFence> getApplicationEleFences() {
        return ApplicationEleFences;
    }

    public void setApplicationEleFences(List<YxApplicationEleFence> applicationEleFences) {
        this.ApplicationEleFences = applicationEleFences;
    }

    public List<YxApplicationFile> getFiles() {
        return files;
    }

    public void setFiles(List<YxApplicationFile> files) {
        this.files = files;
    }
}