package com.api.cyitce.seal2.vo.req.contract.model;

// 字体类
public class Font {
    private String controlsKey;
    private int fontType;
    private int fontSize;
    private String fontColor;
    private int alignment;
    private int verticalAlignment;
    private boolean singleLine;

    // Getters 和 Setters
    public String getControlsKey() {
        return controlsKey;
    }

    public void setControlsKey(String controlsKey) {
        this.controlsKey = controlsKey;
    }

    public int getFontType() {
        return fontType;
    }

    public void setFontType(int fontType) {
        this.fontType = fontType;
    }

    public int getFontSize() {
        return fontSize;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public int getAlignment() {
        return alignment;
    }

    public void setAlignment(int alignment) {
        this.alignment = alignment;
    }

    public int getVerticalAlignment() {
        return verticalAlignment;
    }

    public void setVerticalAlignment(int verticalAlignment) {
        this.verticalAlignment = verticalAlignment;
    }

    public boolean isSingleLine() {
        return singleLine;
    }

    public void setSingleLine(boolean singleLine) {
        this.singleLine = singleLine;
    }
}
