package com.api.cyitce.seal2.vo.req;



/**
 * <AUTHOR>
 */

public class ControlValueApiVO {

    /**
     * 控件ID
     */
    private Long controlsId;

    /**
     * 证书ID
     */
    private Long certId;

    /**
     * 签名ID
     */
    private Long stampId;

    /**
     * 印章ID
     */
    private Long sealId;

    /**
     * 签名、印章图片
     */
    private String imageBase64;

    /**
     * 文本
     */
    private String text;

    public ControlValueApiVO(Long controlsId, Long id) {
        this.controlsId = controlsId;
        this.stampId = id;
        this.sealId = id;
    }

    public Long getControlsId() {
        return controlsId;
    }

    public void setControlsId(Long controlsId) {
        this.controlsId = controlsId;
    }

    public Long getCertId() {
        return certId;
    }

    public void setCertId(Long certId) {
        this.certId = certId;
    }

    public Long getStampId() {
        return stampId;
    }

    public void setStampId(Long stampId) {
        this.stampId = stampId;
    }

    public Long getSealId() {
        return sealId;
    }

    public void setSealId(Long sealId) {
        this.sealId = sealId;
    }

    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
