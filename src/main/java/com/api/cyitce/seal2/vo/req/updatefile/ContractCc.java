package com.api.cyitce.seal2.vo.req.updatefile;

public class ContractCc {

    // 合同ID（必填）
    private Long contractId;

    // 抄送人类型（非必填）
    // 1=个人, 2=企业
    private Integer ccType;

    // 抄送人姓名（非必填）
    private String name;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Integer getCcType() {
        return ccType;
    }

    public void setCcType(Integer ccType) {
        this.ccType = ccType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public Boolean getHasRead() {
        return hasRead;
    }

    public void setHasRead(Boolean hasRead) {
        this.hasRead = hasRead;
    }

    public String getCcTime() {
        return ccTime;
    }

    public void setCcTime(String ccTime) {
        this.ccTime = ccTime;
    }

    // 抄送人用户ID（非必填）
    private String userId;

    // 抄送人用户三方ID（非必填）
    private String thirdUserId;

    // 抄送人企业ID（非必填）
    private String enterpriseId;

    // 抄送人三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 企业名称（非必填，当抄送人类型为企业时可能需要）
    private String enterpriseName;

    // 是否已阅读合同（非必填）
    private Boolean hasRead;

    // 抄送时间（非必填，格式：yyyy-MM-dd HH:mm:ss）
    private String ccTime;

    // 通常这里会有getter和setter方法，但由于您的要求是不写，所以这里省略
    // ... (省略getter和setter方法)

    // 注意：在实际应用中，建议为所有私有字段提供公共的getter和setter方法，
    // 以便于外部类访问和修改这些字段的值。如果您使用的是IDE（如IntelliJ IDEA或Eclipse），
    // 通常可以通过快捷键自动生成这些方法。
}