package com.api.cyitce.seal2.vo.req.updatefile;

public class SignerInfo {

    // 是否为临时用户（非必填）
    private Boolean tempUserFlag;

    // 用户类型（非必填）
    // ENTERPRISE=企业, PERSON=个人
    private String userType;

    // 用户实名状态（非必填）
    private Boolean userAuthStatus;

    // 用户ID（非必填）
    private String userId;

    // 用户三方ID（非必填）
    private String thirdUserId;

    // 账号（非必填）
    private String account;

    // 邮箱（非必填）
    private String email;

    // 签署人姓名（非必填，这里假设签署人即为用户本身，根据具体业务逻辑可能有所不同）
    private String userName;

    // 签署人手机号（非必填，同样假设签署人即为用户）
    private String phone;

    // 签署人企业ID（非必填，适用于企业用户或需要关联企业的个人用户）
    private String enterpriseId;

    // 签署人三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 企业名称（非必填，适用于企业用户）
    private String enterpriseName;

    // 企业简称（非必填）
    private String enterpriseAlias;

    // 企业统一信用代码（非必填，适用于企业用户）
    private String enterpriseCode;

    // 企业实名状态（非必填）
    private Boolean enterpriseAuthStatus;

    public Boolean getTempUserFlag() {
        return tempUserFlag;
    }

    public void setTempUserFlag(Boolean tempUserFlag) {
        this.tempUserFlag = tempUserFlag;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Boolean getUserAuthStatus() {
        return userAuthStatus;
    }

    public void setUserAuthStatus(Boolean userAuthStatus) {
        this.userAuthStatus = userAuthStatus;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getEnterpriseAlias() {
        return enterpriseAlias;
    }

    public void setEnterpriseAlias(String enterpriseAlias) {
        this.enterpriseAlias = enterpriseAlias;
    }

    public String getEnterpriseCode() {
        return enterpriseCode;
    }

    public void setEnterpriseCode(String enterpriseCode) {
        this.enterpriseCode = enterpriseCode;
    }

    public Boolean getEnterpriseAuthStatus() {
        return enterpriseAuthStatus;
    }

    public void setEnterpriseAuthStatus(Boolean enterpriseAuthStatus) {
        this.enterpriseAuthStatus = enterpriseAuthStatus;
    }

    public String getEnterpriseCreatorId() {
        return enterpriseCreatorId;
    }

    public void setEnterpriseCreatorId(String enterpriseCreatorId) {
        this.enterpriseCreatorId = enterpriseCreatorId;
    }

    // 主管理员唯一标识（非必填，适用于企业用户，标识企业的主管理员）
    private String enterpriseCreatorId;

    // 注意：通常在实际开发中，我们会为这些字段提供getter和setter方法以便于访问和修改字段值。
    // 但由于您的要求是不写getter和setter，所以这里只定义了字段和注释。
}