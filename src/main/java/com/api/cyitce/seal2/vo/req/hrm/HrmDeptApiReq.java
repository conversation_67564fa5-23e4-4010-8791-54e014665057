package com.api.cyitce.seal2.vo.req.hrm;

/**
 * @ClassName: HrmDeptVo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  14:59
 * @Version: 1.0
 */
public class HrmDeptApiReq {
    
    /**
     * 合同编号 必填
     **/
    private String code;

    /**
     * 上级部门编码 非必填(不传默认一级)
     **/
    private String parentCode;

    /**
     * 部门名称 必填
     **/
    private String departmentName;

    /**
     * 部门序号 必填
     **/
    private Integer departmentOrder;

    /**
     * 删除状态 0未删除 1已删除 新增数据默认未删除
     **/
    private String deletedState;

    /**
     * 状态 必填 0停用 1启用 默认启用
     **/
    private String state;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getDepartmentOrder() {
        return departmentOrder;
    }

    public void setDepartmentOrder(Integer departmentOrder) {
        this.departmentOrder = departmentOrder;
    }

    public String getDeletedState() {
        return deletedState;
    }

    public void setDeletedState(String deletedState) {
        this.deletedState = deletedState;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}