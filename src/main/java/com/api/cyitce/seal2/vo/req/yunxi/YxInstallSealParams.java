package com.api.cyitce.seal2.vo.req.yunxi;

/**
 * 安装印章接口的参数实体类 (用于 YXYG02_04)
 */
public class YxInstallSealParams {
    /**
     * 设备唯一标识 (必填)
     */
    private String uuid;

    /**
     * 操作人id (必填)
     */
    private String operatorId;

    public YxInstallSealParams() {}

    public YxInstallSealParams(String uuid, String operatorId) {
        this.uuid = uuid;
        this.operatorId = operatorId;
    }

    // Getters and Setters
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }
}