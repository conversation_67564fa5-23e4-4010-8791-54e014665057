package com.api.cyitce.seal2.vo.req.yunxi;

/**
 * 获取实体章授权码接口的参数实体类 (用于 YXYG03_23)
 */
public class YxPhysicalSealAuthCodeParams {
    /**
     * 申请单id, 第三方对接系统的申请单id (必填)
     */
    private String applicationId;

    /**
     * 过期时间, 单位：秒，默认3600秒
     */
    private Long expireTime;

    public YxPhysicalSealAuthCodeParams() {}

    public YxPhysicalSealAuthCodeParams(String applicationId) {
        this.applicationId = applicationId;
    }

    public YxPhysicalSealAuthCodeParams(String applicationId, Long expireTime) {
        this.applicationId = applicationId;
        this.expireTime = expireTime;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    public Long getExpireTime() {
        return expireTime;
    }
    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }
}