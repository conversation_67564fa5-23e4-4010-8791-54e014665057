package com.api.cyitce.seal2.vo.req.contract;



import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/11 0011
 * @description
 */

public class AddTemplateSignerVO implements Serializable {

    /**
     * 签署人类型
     */
    private Integer signerType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 企业id
     */
    private String enterpriseId;

    /**
     * 是否要做意愿认证
     */
    private Boolean isUserWishes;


    private List<Integer> userWishesWay = new ArrayList<>();

    public Integer getSignerType() {
        return signerType;
    }

    public void setSignerType(Integer signerType) {
        this.signerType = signerType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Boolean getIsUserWishes() {
        return isUserWishes;
    }

    public void setIsUserWishes(Boolean userWishes) {
        isUserWishes = userWishes;
    }

    public List<Integer> getUserWishesWay() {
        return userWishesWay;
    }

    public void setUserWishesWay(List<Integer> userWishesWay) {
        this.userWishesWay = userWishesWay;
    }
}
