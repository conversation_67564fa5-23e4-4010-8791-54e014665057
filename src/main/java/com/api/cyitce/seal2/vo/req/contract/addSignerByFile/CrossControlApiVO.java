package com.api.cyitce.seal2.vo.req.contract.addSignerByFile;

public class CrossControlApiVO extends ControlApiVO {

    /**
     * 签署页
     */
    private String pageNum = "0";

    /**
     * 签署控件y坐标,印章中心距离文档下边缘的距离(比例)
     */
    private Float y;


    public CrossControlApiVO() {
    }

    public CrossControlApiVO(Integer id,String pageNum, Float y) {
        super.setId(id);
        this.pageNum = pageNum;
        this.y = y;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public Float getY() {
        return y;
    }

    public void setY(Float y) {
        this.y = y;
    }
}
