package com.api.cyitce.seal2.vo.req.contract;

public class UploadFileApiReq {
    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同文档名称
     */
    private String docName;

    /**
     * 合同文件（base64），支持pdf/doc/docx文件格式. base64与filePath二选一
     */
    private String contractFile;

    /**
     * 文件绝对路径（带文件名）
     */
    private String filePath;

    /**
     * 文件绝对路径类型：1ftp路径（默认）、2http路径
     */
    private Integer pathType;

    /**
     * 合同文件排序序号（此序号用于多文件场景，无多文件场景可不传递）
     */
    private Integer sequence;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getContractFile() {
        return contractFile;
    }

    public void setContractFile(String contractFile) {
        this.contractFile = contractFile;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getPathType() {
        return pathType;
    }

    public void setPathType(Integer pathType) {
        this.pathType = pathType;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
}
