package com.api.cyitce.seal2.vo.req.contract.signByFile;

import com.api.cyitce.seal2.vo.req.contract.addSignerByFile.AddSignerApiVO;

import java.util.List;

public class AddSignerByFileApiReq {

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同签署人
     */
    private List<AddSignerApiVO> signers;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public List<AddSignerApiVO> getSigners() {
        return signers;
    }

    public void setSigners(List<AddSignerApiVO> signers) {
        this.signers = signers;
    }
}
