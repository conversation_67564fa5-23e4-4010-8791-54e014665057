package com.api.cyitce.seal2.integration.physicalSeal.hrm;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.constant.PhysicalSealApiUrl;
import com.api.cyitce.seal2.integration.physicalSeal.PhysicalSealBaseIntergration;
import com.api.cyitce.seal2.vo.req.hrm.AllInfoApiReq;
import com.api.cyitce.seal2.vo.req.hrm.HrmDeptApiReq;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;

/**
 * @ClassName: HrmSyncIntegration
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  14:55
 * @Version: 1.0
 */
public class HrmSyncIntegration extends PhysicalSealBaseIntergration {

    /**
     * @description: 同步机构（部门和人员）
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> syncAllInfo(AllInfoApiReq req) {
        return sendPost(PhysicalSealApiUrl.REMOTE_SYNC_ALL_INFO, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 同步人员
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> syncStaff(HrmResourceApiReq req) {
        return sendPost(PhysicalSealApiUrl.REMOTE_SYNC_STAFF, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 同步部门
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> syncDept(HrmDeptApiReq req) {
        return sendPost(PhysicalSealApiUrl.REMOTE_SYNC_DEPART, JSONUtil.parseObj(req), new JSONObject());
    }
}