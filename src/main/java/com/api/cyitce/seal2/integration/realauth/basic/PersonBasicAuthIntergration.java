package com.api.cyitce.seal2.integration.realauth.basic;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.constant.RealAuthApiUrl;
import com.api.cyitce.seal2.integration.realauth.RealAuthBaseIntergration;
import com.api.cyitce.seal2.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;

/**
 * @ClassName: PersonBasicAuth
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  17:26
 * @Version: 1.0
 */
public class PersonBasicAuthIntergration extends RealAuthBaseIntergration {
    /**
     * @description: 个人手机号实名
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> mobileAuth(RealPersonApiReq req) {
        JSONObject jsonReq = JSONUtil.parseObj(req);
        jsonReq.put("serviceCode","idc0012");
        config.setServiceCode("idc0012");
        info("实名认证接口请求参数：{}", jsonReq.toString());
        return sendPost(RealAuthApiUrl.PERSION_AUTH_IDEN,jsonReq);
    }
}