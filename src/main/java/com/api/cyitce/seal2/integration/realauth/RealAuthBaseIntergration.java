package com.api.cyitce.seal2.integration.realauth;

import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.realauth.RealAuthBaseAuth;
import com.api.cyitce.seal2.util.http.PostUtil;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.weaver.general.BaseBean;

/**
 * @ClassName: RealAuthBaseIntergration
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  16:17
 * @Version: 1.0
 */
public class RealAuthBaseIntergration extends BaseLog {
    public static RealAuthBaseAuth config = Singleton.get(RealAuthBaseAuth.class);
    public  void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }
    public static BaseResp<JSONObject> sendPost(String url, JSONObject requestJson) {

        requestJson.put("appId",config.getConfig().getAppId());
        new BaseBean().writeLog(RealAuthBaseIntergration.class.getName(), "请求url:"+url +" 请求参数:"+requestJson.toString());
        new BaseBean().writeLog(RealAuthBaseIntergration.class.getName(), "config:"+config.toString());
        JSONObject responseJson = PostUtil.toPost(url, requestJson, config);
        new BaseBean().writeLog(RealAuthBaseIntergration.class.getName(), " 请求参数:"+requestJson.toString());

        BaseResp<JSONObject> resp = JSONUtil.toBean(responseJson, new TypeReference<BaseResp<JSONObject>>() {}, false);
        new BaseBean().writeLog(RealAuthBaseIntergration.class.getName(), " 请求参数1:"+requestJson.toString());
        resp.setData(responseJson);

        return resp;
    }
}