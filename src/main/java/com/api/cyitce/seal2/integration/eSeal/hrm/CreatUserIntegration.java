package com.api.cyitce.seal2.integration.eSeal.hrm;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.eSeal.ESealDataSource;
import com.api.cyitce.seal2.constant.ESealApiUrl;
import com.api.cyitce.seal2.integration.eSeal.ESealBaseIntergration;
import com.api.cyitce.seal2.vo.req.hrm.*;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import com.api.cyitce.seal2.vo.resp.org.PersonVoResp;

import java.sql.SQLException;

public class CreatUserIntegration extends ESealBaseIntergration {
    /**
     * 创建用户
     **/
    public BaseResp<JSONObject> createPerson(CreatePersonReq req) {
        if ("".equals(req.getThirdDataId()) || req.getThirdDataId() == null) {
            return MsgResp.error("thirdDataId不能为空");
        }

        req.setAuthentication(true);
        return sendPost(ESealApiUrl.CREATE_PERSON, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * 更新用户
     **/
    public BaseResp<JSONObject> updatePerson(UpdatePersonReq req) {
        return sendPost(ESealApiUrl.UPDATE_PERSON, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * 查询用户
     **/
    public BaseResp<PersonVoResp> queryPerson(QueryPersonApiReq req) {
        return sendPost(ESealApiUrl.USER_DETAILS, JSONUtil.parseObj(req), new PersonVoResp());
    }

    /**
     * 用户列表查询
     **/
    public BaseResp<UserListWrapper> queryUserList(UserListReq req) {

        return sendPost(ESealApiUrl.USER_LIST, JSONUtil.parseObj(req), new UserListWrapper());
    }

    /**
     * 判断用户是否注册
     **/
    public BaseResp<Boolean> isRegister(String thirdDataId) {
        ESealDataSource rs = new ESealDataSource();

        try {
            rs.executeQuery("SELECT uc_data_id FROM uc_third_data_mapping where data_type=? and thrid_data_id=?", new String[]{"2", thirdDataId});
            if (!rs.next()) {
                return MsgResp.ok(false);
            }

            return MsgResp.ok(true);
        } catch (SQLException e) {
            info("查询失败:" + e.getMessage());
            return MsgResp.error("查询失败:" + e.getMessage());
        }
    }

    /**
     * 邀请用户加入企业
     **/
    public BaseResp<JSONObject> inviteUser(JoinOrgReq req) {
        return sendPost(ESealApiUrl.JOIN_ENTERPRISE, JSONUtil.parseObj(req), new JSONObject());
    }


}
