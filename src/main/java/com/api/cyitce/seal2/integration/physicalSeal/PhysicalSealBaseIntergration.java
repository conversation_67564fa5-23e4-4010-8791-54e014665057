package com.api.cyitce.seal2.integration.physicalSeal;

import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.physicalSeal.PhysicalSealBaseAuth;
import com.api.cyitce.seal2.util.http.PostUtil;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.PhysicalResp;

/**
 * @ClassName: PhysicalSeal
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  09:59
 * @Version: 1.0
 */
public class PhysicalSealBaseIntergration extends BaseLog {
    public static PhysicalSealBaseAuth config = Singleton.get(PhysicalSealBaseAuth.class);

    public static <T> BaseResp<T> sendPost(String url, JSONObject requestJson, T t) {
        JSONObject responseJson = PostUtil.toPost(url, requestJson, config);
        // 使用 TypeReference 精确转换 BaseResp<T>
        PhysicalResp<T> tBaseResp = JSONUtil.toBean(responseJson, new TypeReference<PhysicalResp<T>>() {}, false);
        Object o = JSONUtil.toBean(JSONUtil.parseObj(tBaseResp.getData()), t.getClass());
        t = (T) o;
        tBaseResp.setData(t);
        return tBaseResp.baseResp();
    }

    public Boolean isOk(JSONObject responseJson) {
        return responseJson.getInt("statusCode") == 200;
    }

    public Boolean isOk(BaseResp<?> resp) {
        return resp.isStatus();
    }
}