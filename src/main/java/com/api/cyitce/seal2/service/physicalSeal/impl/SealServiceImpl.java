package com.api.cyitce.seal2.service.physicalSeal.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.integration.physicalSeal.hrm.HrmSyncIntegration;
import com.api.cyitce.seal2.integration.physicalSeal.seal.CreateSealTakIntegration;
import com.api.cyitce.seal2.service.physicalSeal.SealService;
import com.api.cyitce.seal2.vo.req.hrm.HrmDeptApiReq;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.PhysicalSealListApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.PrintSealUseTaskApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import com.api.cyitce.seal2.vo.resp.seal.physical.PhysicalSealInfoApiResp;
import com.engine.core.impl.Service;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.Date;

public class SealServiceImpl extends Service implements SealService {
    CreateSealTakIntegration csti = new CreateSealTakIntegration();
    HrmSyncIntegration hsi = new HrmSyncIntegration();

    @Override
    public BaseResp<JSONObject> getSealList(String instrumentCode) {
        PhysicalSealListApiReq req = new PhysicalSealListApiReq();
        req.setInstrumentCode(instrumentCode);
        req.setPageNum(0);
        req.setPageSize(1);

        BaseResp<PhysicalSealInfoApiResp> resp = csti.getSealList(req);

        if (!resp.isStatus()) {
            return MsgResp.error("获取印章列表失败，e：" + resp.getMessage());
        }

        return MsgResp.ok(resp.getData());
    }

    @Override
    public BaseResp<JSONObject> createPrintSealUseTaskList(PrintSealUseTaskApiReq req) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // String s = format.format(new Date());
        req.setApplyTime(format.format(new Date()));


        new BaseBean().writeLog("创建(打印扫描盖章机)盖章任务：请求参数" + JSONUtil.toJsonStr(JSONUtil.parseObj(req)));
        BaseResp<JSONObject> resp = csti.createPrintSealUseTask(req);
        new BaseBean().writeLog("创建(打印扫描盖章机)盖章任务：请求响应结果" + resp.toString());
        if (!resp.isStatus()) {
            return MsgResp.error("创建(打印扫描盖章机)盖章任务失败，e：" + resp.getMessage());
        }
        return MsgResp.ok(resp.getData());
    }

    @Override
    public BaseResp<JSONObject> createDept(HrmDeptApiReq req) {
        return hsi.syncDept(req);
    }

    @Override
    public BaseResp<JSONObject> createUser(HrmResourceApiReq req) {
        return hsi.syncStaff(req);
    }
}
