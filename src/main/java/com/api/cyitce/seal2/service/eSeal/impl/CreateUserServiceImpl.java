package com.api.cyitce.seal2.service.eSeal.impl;

import cn.hutool.json.JSONObject;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.enums.account.AuthSource;
import com.api.cyitce.seal2.enums.account.CertificateTypeEnum;
import com.api.cyitce.seal2.enums.account.ESealUserType;
import com.api.cyitce.seal2.enums.enterprise.EnterpriseTypeEnum;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal2.integration.realauth.basic.CompanyBasicAuthIntergration;
import com.api.cyitce.seal2.integration.realauth.basic.PersonBasicAuthIntergration;
import com.api.cyitce.seal2.service.eSeal.CreateUserService;
import com.api.cyitce.seal2.vo.req.contract.BindLegalPerson;
import com.api.cyitce.seal2.vo.req.hrm.*;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import org.springframework.util.StringUtils;

public class CreateUserServiceImpl extends BaseLog implements CreateUserService {
    PersonBasicAuthIntergration pbai = new PersonBasicAuthIntergration();
    CreatUserIntegration cui = new CreatUserIntegration();
    CompanyBasicAuthIntergration cbai = new CompanyBasicAuthIntergration();
    CreateOrgIntegration coi = new CreateOrgIntegration();

    @Override
    public BaseResp<JSONObject> createUser(String displayName, String idCardNum, String mobile, String thirdDataId) {
        RealPersonApiReq req1 = new RealPersonApiReq();
        req1.setName(displayName);
        req1.setIdCard(idCardNum);
        req1.setMobile(mobile);

        info("开始个人实名认证，RealPersonApiReq：{?}", JSONUtil.toJsonStr(req1));

        BaseResp<JSONObject> resp2 = pbai.mobileAuth(req1);

        if (!resp2.isStatus()) {
            return resp2;
        }

        info("个人实名认证成功");

        CreatePersonReq req = new CreatePersonReq();
        req.setDisplayName(displayName);
        req.setIdCardNum(idCardNum);
        req.setPhone(mobile);
        req.setThirdDataId(thirdDataId);

        return createPersonAccount(req);
    }


    @Override
    public BaseResp<JSONObject> createUser(CreatePersonReq req) {
        RealPersonApiReq req1 = new RealPersonApiReq();
        req1.setName(req.getDisplayName());
        req1.setIdCard(req.getIdCardNum());
        req1.setMobile(req.getPhone());

        info("开始个人实名认证，RealPersonApiReq：{?}", JSONUtil.toJsonStr(req1));
        // 保证天威云的实名认证是正确的 从而确定了私有化的用户是正确的
        BaseResp<JSONObject> resp = pbai.mobileAuth(req1);
        info("个人实名认证结果----------------->", resp.getData().toString());
        if (!resp.isStatus()) {
            info("个人实名认证失败");
            return resp;
        }

        info("个人实名认证成功");

        return createPersonAccount(req);
    }

    private BaseResp<JSONObject> createPersonAccount(CreatePersonReq req) {
        req.setType(ESealUserType.MOBILE.getCode());
        req.setAuthentication(AuthSource.CUSTOMER.getCode());

        info("开始创建个人账号，CreatePersonReq：{?}", JSONUtil.toJsonStr(req));

        BaseResp<JSONObject> resp1 = cui.createPerson(req);

        if (!resp1.isStatus()) {
            return resp1;
        }

        info("创建个人账号成功");
        info("个人账号成功,---------> 返回值 : resp:" + resp1.toString());
        return resp1;
    }

    @Override
    public BaseResp<JSONObject> createCompany(String type, String userId, String orgName, String orgCode, String legalName, String legalIdCard, String thirdUniqueId) {
        RealOrgApiReq req1 = new RealOrgApiReq();
        req1.setType(type);
        req1.setName(orgName);
        req1.setLegalName(legalName);
        req1.setLegalId(legalIdCard);
        req1.setIdCode(orgCode);

        info("开始企业实名认证，RealOrgApiReq：{?}", JSONUtil.toJsonStr(req1));

        BaseResp<JSONObject> resp = cbai.companyAuth(req1);
        info("开始企业实名认证，RealOrgApiReq：认证返回数据", JSONUtil.toJsonStr(resp));
        if (!resp.isStatus()) {
            return resp;
        }

        info("企业实名认证成功");

        CreateOrgApiReq req = new CreateOrgApiReq();
        req.setUserId(userId);
        req.setOrgName(orgName);
        req.setLegalName(legalName);
        req.setLegalIdCard(legalIdCard);
        if (StringUtils.hasText(thirdUniqueId)) {
            req.setThirdUniqueId(thirdUniqueId);
        }
        req.setOrgCode(orgCode);
        req.setAuthentication(AuthSource.CUSTOMER.getCode());
        req.setIdType(CertificateTypeEnum.UNIFIED_SOCIAL_CREDIT_CODE.getCode());
        req.setEnterpriseType(EnterpriseTypeEnum.ET_PE.getCode());
        req.setLegalIdCardType(CertificateTypeEnum.IDENTITY_CARD.getCode());

        info("开始创建企业账号，CreateOrgApiReq：{?}", JSONUtil.toJsonStr(req1));

        BaseResp<JSONObject> resp1 = coi.createOrg(req);

        if (!resp1.isStatus()) {
            return resp1;
        }

        info("创建企业账号成功");

        return resp1;
    }

    @Override
    public BaseResp<JSONObject> bindLegalPerson(String enterpriseId, String legalUserId) {
        BaseResp<JSONObject> resp1 = coi.bindLegalPerson(new BindLegalPerson(legalUserId, enterpriseId));
        info("绑定企业法人 响应结果:" + JSONUtil.toJsonStr(resp1));
        if (!resp1.isStatus()) {
            return resp1;
        }
        info("绑定企业法人成功");
        return resp1;
    }
}
