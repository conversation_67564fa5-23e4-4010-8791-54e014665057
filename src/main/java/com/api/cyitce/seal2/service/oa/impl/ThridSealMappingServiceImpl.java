package com.api.cyitce.seal2.service.oa.impl;

import com.api.cyitce.seal2.cmd.thrid.GetThridSealDataCmd;
import com.api.cyitce.seal2.cmd.thrid.InsertThridSealDataCmd;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.service.oa.ThridSealMappingService;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import com.engine.core.impl.Service;

public class ThridSealMappingServiceImpl extends Service implements ThridSealMappingService {
    @Override
    public BaseResp<Void> insertThridDataRequestMapping(String thridId, String oaId, int type, String requestid) {
        return null;
    }

    @Override
    public BaseResp<Void> insertThridDataMapping(String thridId, String oaId, int type) {
        boolean boo = this.commandExecutor.execute(new InsertThridSealDataCmd(type,oaId,thridId,new ThirdMappConfig()));

        if(!boo){
            return MsgResp.error("三方系统数据映射插入失败");
        }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<String> getThridIdByOaId(String oaId, int type) {
        String thridId = this.commandExecutor.execute(new GetThridSealDataCmd(oaId,type,new ThirdMappConfig()));

        if("".equals(thridId)||thridId==null){
            return MsgResp.error("未找到对应的三方系统数据映射");
        }

        return MsgResp.ok(thridId);
    }

    @Override
    public BaseResp<String> getThridIdBysealId(String sealid, int type) {
        return null;
    }

    @Override
    public BaseResp<String> getThridoaIdBySealId(String sealid, int type) {
        return null;
    }

    @Override
    public BaseResp<String> deleteThridBySealId(String sealid, int type) {
        return null;
    }
}
