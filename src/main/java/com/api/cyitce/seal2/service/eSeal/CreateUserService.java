package com.api.cyitce.seal2.service.eSeal;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal2.vo.req.hrm.CreatePersonReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;

public interface CreateUserService {

    /**
     * 电子印章 个人注册
     **/
    BaseResp<JSONObject> createUser(String displayName, String idCardNum, String mobile, String thirdDataId);

    /**
     * 电子印章 个人注册
     **/
    BaseResp<JSONObject> createUser(CreatePersonReq req);

    /**
     * 电子印章 企业注册
     **/
    BaseResp<JSONObject> createCompany(String type, String userId, String orgName, String orgCode, String legalName, String legalIdCard, String thirdUniqueId);

    /**
     * 绑定法定企业人
     **/
    BaseResp<JSONObject> bindLegalPerson(String enterpriseId, String legalUserId);
}
