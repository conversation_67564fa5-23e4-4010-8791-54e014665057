package com.api.cyitce.seal2.service.realAuth;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal2.vo.resp.BaseResp;

import java.util.Map;

public interface UserRealService {
    /**
     * 企业实名认证
     **/
    BaseResp<JSONObject> realOrg(String name,String idCode,String relegalName,String legalId);

    /**
     * 个人实名认证
     **/
    BaseResp<JSONObject> realPerson(String name,String idCode,String mobile);

    /**
     * 个体工商户认证
     **/
    BaseResp<JSONObject> realIndividual(String name,String idCode,String relegalName,String legalId);

    /**
     * 实名认证
     **/
    BaseResp<JSONObject> realAuth(String type, Map<String,Object> req);
}
