package com.api.cyitce.seal2.service.realAuth.impl;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal2.enums.account.UserTypeEnum;
import com.api.cyitce.seal2.integration.realauth.basic.CompanyBasicAuthIntergration;
import com.api.cyitce.seal2.integration.realauth.basic.PersonBasicAuthIntergration;
import com.api.cyitce.seal2.service.realAuth.UserRealService;
import com.api.cyitce.seal2.vo.req.hrm.RealOrgApiReq;
import com.api.cyitce.seal2.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;

import java.util.Map;

public class UserRealServiceImpl implements UserRealService {
    PersonBasicAuthIntergration pbai = new PersonBasicAuthIntergration();
    CompanyBasicAuthIntergration cbai = new CompanyBasicAuthIntergration();

    @Override
    public BaseResp<JSONObject> realOrg(String name,String idCode,String relegalName,String legalId) {
        RealOrgApiReq req = new RealOrgApiReq();
        req.setName(name);
        req.setIdCode(idCode);
        req.setLegalName(relegalName);
        req.setLegalId(legalId);
        req.setType(UserTypeEnum.ENTERPRISE.getType());

        BaseResp<JSONObject> resp = cbai.companyAuth(req);

        if(resp.isStatus()){
            return MsgResp.error(resp.getMessage());
        }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<JSONObject> realPerson(String name,String idCode,String mobile) {
        RealPersonApiReq req = new RealPersonApiReq();
        req.setName(name);
        req.setIdCard(idCode);
        req.setMobile(mobile);

        BaseResp<JSONObject> resp = pbai.mobileAuth(req);

        if(resp.isStatus()){
            return MsgResp.error(resp.getMessage());
        }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<JSONObject> realIndividual(String name,String idCode,String relegalName,String legalId) {
        RealOrgApiReq req = new RealOrgApiReq();
        req.setName(name);
        req.setIdCode(idCode);
        req.setLegalName(relegalName);
        req.setLegalId(legalId);
        req.setType(UserTypeEnum.ENTERPRISE.getType());

        BaseResp<JSONObject> resp = cbai.companyAuth(req);

        if(resp.isStatus()){
            return MsgResp.error(resp.getMessage());
        }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<JSONObject> realAuth(String type, Map<String, Object> param) {
        BaseResp<JSONObject> resp;

        if("".equals(type)||type==null){
            RealPersonApiReq req1 = new RealPersonApiReq();
            req1.setName(param.get("name").toString());
            req1.setIdCard(param.get("idCode").toString());
            req1.setMobile(param.get("mobile").toString());

            resp = pbai.mobileAuth(req1);
        }else{
            RealOrgApiReq req = new RealOrgApiReq();

            if(UserTypeEnum.ENTERPRISE.getType().equals(type)){
                req.setName(param.get("name").toString());
                req.setIdCode(param.get("idCode").toString());
                req.setLegalName(param.get("legalName").toString());
                req.setLegalId(param.get("legalId").toString());
                req.setType(UserTypeEnum.ENTERPRISE.getType());
            }else if(UserTypeEnum.PER_ENTERPRISE.getType().equals(type)){
                req.setName(param.get("name").toString());
                req.setIdCode(param.get("idCode").toString());
                req.setLegalName(param.get("legalName").toString());
                req.setLegalId(param.get("legalId").toString());
                req.setType(UserTypeEnum.PER_ENTERPRISE.getType());
            }else if(UserTypeEnum.ET_OU.getType().equals(type)){
                req.setName(param.get("name").toString());
                req.setIdCode(param.get("idCode").toString());
                req.setLegalName(param.get("legalName").toString());
                req.setLegalId(param.get("legalId").toString());
                req.setType(UserTypeEnum.ET_OU.getType());
            }

            resp = cbai.companyAuth(req);
        }

        if(!resp.isStatus()){
            return MsgResp.error(resp.getMessage());
        }

        return MsgResp.ok(resp.getData());
    }
}
