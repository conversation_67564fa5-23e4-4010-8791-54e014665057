package com.api.cyitce.seal2.service.oa;

import com.api.cyitce.seal2.vo.resp.BaseResp;

public interface ThridMappingService {

    /**
     * 创建数据映射关系 印章
     **/
    BaseResp<Void> insertThridDataRequestMapping(String thridId, String oaId, int type,String requestid);
    /**
     * 创建数据映射关系 印章
     **/
    BaseResp<Void> insertThridDataMapping(String thridId, String oaId, int type);

    /**
     * 查询三分数据ID
     **/
    BaseResp<String> getThridIdByOaId(String oaId,int type);


    /**
     * 查询三分数据ID
     **/
    BaseResp<String> getThridIdBysealId(String sealid,int type);

    /**
     * 查询三分数据ID
     **/
    BaseResp<String> getThridoaIdBySealId(String sealid,int type);


    /**
     * 查询三分数据ID
     **/
    BaseResp<String> deleteThridBySealId(String sealid,int type);
}
