package com.api.cyitce.seal2.service.physicalSeal;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal2.vo.req.hrm.HrmDeptApiReq;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.PrintSealUseTaskApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;

public interface SealService {
    /**
     * 获取印章列表
     **/
    BaseResp<JSONObject> getSealList(String instrumentCode);

    /**
     * 创建(打印扫描盖章机)盖章任务【POST】
     **/
    BaseResp<JSONObject> createPrintSealUseTaskList(PrintSealUseTaskApiReq req);

    /**
     * 创建部门
     **/
    BaseResp<JSONObject> createDept(HrmDeptApiReq req);

    /**
     * 创建人员账号
     **/
    BaseResp<JSONObject> createUser(HrmResourceApiReq req);
}
