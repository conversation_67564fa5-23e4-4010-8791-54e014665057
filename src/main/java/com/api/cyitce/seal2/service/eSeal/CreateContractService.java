package com.api.cyitce.seal2.service.eSeal;

import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;

public interface CreateContractService {
    /**
     * 创建合同 默认签署人只有一个
     **/
    BaseResp<ContractApiVO> createContract(String contractCode, String contractName, String creator, byte[] bytes, String enterpriseId, String file1);
    BaseResp<ContractApiVO> createContractCon(String contractCode, String contractName, String creator, byte[] bytes, String enterpriseId, String file1,String callback);

    /**
     * 合同详情
     **/
    BaseResp<ContractApiVO> search(Long contractId, boolean responseContractFile);
}
