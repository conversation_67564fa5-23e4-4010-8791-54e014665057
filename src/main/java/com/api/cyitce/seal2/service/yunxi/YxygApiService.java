package com.api.cyitce.seal2.service.yunxi;

import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.yunxi.ApiClient;
import com.api.cyitce.seal2.config.yunxi.ApiException;
import com.api.cyitce.seal2.enums.yunxi.YxygApiKey;
import com.api.cyitce.seal2.vo.req.yunxi.*;
import org.springframework.util.CollectionUtils;

import java.util.*;


public class YxygApiService extends BaseLog {

    private final ApiClient apiClient;

    /**
     * 构造函数
     *
     * @param baseUrl API基础地址
     * @param appKey  应用标识
     * @param tenant  租户标识
     */
    public YxygApiService(String baseUrl, String appKey, String tenant, String appSecret, boolean encryptionEnabled) {
        this.apiClient = new ApiClient(baseUrl, appKey, tenant, appSecret, encryptionEnabled);
    }

    /**
     * 【YXYG01_07】增量同步组织
     */
    public void syncOrganizationsIncremental(List<YxOrganization> organizations) throws ApiException {
        if (organizations == null || organizations.isEmpty()) {
            info("组织列表为空，无需同步。");
            return;
        }

        YxygApiKey apiKey = YxygApiKey.SYNC_ORGANIZATION_INCREMENTAL;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // 直接使用内部的 apiClient 调用
        apiClient.execute(apiKey.getKey(), organizations, Void.class);
    }


    /**
     * 【YXYG01_08】增量同步用户
     * 方法名和参数类型已更新
     *
     * @param yxUsers 要同步的用户列表
     * @throws ApiException 如果API返回业务错误
     */
    public void syncYxUsersIncremental(List<YxUser> yxUsers) throws ApiException {
        if (yxUsers == null || yxUsers.isEmpty()) {
            info("用户列表为空，无需同步。");
            return;
        }

        YxygApiKey apiKey = YxygApiKey.SYNC_USER_INCREMENTAL;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        apiClient.execute(apiKey.getKey(), yxUsers, Void.class);
    }


    /**
     * 【YXYG03_08】增删改申请文件类型
     *
     * @param yxFileType 包含文件类型信息的对象
     * @throws ApiException 如果API返回业务错误
     */
    public void manageYxApplicationFileType(YxApplicationFileType yxFileType) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.MANAGE_APPLICATION_FILE_TYPES;

        // 根据 mode 字段打印更具体的日志
        String modeDescription = "未知操作";
        if (yxFileType.getMode() != null) {
            switch (yxFileType.getMode()) {
                case 0:
                    modeDescription = "删除";
                    break;
                case 1:
                    modeDescription = "新增或更新";
                    break;
            }
        }

        info("正在调用接口: " + apiKey.getDescription() + " - " + modeDescription + " (" + apiKey.getKey() + ")");

        // params是单个对象，响应中没有data，所以返回类型是Void.class
        apiClient.execute(apiKey.getKey(), yxFileType, Void.class);
    }


    /**
     * 【YXYG03_07】创建申请单
     *
     * @param yxApplication 包含所有申请单信息的对象
     * @return 包含赋码后文件信息的列表
     * @throws ApiException 如果API返回业务错误
     */
    public List<YxApplicationResponseData> createYxApplication(YxApplication yxApplication) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.CREATE_APPLICATION;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // params是单个对象，data是对象数组
        // Hutool处理泛型List反序列化有时不便，直接反序列化为数组更稳定
        YxApplicationResponseData[] responseArray = apiClient.execute(
                apiKey.getKey(),
                yxApplication,
                YxApplicationResponseData[].class
        );

        if (responseArray == null) {
            return Collections.emptyList(); // 返回空列表，避免调用方出现NullPointerException
        }

        return Arrays.asList(responseArray);
    }


    /**
     * 【YXYG03_25】创建申请单(附件为Base64)
     *
     * @param yxApplication 包含所有申请单信息的对象 (Base64版本)
     * @throws ApiException 如果API返回业务错误
     */
    public void createYxApplicationWithBase64(YxApplicationBase64 yxApplication) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.CREATE_APPLICATION_WITH_BASE64;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // params是单个对象，响应中没有data，所以返回类型是Void.class
        apiClient.execute(apiKey.getKey(), yxApplication, Void.class);
    }

    /**
     * 【YXYG03_01】推送申请单
     * 将申请单信息推送至印章设备，使其处于待解锁用印状态。
     *
     * @param yxApplication 包含所有申请单信息的对象。此接口与创建申请单接口(YXYG03_07)使用相同的实体类。
     * @throws ApiException 如果API返回业务错误
     */
    public void pushYxApplication(YxApplication yxApplication) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.PUSH_APPLICATION;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // params是单个对象，响应中没有data，所以返回类型是Void.class
        apiClient.execute(apiKey.getKey(), yxApplication, Void.class);
    }


    /**
     * 【YXYG02_04】安装印章
     * 用于印章设备安装实体印章或更换墨盒。
     *
     * @param installParams 包含设备标识和操作人ID的参数对象
     * @throws ApiException 如果API返回业务错误
     */
    public void installYxSeal(YxInstallSealParams installParams) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.INSTALL_SEAL;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // params是单个对象，响应中没有data，所以返回类型是Void.class
        apiClient.execute(apiKey.getKey(), installParams, Void.class);
    }


    /**
     * 【YXYG03_04】获取授权码
     * 获取申请单的授权码，用于通过授权码使用印章设备。
     *
     * @param authCodeParams 包含申请单ID、设备UUID和可选的过期时间的参数对象
     * @return 包含授权码信息的响应数据对象
     * @throws ApiException 如果API返回业务错误
     */
    public YxAuthCodeResponseData getYxAuthorizationCode(YxAuthCodeParams authCodeParams) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.GET_AUTHORIZATION_CODE;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        // params是单个对象，响应中有data，所以返回类型是我们定义的 YxAuthCodeResponseData.class
        return apiClient.execute(apiKey.getKey(), authCodeParams, YxAuthCodeResponseData.class);
    }


    /**
     * 【YXYG03_23】获取授权码(实体章)
     * 获取实体印章申请单的授权码。
     *
     * @param authCodeParams 包含申请单ID和可选的过期时间的参数对象
     * @return 包含授权码信息的响应数据对象
     * @throws ApiException 如果API返回业务错误
     */
    public YxPhysicalSealAuthCodeResponseData getPhysicalSealAuthorizationCode(YxPhysicalSealAuthCodeParams authCodeParams) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.GET_PHYSICAL_SEAL_AUTHORIZATION_CODE;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        return apiClient.execute(apiKey.getKey(), authCodeParams, YxPhysicalSealAuthCodeResponseData.class);
    }


    /**
     * 【YXYG01_02】删除组织
     * (这是一个示例，用于展示未来如何添加新方法)
     *
     * @throws ApiException 如果API返回业务错误
     */
    /*
    public void deleteOrganization(String orgId) throws ApiException {
        // 删除组织的 params 可能是一个简单的对象，例如: {"id": "some_id"}
        class DeleteOrgParams {
            public String id;
            public DeleteOrgParams(String id) { this.id = id; }
        }

        DeleteOrgParams params = new DeleteOrgParams(orgId);

        YxygApiKey apiKey = YxygApiKey.DELETE_ORGANIZATION;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");

        apiClient.execute(apiKey.getKey(), params, Void.class);
    }
    */


    /**
     * 【YXYG04_01】获取智能印章列表
     */
    public YxDeviceListResponseData getDeviceList(YxDeviceListParams deviceListParams) throws ApiException {
        YxygApiKey apiKey = YxygApiKey.GET_DEVICE_LIST;
        info("正在调用接口: " + apiKey.getDescription() + " (" + apiKey.getKey() + ")");
        return apiClient.execute(apiKey.getKey(), deviceListParams, YxDeviceListResponseData.class);
    }

    public static void main(String[] args) throws ApiException {
        String yxUuid = "0X1621SPK244402V00007318";
        String startTime="2023-08-01 15:15:15";
        String endTime="2023-08-01 20:00:00";
        // 假设这些是配置文件中的默认值
        String baseUrl = "http://*************:18585/stamper/apis";
        String appKey = "cqxkgc";
        String tenant = "cqxkgc";
        String appSecret = "cn5o3JAG657Cjcs9";
        // ApiClient client = new ApiClient(baseUrl, appKey, tenant, appSecret, true);
        YxygApiService service = new YxygApiService(baseUrl, appKey, tenant, appSecret, true);
        //     List<YxOrganization> organizations = Arrays.asList(
        //             new YxOrganization("10002", "测试部门", "10001", 0)
        //     );
        //     service.syncOrganizationsIncremental(organizations);
        // }
        YxApplication yxApplication = new YxApplication("21", "3810", "3810", yxUuid, 2, 2, 0, "1", "3810", "3810");
        // YxApplicationResponseData yxApplication = new YxApplicationResponseData("8", "3810", "3810", yxUuid, 2, 2, 0, "1", "3810", "3810",null);

        // List<YxApplicationEleFence> listFence = new ArrayList<>();
        // YxApplicationEleFence fence = new YxApplicationEleFence();
        // if (StringUtils.hasText(startTime) || StringUtils.hasText(endTime)) {
        //     if (StringUtils.hasText(startTime)) {
        //         fence.setStartTime(startTime);
        //     }
        //     if (StringUtils.hasText(endTime)) {
        //         fence.setEndTime(endTime);
        //     }
        //     listFence.add(fence);
        // }
        List<YxApplicationFile> listFile = new ArrayList<>();

        // if (StringUtils.hasText(wdfj)) {
        //     List<String> list = Arrays.asList(wdfj.split(","));
        //     for (String s : list) {
        //         YxApplicationFile application = new YxApplicationFile();
        //         Map<String, String> fileMap = getfileName(Integer.parseInt(docId));
        //         if (CollectionUtils.isEmpty(fileMap)) {
        //             continue;
        //         }
        //         application.setFileUrl(SignAction.DownFilePath(fileMap.get("fileid"), false));
        //         application.setFileName(fileMap.get("filename"));
        //         listFile.add(application);
        //     }
        // }
        YxApplicationFile application = new YxApplicationFile();
        application.setFileUrl("https://oa.cyitce.com/weaver/weaver.file.FileDownload?fileid=1861347&download=1&ddcode=847e6c16412081e837547b58020d0bce");
        application.setFileName("测试.pdf");
        listFile.add(application);
        if (!CollectionUtils.isEmpty(listFile)) {
            yxApplication.setFiles(listFile);
        }
        // YxApplicationFileBase64 application = new YxApplicationFileBase64();
        // application.setFileBase64("https://oa.cyitce.com/weaver/weaver.file.FileDownload?fileid=1861347&download=1&ddcode=847e6c16412081e837547b58020d0bce");
        // application.setFileName("测试.pdf");
        // listFile.add(application);
        // if (!CollectionUtils.isEmpty(listFile)) {
        //     yxApplication.setFiles(listFile);
        // }
        // if (!CollectionUtils.isEmpty(listFence)) {
        //     yxApplication.setApplicationEleFences(listFence);
        // }


        // service.syncYxUsersIncremental(Arrays.asList(
        //         new YxUser("3810", "李俊鸿", "10002", "ljh2947", "15922785574", 0)
        // ));
        //
        //
        // YxApplicationFileType yxApplicationFileType = new YxApplicationFileType(1, "10002", "2", "1", true, true, "common", 0, "up", false, "all", Arrays.asList("2947"), 0, 0, 0);
        //
        // System.out.println(JSONUtil.toJsonStr(yxApplicationFileType));
        //
        // service.manageYxApplicationFileType(
        //         yxApplicationFileType
        // );

        // service.createYxApplicationWithBase64(
        //         new YxApplicationBase64("1","测试","测试",
        //                 "1",2,1,2,"1","2947",
        //                 "2947",null
        //                 )
        // // );
        // DeviceSyncService syncService = new DeviceSyncService(service);
        // syncService.syncAllDevices();
        // System.out.println(JSONUtil.toJsonStr(yxApplication));
        // service.pushYxApplication(yxApplication);
        service.createYxApplication(yxApplication);
        System.out.println("yxApplication = " + 11);
    }


}