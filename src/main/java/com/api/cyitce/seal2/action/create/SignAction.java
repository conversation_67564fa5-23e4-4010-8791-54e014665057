package com.api.cyitce.seal2.action.create;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.config.eSeal.ESealConfig;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.service.physicalSeal.impl.SealServiceImpl;
import com.api.cyitce.seal2.util.FileUtils;
import com.api.cyitce.seal2.util.data.Now;
import com.api.cyitce.seal2.util.data.OaHrmUtils;
import com.api.cyitce.seal2.vo.req.seal.physical.FileDataVo;
import com.api.cyitce.seal2.vo.req.seal.physical.PrintSealUseTaskApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.PrintSealUseTaskVo;
import com.engine.common.util.ServiceUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.service.eSeal.impl.SignServiceImpl;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.finance.system.toolkit.DesUtils;
import com.weaver.general.BaseBean;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.*;

import static com.api.cyitce.seal2.action.contract.ContaractAction.getInfo;
import static com.api.cyitce.seal2.action.create.checkAction.BMBM;
import static com.api.cyitce.seal2.util.FileUtils.getFormName;
import static com.api.cyitce.seal2.util.sql.SqlUtils.*;

/**
 * @ClassName: 统一印控中心 页面签署
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-23  16:05
 * @Version: 1.0
 */
public class SignAction extends BaseLog implements Action {
    public String configId = "1";
    public int type = 0; // 0 华来 1 信科


    /**
     * 保密部门 344 	jbrbm
     * 经办人 	jbr
     */

    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        int currentUserId = user.getUID();
        info("拿到当前操作人--------------->id:" + currentUserId);
        String sfxyqz = "1"; // 是否需要签字
        int sealType = -1;
        String docId = "";
        int signerId = 0;
        String yzlx = "";
        int sqfs = 1; // 用印文件数量
        String useSealType = "0";
        String scanType = "0";
        int useCount = 0;
        String nuseCount = "";
        int companyId = -1;
        String ischeckMark = "0";
        String checkMarkType = "0";
        String checkMarkUseCount = "";
        String tag = "xynr";
        String lcbh = "default-";
        boolean flag = false;
        int num = 1; // 计数
        info("SignAction  -----------------------> 执行盖章逻辑");
        info("SignAction  -----------------------> configId" + configId);
        SignServiceImpl ssi = new SignServiceImpl();
        ESealConfig config = new ESealConfig();
        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();

        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        String jbrbm = "-1";
        String jbr = "";
        // String uesSealType = "1";
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            info("SignAction  ----------------------->进入明细表");
            for (Property p : properties) {
                info("SignAction  -----------------------> 主表参数  " + p.getName() + " : " + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("CreateContractAction-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        }
                    }
                } else if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if ("swszfb".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("szgs".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("lcbh".equals(p.getName())) {
                    // 流程编号
                    lcbh = p.getValue();
                } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbr()) && p.getName().equals(wconfig.getJbr())) {
                    if (StringUtils.hasText(p.getValue())) {
                        jbr = p.getValue();
                    }
                } else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }

            if (sealType != SealFromEnum.PHYSICAL_SEAL.getValue()) {
                return Action.SUCCESS;
            }

            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }
            // // 实体章先不执行执行
            // if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            //     info("SignAction------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }
            // 用印模式
            String yyms = obj.get("yyms");
            if (StringUtils.hasText(yyms)) {
                useSealType = yyms;
            }

            // boolean sqbmFlag = false;
            // String sqbm = obj.get("sqbm");
            // if (StringUtils.hasText(sqbm) && sqbm.equals("344")) {
            //     sqbmFlag = true;
            // }
            // jbr = obj.get("jbr");
            String jbrCode = "";
            if (StringUtils.hasText(jbr)) {
                jbrCode = OaHrmUtils.getworkCode(Integer.valueOf(jbr));
            }
            lcbh = obj.get(wconfig.getLcbh());
            info("SignAction  ----------------------->拿到流程编号: " + wconfig.getLcbh() + ": " + lcbh);
            tag = wconfig.getFfile();
            if (wconfig.isDetail()) {
                DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt = detailTables[wconfig.getDatasource() - 1];
                // 校验明细表行 数据，
                if (sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) {
                    for (Row r : dt.getRow()) {
                        String id = r.getId();
                        info("SignAction  ----------------------->明细表字符串规则校验" + id);

                        Cell[] cs = r.getCell();
                        for (Cell c : cs) {
                            info("SignAction  -----------------------> 主表参数  " + c.getName() + " : " + c.getValue());
                            if (wconfig.getYzlx().equals(c.getName())) {
                                if (StringUtils.hasText(c.getValue())) {
                                    yzlx = c.getValue();
                                }
                                // 印章库多选
                            } else if ("yzkdx".equals(c.getName())) {
                                if (StringUtils.hasText(c.getValue())) {
                                    yzlx = c.getValue();
                                }
                                // 骑缝章数量
                            } else if ("nqfzsl".equals(c.getName())) {
                                if (StringUtils.hasText(c.getValue())) {
                                    checkMarkUseCount = c.getValue();
                                }
                                // 修改后 用印总次数
                            } else if ("ngzcs".equals(c.getName())) {
                                if (StringUtils.hasText(c.getValue())) {
                                    nuseCount = c.getValue();
                                }
                            }
                        }
                        if (!isValid(nuseCount.trim())) {
                            requestInfo.getRequestManager().setMessagecontent("明细表第[" + num + "]行,用印总次数:填写格式错误,请按照约定格式填写");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        if (!isValid(checkMarkUseCount.trim())) {
                            requestInfo.getRequestManager().setMessagecontent("明细表第[" + num + "]行,骑缝章数量:填写格式错误,请按照约定格式填写");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        num++;
                    }
                }

                for (Row r : dt.getRow()) {
                    String id = r.getId();
                    info("SignAction  ----------------------->r.getId()::" + id);
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        info("SignAction  -----------------------> name:" + c.getName() + " value:" + c.getValue());
                        info("SignAction  -----------------------> name:1" + wconfig.getFfile());
                        if (c.getName().equals(wconfig.getFfile())) {
                            if (!flag) {
                                info("SignAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length > 1) {
                                    requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                info("docId" + c.getValue());
                                docId = c.getValue();
                            }
                        } else if (wconfig.getFsigner().equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                signerId = Integer.parseInt(c.getValue());
                            }
                        } else if (wconfig.getYzlx().equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                yzlx = c.getValue();
                            }
                            // 印章库多选
                        } else if ("yzkdx".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                yzlx = c.getValue();
                            }
                            // 申请份数
                        } else if ("sqfs".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sqfs = Integer.parseInt(c.getValue());
                            }
                            // 法律事务办理流程  是否需要用印
                        } else if ("shnryy".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                tag = "shnryy";
                                docId = c.getValue();
                            }
                            // 是否需要签字
                        } else if ("sfxyqz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sfxyqz = c.getValue();
                            }
                            // 打印模式 , 1 扫描盖章,2 仅盖
                            // 章,3 扫描识别盖章,4 批量盖章
                            // 默认打印盖章
                        } else if ("dyms".equals(c.getName())) {
                            //
                            if (StringUtils.hasText(c.getValue())) {
                                useSealType = c.getValue();
                            }
                            // 0 单面扫描,1 双面扫描
                        } else if ("smms".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                scanType = c.getValue();
                            }
                            // 盖章次数
                        } else if ("gzcs".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                useCount = Integer.parseInt(c.getValue());
                            }
                            // 非必填,0 否,1 是
                            // 默认 不加盖
                        } else if ("sfjgqfz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                ischeckMark = c.getValue();
                            }
                            // 非必填 0 普通,1 打印
                            // 默认 普通
                        } else if ("checkMarkType".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                checkMarkType = c.getValue();
                            }
                            // 骑缝章数量
                        } else if ("qfzsl".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                checkMarkUseCount = c.getValue();
                            }
                            // 骑缝章数量
                        } else if ("nqfzsl".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                checkMarkUseCount = c.getValue();
                            }
                            // 修改后 用印总次数
                        } else if ("ngzcs".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                nuseCount = c.getValue();
                            }
                        }
                    }

                    String seal_signer;

                    if (wconfig.isSigner()) {
                        signerId = wconfig.getSigner();
                        seal_signer = OaHrmUtils.getworkCode(wconfig.getSigner());
                    } else {
                        seal_signer = OaHrmUtils.getworkCode(signerId);
                    }
                    info("SignAction  -----------------------> 固定签署人: " + seal_signer);
                    // 发送页面签署  盖章
                    if (SealFromEnum.E_SEAL.getValue() == sealType) {
                        // try {
                        //     PageSignReq psr = new PageSignReq();
                        //
                        //     info("SignAction  -----------------------> 查询对应合同: ");
                        //     BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                        //
                        //     if (!resp3.isStatus()) {
                        //         info("SignAction  -----------------------> 查询文档对应合同失败... 没用对应合同 ");
                        //         requestInfo.getRequestManager().setMessagecontent(resp3.getMessage());
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     info("SignAction  -----------------------> 查询文档对应合同id: " + resp3.getData());
                        //     psr.setContractId(Long.valueOf(resp3.getData()));
                        //
                        //     AddContractSignerVO acsv = new AddContractSignerVO();
                        //
                        //     // BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(seal_signer, 2);
                        //     //
                        //     // if (!resp4.isStatus()) {
                        //     //     info("SignAction  ----------------------->  查询用户 对应三方id 失败  不存在: ");
                        //     //     // 返回适当的响应或记录错误
                        //     //     return Action.FAILURE_AND_CONTINUE;
                        //     // }
                        //
                        //     BaseResp<String> resp5 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                        //
                        //     if (!resp5.isStatus()) {
                        //         info("SignAction  ----------------------->  查询用户 对应三方id 失败  不存在: ");
                        //         // 返回适当的响应或记录错误
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     // 设置当前签署人
                        //     // String currentCode = OaHrmUtils.getworkCode(currentUserId);
                        //     BaseResp<String> resp6 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId("1985", 2);
                        //     if (!resp6.isStatus()) {
                        //         info("SignAction  ----------------------->  查询用户 对应三方id 失败  不存在: ");
                        //         // 返回适当的响应或记录错误
                        //         requestInfo.getRequestManager().setMessagecontent("用印执行人 三方id数据不存在");
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     acsv.setUserId(resp6.getData());
                        //     // acsv.setUserId(jbrCode);
                        //     // 企业签署
                        //     acsv.setSignerType(2);
                        //     // 根据企业对应的UUID 查询对应的企业id
                        //     acsv.setEnterpriseId(resp5.getData());
                        //     info("SignAction  -----------------------> 企业固定UUID: " + resp5.getData());
                        //     SignFileApiVO sfav = new SignFileApiVO();
                        //     info("SignAction  -----------------------> 拿到合同文档的id:");
                        //     BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 1);
                        //     if (!resp.isStatus()) {
                        //         info("SignAction  -----------------------> 合同文档查询出错，不存在:");
                        //         requestInfo.getRequestManager().setMessagecontent(resp.getMessage());
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     info("SignAction  -----------------------> 合同文档的id: " + resp.getData());
                        //     sfav.setDocId(Long.valueOf(resp.getData()));
                        //     List<SignFileApiVO> sfavList = new ArrayList<>();
                        //     sfavList.add(sfav);
                        //     acsv.setSignFiles(sfavList);
                        //     psr.setSigner(acsv);
                        //     // psr.setIsUserWishes(true);
                        //     // Integer[] isWish = {1, 2, 4};
                        //     // psr.setUserWishesWay(isWish);
                        //     info("SignAction  -----------------------> 发送企业签署 psr " + JSONUtil.toJsonStr(psr));
                        //     BaseResp<PageSignResp> resp1 = ssi.orgPageSign(psr);
                        //     if (!resp1.isStatus()) {
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     Map<String, String> map = getqsrInfo(0, Base64.encode(docId), new ThirdMappConfig());
                        //     // 执行qsym回显
                        //     info("SignAction ---------------------------> 修改对应链接地址的个人会签页面 :");
                        //     String tableName = getFormName(map.get("requestid1"));
                        //     if (!StringUtils.hasText(tableName)) {
                        //         requestInfo.getRequestManager().setMessagecontent("SignAction：主表名称不存在");
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     Map<String, String> tid = getTableDTid(Integer.parseInt(map.get("requestid1")), tableName, 2, docId, tag);
                        //     if (!StringUtils.hasText(tid.get("id"))) {
                        //         requestInfo.getRequestManager().setMessagecontent("SignAction：明细表不存在");
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        //     String detailId = tid.get("id");
                        //     int res = updateQsym(Integer.parseInt(detailId), tableName, 2, resp1.getData().getSignUrl());
                        //     if (res == 1) {
                        //         if (!StringUtils.hasText(tid.get("dtid"))) {
                        //             requestInfo.getRequestManager().setMessagecontent("SignAction：明细表id 查询失败不存在");
                        //             return Action.FAILURE_AND_CONTINUE;
                        //         }
                        //     }
                        //     info("SignAction ---------------------------> 电子签 盖章页面 结束");
                        // } catch (Exception e) {
                        //     requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                        //     return Action.FAILURE_AND_CONTINUE;
                        // }

                        return Action.SUCCESS;
                    } else if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                        if (!StringUtils.hasText(nuseCount)) {
                            info("SignAction  物理印章  -----------------------> 现参数 没有值,  useCount: " + useCount);
                            nuseCount = String.valueOf(useCount);
                            info("SignAction  物理印章  -----------------------> 现参数 没有值,  nuseCount: " + nuseCount);
                        }
                        if (!StringUtils.hasText(jbr)) {
                            requestInfo.getRequestManager().setMessagecontent("经办人取值失败，请检查经办人是否选择或者联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        if (!StringUtils.hasText(yzlx)) {
                            requestInfo.getRequestManager().setMessagecontent("印章未选择，请用印审批节点确认或联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }

                        info("SignAction  物理印章  -----------------------> 物理印章 签署，经办人存在 ");
                        PrintSealUseTaskApiReq req = new PrintSealUseTaskApiReq();
                        // String sqlId = obj.get(wconfig.getCreaterfieldname());
                        // info("SignAction  物理印章  -----------------------> 物理印章 createrfieldname" + sqlId);
                        // int i = Integer.valueOf(sqlId);
                        // HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(i);
                        // String username = allInfo.getUsername();
                        // info("SignAction  物理印章  -----------------------> 拿到申请人登录名: " + username);
                        // req.setApplyStaffLoginName(username);
                        // String signerName = OaHrmUtils.getAllInfo(signerId).getUsername();
                        //
                        // info("SignAction  物理印章  -----------------------> 拿到固定签署人登录名: " + signerName);
                        List<String> list = new ArrayList<>();
                        // list.add(username);
                        // String jbrName = OaHrmUtils.getAllInfo(Integer.parseInt(jbr)).getUsername();
                        // if (!list.contains(jbrName)) {
                        //     list.add(jbrName);
                        // }
                        String createrCode = OaHrmUtils.getworkCode(Integer.parseInt(jbr));
                        BaseResp<String> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 4);
                        if (!resp1.isStatus()) {
                            info("SignAction  物理印章  -----------------------> 经办人三方数据查询 失败: " + JSONUtil.toJsonStr(resp1));
                        }
                        req.setApplyStaffLoginName(resp1.getData());
                        list.add(resp1.getData());
                        info("SignAction  物理印章  -----------------------> 拿到申请人登录名list: " + list.toString());
                        req.setSealUseStaffLoginNames(list);
                        info("SignAction  物理印章  -----------------------> 拿到选择的印章id 数组 " + yzlx);
                        // BaseResp<String> resp3 = ServiceUtil.getService(ThridSealMappingServiceImpl.class).getThridIdByOaId(null, yzlx);

                        /**
                         * FileData 数据参数
                         * fileName 文件名 String 必填
                         * filePath 文件访问路径 String 必填
                         * totalCount 申请份数 Integer 必填
                         *
                         * fileName 文件名 String必填 (该印章所要盖章文件的 文件名称,与上述 fileData 的 fileName 字段数值保证一致)
                         * sealCode 印章编码 String 必填
                         * useCount 盖章次数 Integer 必填
                         */
                        Map<String, String> file = new HashMap<>();

                        // if (useCount == 0) {
                        //     // 盖章次数为0  不推送任务
                        //     info("SignAction  物理印章  -----------------------> 盖章次数为0  不推送任务");
                        //     continue;
                        // }
                        if (isAllZero(nuseCount)) {
                            // 盖章次数为0  不推送任务
                            info("SignAction  物理印章  -----------------------> 盖章次数为0  不推送任务: " + nuseCount);
                            continue;
                        }
                        try {
                            info("SignAction  物理印章  -----------------------> docId参数  " + docId);
                            // file = FileUtils.oaFileInfo(docId);
                            if (!flag) {
                                sfxyqz = "0"; //
                                if ("1".equals(sfxyqz)) {
                                    // 需要签字  拿到签字后文件并传给实体印章
                                    String requestId = requestInfo.getRequestid();
                                    String tableName = getFormName(requestId);
                                    if (!StringUtils.hasText(tableName)) {
                                        requestInfo.getRequestManager().setMessagecontent("SignAction：主表名称不存在");
                                        return Action.FAILURE_AND_CONTINUE;
                                    }
                                    Map<String, String> tid = getTableDTid(Integer.parseInt(requestId), tableName, 2, docId, tag);
                                    if (!StringUtils.hasText(tid.get("id"))) {
                                        requestInfo.getRequestManager().setMessagecontent("SignAction：明细表不存在");
                                        return Action.FAILURE_AND_CONTINUE;
                                    }
                                    if (StringUtils.hasText(tid.get("qzhfj"))) {
                                        docId = tid.get("qzhfj");
                                    }
                                    // info("SignAction  物理印章  -----------------------> 签字后附件   requestId " + requestId + "  tableName" + tableName);
                                    // Map<String, String> tableInfo = getTableInfo(tableName, requestId);
                                    // String newId = tableInfo.get("qzhfj");
                                    // info("SignAction  物理印章  -----------------------> 签字后附件 docId  :" + newId);
                                }
                                file = getfileName(Integer.parseInt(docId));
                            }
                        } catch (Exception e) {
                            requestInfo.getRequestManager().setMessagecontent("执行出错");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        info("SignAction  物理印章  -----------------------> file参数  ");
                        List<FileDataVo> fileDataVoList = new ArrayList<>();
                        FileDataVo fileDataVo = new FileDataVo();
                        fileDataVo.setFileName(flag ? "敏感项目默认空白合同.pdf" : file.get("filename"));
                        fileDataVo.setTotalCount(sqfs);
                        fileDataVo.setScanType(scanType);
                        fileDataVo.setFilePath(DownFilePath(file.get("fileid"), flag));
                        if (flag) { // 涉密项目
                            // useSealType = "2";
                            // 保密文件
                            fileDataVo.setClassified("1");
                        }
                        fileDataVo.setScanType(scanType);
                        fileDataVo.setUseSealType(useSealType);
                        fileDataVoList.add(fileDataVo);
                        req.setPushSealUseAioFileData(fileDataVoList);
                        List<PrintSealUseTaskVo> l = new ArrayList<>();
                        String[] split = yzlx.split(",");
                        String[] markSplit = checkMarkUseCount.trim().split(",");
                        String[] countSplit = nuseCount.trim().split(",");

                        // for (String s : split) {
                        for (int i = 0; i < split.length; i++) {
                            String respSF = getSealid1(Integer.parseInt(split[i]), new ThirdMappConfig());
                            if (!"1".equals(respSF)) {
                                info("SignAction  物理印章  -----------------------> 印章id :" + split[i] + "    未放入印控仪中");
                                continue;
                            }
                            String markValue = markSplit.length - 1 >= i ? markSplit[i] : markSplit[markSplit.length - 1];
                            String countValue = countSplit.length - 1 >= i ? countSplit[i] : countSplit[countSplit.length - 1];
                            if ("0".equals(countValue) && "0".equals(markValue)) {
                                info("SignAction 物理印章  -----------------------> 印章id :" + split[i] + "用印次数与骑缝章都为0,不推送");
                                continue;
                            }
                            String resp3 = getSealid(Integer.parseInt(split[i]), new ThirdMappConfig());
                            info("SignAction  物理印章  -----------------------> 拿到选择的印章对应的外部编号 " + resp3);
                            if (!StringUtils.hasText(resp3)) {
                                info("SignAction  物理印章  -----------------------> 拿到选择的印章对应的外部编号 " + resp3);
                                requestInfo.getRequestManager().setMessagecontent("三方印章编号不存在");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                            // 印章外部编号
                            String data = resp3;
                            PrintSealUseTaskVo vo = new PrintSealUseTaskVo();
                            vo.setSealCode(data);
                            vo.setFileName(flag ? "敏感项目默认空白合同.pdf" : file.get("filename"));
                            vo.setUseCount(Integer.parseInt(countValue));
                            String markFlag = "0".equals(markValue) ? "0" : "1";
                            vo.setIsCheckMark("0".equals(ischeckMark) ? "0" : markFlag);
                            vo.setCheckMarkType(checkMarkType);
                            if (!"0".equals(ischeckMark) && !"0".equals(markFlag)) {
                                vo.setCheckMarkUseCount(markValue);
                            }
                            l.add(vo);
                        }
                        if (!CollectionUtils.isEmpty(l)) {
                            req.setData(l);
                            info("SignAction  物理印章  -----------------------> 创建盖章任务请求 请求参数:  " + req.toString());
                            Long appId = Long.valueOf(new Now().toString());
                            req.setApplyId(appId);
                            req.setApplyReason("申请用印: " + lcbh);
                            req.setApplyTitle("申请用印: " + lcbh);
                            BaseResp<JSONObject> resp = ServiceUtil.getService(SealServiceImpl.class).createPrintSealUseTaskList(req);
                            info("SignAction  物理印章  -----------------------> 创建盖章任务请求 返回结果:  " + JSONUtil.toJsonStr(resp));
                            if (!resp.isStatus()) {
                                requestInfo.getRequestManager().setMessagecontent(resp.getMessage());
                                return Action.FAILURE_AND_CONTINUE;
                            }
                            info("SignAction  物理印章  ----------------------->  插入对应数据" + appId + "  " + docId + "  " + requestInfo.getRequestid());
                            Map<String, String> repect = getRepect(5, docId, new ThirdMappConfig(), requestInfo.getRequestid());
                            if (repect.isEmpty()) {
                                insertRequest(String.valueOf(appId), docId, 5, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(r.getId()), wconfig.getDatasource(), configId);
                            } else {
                                info("SignAction  物理印章  -----------------------> 更新盖章任务请求 已存在 " + appId + "  " + docId + "  " + requestInfo.getRequestid());
                                int i = updateRepect(docId, 5, new ThirdMappConfig(), requestInfo.getRequestid(), String.valueOf(appId));
                                if (i != 0) {
                                    info("SignAction  物理印章  -----------------------> 更新盖章任务请求 失败 :" + i);
                                    requestInfo.getRequestManager().setMessagecontent("更新盖章任务请求失败,请联系管理员!");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            }
                            // insertRequest(String.valueOf(appId), docId, 11, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(r.getId()), wconfig.getDatasource(), configId);
                            info("SignAction  物理印章  -----------------------> 创建盖章任务请求 执行完成  ");
                        } else {
                            info("SignAction  物理印章  -----------------------> 选择的印章没有在印控仪中,不推送任务");
                        }

                    }
                }
            }
        } else {
            Map<String, String> map = getInfo(requestInfo.getRequestManager().getBillTableName(), requestInfo.getRequestid(), wconfig.getFfile());
            docId = map.get(wconfig.getFfile());
            if (!StringUtils.hasText(docId)) {
                requestInfo.getRequestManager().setMessagecontent("未查询到生成的合同文档");
                return Action.FAILURE_AND_CONTINUE;
            }
            info("SignAction  ----------------------->进入主表");
            for (Property p : properties) {
                info("用户授权------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    if (configId.equals("7")) {
                        // 支出合同 0 纸质 1 电子
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        }
                    } else if (configId.equals("8")) {
                        // 采购结算单 1 纸质  0 电子
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        }
                    }
                } else if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if (StringUtils.hasText(wconfig.getJbr()) && p.getName().equals(wconfig.getJbr())) {
                    if (StringUtils.hasText(p.getValue())) {
                        jbr = p.getValue();
                    }
                } else if (wconfig.getYzlx().equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        yzlx = p.getValue();
                    }
                    // 印章库多选
                } else if ("yzkdx".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        yzlx = p.getValue();
                    }
                    // 申请份数
                } else if (wconfig.getSqfs().equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        sqfs = Integer.parseInt(p.getValue());
                    }
                    // 法律事务办理流程  是否需要用印
                } else if ("sfxyqz".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        sfxyqz = p.getValue();
                    }
                    // 打印模式 , 1 扫描盖章,2 仅盖
                    // 章,3 扫描识别盖章,4 批量盖章
                    // 默认打印盖章
                } else if ("useSealType".equals(p.getName())) {
                    //
                    if (StringUtils.hasText(p.getValue())) {
                        useSealType = p.getValue();
                    }
                    // 0 单面扫描,1 双面扫描
                } else if ("scanType".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        scanType = p.getValue();
                    }
                    // 盖章次数
                } else if ("useCount".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        useCount = Integer.parseInt(p.getValue());
                    }
                    // 非必填,0 否,1 是
                    // 默认 不加盖
                } else if ("ischeckMark".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        ischeckMark = p.getValue();
                    }
                    // 非必填 0 普通,1 打印
                    // 默认 普通
                } else if ("checkMarkType".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        checkMarkType = p.getValue();
                    }
                    // 骑缝章数量
                } else if ("checkMarkUseCount".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        checkMarkUseCount = p.getValue();
                    }
                }
                obj.put(p.getName(), p.getValue());
            }
            // String sfxyyy = wconfig.getSfxyyy();
            // if (!StringUtils.hasText(sfxyyy)) {
            //     requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            //     return Action.FAILURE_AND_CONTINUE;
            // }
            // if (!obj.get(sfxyyy).equals("1")) {
            //     info("执行 不盖章逻辑" + obj.get(sfxyyy));
            //     return Action.SUCCESS;
            // }
            // 实体章先不执行执行
            if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
                info("SignAction------------------->  实体章先不执行执行");
                return Action.SUCCESS;
            }


            // boolean sqbmFlag = false;
            // String sqbm = obj.get("sqbm");
            // if (StringUtils.hasText(sqbm) && sqbm.equals("344")) {
            //     sqbmFlag = true;
            // }
            // jbr = obj.get("jbr");
            String jbrCode = "";
            if (StringUtils.hasText(jbr)) {
                jbrCode = OaHrmUtils.getworkCode(Integer.valueOf(jbr));
            }

            String seal_signer;

            if (wconfig.isSigner()) {
                signerId = wconfig.getSigner();
                seal_signer = OaHrmUtils.getworkCode(wconfig.getSigner());
            } else {
                seal_signer = OaHrmUtils.getworkCode(signerId);
            }
            info("SignAction  -----------------------> 固定签署人: " + seal_signer);
            // 发送页面签署  盖章
            if (SealFromEnum.E_SEAL.getValue() == sealType) {

            } else if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                if (!StringUtils.hasText(jbr)) {
                    requestInfo.getRequestManager().setMessagecontent("经办人取值失败，请检查经办人是否选择或者联系管理员");
                    return Action.FAILURE_AND_CONTINUE;
                }
                if (!StringUtils.hasText(yzlx)) {
                    requestInfo.getRequestManager().setMessagecontent("印章未选择，请用印审批节点确认或联系管理员");
                    return Action.FAILURE_AND_CONTINUE;
                }

                info("SignAction  物理印章  -----------------------> 物理印章 签署，经办人存在 ");
                PrintSealUseTaskApiReq req = new PrintSealUseTaskApiReq();
                // String sqlId = obj.get(wconfig.getCreaterfieldname());
                // info("SignAction  物理印章  -----------------------> 物理印章 createrfieldname" + sqlId);
                // int i = Integer.valueOf(sqlId);
                // HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(i);
                // String username = allInfo.getUsername();
                // info("SignAction  物理印章  -----------------------> 拿到申请人登录名: " + username);
                // req.setApplyStaffLoginName(username);
                // String signerName = OaHrmUtils.getAllInfo(signerId).getUsername();
                //
                // info("SignAction  物理印章  -----------------------> 拿到固定签署人登录名: " + signerName);
                List<String> list = new ArrayList<>();
                // list.add(username);
                // String jbrName = OaHrmUtils.getAllInfo(Integer.parseInt(jbr)).getUsername();
                // if (!list.contains(jbrName)) {
                //     list.add(jbrName);
                // }
                String createrCode = OaHrmUtils.getworkCode(Integer.parseInt(jbr));
                BaseResp<String> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 4);
                if (!resp1.isStatus()) {
                    info("SignAction  物理印章  -----------------------> 经办人三方数据查询 失败: " + JSONUtil.toJsonStr(resp1));

                }
                req.setApplyStaffLoginName(resp1.getData());
                list.add(resp1.getData());
                info("SignAction  物理印章  -----------------------> 拿到申请人登录名list: " + list.toString());
                req.setSealUseStaffLoginNames(list);
                info("SignAction  物理印章  -----------------------> 拿到选择的印章id 数组 " + yzlx);
                // BaseResp<String> resp3 = ServiceUtil.getService(ThridSealMappingServiceImpl.class).getThridIdByOaId(null, yzlx);

                /**
                 * FileData 数据参数
                 * fileName 文件名 String 必填
                 * filePath 文件访问路径 String 必填
                 * totalCount 申请份数 Integer 必填
                 *
                 * fileName 文件名 String必填 (该印章所要盖章文件的 文件名称,与上述 fileData 的 fileName 字段数值保证一致)
                 * sealCode 印章编码 String 必填
                 * useCount 盖章次数 Integer 必填
                 */
                Map<String, String> file = new HashMap<>();

                try {
                    info("SignAction  物理印章  -----------------------> docId参数  " + docId);
                    file = FileUtils.oaFileInfo(docId);
                    // 这一块主要是 拿到签字后的文件在上传实体印章
                    // if (!flag) {
                    //     if ("1".equals(sfxyqz)) {
                    //         // 需要签字  拿到签字后文件并传给实体印章
                    //         String requestId = requestInfo.getRequestid();
                    //         String tableName = getFormName(requestId);
                    //         if (!StringUtils.hasText(tableName)) {
                    //             requestInfo.getRequestManager().setMessagecontent("SignAction：主表名称不存在");
                    //             return Action.FAILURE_AND_CONTINUE;
                    //         }
                    //         Map<String, String> tid = getTableDTid(Integer.parseInt(requestId), tableName, 2, docId, tag);
                    //         if (!StringUtils.hasText(tid.get("id"))) {
                    //             requestInfo.getRequestManager().setMessagecontent("SignAction：明细表不存在");
                    //             return Action.FAILURE_AND_CONTINUE;
                    //         }
                    //         if (StringUtils.hasText(tid.get("qzhfj"))) {
                    //             docId = tid.get("qzhfj");
                    //         }
                    //         // info("SignAction  物理印章  -----------------------> 签字后附件   requestId " + requestId + "  tableName" + tableName);
                    //         // Map<String, String> tableInfo = getTableInfo(tableName, requestId);
                    //         // String newId = tableInfo.get("qzhfj");
                    //         // info("SignAction  物理印章  -----------------------> 签字后附件 docId  :" + newId);
                    //     }
                    //     file = getfileName(Integer.parseInt(docId));
                    // }
                } catch (Exception e) {
                    requestInfo.getRequestManager().setMessagecontent("执行出错");
                    return Action.FAILURE_AND_CONTINUE;
                }
                info("SignAction  物理印章  -----------------------> file参数  ");
                List<FileDataVo> fileDataVoList = new ArrayList<>();
                FileDataVo fileDataVo = new FileDataVo();
                fileDataVo.setFileName(flag ? "测试合同.pdf" : file.get("filename"));
                fileDataVo.setTotalCount(sqfs);
                fileDataVo.setScanType(scanType);
                fileDataVo.setFilePath(DownFilePath(file.get("fileid"), flag));
                if (flag) { // 涉密项目
                    useSealType = "2";
                    // 保密文件
                    fileDataVo.setClassified("1");
                }
                fileDataVo.setScanType(scanType);
                fileDataVo.setUseSealType(useSealType);
                fileDataVoList.add(fileDataVo);
                req.setPushSealUseAioFileData(fileDataVoList);
                List<PrintSealUseTaskVo> l = new ArrayList<>();
                String[] split = yzlx.split(",");

                for (String s : split) {
                    String resp3 = getSealid(Integer.parseInt(s), new ThirdMappConfig());
                    info("SignAction  物理印章  -----------------------> 拿到选择的印章对应的外部编号 " + resp3);
                    if (!StringUtils.hasText(resp3)) {
                        info("SignAction  物理印章  -----------------------> 拿到选择的印章对应的外部编号 " + resp3);
                        requestInfo.getRequestManager().setMessagecontent("三方印章编号不存在");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                    // 印章外部编号
                    String data = resp3;
                    PrintSealUseTaskVo vo = new PrintSealUseTaskVo();
                    vo.setSealCode(data);
                    vo.setFileName(flag ? "测试合同.pdf" : file.get("filename"));
                    vo.setUseCount(useCount);
                    vo.setIsCheckMark(ischeckMark);
                    vo.setCheckMarkType(checkMarkType);
                    if (StringUtils.hasText(checkMarkUseCount)) {
                        vo.setCheckMarkUseCount(checkMarkUseCount);
                    }
                    l.add(vo);
                }

                req.setData(l);
                info("SignAction  物理印章  -----------------------> 创建盖章任务请求 请求参数:  " + req.toString());
                Long appId = Long.valueOf(new Now().toString());
                req.setApplyId(appId);
                req.setApplyReason("申请用印: " + lcbh);
                req.setApplyTitle("申请用印: " + lcbh);
                BaseResp<JSONObject> resp = ServiceUtil.getService(SealServiceImpl.class).createPrintSealUseTaskList(req);
                info("SignAction  物理印章  -----------------------> 创建盖章任务请求 返回结果:  " + resp.toString());
                if (!resp.isStatus()) {
                    requestInfo.getRequestManager().setMessagecontent(resp.getMessage());
                    return Action.FAILURE_AND_CONTINUE;
                }
                info("SignAction  物理印章  ----------------------->  插入对应数据" + appId + "  " + docId + "  " + requestInfo.getRequestid());
                insertRequest(String.valueOf(appId), docId, 5, requestInfo.getRequestid(), new ThirdMappConfig(), 0, wconfig.getDatasource(), configId);
                info("SignAction  物理印章  -----------------------> 创建盖章任务请求 执行完成  ");
            }
        }


        return Action.SUCCESS;
    }


    // public static void main(String[] args) {
    //     String s = new SignAction().DownFilePath("1894960", false);
    //     System.out.println("s = " + s);
    // }

    /**
     * 拿到文件下载地址
     *
     * @param fileId
     * @return
     */
    public static String DownFilePath(String fileId, boolean flag) {
        /***
         fileid  ： 流程中文件的id
         serverIp: 服务器ip地址、或者域名
         默认  http://**************:7001/weaver/weaver.file.FileDownload?fileid=1861346&download=1&ddcode=847e6c16412081e837547b58020d0bce
         ***/
        String downUrl = "%s/weaver/weaver.file.FileDownload?fileid=%s&download=1&ddcode=%s";
        // 使用泛微对应的该方法
        String ddcode = null;
        try {
            ddcode = new DesUtils().encrypt(1 + "_" + fileId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        https:
        // oa.cyitce.com/weaver/weaver.file.FileDownload?fileid=&download=1&ddcode=e5ab5f62287c1bb071362141037cfb4e
        // 是否保密办
        if (flag) {
            // return "http://**************:7001/weaver/weaver.file.FileDownload?fileid=1861347&download=1&ddcode=847e6c16412081e837547b58020d0bce";
            return "https://oa.cyitce.com/weaver/weaver.file.FileDownload?fileid=1861347&download=1&ddcode=847e6c16412081e837547b58020d0bce";
        }
        // String format = String.format(downUrl, "http://**************:7001", fileId, ddcode);
        String format = String.format(downUrl, "https://oa.cyitce.com", fileId, ddcode);

        new BaseBean().writeLog("下载地址:{?}", format);
        return format;
    }

    /**
     * 校验字符串是否符合规则：只能是以逗号分割的数字
     *
     * @param input 待校验的字符串
     * @return 符合规则返回true，否则返回false
     */
    public static boolean isValid(String input) {
        if (!StringUtils.hasText(input)) {
            return true;
        }
        return input.matches("^(0|[1-9]\\d*)(,(0|[1-9]\\d*))*$");
    }

    /**
     * 校验字符串是否全由0组成（符合数字逗号分隔规则）
     *
     * @param input 待校验的字符串
     * @return 全由0组成返回true，否则返回false
     */
    public static boolean isAllZero(String input) {
        if (!isValid(input)) {
            return false;
        }
        // 检查每个数字段是否为0
        String[] parts = input.split(",");
        for (String part : parts) {
            if (!"0".equals(part)) {
                return false;
            }
        }
        return true;
    }

    public static void main1(String[] args) {
        // // 有效输入测试
        // System.out.println("有效输入测试:");
        // System.out.println("1,2,3,4: " + isValid("1,2.5,3,4"));      // true
        SignAction action = new SignAction();
        RequestInfo info = new RequestInfo();
        Property[] var1 = new Property[100];

        // info.getMainTableInfo().setProperty();
        // action.execute()
    }


    /**
     * 这是一个示例 main 方法，用于演示如何为 execute 方法构建参数。
     * 注意：此代码依赖泛微OA环境的类，无法直接独立运行。
     * 它旨在展示如何构造 RequestInfo 对象。
     */
    public static void main(String[] args) {
        // 1. 创建 Action 实例并设置配置ID
        SignAction action = new SignAction();
        action.setConfigId("1"); // 这个configId对应你的WorkflowConfig配置

        // 2. 创建核心对象 RequestInfo
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setRequestid("99999"); // 设置流程请求ID

        // 3. 模拟当前操作用户
        User currentUser = new User();
        currentUser.setUid(3810);
        currentUser.setLoginid("ljh2947");
        currentUser.setLastname("管理员");

        // RequestManager requestManager = new RequestManager();
        // requestManager.setUser(currentUser);
        // requestManager.setBilltablename("formtable_main_123");
        // requestInfo.setRequestManager(requestManager);

        // 4. 填充主表字段数据 (MainTableInfo)
        // CORRECTED: Create Property objects using the default constructor and setters
        MainTableInfo mainTableInfo = new MainTableInfo();
        Property[] mainProperties = new Property[7];

        mainProperties[0] = new Property();
        mainProperties[0].setName("yzlxfs");
        mainProperties[0].setValue("1");

        mainProperties[1] = new Property();
        mainProperties[1].setName("szgs");
        mainProperties[1].setValue("6");

        mainProperties[2] = new Property();
        mainProperties[2].setName("lcbh");
        mainProperties[2].setValue("LC-2025-07-21-001");

        mainProperties[3] = new Property();
        mainProperties[3].setName("jbrbm");
        mainProperties[3].setValue("444");

        mainProperties[4] = new Property();
        mainProperties[4].setName("jbr");
        mainProperties[4].setValue("3810");

        mainProperties[5] = new Property();
        mainProperties[5].setName("sfxyyy");
        mainProperties[5].setValue("1");
        mainProperties[6] = new Property();
        mainProperties[6].setName("gdxs");
        mainProperties[6].setValue("1");

        mainTableInfo.setProperty(mainProperties);
        requestInfo.setMainTableInfo(mainTableInfo);

        // 5. 填充明细表数据 (DetailTableInfo)
        DetailTableInfo detailTableInfo = new DetailTableInfo();
        DetailTable[] detailTables = new DetailTable[1];
        DetailTable detailTable = new DetailTable();
        Row[] rows = new Row[2];

        // --- 明细表第1行 ---
        Row row1 = new Row();
        row1.setId("10");
        Cell[] cells1 = new Cell[7];

        cells1[0] = new Cell();
        cells1[0].setName("xynr");
        cells1[0].setValue("1894960");

        cells1[1] = new Cell();
        cells1[1].setName("yzkdx");
        cells1[1].setValue("25,26");

        cells1[2] = new Cell();
        cells1[2].setName("nqfzsl");
        cells1[2].setValue("2,1");

        cells1[3] = new Cell();
        cells1[3].setName("ngzcs");
        cells1[3].setValue("3,5");

        cells1[4] = new Cell();
        cells1[4].setName("sqfs");
        cells1[4].setValue("2");

        cells1[5] = new Cell();
        cells1[5].setName("dyms");
        cells1[5].setValue("1");

        cells1[6] = new Cell();
        cells1[6].setName("smms");
        cells1[6].setValue("0");

        row1.setCell(cells1);
        rows[0] = row1;

        // --- 明细表第2行 ---
        Row row2 = new Row();
        row2.setId("11");
        Cell[] cells2 = new Cell[7];

        cells2[0] = new Cell();
        cells2[0].setName("xynr");
        cells2[0].setValue("1894961");

        cells2[1] = new Cell();
        cells2[1].setName("yzkdx");
        cells2[1].setValue("30");

        cells2[2] = new Cell();
        cells2[2].setName("nqfzsl");
        cells2[2].setValue("1");

        cells2[3] = new Cell();
        cells2[3].setName("ngzcs");
        cells2[3].setValue("4");

        cells2[4] = new Cell();
        cells2[4].setName("sqfs");
        cells2[4].setValue("1");

        cells2[5] = new Cell();
        cells2[5].setName("dyms");
        cells2[5].setValue("2");

        cells2[6] = new Cell();
        cells2[6].setName("smms");
        cells2[6].setValue("1");

        row2.setCell(cells2);
        rows[1] = row2;

        detailTable.setRow(rows);
        detailTables[0] = detailTable;
        detailTableInfo.setDetailTable(detailTables);
        requestInfo.setDetailTableInfo(detailTableInfo);


        // 6. 执行方法并打印结果
        System.out.println("--- 准备执行 Action，模拟传入的 RequestInfo 对象如下 ---");
        System.out.println(JSONUtil.toJsonStr(requestInfo));
        System.out.println("\n--- 开始执行 execute 方法 ---\n");

        String result = action.execute(requestInfo);

        System.out.println("\n--- execute 方法执行完毕 ---");
        System.out.println("返回结果: " + result);
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }
}
