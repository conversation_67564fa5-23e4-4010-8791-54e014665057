package com.api.cyitce.seal2.action.create;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.util.data.OaHrmUtils;
import com.api.cyitce.seal2.vo.req.contract.CompanyInfo;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.req.hrm.JoinOrgReq;
import com.api.cyitce.seal2.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal2.vo.req.updatefile.Signer;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.*;

import static com.api.cyitce.seal2.action.create.checkAction.BMBM;
import static com.api.cyitce.seal2.enums.EnumExample.WorkflowStatus.getStatusById;
import static com.api.cyitce.seal2.util.FileUtils.getFormName;
import static com.api.cyitce.seal2.util.sql.SqlUtils.getqsrInfo;
import static com.api.cyitce.seal2.util.sql.SqlUtils.updateStatus;
import static com.api.cyitce.seal2.web.SealWeb.searchInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/12 16:32
 * @describe 校验签字人是否都已经签字或者拒签
 */
public class jbrCheckAction extends BaseLog implements Action {

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String configId = "1";
    private static Set<Integer> realAccountSet = new HashSet<>();

    /**
     * 保密部门 344 	jbrbm
     * 经办人 	jbr
     */

    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        info("拿到当前操作人--------------->id:" + user.getUID());
        // 是否需要用印
        int sealType = -1;
        int signerId = 0;
        boolean flag = false;
        String jbrbm = "-1";
        int companyId = -1;
        String docId = "";
        String qzr1 = "";

        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("checkAction------------------> configId:" + configId);
        info("checkAction------------------> wconfig:" + JSONUtil.toJsonStr(wconfig));
        String jbr = "";
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        for (Property p : properties) {
            info("Property打印 属性名称:" + p.getName() + "属性值:" + p.getValue());
            if (p.getName().equals(wconfig.getFsealType())) {
                info("jbrCheckAction-------------------> 主表 sealType: " + p.getValue());
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                }
            } else if (p.getName().equals(wconfig.getGszd())) {
                if (StringUtils.hasText(wconfig.getGszd())) {
                    companyId = Integer.parseInt(p.getValue());
                }
            } else if ("swszfb".equals(p.getName())) {
                companyId = Integer.parseInt(p.getValue());
            } else if ("szgs".equals(p.getName())) {
                companyId = Integer.parseInt(p.getValue());
            } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (!"0".equals(p.getValue())) {
                        info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (BMBM.equals(p.getValue())) {
                        flag = true;
                    }
                }
            } else if ("smdj".equals(p.getName())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                        flag = true;
                    }
                }
            }
            obj.put(p.getName(), p.getValue());
        }
        String sfxyyy = wconfig.getSfxyyy();
        if (!StringUtils.hasText(sfxyyy)) {
            requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!obj.get(sfxyyy).equals("1")) {
            info("执行 不盖章逻辑" + obj.get(sfxyyy));
            return Action.SUCCESS;
        }
        // 实体章先不执行执行
        if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            info("CreateContractAction------------------->  实体章先不执行执行");
            return Action.SUCCESS;
        }
        if (wconfig.isDetail()) {
            String sfxyqz = "0";
            String qzr = "";
            DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
            DetailTable dt = detailTables[wconfig.getDatasource() - 1];
            for (Row r : dt.getRow()) {
                String lwqzr = "";
                Cell[] cs = r.getCell();
                for (Cell c : cs) {
                    info("明细表" + (wconfig.getDatasource() - 1) + "  属性名称:" + c.getName() + "属性值:" + c.getValue());
                    if (c.getName().equals(wconfig.getFfile())) {
                        if (!flag) {
                            info("jbrCheckAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                        info("docId" + c.getValue());
                        docId = c.getValue();
                    } else if (wconfig.getFsigner().equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            info("docId1: " + c.getName());
                            signerId = Integer.parseInt(c.getValue());
                            info("docId1: " + signerId);
                        }
                    } else if ("shnryy".equals(c.getName())) {
                        if (!flag) {
                            info("jbrCheckAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                        if (StringUtils.hasText(c.getValue())) {
                            info("docId" + c.getValue());
                            docId = c.getValue();
                        }
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    } else if ("lwqzr".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            lwqzr = c.getValue();
                        }
                    } else if ("qzr".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            qzr = c.getValue();
                        }
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    } else if ("qzr1".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            qzr1 = c.getValue();
                        }
                    }
                }
                // boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                // info("jbrAction------------> ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
                if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                    info("jbrCheckAction  ----------------------->  签字 实体章 不需要");
                    return Action.SUCCESS;
                }

                if ("1".equals(obj.get("sfxyqz")) && "7".equals(configId) && "1".equals(obj.get("htxs"))) {
                    info("jbrCheckAction  ----------------------->  支出合同-电子模板 主表拿到签字人");
                    sfxyqz = obj.get("sfxyqz");
                    qzr = obj.get("qzr1");

                }
                if ("7".equals(configId) && "0".equals(obj.get("htxs"))) {
                    info("jbrCheckAction  ----------------------->  支出合同-自拟格式 明细表拿到签字人");
                    qzr = qzr1;

                }
                // 发送页面签署  盖章
                if (SealFromEnum.E_SEAL.getValue() == sealType) {
                    // 校验当前是否需要签字
                    if ("1".equals(sfxyqz)) {
                        // 签字
                        // 判断操作人是否已经在天威云上面实名认证了
                        // 查看当前操作人是否需要签字
                        int userId = Integer.parseInt(qzr);
                        info("jbrCheckAction  ----------------------->  签字 签字人: " + qzr);
                        String userCode = OaHrmUtils.getworkCode(userId);
                        HrmResourceApiReq info = OaHrmUtils.getAllInfo(userId);
                        Map<String, String> map = getqsrInfo(0, Base64.encode(docId), new ThirdMappConfig());
                        info("jbrCheckAction ---------------------------> getqsrInfo:" + map.toString());
                        if (map.isEmpty()) {
                            info("jbrCheckAction ---------------------------> map.isEmpty() :" + map.toString());
                            requestInfo.getRequestManager().setMessagecontent("系统错误，请联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        ContractApiVO vo = searchInfo(Long.valueOf(map.get("sealid")));
                        if (vo == null) {
                            requestInfo.getRequestManager().setMessagecontent("查询合同失败，系统错误，请联系管理员");
                            info("jbrCheckAction ---------------------------> 查询到的合同签署人 :" + JSONUtil.toJsonStr(vo.getSigners()));
                            return Action.FAILURE_AND_CONTINUE;
                        }

                        // 开始校验
                        // 判断这个人 如果没签署 就不提交，只要操作了就可以提交
                        Integer status = vo.getStatus();
                        info("jbrCheckAction ---------------------------> 当前合同状态: " + status);

                        BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                        List<Signer> signers = vo.getSigners();
                        info("jbrCheckAction ---------------------------> 当前合同状态签署人: " + JSONUtil.toJsonStr(signers));
                        boolean sign = false;
                        String status1 = getStatusById(status).getStatus();
                        if (CollectionUtils.isEmpty(signers)) {
                            requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + ": 请先完成电子签名再点击提交");
                            return Action.FAILURE_AND_CONTINUE;
                        }

                        if (status == 2 || status == 1) {
                            for (Signer signer : signers) {
                                // if (signer.getUserId().equals(resp4.getData()) && signer.getSignStatus()==2) {
                                //     info("jbrCheckAction ---------------------------> 存在签署 :" + signer.getUserId() + ": userCode : " + userCode);
                                //     sign = true;
                                // }
                                info("jbrCheckAction ---------------------------> 当前合同状态签署人 :" + JSONUtil.toJsonStr(signer));
                                if (signer.getUserId().equals(resp4.getData())) {
                                    if (signer.getSignStatus() != 3) {
                                        requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + ":请先完成电子签名再点击提交");
                                        return Action.FAILURE_AND_CONTINUE;
                                    }
                                }
                            }

                            // info("jbrCheckAction ---------------------------> sign : " + sign);
                            // if (sign) {
                            //     requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + ":  尚未签署合同。");
                            //     return Action.FAILURE_AND_CONTINUE;
                            // }
                        } else {
                            // requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + ":  尚未签署合同。");
                            requestInfo.getRequestManager().setMessagecontent("拒签提示：\n" + "如您已拒签该文件，请退回至申请节点重新发起。\n" + "如您正常签署文件，无法提交请联系管理员。");
                            return Action.FAILURE_AND_CONTINUE;
                        }

                        String tableName = getFormName(map.get("requestid1"));
                        updateStatus(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), status1);
                        // // 如果拒签就不提交 就执行一下代码
                        // requestInfo.getRequestManager().setMessagecontent(info.getUsername() + " 签署的合同状态为： " + status1);
                        // return Action.FAILURE_AND_CONTINUE;

                    }


                    // info("jbrAction  ----------------------->  经办人 实名开始");
                    // BaseResp<JSONObject> resp = new UserRealAuthAction().realCreaterAccount(Integer.valueOf(jbr), companyId);
                    // if (!resp.isStatus()) {
                    //     requestInfo.getRequestManager().setMessagecontent("经办人实名认证失败");
                    //     return Action.FAILURE_AND_CONTINUE;
                    // }
                    // 判断当前这个是否已经签了字
                    // 发送页面签署

                    // info("jbrAction  ----------------------->  电子印章  添加 签署人数量，签署人");
                    // int i = changeThirdNum(0, Base64.encode(docId), Integer.parseInt(jbr), new ThirdMappConfig());
                    // if (i == 1) {
                    //     requestInfo.getRequestManager().setMessagecontent("三方表修改失败");
                    //     return Action.FAILURE_AND_CONTINUE;
                    // }
                    info("jbrAction  ----------------------->  电子印章  添加 签署人数量，签署人成功");
                }


                // if (b && StringUtils.hasText(lwqzr)) {
                //     info("jbrAction  ----------------------->  实体印章 另外签字人 实名开始 ");
                //     BaseResp<JSONObject> resp = new UserRealAuthAction().realCreaterAccount(Integer.valueOf(lwqzr), companyId);
                //     if (!resp.isStatus()) {
                //         requestInfo.getRequestManager().setMessagecontent("实体印章 另外签字人实名认证失败");
                //         return Action.FAILURE_AND_CONTINUE;
                //     }
                //     // 发送页面签署
                //     Response response = new SealWeb().sendToSms(Integer.valueOf(lwqzr), docId, String.valueOf(companyId), null, null);
                //     if (response.getStatus() != 200) {
                //         requestInfo.getRequestManager().setMessagecontent("实体印章 另外签字人 发送页面签署失败");
                //         return Action.FAILURE_AND_CONTINUE;
                //     }
                //     // info("jbrAction  ----------------------->  实体印章 电子签字  添加 签署人数量，签署人：" + Base64.encode(docId));
                //     // int i = changeThirdNum(0, Base64.encode(docId), Integer.parseInt(lwqzr), new ThirdMappConfig());
                //     // if (i == 1) {
                //     //     requestInfo.getRequestManager().setMessagecontent("三方表修改失败");
                //     //     return Action.FAILURE_AND_CONTINUE;
                //     // }
                //     info("jbrAction  ----------------------->  实体印章 电子签字  添加 签署人数量，签署人成功：" + Base64.encode(docId));
                // }
            }
        }
        return Action.SUCCESS;
    }


    private BaseResp<JSONObject> realCreaterAccount(Integer userId, int companyId) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();
        CreateOrgIntegration orgIntegration = new CreateOrgIntegration();


        if (!realAccountSet.contains(userId)) {
            String createrCode = OaHrmUtils.getworkCode(userId);

            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
            HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(userId);
            String subcompanyid1 = allInfo.getSubcompanyid1();
            info("拿到当前人的subCompany: " + subcompanyid1);
            BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(subcompanyid1, 3);

            if (resp2.isStatus()) {
                // if (resp3.isStatus()) {
                //     info("企业详情查询1 : ");
                //     BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));
                //     if (!baseResp.isStatus()) {
                //         return new BaseResp<JSONObject>(false);
                //     }
                //     info("企业详情查询 结果1 : " + JSONUtil.toJsonStr(baseResp));
                //     RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
                //     JoinOrgReq joinOrgReq = new JoinOrgReq();
                //     joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
                //     joinOrgReq.setInviteUserId(resp2.getData());
                //     joinOrgReq.setEnterpriseId(resp3.getData());
                //     info("邀请用户 进入企业11 : " + JSONUtil.toJsonStr(joinOrgReq));
                //     integration.inviteUser(joinOrgReq);
                //     return MsgResp.ok();
                // }
                return MsgResp.ok();
            }
            RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
            CreateUserServiceImpl cusi = new CreateUserServiceImpl();
            resp = cusi.createUser(realUser.getName(), realUser.getIdCard(), realUser.getMobile(), createrCode);
            JoinOrgReq joinOrgReq = new JoinOrgReq();

            BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));

            info("企业详情查询 结果 : " + JSONUtil.toJsonStr(baseResp));
            joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
            joinOrgReq.setInviteUserId((String) resp.getData().get("userId"));
            joinOrgReq.setEnterpriseId(resp3.getData());
            info("邀请用户 进入企业 : " + JSONUtil.toJsonStr(joinOrgReq));
            integration.inviteUser(joinOrgReq);

            if (resp.isStatus()) {
                BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping((String) resp.getData().get("userId"), createrCode, 2);
                info("resp1 : " + resp1.toString());
                if (!resp1.isStatus()) {
                    info("第三方系统数据插入 : 失败");
                    return MsgResp.error("第三方系统数据插入失败");
                }
                info("第三方系统数据插入 : 成功");
                realAccountSet.add(userId);
            }
        }

        return resp;
    }


}