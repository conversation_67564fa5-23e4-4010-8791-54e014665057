package com.api.cyitce.seal2.action.qfz;

import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.util.sql.SqlUtils;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.api.cyitce.seal2.util.sql.SqlUtils.yzInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/18 10:29
 * @describe 取放章 执行后 修改对应信息
 */
public class FinishQfzAction extends BaseLog implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String yzxz = "";
        String qfzlx = "0";
        String sqr = "0";
        String lcbh = "默认-";
        String dyrid = "";
        String uuid = "";
        for (Property property : properties) {
            info("FinishQfzAction-------------->属性名称：" + property.getName() + ": " + property.getValue());
            if ("yz".equals(property.getName())) {
                yzxz = property.getValue();
            }
            if ("qfzlx".equals(property.getName())) {
                qfzlx = property.getValue();
            }
            if ("sqr".equals(property.getName())) {
                sqr = property.getValue();
            }
            if ("lcbh".equals(property.getName())) {
                lcbh = property.getValue();
            }
            if ("dyrid".equals(property.getName())) {
                dyrid = property.getValue();
            }
            if ("uuid".equals(property.getName())) {
                uuid = property.getValue();
            }
        }
        if (!StringUtils.hasText(yzxz)) {
            requestInfo.getRequestManager().setMessagecontent("印章选择为空，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!StringUtils.hasText(qfzlx)) {
            requestInfo.getRequestManager().setMessagecontent("取放章未选择，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!StringUtils.hasText(sqr)) {
            requestInfo.getRequestManager().setMessagecontent("申请人为空，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        // if (!StringUtils.hasText(dyrid)) {
        //     requestInfo.getRequestManager().setMessagecontent("对应的投标流程查询失败，请联系管理员");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        if (!StringUtils.hasText(uuid)) {
            requestInfo.getRequestManager().setMessagecontent("对应的投标流程查询失败，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        // 修改对应的yz zkzt
        String[] split = yzxz.split(",");
        // 将数组转换为 ArrayList
        List<String> yzlxList = new ArrayList<>(Arrays.asList(split));
        // 判断印章是否在库
        for (String s : yzlxList) {
            Map<String, String> map = yzInfo(s, new ThirdMappConfig());
            if (map.isEmpty()) {
                requestInfo.getRequestManager().setMessagecontent("未查询到对应的印章，请联系管理员");
                return Action.FAILURE_AND_CONTINUE;
            }
        }
        // 判断取放章
        if (qfzlx.equals("0")) {
            info("FinishQfzAction-------------->执行取章");
            // 修改对应的印章状态
            for (String s : yzlxList) {
                int i = SqlUtils.updateZKZT(s, "1", new ThirdMappConfig());
                if (i == 1) {
                    requestInfo.getRequestManager().setMessagecontent("修改对应的印章状态错误，请联系管理员");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
            // 修改对应的流程
            int i = SqlUtils.updateZTB(uuid, "1", new ThirdMappConfig());
            if (i == 1) {
                requestInfo.getRequestManager().setMessagecontent("修改对应的流程错误，请联系管理员");
                return Action.FAILURE_AND_CONTINUE;
            }
        } else if (qfzlx.equals("1")) {
            info("FinishQfzAction-------------->执行放章");
            // 修改对应的印章状态
            for (String s : yzlxList) {
                int i = SqlUtils.updateZKZT(s, "0", new ThirdMappConfig());
                if (i == 1) {
                    requestInfo.getRequestManager().setMessagecontent("修改对应的印章状态错误，请联系管理员");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
            // 修改对应的流程
            int i = SqlUtils.updateZTB(uuid, "1", new ThirdMappConfig());
            if (i == 1) {
                requestInfo.getRequestManager().setMessagecontent("修改对应的流程错误，请联系管理员");
                return Action.FAILURE_AND_CONTINUE;
            }
        } else {
            requestInfo.getRequestManager().setMessagecontent("取放章类型为空，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;

    }


}
