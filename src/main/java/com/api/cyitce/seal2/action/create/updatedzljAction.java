package com.api.cyitce.seal2.action.create;


import cn.hutool.core.codec.Base64;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.config.eSeal.ESealConfig;
import com.api.cyitce.seal2.enums.DocumentInfo;
import com.api.cyitce.seal2.enums.EnumExample;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;
import com.api.cyitce.seal2.vo.resp.template.DocumentApiVO;
import com.engine.common.util.ServiceUtil;
import com.weaver.general.BaseBean;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.api.cyitce.seal2.action.contract.ContaractAction.getInfo;
import static com.api.cyitce.seal2.action.create.SignAction.DownFilePath;
import static com.api.cyitce.seal2.action.create.checkAction.BMBM;
import static com.api.cyitce.seal2.enums.EnumExample.WorkflowStatus.getStatusById;
import static com.api.cyitce.seal2.util.FileUtils.getFormName;
import static com.api.cyitce.seal2.util.FileUtils.uploadToOA;
import static com.api.cyitce.seal2.util.sql.SqlUtils.*;
import static com.api.cyitce.seal2.web.SealWeb.searchInfo;

/**
 * @ClassName: 统一印控中心 结束签署
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  10:21
 * @Version: 1.0
 */
public class updatedzljAction extends BaseLog implements Action {
    public String configId = "1";

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String tag = "xynr";
        String sfxyqz = "1";
        int sealType = -1;
        String docId = "";
        String title = "";
        String jbrbm = "-1";
        boolean flag = false;
        info("updatedzljAction------------------------> 进行合同结束Action");
        String requestId = requestInfo.getRequestid();
        CreateContractServiceImp ccsi = new CreateContractServiceImp();
        ESealConfig config = new ESealConfig();
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();
        ContractIntegration ci = new ContractIntegration();
        info("updatedzljAction------------------------> 开始执行");
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            for (Property p : properties) {
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("updatedzljAction-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }


            // 是否需要用印
            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }

            // 实体章先不执行执行
            if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
                info("FinishContractAction------------------->  实体章先不执行执行");
                return Action.SUCCESS;
            }


            // jbrbm = obj.get("jbrbm");
            // String s = obj.get("isModel");
            // if (StringUtils.hasText(jbrbm) && jbrbm.equals("344")) {
            //     flag = true;
            // }

            if (wconfig.isDetail()) {
                DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt = detailTables[wconfig.getDatasource() - 1];
                for (Row r : dt.getRow()) {
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        if (c.getName().equals(wconfig.getFfile())) {
                            if (!flag) {
                                info("updatedzljAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length > 1) {
                                    requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            }
                            docId = c.getValue();
                        } else if (c.getName().equals(wconfig.getFtitle())) {
                            if (StringUtils.hasText(c.getValue())) {
                                title = c.getValue();
                            }
                        } else if ("sfxyqz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sfxyqz = c.getValue();
                            }
                        } else if ("shnryy".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                docId = c.getValue();
                                tag = "shnryy";
                            }
                            // 是否需要签字
                        }
                    }
                    boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                    info("updatedzljAction  ----------------------->  ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
                    // 电子合同 上传合同附件
                    if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
                        // 结束合同签署。
                        try {
                            BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                            if (resp.isStatus()) {
                                String newDocId = "";
                                Map<String, String> map = getDocIdInfoByDocId(0, Base64.encode(docId), new ThirdMappConfig());
                                String tableName = getFormName(requestId);
                                if (SealFromEnum.E_SEAL.getValue() == sealType) {
                                    ContractApiVO vo = searchInfo(Long.valueOf(map.get("sealid")));

                                    info("updatedzljAction ---------------------------> 拿到合同详情");
                                    if (vo.getStatus() == EnumExample.WorkflowStatus.COMPLETED.getId()) {
                                        List<DocumentApiVO> documents = vo.getDocuments();
                                        if (!CollectionUtils.isEmpty(documents)) {
                                            info("updatedzljAction ---------------------------> 合同文档:");
                                            DocumentApiVO documentApiVO = documents.get(0);
                                            String docBase64 = documentApiVO.getDocBase64();
                                            info("updatedzljAction ---------------------------> 上传文件:");
                                            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestId, DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
                                            info("updatedzljAction ---------------------------> 上传文件 返回的文档id:" + newDocId);
                                        }
                                        info("updatedzljAction  -----------------------> 更新电子合同dzlj信息 ");
                                        Map<String, String> file = getfileName(Integer.parseInt(newDocId));
                                        updatedzlj(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), DownFilePath(file.get("fileid"), false));
                                    }
                                    EnumExample.WorkflowStatus statusById = getStatusById(vo.getStatus());
                                    String status = "未知";
                                    if (statusById != null) {
                                        status = statusById.getStatus();
                                    }
                                    updateStatus(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), status);
                                }
                            }

                        } catch (Exception e) {
                            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                            return Action.FAILURE_AND_CONTINUE;
                        }
                    }
                }
            }
        } else {
            info("updatedzljAction  ----------------------->进入主表");
            Map<String, String> map = getInfo(requestInfo.getRequestManager().getBillTableName(), requestInfo.getRequestid(), wconfig.getFfile());
            docId = map.get(wconfig.getFfile());
            if (!StringUtils.hasText(docId)) {
                requestInfo.getRequestManager().setMessagecontent("未查询到生成的合同文档");
                return Action.FAILURE_AND_CONTINUE;
            }
            for (Property p : properties) {
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("updatedzljAction-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if (configId.equals("7")) {
                            // 支出合同 0 纸质 1 电子
                            if ("0".equals(p.getValue())) {
                                sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                            } else if ("1".equals(p.getValue())) {
                                sealType = SealFromEnum.E_SEAL.getValue();
                            }
                        } else if (configId.equals("8")) {
                            // 采购结算单 1 纸质  0 电子
                            if ("0".equals(p.getValue())) {
                                sealType = SealFromEnum.E_SEAL.getValue();
                            } else if ("1".equals(p.getValue())) {
                                sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                            }
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }


            // // 是否需要用印
            // String sfxyyy = wconfig.getSfxyyy();
            // if (!StringUtils.hasText(sfxyyy)) {
            //     requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            //     return Action.FAILURE_AND_CONTINUE;
            // }
            // if (!obj.get(sfxyyy).equals("1")) {
            //     info("执行 不盖章逻辑" + obj.get(sfxyyy));
            //     return Action.SUCCESS;
            // }

            // 实体章先不执行执行
            if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
                info("FinishContractAction------------------->  实体章先不执行执行");
                return Action.SUCCESS;
            }

            // boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
            // info("updatedzljAction  ----------------------->  ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
            // 电子合同 上传合同附件
            // if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
            if (SealFromEnum.E_SEAL.getValue() == sealType) {
                // 结束合同签署。
                try {
                    BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                    if (resp.isStatus()) {
                        String newDocId = "";
                        Map<String, String> map1 = getDocIdInfoByDocId(0, Base64.encode(docId), new ThirdMappConfig());
                        String tableName = getFormName(requestId);
                        if (SealFromEnum.E_SEAL.getValue() == sealType) {
                            ContractApiVO vo = searchInfo(Long.valueOf(map1.get("sealid")));

                            info("updatedzljAction ---------------------------> 拿到合同详情");
                            if (vo.getStatus() == EnumExample.WorkflowStatus.COMPLETED.getId()) {
                                List<DocumentApiVO> documents = vo.getDocuments();
                                if (!CollectionUtils.isEmpty(documents)) {
                                    info("updatedzljAction ---------------------------> 合同文档:");
                                    DocumentApiVO documentApiVO = documents.get(0);
                                    String docBase64 = documentApiVO.getDocBase64();
                                    info("updatedzljAction ---------------------------> 上传文件:");
                                    newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestId, DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
                                    info("updatedzljAction ---------------------------> 上传文件 返回的文档id:" + newDocId);
                                }
                                info("updatedzljAction  -----------------------> 更新电子合同dzlj信息 ");
                                Map<String, String> file = getfileName(Integer.parseInt(newDocId));
                                updateMaindzlj(Integer.parseInt(requestId), tableName, DownFilePath(file.get("fileid"), false));
                                // updatedzlj(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), DownFilePath(file.get("fileid"), false));
                            }
                            EnumExample.WorkflowStatus statusById = getStatusById(vo.getStatus());
                            String status = "未知";
                            if (statusById != null) {
                                status = statusById.getStatus();
                            }
                            // updateStatus(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), status);
                            updateMainStatus(requestId, tableName, status);
                        }
                    }

                } catch (Exception e) {
                    requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                    return Action.FAILURE_AND_CONTINUE;
                }

            }
        }

        return Action.SUCCESS;
    }


    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }
}