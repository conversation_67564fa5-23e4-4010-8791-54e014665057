package com.api.cyitce.seal2.action.create;

import com.api.cyitce.seal2.config.BaseLog;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/8 10:51
 * @describe
 */
public class DefaultAction extends BaseLog implements Action {
    public String getConfigId() {
        return configId;
    }
    public void setConfigId(String configId) {
        this.configId = configId;
    }
    public String configId = "1";

    @Override
    public String execute(RequestInfo requestInfo) {
        return null;
    }




}
