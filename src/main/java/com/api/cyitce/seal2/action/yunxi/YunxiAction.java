package com.api.cyitce.seal2.action.yunxi;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.action.create.SignAction;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.config.YunxiConfig;
import com.api.cyitce.seal2.config.eSeal.ESealConfig;
import com.api.cyitce.seal2.config.yunxi.ApiException;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.enums.yunxi.YxAppointSealUserType;
import com.api.cyitce.seal2.enums.yunxi.YxQrCodePosition;
import com.api.cyitce.seal2.enums.yunxi.YxUnlockType;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.service.yunxi.YxygApiService;
import com.api.cyitce.seal2.util.data.Now;
import com.api.cyitce.seal2.util.data.OaHrmUtils;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.req.yunxi.*;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.*;

import static com.api.cyitce.seal2.action.create.checkAction.BMBM;
import static com.api.cyitce.seal2.util.sql.SqlUtils.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/23 15:20
 * @describe 外带桶
 * http://183.230.9.103:18585/h5/#/pages/tabBar/index?userId=51F4273B50ACEA8B46481DD0BFA75BDD&_tenant=cqxkgc
 */
public class YunxiAction extends BaseLog implements Action {
    public String configId = "1";


    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        info("YunxiAction拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        int currentUserId = user.getUID();
        info("YunxiAction拿到当前操作人--------------->id:" + currentUserId);
        int sealType = -1;
        String docId = "";
        int signerId = 0;
        String yzlx = "";
        int sqfs = 1; // 合同份数
        int useCount = 1; // 用印次数
        int qfzCount = 0; // 骑缝章用印次数
        int companyId = -1;
        String yysp = "";

        int fileUnlockType = 1; // 文件类型 解锁方式 普通解锁
        int unlockThreshold = 0; // 解锁阈值
        int qrCodePosition = 2; // 赋码位置
        int appointSealUserEnabled = 0; // 是否专人盖章
        int appointSealUserType = 0; // 专人盖章类型
        int beforeRatioThreshold = 0; // 事前比对阈值
        int usingRatioThreshold = 0; // 事中比对阈值
        int afterRatioThreshold = 0; // 事后比对阈值
        String yxid = "-1";
        List<String> appointSealUserIds = new ArrayList(); // 指定用户id

        String yxUuid = "-1"; // 云玺外带桶uuid
        String lcbh = "default-";
        boolean flag = false;
        info("YunxiAction  -----------------------> configId" + configId);
        ESealConfig config = new ESealConfig();

        String syncUserId = "-1";

        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();

        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        String jbrbm = "-1";
        String jbr = "";
        // String uesSealType = "1";
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            for (Property p : properties) {
                info("YunxiAction  -----------------------> 主表参数  " + p.getName() + " : " + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("YunxiAction-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        } else if ("2".equals(p.getValue())) {
                            sealType = SealFromEnum.OUT_SEAL.getValue();
                        }
                    }
                } else if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if ("swszfb".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("szgs".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("lcbh".equals(p.getName())) {
                    // 流程编号
                    lcbh = p.getValue();
                    // } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    //     if (StringUtils.hasText(p.getValue())) {
                    //         if (!"0".equals(p.getValue())) {
                    //             info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                    //             return Action.SUCCESS;
                    //         }
                    //     }
                    // } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    //     if (StringUtils.hasText(p.getValue())) {
                    //         if ("0".equals(p.getValue())) {
                    //             info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                    //             return Action.SUCCESS;
                    //         }
                    //     }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印审批  非部门   不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                        yysp = p.getValue();
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbr()) && p.getName().equals(wconfig.getJbr())) {
                    if (StringUtils.hasText(p.getValue())) {
                        jbr = p.getValue();
                    }
                } else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }
            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }

            if ("-1".equals(companyId)) {
                requestInfo.getRequestManager().setMessagecontent("业务办理所属公司选择出错,请联系管理员!");
                return Action.FAILURE_AND_CONTINUE;
            }
            lcbh = obj.get(wconfig.getLcbh());
            if (wconfig.isDetail()) {
                DetailTable[] detailTables1 = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt1 = detailTables1[Integer.parseInt(wconfig.getWdmx()) - 1];
                info("YunxiAction-------------------> 拿到当前明细表: " + (Integer.parseInt(wconfig.getWdmx()) - 1));
                for (Row r : dt1.getRow()) {
                    String startTime = "";
                    String endTime = "";
                    String wdfj = "";
                    String detailId = r.getId();
                    String typeCode = String.valueOf(new Date().getTime());
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        info("YunxiAction-------------------> 明细表 " + c.getName() + " : " + c.getValue());
                        if (("yztxz").equals(c.getName())) {
                            info("yztxz: " + jbrbm + ": c.value:" + c.getValue());
                            if (StringUtils.hasText(c.getValue())) {
                                yxid = c.getValue();
                            }
                        } else if (("contractCopiesNum").equals(c.getName())) {
                            info("contractCopiesNum: " + jbrbm + ": c.value:" + c.getValue());
                            if (StringUtils.hasText(c.getValue())) {
                                sqfs = Integer.parseInt(c.getValue());
                            }
                        } else if (("mfyycs").equals(c.getName())) {
                            info("mfyycs: " + jbrbm + ": c.value:" + c.getValue());
                            if (StringUtils.hasText(c.getValue())) {
                                useCount = Integer.parseInt(c.getValue());
                            }
                        } else if (("qfzCount").equals(c.getName())) {
                            info("qfzCount: " + jbrbm + ": c.value:" + c.getValue());
                            if (StringUtils.hasText(c.getValue())) {
                                qfzCount = Integer.parseInt(c.getValue());
                            }
                        } else if (wconfig.getWdtfj().equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                wdfj = c.getValue();
                            }
                        }
                    }
                    if ("-1".equals(yxid) && !StringUtils.hasText(yxid)) {
                        info("YunxiAction  ----------------------->  未选择云玺id，不发送印章桶");
                        continue;

                    }
                    Map<String, String> map = yztInfo(yxid);
                    if (CollectionUtils.isEmpty(map)) {
                        requestInfo.getRequestManager().setMessagecontent("未查询到印章桶信息，请联系管理员");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                    yxUuid = map.get("Uuid");
                    syncUserId = jbr;
                    if (sealType == SealFromEnum.OUT_SEAL.getValue() || "0".equals(yysp)) {
                        try {
                            HrmResourceApiReq req = OaHrmUtils.getAllInfo(Integer.parseInt(syncUserId));
                            YunxiConfig yunxiConfig = new YunxiConfig();
                            info("YunxiAction  -----------------------> 拿到云玺对应配置 ： " + yunxiConfig.toString());
                            YxygApiService service = new YxygApiService(yunxiConfig.getBaseUrl(), yunxiConfig.getAppKey(), yunxiConfig.getTenant(), yunxiConfig.getAppSecret(), true);
                            String topId = yunxiConfig.getTopId();
                            info("YunxiAction  ----------------------->开始同步组织, id:  " + companyId + " topid :" + topId);
                            Map<String, String> companyInfo = getCompanyInfo(companyId);
                            Map<String, String> companyInfo1 = getCompanyInfo(Integer.parseInt(req.getSubcompanyid1()));
                            List<YxOrganization> organizations = Arrays.asList(
                                    new YxOrganization(companyInfo.get("id"), companyInfo.get("subcompanyname"), topId, 0),
                                    new YxOrganization(companyInfo1.get("id"), companyInfo1.get("subcompanyname"), topId, 0)
                            );
                            service.syncOrganizationsIncremental(organizations);
                            info("YunxiAction  ----------------------->同步组织完成,开始同步用户");
                            info("YunxiAction  ----------------------->同步用户信息： " + JSONUtil.toJsonStr(req));
                            service.syncYxUsersIncremental(Arrays.asList(
                                    new YxUser(req.getId(), req.getStaffName(), req.getSubcompanyid1(), req.getUsername(), req.getCellphone(), 0)
                            ));
                            info("YunxiAction  ----------------------->同步用户信息完成,创建申请文件类型 ");
                            String requestid = requestInfo.getRequestid();
                            YxApplicationFileType fileType = new YxApplicationFileType(1, String.valueOf(companyId), typeCode, lcbh + ":" + requestid, true,
                                    true, YxUnlockType.fromId(fileUnlockType).getCode(), unlockThreshold, YxQrCodePosition.fromId(qrCodePosition).getCode(), appointSealUserEnabled == 1 ? true : false,
                                    YxAppointSealUserType.fromId(appointSealUserType).getCode(), appointSealUserIds, beforeRatioThreshold, usingRatioThreshold, afterRatioThreshold);
                            info("YunxiAction  ----------------------->创建申请文件类型 请求值:  " + fileType.toString());
                            service.manageYxApplicationFileType(fileType);
                            info("YunxiAction  ----------------------->创建申请文件类型成功,开始推送申请单");
                            Long id = Long.valueOf(new Now().toString());
                            // List<YxApplicationFileBase64> files = new ArrayList<>();
                            // YxApplicationFileBase64 yxApplicationFileBase64 = new YxApplicationFileBase64();
                            // files.add(yxApplicationFileBase64);
                            // YxApplicationBase64 yxApplicationBase64 = new YxApplicationBase64(String.valueOf(id), lcbh, lcbh, yxUuid, sqfs, useCount, 0, requestid, syncUserId, syncUserId, files);
                            YxApplication yxApplication = new YxApplication(String.valueOf(id), lcbh + ":" + requestid, lcbh + ":" + requestid, yxUuid, sqfs, useCount, qfzCount, typeCode, syncUserId, syncUserId);

                            List<YxApplicationEleFence> listFence = new ArrayList<>();
                            // YxApplicationEleFence fence = new YxApplicationEleFence();
                            // if (StringUtils.hasText(startTime) || StringUtils.hasText(endTime)) {
                            //     if (StringUtils.hasText(startTime)) {
                            //         fence.setStartTime(startTime);
                            //     }
                            //     if (StringUtils.hasText(endTime)) {
                            //         fence.setEndTime(endTime);
                            //     }
                            //     listFence.add(fence);
                            // }
                            List<YxApplicationFile> listFile = new ArrayList<>();
                            info("YunxiAction  ----------------------->遍历附件： " + wdfj);
                            if (StringUtils.hasText(wdfj)) {
                                List<String> list = Arrays.asList(wdfj.split(","));

                                for (String s : list) {
                                    YxApplicationFile application = new YxApplicationFile();
                                    Map<String, String> fileMap = getfileName(Integer.parseInt(s));
                                    if (CollectionUtils.isEmpty(fileMap)) {
                                        continue;
                                    }
                                    info("YunxiAction  ----------------------->附件名: " + fileMap.get("filename") + " id:： " + fileMap.get("fileid"));
                                    application.setFileUrl(SignAction.DownFilePath(fileMap.get("fileid"), false));
                                    application.setFileName(fileMap.get("filename"));
                                    listFile.add(application);
                                }
                            }
                            if (!CollectionUtils.isEmpty(listFile)) {
                                yxApplication.setFiles(listFile);
                            }
                            if (!CollectionUtils.isEmpty(listFence)) {
                                yxApplication.setApplicationEleFences(listFence);
                            }
                            info("YunxiAction  ----------------------->推送申请单参数: " + JSONUtil.toJsonStr(yxApplication));
                            // service.pushYxApplication(yxApplication);
                            service.createYxApplication(yxApplication);
                            // service.createYxApplicationWithBase64(yxApplicationBase64);
                            BaseResp<Void> resp3 = insertThirdCodeMapping(String.valueOf(id), requestid, 9, new ThirdMappConfig(), typeCode, detailId, requestid, configId, wconfig.getWdmx());
                        } catch (ApiException e) {
                            requestInfo.getRequestManager().setMessagecontent("请求出错: [" + e.getMessage() + "+ ],请联系管理员!");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                    }
                }
            }
        }
        return Action.SUCCESS;
    }


    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }


}
