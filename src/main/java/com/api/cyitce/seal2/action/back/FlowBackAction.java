package com.api.cyitce.seal2.action.back;

import com.api.cyitce.seal2.config.BaseLog;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:25
 * @describe 旧版本流程提交限制
 */
public class FlowBackAction extends BaseLog implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        requestInfo.getRequestManager().setMessagecontent("当前流程版本属于旧版流程，已暂停提交，请重新创建新流程再提交。");
        return Action.FAILURE_AND_CONTINUE;
    }
}
