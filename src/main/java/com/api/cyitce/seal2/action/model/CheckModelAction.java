package com.api.cyitce.seal2.action.model;

import com.api.cyitce.seal2.config.BaseLog;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import static com.api.cyitce.seal2.util.sql.SqlUtils.insertRequestModel;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/3 15:55
 * @describe
 */
public class CheckModelAction extends BaseLog implements Action {
    public static final String MODEL_CALL_BACK = "http://218.245.99.207:7001/api/Seal/web/model/callback";

    /**
     * 根据选择的id拿到对应的模板编号
     * 根据编号以及对应的企业信息拿到对应的模板信息
     */
    private String configId = "1";

    @Override
    public String execute(RequestInfo requestInfo) {
        // User user = requestInfo.getRequestManager().getUser();
        // info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        // info("拿到当前操作人--------------->id:" + user.getUID());
        // String requestid = requestInfo.getRequestid();
        // // 是否需要用印
        // String sfxyqz = "1";
        // int sealType = -1;
        // int signerId = 0;
        // boolean flag = false;
        // String jbrbm = "-1";
        // int companyId = -1;
        // String lcbh = "default-";
        // // 具体事项
        // String yznr = "";
        // WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        // info("CheckModelAction--------------------> 拿到配置信息 config:" + JSONUtil.toJsonStr(wconfig));
        // String jbr = "";
        // // obj 主表
        // Map<String, String> obj = new HashMap<>();
        // Property[] properties = requestInfo.getMainTableInfo().getProperty();
        //
        //
        // for (Property p : properties) {
        //     info("CheckModelAction------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
        //     if (p.getName().equals(wconfig.getFsealType())) {
        //         if ("0".equals(p.getValue())) {
        //             sealType = SealFromEnum.E_SEAL.getValue();
        //         } else if ("1".equals(p.getValue())) {
        //             sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
        //         }
        //     } else if (p.getName().equals(wconfig.getGszd())) {
        //         if (StringUtils.hasText(wconfig.getGszd())) {
        //             companyId = Integer.parseInt(p.getValue());
        //         }
        //     } else if ("swszfb".equals(p.getName())) {
        //         companyId = Integer.parseInt(p.getValue());
        //     } else if ("szgs".equals(p.getName())) {
        //         companyId = Integer.parseInt(p.getValue());
        //     } else if ("yznr".equals(p.getName())) {
        //         // 具体事项
        //         if (StringUtils.hasText(p.getValue())) {
        //             yznr = p.getValue();
        //         }
        //     } else if ("lcbh".equals(p.getName())) {
        //         // 流程编号
        //         lcbh = p.getValue();
        //     }
        //     obj.put(p.getName(), p.getValue());
        // }
        // String title = "";
        // // 合同名称
        // if (!lcbh.equals("default-")) {
        //     title = lcbh + "-" + title;
        // }
        // // 校验是否支持当前业务公司
        // String yqgs = wconfig.getYqgs();
        // if (StringUtils.hasText(yqgs)) {
        //     ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
        //     boolean stringExist = isStringExist(yqgs, String.valueOf(companyId));
        //     info("CheckModelAction------------------> 判断是否支持改公司companyId：" + companyId + "  res:" + stringExist);
        //     if (!stringExist) {
        //         requestInfo.getRequestManager().setMessagecontent("该流程暂不支持该业务公司办理");
        //         return Action.FAILURE_AND_CONTINUE;
        //     }
        // }
        // // 判断是否支持改模板
        // String mbzd = wconfig.getMbzd();
        // boolean stringExist = isStringExist(mbzd, yznr);
        // info("CheckModelAction------------------> 判断是否支持改模板 mbzd: " + mbzd + "  yznr：" + yznr + "  res:" + stringExist);
        // if (!stringExist) {
        //     return Action.SUCCESS;
        // }
        // // 支持模板，
        // BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(yznr, 5);
        // if (!resp2.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未找到该事项对应的模板编号");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        //
        // // 支持模板，
        // BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
        // if (!resp3.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未找到该业务公司对应的id");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        // String enterpriseId = resp3.getData();
        // String modelCode = resp2.getData();
        // BaseResp<JSONObject> baseResp = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("业务办理公司信息拿取失败");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        // String creator = (String) baseResp.getData().get("creatorUuid");
        // if (StringUtils.hasText(creator)) {
        //     requestInfo.getRequestManager().setMessagecontent("该企业不存在管理员");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        // Map<String, String> map = getDocIdInfoByDocId(5, yznr, new ThirdMappConfig());
        // // 拿到模板对应的文档id
        // String mbdywd = map.get("mbdywd");
        // if (!StringUtils.hasText(mbdywd)) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到该模板对应的文档id");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        // info("CheckModelAction------------------> 到对应的模板文档id:" + mbdywd);
        //
        // info("CheckModelAction------------------> 拿到对应的企业信息:" + JSONUtil.toJsonStr(baseResp));
        // // 拿到对应的企业id
        // info("CheckModelAction------------------> 拿到对应的企业id enterpriseId:" + enterpriseId);
        // info("CheckModelAction------------------> 拿到对应的模板编号 modelCode:" + modelCode);
        // // 模板创建合同
        // ContractIntegration integration = new ContractIntegration();
        // ModelContract modelContract = new ModelContract();
        // modelContract.setName(title);
        // modelContract.setCreator(creator);
        // modelContract.setTemplateCode(modelCode);
        // modelContract.setEnterpriseId(enterpriseId);
        // modelContract.setAsyncUrl(MODEL_CALL_BACK);
        // List<ModelDoc> list = new ArrayList<>();
        // ModelDoc modelDoc = new ModelDoc();
        // modelDoc.setTemplateDocId(Long.parseLong(mbdywd));
        // modelDoc.setDocName(title);
        // // TODO  还差字体
        // info("CheckModelAction------------------> 创建模板合同 modelContract:" + JSONUtil.toJsonStr(modelContract));
        // // BaseResp<JSONObject> modelContractResp = integration.createModelContract(modelContract);
        //
        // info("CheckModelAction------------------> 创建模板合同返回结果:" + JSONUtil.toJsonStr(modelContractResp));
        // if (!modelContractResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("模板合同创建失败");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        // JSONObject data = modelContractResp.getData();
        // String contractId = (String) data.get("contractId");
        // BaseResp<Void> voidBaseResp = insertRequestModel(modelCode, contractId, 6, requestid, new ThirdMappConfig(), mbdywd);
        // if (!voidBaseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("三方映射插入失败");
        //     return Action.FAILURE_AND_CONTINUE;
        // }


        return Action.SUCCESS;
    }


    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }
}
