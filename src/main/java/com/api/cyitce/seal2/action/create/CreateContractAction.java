package com.api.cyitce.seal2.action.create;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.config.eSeal.ESealConfig;
import com.api.cyitce.seal2.enums.CompanyEnum;
import com.api.cyitce.seal2.enums.DocumentInfo;
import com.api.cyitce.seal2.enums.contract.ControlAlignmentTypeEnum;
import com.api.cyitce.seal2.enums.contract.PoliticalStatus;
import com.api.cyitce.seal2.enums.contract.SignFontTypeEnum;
import com.api.cyitce.seal2.enums.contract.VerticalAlignmentTypeEnum;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.enums.signet.ControlTypeEnum;
import com.api.cyitce.seal2.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal2.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal2.service.eSeal.impl.SignServiceImpl;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.util.FileUtils;
import com.api.cyitce.seal2.util.data.OaHrmUtils;
import com.api.cyitce.seal2.vo.req.*;
import com.api.cyitce.seal2.vo.req.contract.*;
import com.api.cyitce.seal2.vo.req.contract.addSignerByFile.*;
import com.api.cyitce.seal2.vo.req.contract.signByFile.AddSignerByFileApiReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.contract.AddSignerApiResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;
import com.api.cyitce.seal2.vo.resp.contract.SignerApiVO;
import com.api.cyitce.seal2.vo.resp.pagesign.PageSignResp;
import com.api.cyitce.seal2.vo.resp.template.DocumentApiVO;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.api.cyitce.seal2.action.create.UserRealAuthAction.isStringExist;
import static com.api.cyitce.seal2.action.create.checkAction.BMBM;
import static com.api.cyitce.seal2.util.FileUtils.*;
import static com.api.cyitce.seal2.util.sql.SqlUtils.*;
import static com.api.cyitce.seal2.web.SealWeb.searchInfo;

/**
 * @ClassName: 统一印控中心 创建合同
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  10:21
 * @Version: 1.0
 */
public class CreateContractAction extends BaseLog implements Action {
    public String configId = "1";
    // 敏感1 涉密2
    // public static final String ModelSyncUrl = "http://**************:7001/api/Seal/web3/update/file";
    public static final String ModelSyncUrl = "https://oa.cyitce.com/api/Seal/web/update/file";


    @Override
    public String execute(weaver.soa.workflow.request.RequestInfo requestInfo) {
        int sealType = -1;
        String jbrbm = "-1";
        boolean flag = false;
        String docId = "";
        String title = "";
        String sfxyqz = "1";
        int companyId = -1;
        String lcbh = "";
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        Map<String, String> obj = new HashMap<>();

        Property[] properties = requestInfo.getMainTableInfo().getProperty();

        for (Property p : properties) {
            info("Property打印 属性名称:" + p.getName() + "属性值:" + p.getValue());
            // jfwjlx
            if (p.getName().equals(wconfig.getFsealType())) {
                info("CreateContractAction-------------------> 主表 sealType: " + p.getValue());
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                }
            } else if (p.getName().equals(wconfig.getGszd())) {
                if (StringUtils.hasText(wconfig.getGszd())) {
                    companyId = Integer.parseInt(p.getValue());
                }
            } else if ("swszfb".equals(p.getName())) {
                companyId = Integer.parseInt(p.getValue());
            } else if ("szgs".equals(p.getName())) {
                if (!StringUtils.hasText(String.valueOf(companyId))) {
                    companyId = Integer.parseInt(p.getValue());
                }
            } else if ("lcbh".equals(p.getName())) {
                // 流程编号
                lcbh = p.getValue();
            } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (!"0".equals(p.getValue())) {
                        info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (BMBM.equals(p.getValue())) {
                        flag = true;
                    }
                }
            } else if ("smdj".equals(p.getName())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                        flag = true;
                    }
                }
            }
            obj.put(p.getName(), p.getValue());
        }

        String sfxyyy = wconfig.getSfxyyy();
        if (!StringUtils.hasText(sfxyyy)) {
            requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!obj.get(sfxyyy).equals("1")) {
            info("执行 不盖章逻辑" + obj.get(sfxyyy));
            return Action.SUCCESS;
        }

        // 实体章先不执行执行
        // 实体章先不执行执行
        if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            info("CreateContractAction------------------->  实体章先不执行执行");
            return Action.SUCCESS;
        }
        CreateContractServiceImp ccsi = new CreateContractServiceImp();
        ESealConfig config = new ESealConfig();
        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();
        ContractIntegration ci = new ContractIntegration();
        String requestid = requestInfo.getRequestid();

        // obj 主表

        // jbrbm = obj.get("sqbm");
        // String s = obj.get("isModel");

        // if (StringUtils.hasText(jbrbm) && jbrbm.equals("344")) {
        //     flag = true;
        // }
        info("wconfig: " + wconfig.toString());
        if (wconfig.isDetail()) {
            DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
            DetailTable dt = detailTables[wconfig.getDatasource() - 1];
            info(dt.getRow().length + "行数据");
            for (Row r : dt.getRow()) {
                Cell[] cs = r.getCell();
                for (Cell c : cs) {
                    info("明细表" + (wconfig.getDatasource() - 1) + "  属性名称:" + c.getName() + "属性值:" + c.getValue());
                    if (c.getName().equals(wconfig.getFfile())) {
                        info("sealType1: " + c.getValue());
                        info("jbrbm: " + jbrbm + ": c.value:" + c.getValue());
                        if (!flag) {
                            info("length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("每行只能上传一个附件!");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                            info("创建合同 当前行 合同id " + dt.getId() + ":" + c.getValue());
                            docId = c.getValue();
                        }

                    } else if (c.getName().equals(wconfig.getFtitle())) {
                        title = c.getValue();
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    } else if ("shnryy".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            docId = c.getValue();
                        }
                        // 是否需要签字
                    }
                }
                if (!flag) {
                    String docName = getDocName(docId);
                    info("CreateContractAction------------> 附件名称：" + docName);
                    // String[] split = docName.split(".");
                    // title = split[0].trim();
                    String nameWithoutExtension = getFileNameWithoutExtension(docName);
                    info("CreateContractAction------------>  解析的附件名称: " + nameWithoutExtension);
                    title = nameWithoutExtension + "-" + obj.get(wconfig.getLcbh());
                    boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                    info("CreateContractAction------------> ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);

                    // 电子合同 上传合同附件
                    if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
                        try {
                            info("CreateContractAction------------> 文档id" + docId);
                            BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                            if (!resp.isStatus()) {
                                info("CreateContractAction------------> 未查询到文档有对应文档 ,进行合同创建");
                                Map<String, String> file = FileUtils.oaFileInfo(docId);
                                info("CreateContractAction------------> 拿到原始合同文档");
                                byte[] fileBytes = file.get("bytes").getBytes();
                                info("CreateContractAction------------> 执行合同创建");
                                String person = obj.get(wconfig.getCreaterfieldname());
                                info("CreateContractAction------------> 申请人: " + person);

                                String code = OaHrmUtils.getworkCode(Integer.parseInt(person));
                                info("CreateContractAction------------> 申请人code: " + code + " 三方表拿取 天威云对应id");
                                BaseResp<String> creator = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(code, 2);
                                info("CreateContractAction------------> 申请人 天威云id: " + creator.getData());
                                // BaseResp<String> company = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                                // 根据企业 拿到企业管理员 id

                                // 拿到对应的企业id
                                BaseResp<String> com = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                                info("CreateContractAction------------> 拿到三方企业id: " + com.getData());

                                if (!com.isStatus()) {
                                    requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业id");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                BaseResp<JSONObject> baseResp = new CreateOrgIntegration().companyInfo(new CompanyInfo(com.getData()));
                                // if (!baseResp.isStatus()) {
                                //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
                                //     return Action.FAILURE_AND_CONTINUE;
                                // }
                                info("-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp));
                                info("CreateContractAction------------> 申请人 天威云id: " + creator.getData());
                                info("CreateContractAction------------> 执行合同创建");
                                BaseResp<ContractApiVO> resp2 = ccsi.createContract(Base64.encode(docId), title, (String) baseResp.getData().get("creatorUuid"), fileBytes, com.getData(), getFjUrl(docId));
                                if (!resp2.isStatus()) {
                                    requestInfo.getRequestManager().setMessagecontent("合同创建失败");
                                    return Action.FAILURE_AND_CONTINUE;
                                }

                                Long contractId = resp2.getData().getContractId();
                                info("CreateContractAction------------> 拿到天威云合同id" + contractId);
                                info("CreateContractAction------------> 三方数据表 插入合同, request");
                                // BaseResp<Void> resp1 = tmsi.insertThridDataRequestMapping(String.valueOf(contractId), Base64.encode(docId), 0, requestid);
                                info("CreateContractAction------------> 三方数据表 插入合同, request1");

                                // if (!resp1.isStatus()) {
                                //     info("CreateContractAction------------> 三方数据表 插入合同, request失败");
                                //     return Action.FAILURE_AND_CONTINUE;
                                // }
                                BaseResp<Void> request = insertRequest(String.valueOf(contractId), Base64.encode(docId), 0, requestid, new ThirdMappConfig(), Integer.parseInt(r.getId()), wconfig.getDatasource(), configId);
                                if (!request.isStatus()) {
                                    info("CreateContractAction------------> 三方数据表 插入合同, request失败");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                info("CreateContractAction------------> 三方数据表 插入合同, request成功");

                                Long contractDocId = resp2.getData().getDocId();
                                info("CreateContractAction------------> 合同文档id：" + contractDocId);
                                info("CreateContractAction------------> 三方数据表 插入合同文档数据");

                                // BaseResp<Void> resp3 = tmsi.insertThridDataMapping(String.valueOf(contractDocId), Base64.encode(docId), 1);
                                BaseResp<Void> resp3 = insertThirdMapping(String.valueOf(contractDocId), Base64.encode(docId), 1, new ThirdMappConfig());
                                if (!resp3.isStatus()) {
                                    info("CreateContractAction------------> 三方数据表 插入合同文档数据失败");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                info("CreateContractAction------------> 三方数据表 插入合同文档数据成功");
                                if ("8".equals(configId)) {
                                    // 如果是模板就自动加盖
                                    info("CreateContractAction------------> 采购结算单形式： " + obj.get("ddxs"));
                                    if ("0".equals(obj.get("ddxs"))) {
                                        info("CreateContractAction------------> 采购结算单 默认加盖");
                                        // 添加合同签署人
                                        ContractIntegration contractIntegration = new ContractIntegration();
                                        AddSignerByFileApiReq req = new AddSignerByFileApiReq();
                                        AddSignerApiVO signer = new AddSignerApiVO();
                                        signer.setEnterpriseId(com.getData());
                                        signer.setSequence(1);
                                        signer.setSendMsg(false);
                                        signer.setSetting(true);
                                        signer.setUserId(String.valueOf(baseResp.getData().get("creatorUuid")));
                                        Set<String> s = new HashSet<>();
                                        s.add("signet");
                                        signer.setControlsType(s);
                                        signer.setSignerType(2);
                                        req.setContractId(contractId);
                                        List<XYControlApiVO> xySignControls = new ArrayList<XYControlApiVO>();
                                        // List<KeywordControlApiVO> keywordSignControls = new ArrayList<KeywordControlApiVO>();
                                        List<CrossControlApiVO> crossSignControls = new ArrayList<CrossControlApiVO>();
                                        KeywordControlApiVO vo = new KeywordControlApiVO(2, ControlTypeEnum.SIGNET.getCode(), "0", "采购方盖章", 2f, 0.95f);
                                        List<KeywordControlApiVO> keywordSignControls = new ArrayList<KeywordControlApiVO>() {{
                                            add(vo);
                                        }};
                                        List<SignFileApiVO> signFiles = new ArrayList<SignFileApiVO>() {{
                                            add(new SignFileApiVO(contractDocId, xySignControls, keywordSignControls, crossSignControls));
                                        }};
                                        signer.setSignFiles(signFiles);
                                        List<AddSignerApiVO> signers = new ArrayList<>();
                                        signers.add(signer);
                                        req.setSigners(signers);
                                        info("CreateContractAction------------> 添加签署人 请求数据:" + JSONUtil.toJsonStr(req));
                                        BaseResp<AddSignerApiResp> respBaseResp = contractIntegration.addSignerByFile(req);
                                        info("CreateContractAction------------> 添加签署人 响应数据:" + JSONUtil.toJsonStr(respBaseResp));
                                        for (SignerApiVO signerApiVO : respBaseResp.getData().getSigners()) {
                                            info("CreateContractAction------------> 进入contractId ： " + contractId);
                                            Long signerId = signerApiVO.getSignerId();
                                            // 208605821758610328  合同专用章
                                            SignByFileOrTemplateApiReq req1 = new SignByFileOrTemplateApiReq();
                                            // 设置合同
                                            req1.setContractId(contractId);
                                            // 设置签署人
                                            SignSignerApiVO signer1 = new SignSignerApiVO();
                                            signer1.setSignerId(signerId);
                                            signer1.setUserWishes(false);
                                            // signer.setIntentionId(profile.getProperty(intentionId));
                                            // 设置签署文件
                                            List<SignSignerFileApiVO> signFiles1 = new ArrayList<>();
                                            SignSignerFileApiVO signFile1 = new SignSignerFileApiVO();
                                            signFile1.setDocId(contractDocId);
                                            // signFile.setSignControl(MapPut.get(e -> e.put("1", "sealId:" + profile.get(autographId))));
                                            // 设置控件  2
                                            String gdyz = wconfig.getGdyz();
                                            List<ControlValueApiVO> controlValues = new ArrayList<ControlValueApiVO>() {{
                                                add(new ControlValueApiVO(2L, Long.parseLong(gdyz)));
                                                // add(new ControlValueApiVO(2L, Long.parseLong(profile.get(autographId).toString())));
                                                // add(new ControlValueApiVO(3L, Long.parseLong(profile.get(sealId).toString())));
                                            }};
                                            signFile1.setControlValues(controlValues);
                                            // signFile.setFonts(getSignFonts(1));
                                            signFile1.setShowTimestamp(true);
                                            signFile1.setSealTimestamp(getSealTimeStampStyle(2));
                                            signFiles1.add(signFile1);
                                            signer1.setSignFiles(signFiles1);
                                            req1.setSigner(signer1);
                                            info("CreateContractAction------------> 签署合同请求数据:" + JSONUtil.toJsonStr(req1));
                                            BaseResp<AddSignerApiResp> pageSignRespBaseResp = contractIntegration.startSignerByFile(req1);
                                            info("CreateContractAction------------> 签署合同响应数据:" + JSONUtil.toJsonStr(pageSignRespBaseResp));
                                            if (!pageSignRespBaseResp.isStatus()) {
                                                requestInfo.getRequestManager().setMessagecontent("签署合同失败，请联系管理员");
                                                return Action.FAILURE_AND_CONTINUE;
                                            }
                                        }
                                    }
                                }
                            }
                            info("CreateContractAction------------> 创建合同成功");


                        } catch (Exception e) {
                            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                            return Action.FAILURE_AND_CONTINUE;

                        }

                    }

                }
            }
            if (configId.equals("4")) {
                info("CreateContractAction------------> 是否使用模板 " + obj.get("sfsymb"));
                if ("1".equals(obj.get("sfsymb"))) {
                    if (SealFromEnum.E_SEAL.getValue() == sealType) {
                        // 拿到对应的企业id
                        BaseResp<String> com = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                        info("CreateContractAction------------> 拿到三方企业id: " + com.getData());

                        if (!com.isStatus()) {
                            requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业id");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        // 判断是否 是对应的模板
                        // 模板 创建
                        String oaid = obj.get(wconfig.getMbzd());
                        String mbsx = wconfig.getMbsx();
                        boolean exist = isStringExist(mbsx, oaid);
                        if (!exist) {
                            info("CreateContractAction------------> 模板事项id :" + oaid + "  不处理");
                            return Action.SUCCESS;
                        }
                        if (!StringUtils.hasText(oaid)) {
                            // 在模板值
                            requestInfo.getRequestManager().setMessagecontent("具体事项为空，请联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }

                        Map<String, String> map = getqsrInfo1(7, oaid, new ThirdMappConfig(), companyId);
                        if (map.isEmpty()) {
                            // 在模板值
                            requestInfo.getRequestManager().setMessagecontent("未查询到对应的模板，请联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        // 拿到对应的模板 code 以及文档
                        String modelCode = map.get("sealid");
                        String modelDocId = map.get("mbdywd");
                        info("CreateContractAction------------> 模板事项 code:" + modelCode + " 文档id:" + modelDocId);
                        /**
                         *  1.根据不同的模板 通过模板创建合同
                         *  模板事项对应模板
                         * 3 离职证明   hr申请
                         * 4 实习协议  需要个人签字  hr申请
                         * 10 实习证明 个人申请
                         * 8 在职证明  个人申请
                         * 9 收入证明  需要hr个人填写 个人申请
                         * 2 解除劳动关系证明书 需要个人签名  hr申请
                         */
                        switch (oaid) {
                            case "2":
                                // // 解除劳动关系证明书
                                // // 根据电话获取userId
                                String bsdh = obj.get("jcdh");
                                // info("模板  用户授权------------------> 天威云授权  北森电话:" + bsdh);
                                // String urlRe = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + bsdh;
                                // JSONObject entries = new JSONObject();
                                // try {
                                //     entries = sendGetRequestWithToken(urlRe, (String) getToken().get("access_token"));
                                // } catch (Exception e) {
                                //     throw new RuntimeException(e);
                                // }
                                // if (entries.size() == 0) {
                                //     requestInfo.getRequestManager().setMessagecontent("北森hr系统 未查询到此人信息，请核实手机号是否正确");
                                //     return Action.FAILURE_AND_CONTINUE;
                                // }
                                Map<String, String> map2 = getcusDataInfo(bsdh, "1");
                                if (!StringUtils.hasText(map2.get("id"))) {
                                    requestInfo.getRequestManager().setMessagecontent("数藤平台未查询到此人信息，请核实手机号是否正确");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                boolean JCLD = createJCLD(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-解除劳动关系证明书", ModelSyncUrl, Integer.parseInt(map2.get("id")), requestInfo, wconfig, configId, obj);
                                if (!JCLD) {
                                    requestInfo.getRequestManager().setMessagecontent("创建解除劳动关系证明书模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            case "3":
                                // 离职证明
                                boolean LZXY = createLZXY(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-离职证明", ModelSyncUrl, Integer.parseInt(obj.get(wconfig.getCreaterfieldname())), requestInfo, wconfig, configId, obj);
                                if (!LZXY) {
                                    requestInfo.getRequestManager().setMessagecontent("创建离职证明模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            case "4":
                                Map<String, String> map4 = new HashMap<>();
                                if (companyId == 6 || companyId == 16) {
                                    map4.put("id", "-1");
                                } else {
                                    // 实习协议  需要个人签字  此人没有账号
                                    // 根据电话获取userId
                                    String bsdh4 = obj.get("bsdh");
                                    info("模板  用户授权------------------> 天威云授权  北森电话:" + bsdh4);
                                    String urlRe4 = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + bsdh4;
                                    JSONObject entries4 = new JSONObject();
                                    try {
                                        entries4 = sendGetRequestWithToken(urlRe4, (String) getToken().get("access_token"));
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                    if (entries4.size() == 0) {
                                        return "北森hr系统 未查询到此人信息，请核实手机号是否正确";
                                    }
                                    // 实习协议 需要个人签字 hr申请
                                    map4 = getcusDataInfo(bsdh4, "1");
                                    if (map4.isEmpty()) {
                                        return "数藤平台未查询到此人信息，请核实手机号是否正确";
                                    }
                                }
                                boolean SXXY = createSXXY(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-实习协议", ModelSyncUrl, Integer.parseInt(map4.get("id")), requestInfo, wconfig, configId, obj, companyId);
                                if (!SXXY) {
                                    requestInfo.getRequestManager().setMessagecontent("创建实习协议模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            case "8":
                                // 在职证明
                                boolean ZZZM = createZZZM(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-在职证明", ModelSyncUrl, Integer.parseInt(obj.get(wconfig.getCreaterfieldname())), requestInfo, wconfig, configId, obj);
                                if (!ZZZM) {
                                    requestInfo.getRequestManager().setMessagecontent("创建在职证明模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            case "9":
                                // 收入证明
                                boolean SRZM = createSRZM(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-收入证明", ModelSyncUrl, Integer.parseInt(obj.get(wconfig.getCreaterfieldname())), requestInfo, wconfig, configId, obj);
                                if (!SRZM) {
                                    requestInfo.getRequestManager().setMessagecontent("创建收入证明模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            case "10":
                                // 实习证明
                                boolean SXZM = createSXZM(modelCode, Long.parseLong(modelDocId), com.getData(), obj.get("lcbh") + "-实习证明", ModelSyncUrl, Integer.parseInt(obj.get(wconfig.getCreaterfieldname())), requestInfo, wconfig, configId, companyId, obj);
                                if (!SXZM) {
                                    requestInfo.getRequestManager().setMessagecontent("创建实习证明模板失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }


        }
        return Action.SUCCESS;
    }

    // 离职证明
    public boolean createLZXY(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, Map<String, String> obj) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("离职证明-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());

        Map<String, String> modelInfo = getModelInfo(userId);
        // 创建一个 Map 来存储键值对
        Map<String, String> infoMap = new HashMap<>();
        // String phone = "";
        // if (modelInfo.isEmpty()) {
        //     info("离职证明-----------------------> 未查询到对应的人员信息 " + userId);
        //     return false;
        // }
        // if (StringUtils.hasText(modelInfo.get("telephone"))) {
        //     phone = modelInfo.get("telephone");
        // } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
        //     phone = modelInfo.get("mobile");
        // } else {
        //     info("离职证明-----------------------> 未查询到对应的人员的手机号 " + userId);
        //     return false;
        // }
        // String url = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
        // JSONObject entries = new JSONObject();
        // try {
        //     entries = sendGetRequestWithToken(url, (String) getToken().get("access_token"));
        // } catch (Exception e) {
        //     throw new RuntimeException(e);
        // }
        // JSONObject items = entries.getJSONObject("staffDto");
        // JSONObject dept = entries.getJSONObject("departmentDto");
        // String enddate = modelInfo.get("enddate");
        // if (!StringUtils.hasText(enddate)) {
        //     info("离职证明-----------------------> oa拿取enddate 失败 为空");
        //     return false;
        // }

        // 添加键值对
        infoMap.put("xm", convertToStringAndCheck(obj.get("lzxm")));
        infoMap.put("sfz", convertToStringAndCheck(obj.get("lzsfz")));
        infoMap.put("bm", convertToStringAndCheck(obj.get("lzbm")));

        Map<String, String> zrqMap = splitDate1(convertToStringAndCheck(obj.get("lzzrq")));
        infoMap.put("zn", zrqMap.get("year"));
        infoMap.put("zy", zrqMap.get("month"));
        infoMap.put("zr", zrqMap.get("day"));
        Map<String, String> drqMap = splitDate1(convertToStringAndCheck(obj.get("lzdrq")));
        infoMap.put("dn", drqMap.get("year"));
        infoMap.put("dy", drqMap.get("month"));
        infoMap.put("dr", drqMap.get("day"));
        infoMap.put("zw", convertToStringAndCheck(obj.get("lzzwei")));
        Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(obj.get(config.getGszd())));
        if (companyInfo.isEmpty()) {
            info("离职证明-----------------------> 未查询到对应的企业信息 " + obj.get(config.getGszd()));
            return false;
        }
        infoMap.put("gs", convertToStringAndCheck(companyInfo.get("subcompanyname")));
        //

        // List<Integer> list = new ArrayList<>();
        // list.add(Integer.parseInt((String) items.get("userId")));
        // JSONObject customProperties = getCustomProperties(list);
        // String yuanyin = (String) customProperties.get("extyuangongtianxielizhiyuanyin_105466_1816368450");
        // if (StringUtils.hasText(yuanyin)) {
        //     // 根据id拿到对应的值
        // }

        infoMap.put("yuanyin", convertToStringAndCheck(obj.get("lzyy")));
        Map<String, String> yzqMap = splitDate1(getNextDayDate(convertToStringAndCheck(obj.get("lzdrq"))));
        Map<String, String> date = getDate();
        infoMap.put("yn", yzqMap.get("year"));
        infoMap.put("yy", yzqMap.get("month"));
        infoMap.put("yr", yzqMap.get("day"));
        infoMap.put("n", date.get("year"));
        infoMap.put("y", date.get("month"));
        infoMap.put("r", date.get("day"));
        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 14f, SignFontTypeEnum.SONG.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("离职证明----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("离职证明-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("离职证明-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("离职证明-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("离职证明-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("离职证明----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("离职协议模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("离职证明----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);
        return true;
    }

    public boolean createSXZM(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, int companyId, Map<String, String> obj) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("实习证明-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());

        Map<String, String> modelInfo = getModelInfo(userId);
        Map<String, String> SchoolInfo = getSchoolInfo(userId);
        // 创建一个 Map 来存储键值对
        Map<String, String> infoMap = new HashMap<>();
        if (companyId == 6 || companyId == 7) {
            // 添加键值对
            infoMap.put("xx", convertToStringAndCheck(obj.get("xx")));
            infoMap.put("zy1", convertToStringAndCheck(obj.get("zy1")));
            infoMap.put("xm", convertToStringAndCheck(obj.get("sxzmxm")));
            infoMap.put("sfz", convertToStringAndCheck(obj.get("sfz")));
            String zn = obj.get("zn");
            Map<String, String> employedDate = splitDate(zn);
            infoMap.put("zn", employedDate.get("year"));
            infoMap.put("zy", employedDate.get("month"));
            infoMap.put("zr", employedDate.get("day"));
            String dn = obj.get("dn");
            Map<String, String> dDate = splitDate(dn);
            infoMap.put("dn", dDate.get("year"));
            infoMap.put("dy", dDate.get("month"));
            infoMap.put("dr", dDate.get("day"));
            infoMap.put("bm", convertToStringAndCheck(obj.get("bm")));
            infoMap.put("zw", convertToStringAndCheck(obj.get("zw")));
            // 添加键值对
            Map<String, String> date = getDate();
            infoMap.put("n", date.get("year"));
            infoMap.put("y", date.get("month"));
            infoMap.put("r", date.get("day"));
        } else {
            String phone = "";
            if (modelInfo.isEmpty()) {
                info("实习证明-----------------------> 未查询到对应的人员信息 " + userId);
                return false;
            }
            if (StringUtils.hasText(modelInfo.get("telephone"))) {
                phone = modelInfo.get("telephone");
            } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
                phone = modelInfo.get("mobile");
            } else {
                info("实习证明-----------------------> 未查询到对应的人员的手机号 " + userId);
                return false;
            }
            String url = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
            JSONObject entries = new JSONObject();
            try {
                entries = sendGetRequestWithToken(url, (String) getToken().get("access_token"));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            JSONObject items = entries.getJSONObject("staffDto");
            JSONObject dept = entries.getJSONObject("departmentDto");


            // 添加键值对
            infoMap.put("xx", convertToStringAndCheck(items.get("graduateFrom")));
            infoMap.put("zy1", convertToStringAndCheck(items.get("major")));
            infoMap.put("xm", convertToStringAndCheck(items.get("name")));
            infoMap.put("sfz", convertToStringAndCheck(items.get("idCardNumber")));
            Map<String, String> employedDate = getDate1((String) items.get("employedDate"));
            infoMap.put("zn", employedDate.get("year"));
            infoMap.put("zy", employedDate.get("month"));
            infoMap.put("zr", employedDate.get("day"));

            infoMap.put("dn", "2024");
            infoMap.put("dy", "12");
            infoMap.put("dr", "31");
            infoMap.put("bm", convertToStringAndCheck(dept.get("name")));
            infoMap.put("zw", convertToStringAndCheck(dept.get("positionName")));
            // 添加键值对
            Map<String, String> date = getDate();
            infoMap.put("n", date.get("year"));
            infoMap.put("y", date.get("month"));
            infoMap.put("r", date.get("day"));
        }

        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 14f, SignFontTypeEnum.SONG.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("实习证明----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("实习证明-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("实习证明-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("实习证明-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("实习证明-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("实习证明----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("实习证明模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("实习证明----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);
        return true;
    }

    public boolean createZZZM(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, Map<String, String> obj) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("在职证明-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());

        Map<String, String> modelInfo = getModelInfo(userId);
        Map<String, String> SchoolInfo = getSchoolInfo(userId);
        // 创建一个 Map 来存储键值对
        Map<String, String> infoMap = new HashMap<>();
        String phone = "";
        if (modelInfo.isEmpty()) {
            info("在职证明-----------------------> 未查询到对应的人员信息 " + userId);
            return false;
        }
        if (StringUtils.hasText(modelInfo.get("telephone"))) {
            phone = modelInfo.get("telephone");
        } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
            phone = modelInfo.get("mobile");
        } else {
            info("在职证明-----------------------> 未查询到对应的人员的手机号 " + userId);
            return false;
        }
        String url = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
        JSONObject entries = new JSONObject();
        try {
            entries = sendGetRequestWithToken(url, (String) getToken().get("access_token"));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        JSONObject items = entries.getJSONObject("staffDto");
        JSONObject dept = entries.getJSONObject("departmentDto");
        Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(obj.get(config.getGszd())));
        info("entries--------------->" + JSONUtil.toJsonStr(entries));
        info("xm--------------->" + items.get("name"));
        // 添加键值对
        infoMap.put("xm", convertToStringAndCheck(items.get("name")));
        infoMap.put("sfz", convertToStringAndCheck(items.get("idCardNumber")));
        // 添加键值对
        // 添加键值对
        Map<String, String> employedDate = getDate1((String) items.get("employedDate"));
        infoMap.put("zn", employedDate.get("year"));
        infoMap.put("zy", employedDate.get("month"));
        infoMap.put("zr", employedDate.get("day"));
        infoMap.put("bm", removeIfEndsWithBu(convertToStringAndCheck(dept.get("name"))));
        infoMap.put("zw", convertToStringAndCheck(items.get("positionName")));
        infoMap.put("gs", convertToStringAndCheck(companyInfo.get("subcompanyname")));
        // 添加键值对
        Map<String, String> date = getDate();
        infoMap.put("qn", date.get("year"));
        infoMap.put("qy", date.get("month"));
        infoMap.put("qr", date.get("day"));
        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 16f, SignFontTypeEnum.SONG.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("在职证明----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("在职证明-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("在职证明-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("在职证明-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("在职证明-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("在职证明----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("在职证明模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("在职证明----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);
        return true;
    }

    public boolean createSRZM(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, Map<String, String> obj) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("收入证明-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());

        Map<String, String> modelInfo = getModelInfo(userId);
        Map<String, String> SchoolInfo = getSchoolInfo(userId);
        // 创建一个 Map 来存储键值对
        Map<String, String> infoMap = new HashMap<>();
        String phone = "";
        if (modelInfo.isEmpty()) {
            info("收入证明-----------------------> 未查询到对应的人员信息 " + userId);
            return false;
        }
        Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(modelInfo.get("subcompanyid1")));
        if (companyInfo.isEmpty()) {
            info("收入证明-----------------------> 未查询到对应的公司信息 " + userId);
            return false;
        }
        if (StringUtils.hasText(modelInfo.get("telephone"))) {
            phone = modelInfo.get("telephone");
        } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
            phone = modelInfo.get("mobile");
        } else {
            info("收入证明-----------------------> 未查询到对应的人员的手机号 " + userId);
            return false;
        }
        String urlRe = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
        JSONObject entries = new JSONObject();
        try {
            entries = sendGetRequestWithToken(urlRe, (String) getToken().get("access_token"));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        JSONObject items = entries.getJSONObject("staffDto");
        JSONObject dept = entries.getJSONObject("departmentDto");
        String enddate = modelInfo.get("enddate");
        if (!StringUtils.hasText(enddate)) {
            info("收入证明-----------------------> oa拿取enddate 失败 为空");
            return false;
        }
        Map<String, String> splitMap = splitDate(enddate);


        // 添加键值对

        // 添加键值对
        infoMap.put("xb", "0".equals(modelInfo.get("sex")) ? "男" : "女");
        infoMap.put("xm", convertToStringAndCheck(items.get("name")));
        infoMap.put("sfz", convertToStringAndCheck(items.get("idCardNumber")));
        infoMap.put("zw", convertToStringAndCheck(items.get("positionName")));
        infoMap.put("qq", obj.get("qq"));
        infoMap.put("dx", obj.get("dx"));
        infoMap.put("zm", obj.get("zm"));
        infoMap.put("lxr", obj.get("lxr2"));
        infoMap.put("lxdh", obj.get("lxdh2"));
        // 添加键值对
        Map<String, String> date = getDate();
        infoMap.put("n", date.get("year"));
        infoMap.put("y", date.get("month"));
        infoMap.put("r", date.get("day"));
        // Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(obj.get(config.getGszd())));
        // if(companyInfo.isEmpty()){
        //     info("离职证明-----------------------> 未查询到对应的企业信息 " + obj.get(config.getGszd()));
        //     return false;
        // }
        infoMap.put("gs", convertToStringAndCheck(companyInfo.get("subcompanyname")));
        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 14f, SignFontTypeEnum.SONG.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("收入证明----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("收入证明-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("收入证明-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("收入证明-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("收入证明-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("收入证明----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("收入证明模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("收入证明----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);
        return true;
    }

    public boolean createJCLD(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, Map<String, String> obj) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("解除劳动-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());

        // Map<String, String> modelInfo = getModelInfo(userId);
        // Map<String, String> SchoolInfo = getSchoolInfo(userId);
        //
        // // 创建一个 Map 来存储键值对
        Map<String, String> infoMap = new HashMap<>();
        // String phone = "";
        // if (modelInfo.isEmpty()) {
        //     info("解除劳动-----------------------> 未查询到对应的人员信息 " + userId);
        //     return false;
        // }
        Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(obj.get(config.getGszd())));
        // if (companyInfo.isEmpty()) {
        //     info("解除劳动-----------------------> 未查询到对应的公司信息 " + userId);
        //     return false;
        // }
        // if (StringUtils.hasText(modelInfo.get("telephone"))) {
        //     phone = modelInfo.get("telephone");
        // } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
        //     phone = modelInfo.get("mobile");
        // } else {
        //     info("解除劳动-----------------------> 未查询到对应的人员的手机号 " + userId);
        //     return false;
        // }
        // String urlRe = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
        // JSONObject entries = new JSONObject();
        // try {
        //     entries = sendGetRequestWithToken(urlRe, (String) getToken().get("access_token"));
        // } catch (Exception e) {
        //     throw new RuntimeException(e);
        // }
        // JSONObject items = entries.getJSONObject("staffDto");
        // JSONObject dept = entries.getJSONObject("departmentDto");
        // String enddate = modelInfo.get("enddate");
        // if (!StringUtils.hasText(enddate)) {
        //     info("解除劳动-----------------------> oa拿取enddate 失败 为空");
        //     return false;
        // }
        // Map<String, String> splitMap = splitDate(enddate);
        // 添加键值对


        // 添加键值对
        // 添加键值对
        infoMap.put("xm", convertToStringAndCheck(obj.get("jcxm")));
        infoMap.put("sfz", convertToStringAndCheck(obj.get("jcsfz")));

        // 0男 1女
        infoMap.put("xb", convertToStringAndCheck(obj.get("jcxb")));
        infoMap.put("rzrq", convertDateFormatNYR(convertToStringAndCheck(obj.get("jcrzrq"))));
        infoMap.put("bm", convertToStringAndCheck(obj.get("jcbm")));
        infoMap.put("zw", convertToStringAndCheck(obj.get("jczw")));
        infoMap.put("lzrq", convertDateFormatNYR(convertToStringAndCheck(obj.get("jclzrq"))));
        // infoMap.put("gs", convertToStringAndCheck(companyInfo.get("subcompanyname")));
        infoMap.put("gzgs", convertToStringAndCheck(companyInfo.get("subcompanyname")));
        // infoMap.put("qzrq", convertDateFormatNYR(convertToStringAndCheck(obj.get("jclzrq"))));
        // Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(obj.get(config.getGszd())));
        // if(companyInfo.isEmpty()){
        //     info("离职证明-----------------------> 未查询到对应的企业信息 " + obj.get(config.getGszd()));
        //     return false;
        // }
        // 添加键值对
        // Map<String, String> date = getDate();
        Map<String, String> date = splitDate1(convertToStringAndCheck(obj.get("jclzrq")));
        infoMap.put("n", date.get("year"));
        infoMap.put("y", date.get("month"));
        infoMap.put("r", date.get("day"));
        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 14f, SignFontTypeEnum.SONG.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        info("解除劳动-----------------------> req: " + JSONUtil.toJsonStr(req));
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("解除劳动----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        // String createrCode = OaHrmUtils.getworkCode(userId);
        // BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
        // if (!resp2.isStatus()) {
        //     info("解除劳动----------------------->查询个人userId失败");
        // }
        // boolean b1 = addSignerByTemplate(String.valueOf(data.getContractId()), "个人", 0, resp2.getData(), "", enterpriseId);
        // if (!b1) {
        //     return false;
        // }

        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("解除劳动-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("解除劳动-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("解除劳动-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("解除劳动-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("解除劳动----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("解除劳动关系协议模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("解除劳动----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);

        // // 发送个人页面签
        // String url = startSignByTemplateFile(data.getContractId(), 1, enterpriseId, resp2.getData(), (String) baseResp1.getData().get("creatorUuid"));
        //
        // // 给个人发送签名短信
        // String content = "【" + companyInfo.get("subcompanyname") + "】 你有一份解除劳动关系证明书需要签署,请前往:" + url + "  去完成签署,签署有效期20小时,请尽快前往签署";
        // BaseResp<JSONObject> baseResp2 = new SignServiceImpl().sendSms(resp2.getData(), url, content);
        // if (!baseResp2.isStatus()) {
        //     info("解除劳动----------------------->   发送个人签署页面失败: " + JSONUtil.toJsonStr(baseResp2));
        //     return false;
        // }
        return true;
    }

    public boolean createSXXY(String modelCode, Long modelDocId, String enterpriseId, String docName, String syncUrl, int userId, RequestInfo requestInfo, WorkflowConfig config, String configId, Map<String, String> obj, int companyId) {
        BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(enterpriseId));
        // if (!baseResp.isStatus()) {
        //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
        //     return Action.FAILURE_AND_CONTINUE;
        // }
        info("实习协议-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp1));
        CreateByTemplateApiReq req = new CreateByTemplateApiReq();
        req.setCode(RandomUtil.randomString(32));
        // req.setTemplateCode("1020250322000086016");
        req.setTemplateCode(modelCode);
        req.setName(docName);
        req.setEnterpriseId(enterpriseId);
        req.setCreator((String) baseResp1.getData().get("creatorUuid"));
        // req.setSyncUrl(syncUrl);
        req.setAsyncUrl(syncUrl);

        List<TemplateFileApiVO> docList = new ArrayList<>();
        TemplateFileApiVO doc = new TemplateFileApiVO();
        doc.setTemplateDocId(modelDocId);
        doc.setDocName(req.getName());
        String content = "";
        String companyName = "";

        Map<String, String> infoMap = new HashMap<>();
        if (companyId == 6 || companyId == 16) {
            // 工程 华来  使用自己填写的数据
            infoMap.put("sx", obj.get("sx"));
            infoMap.put("dz", obj.get("dz"));
            infoMap.put("lxr", obj.get("lxr"));
            infoMap.put("hrlxdh", obj.get("hrlxdh"));
            infoMap.put("csny", obj.get("csny"));
            infoMap.put("xx", obj.get("jdyx"));
            infoMap.put("sxsj", obj.get("sxsj"));
            infoMap.put("xb", "0".equals(obj.get("sex")) ? "男" : "女");
            infoMap.put("xm", obj.get("sxxyxm"));

            infoMap.put("zzmm", obj.get("zzmm"));
            infoMap.put("sxzy", obj.get("sxzy"));
            // 这里的联系电话键重复，实际使用时可根据情况修改键名，这里先保留
            infoMap.put("lxdh", obj.get("lxdh"));
            infoMap.put("gsdz", obj.get("gsdz"));
            String bz = obj.get("bz");
            Map<String, String> map = splitBz(bz, 10);

            infoMap.put("bz1", convertToStringAndCheck(map.get("bz1")));
            infoMap.put("bz2", convertToStringAndCheck(map.get("bz2")));
            infoMap.put("bz3", convertToStringAndCheck(map.get("bz3")));
            infoMap.put("bz4", convertToStringAndCheck(map.get("bz4")));
            // 添加键值对
            Map<String, String> date = getDate();
            infoMap.put("qn", date.get("year"));
            infoMap.put("qy", date.get("month"));
            infoMap.put("qr", date.get("day"));
            infoMap.put("gn", date.get("year"));
            infoMap.put("gy", date.get("month"));
            infoMap.put("gr", date.get("day"));
            companyName = obj.get("sx");
        } else {
            Map<String, String> modelInfo = getModelInfo(userId);
            Map<String, String> SchoolInfo = getSchoolInfo(userId);
            // 创建一个 Map 来存储键值对
            String phone = "";
            if (modelInfo.isEmpty()) {
                info("实习协议-----------------------> 未查询到对应的人员信息 " + userId);
                return false;
            }
            Map<String, String> companyInfo = getCompanyInfo(Integer.parseInt(modelInfo.get("subcompanyid1")));
            if (companyInfo.isEmpty()) {
                info("实习协议-----------------------> 未查询到对应的公司信息 " + userId);
                return false;
            }
            if (StringUtils.hasText(modelInfo.get("telephone"))) {
                phone = modelInfo.get("telephone");
            } else if (StringUtils.hasText(modelInfo.get("mobile"))) {
                phone = modelInfo.get("mobile");
            } else {
                info("实习协议-----------------------> 未查询到对应的人员的手机号 " + userId);
                return false;
            }
            String urlRe = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + phone;
            JSONObject entries = new JSONObject();
            try {
                entries = sendGetRequestWithToken(urlRe, (String) getToken().get("access_token"));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            JSONObject items = entries.getJSONObject("staffDto");
            JSONObject dept = entries.getJSONObject("departmentDto");
            String enddate = modelInfo.get("enddate");
            if (!StringUtils.hasText(enddate)) {
                info("实习协议-----------------------> oa拿取enddate 失败 为空");
                return false;
            }
            Map<String, String> splitMap = splitDate(enddate);
            // 添加键值对
            infoMap.put("sx", companyInfo.get("subcompanyname"));
            infoMap.put("dz", "重庆市渝北区光电园双子座A座16-2");
            infoMap.put("lxr", obj.get("lxr"));
            infoMap.put("hrlxdh", obj.get("hrlxdh"));
            infoMap.put("csny", convertDate((String) items.get("birthday")));
            infoMap.put("xx", convertToStringAndCheck((String) items.get("graduateFrom")));
            infoMap.put("sxsj", convertToStringAndCheck(convertDateFormat((String) items.get("employedDate"))));
            infoMap.put("xb", "0".equals(modelInfo.get("sex")) ? "男" : "女");
            infoMap.put("xm", convertToStringAndCheck(items.get("name")));
            String s1 = convertToStringAndCheck(items.get("politicalStatus"));

            infoMap.put("zzmm", PoliticalStatus.getValueById(StringUtils.hasText(s1) ? Integer.parseInt(s1) : 9999));
            infoMap.put("sxzy", convertToStringAndCheck(items.get("major")));
            // 这里的联系电话键重复，实际使用时可根据情况修改键名，这里先保留
            infoMap.put("lxdh", convertToStringAndCheck(items.get("mobile")));
            infoMap.put("gsdz", convertToStringAndCheck(CompanyEnum.getAddressById(Integer.parseInt(modelInfo.get("subcompanyid1")))));

            infoMap.put("bz1", "");
            infoMap.put("bz2", "");
            infoMap.put("bz3", "");
            infoMap.put("bz4", "");
            // 添加键值对
            Map<String, String> date = getDate();
            infoMap.put("qn", date.get("year"));
            infoMap.put("qy", date.get("month"));
            infoMap.put("qr", date.get("day"));
            infoMap.put("gn", date.get("year"));
            infoMap.put("gy", date.get("month"));
            infoMap.put("gr", date.get("day"));
            companyName = companyInfo.get("subcompanyname");
        }
        // infoMap.put("yz", "公司印章");
        List<FontApiVO> l = new ArrayList<>();
        for (String s : infoMap.keySet()) {
            List<FontApiVO> fonts = getFonts(s, 10f, SignFontTypeEnum.HEI.getIndex());
            l.addAll(fonts);
        }
        doc.setParams(infoMap);
        doc.setFonts(l);
        docList.add(doc);
        req.setDocList(docList);
        BaseResp<ContractApiVO> baseResp = new ContractIntegration().createModelContract(req);
        if (!baseResp.isStatus()) {
            info("实习协议----------------------->  创建模板合同失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        ContractApiVO data = baseResp.getData();
        // 添加合同签署人

        // boolean b = addSignerByTemplate(String.valueOf(data.getContractId()), "企业", 1, "", (String) baseResp1.getData().get("creatorUuid"), enterpriseId);
        // if (!b) {
        //     return false;
        // }
        String createrCode = "";
        String newUserId = "";
        if (companyId == 6 || companyId == 16) {
            createrCode = obj.get("sxsjh");

            // 判断是否存在
            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
            if (!resp2.isStatus()) {
                // 去创建用户 拿到对应的userId
                CreateUserServiceImpl cusi = new CreateUserServiceImpl();
                // sxxm sxsfz sxsj
                BaseResp<JSONObject> user = cusi.createUser(obj.get("sxxm"), obj.get("sxsfz"), obj.get("sxsjh"), createrCode);
                if (!user.isStatus()) {
                    info("实习协议-----------------------> 创建用户失败");
                    return false;
                }
                newUserId = (String) user.getData().get("userId");
                BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(newUserId, createrCode, 2);
                info("实习协议-----------------------> 个人手机实名" + resp1.toString());
                if (!resp1.isStatus()) {
                    info("实习协议-----------------------> 个人手机实名 第三方系统数据插入 : 失败");
                    return false;
                }

            } else {
                newUserId = resp2.getData();
            }
            boolean b1 = addSignerByTemplate(String.valueOf(data.getContractId()), "个人", 0, newUserId, "", enterpriseId);
            if (!b1) {
                return false;
            }

        } else {
            createrCode = OaHrmUtils.getworkCode(userId);
            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
            if (!resp2.isStatus()) {
                info("实习协议----------------------->查询个人userId失败");
            }
            newUserId = resp2.getData();
            boolean b1 = addSignerByTemplate(String.valueOf(data.getContractId()), "个人", 0, "", newUserId, enterpriseId);
            if (!b1) {
                return false;
            }
        }


        // 拿到合同详情信息 存入明细表
        ContractApiVO vo = searchInfo(data.getContractId());
        info("实习协议-----------------------> 拿到合同详情");
        String newDocId = "";
        List<DocumentApiVO> documents = vo.getDocuments();
        if (!CollectionUtils.isEmpty(documents)) {
            info("实习协议-----------------------> 合同文档:");
            DocumentApiVO documentApiVO = documents.get(0);
            String docBase64 = documentApiVO.getDocBase64();
            info("实习协议-----------------------> 上传文件:");
            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestInfo.getRequestid(), DocumentInfo.DOWNLOADABLE_CONTENT.getValue());
            info("实习协议-----------------------> 上传文件 返回的文档id:" + newDocId);
        }
        // 根据返回的id拿到对应明细表 id
        // 插入对应的明细表
        Map<String, String> info = getMainTableInfo(requestInfo.getRequestid(), requestInfo.getRequestManager().getBillTableName());
        if (info.isEmpty()) {
            info("实习协议----------------------->  查询主表id 错误");
            return false;
        }
        insertMXB("实习协议关系模板", newDocId, Integer.parseInt(info.get("id")));
        // 根据id 拿到明细表id
        Map<String, String> tableDTid = getTableDTid(Integer.parseInt(requestInfo.getRequestid()), requestInfo.getRequestManager().getBillTableName(), config.getDatasource(), newDocId, config.getFfile());
        // 将对应的模板合同存入
        if (tableDTid.isEmpty()) {
            info("实习协议----------------------->  查询明细表id 错误");
            return false;
        }
        String mxbId = tableDTid.get("id");
        insertRequestModel(String.valueOf(data.getContractId()), newDocId, 0, requestInfo.getRequestid(), new ThirdMappConfig(), Integer.parseInt(mxbId), config.getDatasource(), configId, 1);

        // 发送个人页面签
        String url = startSignByTemplateFile(data.getContractId(), 1, enterpriseId, newUserId, (String) baseResp1.getData().get("creatorUuid"));
        if (!StringUtils.hasText(url)) {
            info("实习协议----------------------->   发送个人签署页面失败: " + url);
            return false;
        }
        content = generateContent(companyName, url);
        // 给个人发送签名短信
        BaseResp<JSONObject> baseResp2 = new SignServiceImpl().sendSms(newUserId, url, content);
        if (!baseResp2.isStatus()) {
            info("实习协议----------------------->   发送个人签署页面失败: " + JSONUtil.toJsonStr(baseResp2));
            return false;
        }
        return true;
    }

    public static String removeIfEndsWithBu(String input) {
        if (input != null && input.endsWith("部")) {
            return input.substring(0, input.length() - 1);
        }
        return input;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    /**
     * @param contractId
     * @param signerRole
     * @param flag         flag 0 代表个人 1代表企业
     * @param userId
     * @param adminId
     * @param enterpriseId
     * <AUTHOR>
     * @date 2025/3/23 11:48
     */
    // 添加模板合同签署人
    public boolean addSignerByTemplate(String contractId, String signerRole, int flag, String userId, String adminId, String enterpriseId) {
        AddSignerByTemplateApiReq req = new AddSignerByTemplateApiReq();
        req.setContractId(Long.parseLong(contractId));

        List<TemplateSignerApiVO> templateSigners = new ArrayList<>();
        TemplateSignerApiVO singer = new TemplateSignerApiVO();
        singer.setSignerRole(signerRole);
        if (flag == 0) {
            singer.setUserId(userId);
        } else {
            singer.setEnterpriseId(enterpriseId);
            singer.setUserId(adminId);
        }
        // 个人 L021KOILI8KJK3Q   企业 L021KI1KC53R06O   企业码L021KZCZ85C5QCK

        // singer.setEnterpriseId(configUtil.getCompanyUuid());
        singer.setSendMsg(Boolean.FALSE);
        templateSigners.add(singer);
        req.setTemplateSigners(templateSigners);
        info("添加合同签署人------------> 请求参数: " + JSONUtil.toJsonStr(req));
        BaseResp<AddSignerApiResp> baseResp = new ContractIntegration().addSignerByTemplate(req);
        if (!baseResp.isStatus()) {
            info("添加合同签署人失败: " + JSONUtil.toJsonStr(baseResp));
            return false;
        }
        info("添加合同签署人成功: " + JSONUtil.toJsonStr(baseResp));
        return true;
    }

    public List<FontApiVO> getFonts(String id, float fz, int index) {
        List<FontApiVO> fonts = new ArrayList<>();
        FontApiVO fontApiVO = new FontApiVO();
        fontApiVO.setControlsKey(id);
        fontApiVO.setFontType(index);
        fontApiVO.setFontSize(fz);
        fontApiVO.setFontColor("#000000");
        fontApiVO.setAlignment(ControlAlignmentTypeEnum.LEFT.getIndex());
        fontApiVO.setVerticalAlignment(VerticalAlignmentTypeEnum.TOP.getIndex());
        fontApiVO.setSingleLine(false);
        fonts.add(fontApiVO);
        return fonts;
    }

    public static Map<String, String> getDate1(String inputDate) {
        LocalDate date;
        if (inputDate != null) {
            try {
                // 定义输入日期的格式
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSS");
                // 解析输入的日期字符串
                date = LocalDate.parse(inputDate, inputFormatter);
            } catch (Exception e) {
                // 如果解析失败，使用当前日期
                date = LocalDate.now();
            }
        } else {
            // 如果传入的日期字符串为null，使用当前日期
            date = LocalDate.now();
        }

        // 定义输出日期的格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        String formattedDate = date.format(outputFormatter);
        // 分割日期字符串
        String[] dateParts = formattedDate.split("-");

        // 创建一个 Map 来存储日期信息
        Map<String, String> dateMap = new HashMap<>();
        dateMap.put("year", dateParts[0]);
        dateMap.put("month", dateParts[1]);
        dateMap.put("day", dateParts[2]);
        return dateMap;
    }

    public Map<String, String> getDate() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        String formattedDate = currentDate.format(formatter);
        // 分割日期字符串
        String[] dateParts = formattedDate.split("-");

        // 创建一个 Map 来存储日期信息
        Map<String, String> dateMap = new HashMap<>();
        dateMap.put("year", dateParts[0]);
        dateMap.put("month", dateParts[1]);
        dateMap.put("day", dateParts[2]);
        return dateMap;
    }

    // 发送个人页面签
    public String startSignByTemplateFile(Long contractId, int signerType, String enterpriseId, String userId, String adminId) {
        StartSignByFileTemplateRequestVO requestVO = new StartSignByFileTemplateRequestVO();
        // 1 页面云端签署(默认) 2 页面ukey签署
        requestVO.setSignType(1);
        requestVO.setAsyncUrl(ModelSyncUrl);
        // 合同id
        requestVO.setContractId(contractId);
        // 链接超时时间
        requestVO.setExpire(1440);
        requestVO.setSendMsg(false);
        // 1：个 人，2：企业
        AddTemplateSignerVO signers = new AddTemplateSignerVO();
        signers.setSignerType(signerType);
        if (signerType == 1) {
            signers.setUserId(userId);
        } else {
            signers.setEnterpriseId(enterpriseId);
            signers.setUserId(adminId);
        }
        signers.setIsUserWishes(true);
        List<Integer> ways = new ArrayList<>();
        if (signerType == 1) {
            ways.add(1);
        } else {
            ways.add(1);
            ways.add(2);
        }
        signers.setUserWishesWay(ways);
        requestVO.setSigner(signers);
        info("发送个人页面签参数:" + JSONUtil.toJsonStr(requestVO));
        BaseResp<PageSignResp> resp = new ContractIntegration().startSignByTemplateFile(requestVO);
        if (!resp.isStatus()) {
            info("发送个人页面签失败 " + JSONUtil.toJsonStr(resp));
            return "";
        }
        return resp.getData().getSignUrl();

    }

    /**
     * 发送GET请求，并将token放入请求头，返回解析后的JSONObject
     *
     * @param url   请求的URL
     * @param token 要放入请求头的token
     * @return 解析后的JSONObject，包含GET请求的响应数据
     * @throws IOException 如果发生I/O异常
     */
    public static JSONObject sendGetRequestWithToken(String url, String token) throws IOException, IOException {
        URL targetUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) targetUrl.openConnection();

        // 设置请求方法为GET
        connection.setRequestMethod("GET");
        // 设置请求头，将token放入Authorization字段（假设使用Bearer Token的形式）
        connection.setRequestProperty("Authorization", "Bearer " + token);

        // 获取响应码
        int responseCode = connection.getResponseCode();

        // 读取响应内容
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            if (jsonResponse.containsKey("items")) {
                JSONArray itemsArray = jsonResponse.getJSONArray("items");
                if (itemsArray.size() > 0) {
                    return itemsArray.getJSONObject(0);
                }
            }
            return new JSONObject();
        } finally {
            // 断开连接
            connection.disconnect();
        }
    }


    /**
     * 发送POST请求并返回解析后的JSONObject
     *
     * @return 解析后的JSONObject
     * @throws Exception 如果发生I/O异常
     */
    public static JSONObject getToken() throws Exception {
        String requestBody = "{\"grant_type\":\"client_credentials\",\"app_key\":\"B83EFCE7F9A24D6EA8EA17CD930664DE\",\"app_secret\":\"C4D7437DD6E7470D989807EF738EF6385F2B508972E64EFAA6C25CA845367FE5\"}";
        URL targetUrl = new URL("https://openapi.italent.cn/token");
        HttpURLConnection connection = (HttpURLConnection) targetUrl.openConnection();

        // 设置请求方法为POST
        connection.setRequestMethod("POST");
        // 设置请求头
        connection.setRequestProperty("Content-Type", "application/json");
        // 允许输出
        connection.setDoOutput(true);

        // 发送请求
        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
            wr.writeBytes(requestBody);
            wr.flush();
        }

        // 获取响应码
        int responseCode = connection.getResponseCode();

        // 读取响应内容
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            // 将响应内容存入JSONObject
            return new JSONObject(response.toString());
        } finally {
            // 断开连接
            connection.disconnect();
        }
    }

    public static String convertToStringAndCheck(Object value) {
        if (value == null) {
            return " ";
        }
        String str = value.toString();
        if (str.isEmpty()) {
            return " ";
        }
        return str;
    }

    public static String convertDateFormat(String inputDateStr) {
        // 定义输入日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSS");
        // 定义输出日期格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

        LocalDateTime dateTime;
        if (inputDateStr == null) {
            // 如果传入 null，获取当前时间
            dateTime = LocalDateTime.now();
        } else {
            // 将输入的日期字符串解析为 LocalDateTime 对象
            dateTime = LocalDateTime.parse(inputDateStr, inputFormatter);
        }
        // 将 LocalDateTime 对象格式化为目标日期字符串
        return dateTime.format(outputFormatter);
    }

    public static String convertDateFormatNYR(String inputDateStr) {
        // 定义输入日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 定义输出日期格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

        LocalDate date;
        if (inputDateStr == null) {
            // 如果传入 null，获取当前日期
            date = LocalDate.now();
        } else {
            // 将输入的日期字符串解析为 LocalDate 对象
            date = LocalDate.parse(inputDateStr, inputFormatter);
        }
        // 将 LocalDate 对象格式化为目标日期字符串
        return date.format(outputFormatter);
    }


    public static String getNextDayDate(String inputDate) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将输入的日期字符串解析为 LocalDate 对象
        LocalDate date = LocalDate.parse(inputDate, formatter);
        // 获取下一天的日期
        LocalDate nextDay = date.plusDays(1);
        // 将下一天的日期格式化为字符串
        return nextDay.format(formatter);
    }

    public static String convertDate(String inputDate) {
        // 定义输入日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSS");
        // 定义输出日期格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月");
        try {
            // 解析输入日期字符串为 LocalDate 对象
            LocalDate date = LocalDate.parse(inputDate, inputFormatter);
            // 按照输出格式格式化日期
            return date.format(outputFormatter);
        } catch (Exception e) {
            System.out.println("日期格式转换失败:" + e.getMessage());
            return null;
        }
    }

    public static JSONObject getCustomProperties(List<Integer> oIds) {
        String apiUrl = "http://openapi.italent.cn/TenantBaseExternal/api/v5/Employee/GetServiceInfoByIds";

        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("oIds", oIds);
        requestBody.put("approvalStatus", Arrays.asList(1, 2));
        requestBody.put("empStatus", Arrays.asList(8));

        try {
            // 创建 URL 对象
            URL url = new URL(apiUrl);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为 POST
            connection.setRequestMethod("POST");
            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer " + (String) getToken().get("access_token"));
            // 允许输出
            connection.setDoOutput(true);

            // 发送请求体
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(requestBody.toString());
                wr.flush();
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // 解析响应
            JSONObject responseJson = new JSONObject(response.toString());
            JSONArray dataArray = responseJson.getJSONArray("data");
            if (dataArray.size() > 0) {
                JSONObject dataObject = dataArray.getJSONObject(0);
                JSONObject customProperties = dataObject.getJSONObject("customProperties");

                // 创建新的 JSONObject 存储属性
                JSONObject result = new JSONObject();
                for (Object key : customProperties.keySet()) {
                    result.put((String) key, customProperties.get(key));
                }

                return result;
            }

            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static Map<String, String> splitBz(String bz, int length) {
        Map<String, String> resultMap = new HashMap<>();
        if (bz == null) {
            bz = "";
        }
        int index = 0;
        for (int i = 1; i <= 3; i++) {
            String key = "bz" + i;
            if (index < bz.length()) {
                int endIndex = Math.min(index + length, bz.length());
                resultMap.put(key, bz.substring(index, endIndex));
                index = endIndex;
            } else {
                resultMap.put(key, " ");
            }
        }
        // 处理 bz4
        String key = "bz4";
        if (index < bz.length()) {
            resultMap.put(key, bz.substring(index));
        } else {
            resultMap.put(key, " ");
        }
        return resultMap;
    }

    public static String generateContent(String name, String url) {
        return "【" + name + "】你有一份实习协议的合同需要签署,请前往:" + url + "  去完成签署,签署有效期20小时";
    }

    public static Map<String, String> splitDate(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateStr, formatter);
        Map<String, String> dateMap = new HashMap<>();
        dateMap.put("year", String.valueOf(date.getYear()));
        dateMap.put("month", String.valueOf(date.getMonthValue()));
        dateMap.put("day", String.valueOf(date.getDayOfMonth()));
        return dateMap;
    }

    public static Map<String, String> splitDate1(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateStr, formatter);
        Map<String, String> dateMap = new HashMap<>();
        dateMap.put("year", String.valueOf(date.getYear()));
        // 对月份进行格式化，保证输出为两位数字
        dateMap.put("month", String.format("%02d", date.getMonthValue()));
        // 对日期进行格式化，保证输出为两位数字
        dateMap.put("day", String.format("%02d", date.getDayOfMonth()));
        return dateMap;
    }

    public static void main1(String[] args) {
        try {
            JSONObject result = getToken();
            // 打印返回参数示例
            System.out.println("access_token: " + result.get("access_token"));
            System.out.println("token_type: " + result.get("token_type"));
            System.out.println("expires_in: " + result.get("expires_in"));
            System.out.println("refresh_token: " + result.get("refresh_token"));

            // 第一个GET请求的URL
            String getUrl = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=15922785574";

            // 发送GET请求，并将token放入请求头，获取解析后的JSONObject
            JSONObject getResponse = sendGetRequestWithToken(getUrl, (String) result.get("access_token"));
            System.out.println(JSONUtil.toJsonStr(getResponse));
            System.out.println(getResponse.getJSONObject("staffDto").get("name"));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public SealTimestampApiVO getSealTimeStampStyle(Integer id) {
        SealTimestampApiVO sealTimestamp = new SealTimestampApiVO();
        sealTimestamp.setAllAddTimestamp(true);
        TimestampStyleApiVO unitTimestampStyle = new TimestampStyleApiVO();
        unitTimestampStyle.setControlsId(id);
        unitTimestampStyle.setPattern(TimePatternTypeEnum.STANDARD_DAY_WITH_CHINESE.getIndex());
        unitTimestampStyle.setColor("#000000");
        sealTimestamp.setUnitTimestampStyle(unitTimestampStyle);
        return sealTimestamp;
    }

    public String getFileNameWithoutExtension(String fileName) {
        if (fileName == null) {
            return "";
        }

        // 转为小写后检查是否以.pdf或.docx结尾
        String lowerCaseName = fileName.toLowerCase();
        if (!lowerCaseName.endsWith(".pdf") && !lowerCaseName.endsWith(".docx")) {
            return "";
        }

        // 保留原始大小写，截取扩展名前的部分
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? fileName : fileName.substring(0, dotIndex);
    }

    public static void main(String[] args) {
        // String inputDate = "2024-12-03T00:00:00.0000";
        // Map<String, String> dateMap = getDate1(null);
        // System.out.println("Year: " + dateMap.get("year"));
        // System.out.println("Month: " + dateMap.get("month"));
        // System.out.println("Day: " + dateMap.get("day"));
        // System.out.println("Day: " + (String) dateMap.get("1"));
        // 模拟从 JSON 中获取的值，这里以 null 和空字符串为例
        // Object value1 = null;
        // Object value2 = "";
        // Object value3 = "Hello, World!";
        //
        // String result1 = convertToStringAndCheck(value1);
        // String result2 = convertToStringAndCheck(value2);
        // String result3 = convertToStringAndCheck(value3);
        //
        // System.out.println("result1: " + StringUtils.hasText(result1));
        // System.out.println("result1: " + StringUtils.hasText(result2));
        // System.out.println("result1: " + StringUtils.hasText(result3));
        // System.out.println("result2: " + result2);
        // System.out.println("result3: " + result3);
        // List<Integer> list = new ArrayList<>();
        // list.add(147809548);
        // JSONObject customProperties = getCustomProperties(list);
        // System.out.println("JSONUtil.toJsonStr(customProperties) = " + JSONUtil.toJsonStr(customProperties));


        // Map<String, String> map = splitBz("", 3);
        // map.keySet().forEach(key -> System.out.println("Key: " + key + ", Value: " + map.get(key)));

        // Map<String, String> nextDayDate = splitDate1("2025-03-31");
        // nextDayDate.keySet().forEach(v -> {
        //     System.out.println("v = " + nextDayDate.get(v));
        // });
        // System.out.println("nextDayDate = " + nextDayDate);
        String fileNameWithoutExtension = new CreateContractAction().getFileNameWithoutExtension("默认-XKGC-CGGC20250192-车辆租赁合同.docx");
        System.out.println("fileNameWithoutExtension = " + fileNameWithoutExtension);
    }
}