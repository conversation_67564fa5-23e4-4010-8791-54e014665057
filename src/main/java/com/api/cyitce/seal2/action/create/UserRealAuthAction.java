package com.api.cyitce.seal2.action.create;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal2.integration.physicalSeal.seal.CreateSealTakIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.service.physicalSeal.impl.SealServiceImpl;
import com.api.cyitce.seal2.util.data.OaHrmUtils;
import com.api.cyitce.seal2.vo.req.contract.CompanyInfo;
import com.api.cyitce.seal2.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal2.vo.req.hrm.JoinOrgReq;
import com.api.cyitce.seal2.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.LoginInfoReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

import static com.api.cyitce.seal2.action.create.checkAction.BMBM;

/**
 * @ClassName: 统一印控中心 人员认证
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  16:37
 * @Version: 1.0
 */
public class UserRealAuthAction extends BaseLog implements Action {
    public String configId = "1";

    private static Set<Integer> realAccountSet = new HashSet<>();
    public static final String DefaultDepartment = "yzgls";


    @Override
    public String execute(RequestInfo requestInfo) {

        User user = requestInfo.getRequestManager().getUser();
        info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        info("拿到当前操作人--------------->id:" + user.getUID());
        // 是否需要用印
        String sfxyqz = "1";
        int sealType = -1;
        int signerId = 0;
        boolean flag = false;
        String jbrbm = "-1";
        int companyId = -1;
        String jbr = "";
        info("用户授权------------------> 进入用户实名方法");
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("用户授权------------------> configId:" + configId);
        info("用户授权------------------> wconfig:" + JSONUtil.toJsonStr(wconfig));
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            info("用户授权------------------> 进入明细表");
            for (Property p : properties) {
                info("用户授权------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                } else if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                        info("1");
                    }
                } else if ("swszfb".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("szgs".equals(p.getName())) {
                    if (!StringUtils.hasText(String.valueOf(companyId))) {
                        info("3" + " " + companyId);
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbr()) && p.getName().equals(wconfig.getJbr())) {
                    if (StringUtils.hasText(p.getValue())) {
                        jbr = p.getValue();
                    }
                } else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }


            // 校验是否支持当前业务公司
            String yqgs = wconfig.getYqgs();
            if (StringUtils.hasText(yqgs)) {
                ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
                boolean stringExist = isStringExist(yqgs, String.valueOf(companyId));
                info("用户授权------------------> 判断是否支持改公司companyId：" + companyId + "  res:" + stringExist);
                if (!stringExist) {
                    requestInfo.getRequestManager().setMessagecontent("该流程暂不支持该业务公司办理");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }

            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }
            // // 实体章先不执行执行
            // if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            //     info("BackAction 回退开始执行------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }

            // jbr = obj.get("jbr");

            // // 法律  jbrbm 涉密项目
            // jbrbm = obj.get("jbrbm");
            //
            // if (StringUtils.hasText(jbrbm) && jbrbm.equals("344")) {
            //     flag = true;
            // }

            if (wconfig.isDetail()) {

                DetailTable[] detailTables1 = requestInfo.getDetailTableInfo().getDetailTable();

                DetailTable dt1 = detailTables1[wconfig.getDatasource() - 1];
                info("CreateContractAction-------------------> 拿到当前明细表: " + (wconfig.getDatasource() - 1));
                for (Row r : dt1.getRow()) {
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        info("明细表" + (wconfig.getDatasource() - 1) + "  属性名称:" + c.getName() + "属性值:" + c.getValue());
                        if (c.getName().equals(wconfig.getFfile())) {
                            info("jbrbm: " + jbrbm + ": c.value:" + c.getValue());
                            if (!flag) {
                                info("length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length != 1) {
                                    requestInfo.getRequestManager().setMessagecontent("每行只能上传一个附件!");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            }
                        } else if (("shnryy").equals(c.getName())) {
                            info("jbrbm: " + jbrbm + ": c.value:" + c.getValue());
                            if (!flag) {
                                info("length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length != 1) {
                                    requestInfo.getRequestManager().setMessagecontent("每行只能上传一个附件!");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            }
                        }
                    }
                }
            }

            if (wconfig.isDetail()) {
                DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt = detailTables[wconfig.getDatasource() - 1];
                for (Row r : dt.getRow()) {
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        info("用户授权------------------>明细数据 属性：" + c.getName() + " 值：" + c.getValue());
                        if (wconfig.getFsigner().equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                signerId = Integer.parseInt(c.getValue());
                            }
                        } else if ("sfxyqz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sfxyqz = c.getValue();
                            }
                        } else if (wconfig.getFsigner().equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                signerId = Integer.parseInt(c.getValue());
                            }
                        }
                    }
                    signerId = wconfig.isSigner() ? wconfig.getSigner() : signerId;
                    // 电子签署
                    if (SealFromEnum.E_SEAL.getValue() == sealType) {
                        if (configId.equals("4")) {
                            info("模板 用户授权------------> 是否使用模板 " + obj.get("sfsymb") + " ：目前不需要实名");
                            // if ("1".equals(obj.get("sfsymb"))) {
                            //     // 人力模板
                            //     String s = obj.get(wconfig.getMbzd());
                            //     if (StringUtils.hasText(s)) {
                            //         if ("2".equals(s)) {  //  劳动解除
                            //             String bsdh = obj.get("jcdh");
                            //             info("模板  用户授权------------------> 天威云授权  北森电话:" + bsdh);
                            //             // String urlRe = "https://openapi.italent.cn/UserFrameworkApiV3/api/v1/staffs/Get?mobiles=" + bsdh;
                            //             // JSONObject entries = new JSONObject();
                            //             // try {
                            //             //     entries = sendGetRequestWithToken(urlRe, (String) getToken().get("access_token"));
                            //             // } catch (Exception e) {
                            //             //     throw new RuntimeException(e);
                            //             // }
                            //             // if (entries.size() == 0) {
                            //             //     requestInfo.getRequestManager().setMessagecontent("北森hr系统 未查询到此人信息，请核实手机号是否正确");
                            //             //     return Action.FAILURE_AND_CONTINUE;
                            //             // }
                            //             // 需要个人签字 hr申请
                            //             Map<String, String> map = getcusDataInfo(bsdh, "1");
                            //             if (map.isEmpty()) {
                            //                 requestInfo.getRequestManager().setMessagecontent("数藤平台未查询到此人信息，请核实手机号是否正确");
                            //                 return Action.FAILURE_AND_CONTINUE;
                            //             }
                            //             info("模板  用户授权------------------> 天威云授权 :" + s);
                            //             BaseResp<JSONObject> resp;
                            //             // 合同申请人创建电子印章系统账号
                            //             resp = realCreaterAccount(Integer.valueOf(map.get("id")), companyId);
                            //             if (!resp.isStatus()) {
                            //                 requestInfo.getRequestManager().setMessagecontent("实名认证失败:\n" + resp.getMessage());
                            //                 return Action.FAILURE_AND_CONTINUE;
                            //
                            //             }
                            //         }
                            //     }
                            // }
                        }

                    }
                    if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                        if (!StringUtils.hasText(jbr)) {
                            requestInfo.getRequestManager().setMessagecontent("经办人 取值失败，检查是否选择经办人或者联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        info("用户授权 物理印章------------------> 执行物理印章 用户实名");

                        info("用户授权 物理印章------------------> 执行物理印章 实名申请人以及经办人:  " + obj.get(wconfig.getCreaterfieldname()) + ":" + jbr);
                        Set<String> set = new HashSet<>();
                        // set.add(obj.get(wconfig.getCreaterfieldname()));
                        set.add(jbr);
                        // if (StringUtils.hasText(String.valueOf(signerId))) {
                        //     set.add(String.valueOf(signerId));
                        // }
                        // 是否需要电子签字
                        // sfxyqz = obj.get("sfxyqz");
                        // todo 这一块属于签字 暂时不管
                        // info("用户授权------------------> 是否需要电子签字 " + sfxyqz);
                        // boolean equals = "1".equals(sfxyqz);
                        // info("用户授权------------------> 是否需要电子签字 " + equals);
                        // // if (equals) {
                        // // 需要电子签名 创建对应的天薇云账户
                        // BaseResp<JSONObject> resp;
                        // info("用户授权------------------> 实名申请人" + wconfig.getCreaterfieldname());
                        // // 合同申请人创建电子印章系统账号
                        // resp = realCreaterAccount(Integer.valueOf(obj.get(wconfig.getCreaterfieldname())), companyId);
                        //
                        // if (!resp.isStatus()) {
                        //     requestInfo.getRequestManager().setMessagecontent("申请人实名认证失败:\n" + resp.getMessage());
                        //     return Action.FAILURE_AND_CONTINUE;
                        // }
                        // info("用户授权------------------> jbr" + jbr);
                        // if (StringUtils.hasText(jbr)) {
                        //     // 经办人创建电子印章系统账号
                        //     resp = realCreaterAccount(Integer.valueOf(jbr), companyId);
                        //
                        //     if (!resp.isStatus()) {
                        //         requestInfo.getRequestManager().setMessagecontent("经办人实名认证失败:\n" + resp.getMessage());
                        //         return Action.FAILURE_AND_CONTINUE;
                        //     }
                        // }
                        // }
                        for (String s : set) {
                            info("用户授权------------------> 实体印章实名：s" + s);
                            String createrCode = OaHrmUtils.getworkCode(Integer.parseInt(s));
                            info("用户授权 物理印章------------------> 查询是否存在对应的用户 ");
                            info("用户授权 物理印章------------------> 查询 sqr 对应用户不存在，执行创建");
                            HrmResourceApiReq req = OaHrmUtils.getAllInfo(Integer.parseInt(s));
                            if (!StringUtils.hasText(req.getUsername())) {
                                requestInfo.getRequestManager().setMessagecontent("经办人账号查询失败，请联系管理员");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 4);
                            if (resp2.isStatus()) {
                                // 存在  就更新密码
                                LoginInfoReq loginInfoReq = new LoginInfoReq();
                                loginInfoReq.setLoginName(req.getUsername());
                                loginInfoReq.setNewpassword(req.getPassword());
                                info("用户授权 物理印章-------------->更新用户密码: " + JSONUtil.toJsonStr(loginInfoReq));
                                BaseResp<JSONObject> resp = new CreateSealTakIntegration().updatePassword(loginInfoReq);
                                if (!resp.isStatus()) {
                                    info("用户授权 物理印章-------------->更新用户密码失败: " + JSONUtil.toJsonStr(resp.getData()));
                                    requestInfo.getRequestManager().setMessagecontent("账号更新密码，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            } else {

                                info("用户授权 物理印章------------------> 查询对应用户：" + resp2.getData());
                                List<String> departmengCodes = new ArrayList<>();
                                // departmengCodes.add(DefaultDepartment);

                                info("用户授权 物理印章------------------> 查询 sqr 对应用户不存在，执行创建");

                                if ("1985".equals(jbr) || "7289".equals(jbr) || "3810".equals(jbr)) {
                                    departmengCodes.add(DefaultDepartment);
                                } else {
                                    departmengCodes.add(req.getSubcompanyid1());
                                }
                                req.setDepartCodes(departmengCodes);
                                info(" 用户授权 物理印章 createUser---------------------> 创建物理印章用户  请求参数:" + req.toString());
                                // 创建申请人账号
                                BaseResp<JSONObject> resp4 = ServiceUtil.getService(SealServiceImpl.class).createUser(req);
                                info("用户授权 物理印章------------------> 创建用户返回值:" + JSONUtil.toJsonStr(resp4));
                                if (!resp4.isStatus()) {
                                    requestInfo.getRequestManager().setMessagecontent("经办人账号创建失败，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                // 三方表不存在
                                if (!resp2.isStatus()) {
                                    info("用户授权 物理印章------------------> 创建三方表 用户创建");
                                    BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(req.getUsername(), createrCode, 4);
                                    if (!resp1.isStatus()) {
                                        info("用户授权 物理印章------------------> 创建三方表 用户创建失败");
                                        requestInfo.getRequestManager().setMessagecontent("经办人三方数据加入失败，请联系管理员");
                                        return Action.FAILURE_AND_CONTINUE;
                                    }
                                }
                            }


                        }

                    }
                }
            }
        } else {
            info("用户授权------------------> 主表");
            for (Property p : properties) {
                info("用户授权------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    // if (configId.equals("7")) {
                    //     // 支出合同 0 纸质 1 电子
                    //     if ("0".equals(p.getValue())) {
                    //         sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    //     } else if ("1".equals(p.getValue())) {
                    //         sealType = SealFromEnum.E_SEAL.getValue();
                    //     }
                    // } else if (configId.equals("8")) {
                    //     // 采购结算单 1 纸质  0 电子
                    //     if ("0".equals(p.getValue())) {
                    //         sealType = SealFromEnum.E_SEAL.getValue();
                    //     } else if ("1".equals(p.getValue())) {
                    //         sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    //     }
                    // }
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                } else if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if (StringUtils.hasText(wconfig.getJbr()) && p.getName().equals(wconfig.getJbr())) {
                    if (StringUtils.hasText(p.getValue())) {
                        jbr = p.getValue();
                    }
                }
                obj.put(p.getName(), p.getValue());
            }


            // 校验是否支持当前业务公司
            String yqgs = wconfig.getYqgs();
            if (StringUtils.hasText(yqgs)) {
                ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
                boolean stringExist = isStringExist(yqgs, String.valueOf(companyId));
                info("用户授权------------------> 判断是否支持改公司companyId：" + companyId + "  res:" + stringExist);
                if (!stringExist) {
                    requestInfo.getRequestManager().setMessagecontent("该流程暂不支持该业务公司办理");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }

            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!"1".equals(obj.get(sfxyyy))) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }

            // 实体章先不执行执行
            if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
                info("用户授权 ------------------->  实体章先不执行执行");
                return Action.SUCCESS;
            }


            if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                if (!StringUtils.hasText(jbr)) {
                    requestInfo.getRequestManager().setMessagecontent("经办人 取值失败，检查是否选择经办人或者联系管理员");
                    return Action.FAILURE_AND_CONTINUE;
                }
                info("用户授权 物理印章------------------> 执行物理印章 用户实名");

                info("用户授权 物理印章------------------> 执行物理印章 实名申请人以及经办人:  " + obj.get(wconfig.getCreaterfieldname()) + ":" + jbr);
                Set<String> set = new HashSet<>();
                // set.add(obj.get(wconfig.getCreaterfieldname()));
                set.add(jbr);
                // if (StringUtils.hasText(String.valueOf(signerId))) {
                //     set.add(String.valueOf(signerId));
                // }
                // 是否需要电子签字
                // sfxyqz = obj.get("sfxyqz");
                // todo 这一块属于签字 暂时不管
                // info("用户授权------------------> 是否需要电子签字 " + sfxyqz);
                // boolean equals = "1".equals(sfxyqz);
                // info("用户授权------------------> 是否需要电子签字 " + equals);
                // // if (equals) {
                // // 需要电子签名 创建对应的天薇云账户
                // BaseResp<JSONObject> resp;
                // info("用户授权------------------> 实名申请人" + wconfig.getCreaterfieldname());
                // // 合同申请人创建电子印章系统账号
                // resp = realCreaterAccount(Integer.valueOf(obj.get(wconfig.getCreaterfieldname())), companyId);
                //
                // if (!resp.isStatus()) {
                //     requestInfo.getRequestManager().setMessagecontent("申请人实名认证失败:\n" + resp.getMessage());
                //     return Action.FAILURE_AND_CONTINUE;
                // }
                // info("用户授权------------------> jbr" + jbr);
                // if (StringUtils.hasText(jbr)) {
                //     // 经办人创建电子印章系统账号
                //     resp = realCreaterAccount(Integer.valueOf(jbr), companyId);
                //
                //     if (!resp.isStatus()) {
                //         requestInfo.getRequestManager().setMessagecontent("经办人实名认证失败:\n" + resp.getMessage());
                //         return Action.FAILURE_AND_CONTINUE;
                //     }
                // }
                // }
                for (String s : set) {
                    info("用户授权------------------> 实体印章实名：s" + s);
                    String createrCode = OaHrmUtils.getworkCode(Integer.parseInt(s));
                    info("用户授权 物理印章------------------> 查询是否存在对应的用户 ");
                    BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 4);
                    info("用户授权 物理印章------------------> 查询对应用户：" + resp2.getData());
                    List<String> departmengCodes = new ArrayList<>();
                    // departmengCodes.add(DefaultDepartment);

                    info("用户授权 物理印章------------------> 查询 sqr 对应用户不存在，执行创建");
                    HrmResourceApiReq req = OaHrmUtils.getAllInfo(Integer.parseInt(s));

                    if ("1985".equals(jbr) || "7289".equals(jbr) || "3810".equals(jbr)) {
                        departmengCodes.add(DefaultDepartment);
                    } else {
                        departmengCodes.add(req.getSubcompanyid1());
                    }
                    req.setDepartCodes(departmengCodes);
                    info(" 用户授权 物理印章 createUser---------------------> 创建物理印章用户  请求参数:" + req.toString());
                    // 创建申请人账号
                    BaseResp<JSONObject> resp4 = ServiceUtil.getService(SealServiceImpl.class).createUser(req);
                    info("用户授权 物理印章------------------> 创建用户返回值:" + JSONUtil.toJsonStr(resp4));
                    if (!resp4.isStatus()) {
                        requestInfo.getRequestManager().setMessagecontent("经办人账号创建失败，请联系管理员");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                    // 三方表不存在
                    if (!resp2.isStatus()) {
                        info("用户授权 物理印章------------------> 创建三方表 用户创建");
                        BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(req.getUsername(), createrCode, 4);
                        if (!resp1.isStatus()) {
                            info("用户授权 物理印章------------------> 创建三方表 用户创建失败");
                            requestInfo.getRequestManager().setMessagecontent("经办人三方数据加入失败，请联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                    }

                }

            }

        }


        return Action.SUCCESS;
    }

    public BaseResp<JSONObject> realCreaterAccount(Integer userId, int companyId) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();
        CreateOrgIntegration orgIntegration = new CreateOrgIntegration();
        if (!realAccountSet.contains(userId)) {
            String createrCode = OaHrmUtils.getworkCode(userId);
            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
            HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(userId);
            if (resp2.isStatus()) {
                // if (resp3.isStatus()) {
                //     info("企业详情查询1 : ");
                //     BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));
                //     if (!baseResp.isStatus()) {
                //         return new BaseResp<JSONObject>(false);
                //     }
                //     info("企业详情查询 结果1 : " + JSONUtil.toJsonStr(baseResp));
                //     RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
                //     JoinOrgReq joinOrgReq = new JoinOrgReq();
                //     joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
                //     joinOrgReq.setInviteUserId(resp2.getData());
                //     joinOrgReq.setEnterpriseId(resp3.getData());
                //     info("邀请用户 进入企业11 : " + JSONUtil.toJsonStr(joinOrgReq));
                //     integration.inviteUser(joinOrgReq);
                //     return MsgResp.ok();
                // }
                return MsgResp.ok();
            }
            RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
            CreateUserServiceImpl cusi = new CreateUserServiceImpl();
            resp = cusi.createUser(realUser.getName(), realUser.getIdCard(), realUser.getMobile(), createrCode);
            JoinOrgReq joinOrgReq = new JoinOrgReq();
            boolean flag = false;
            // 判断是否在已上线的公司下面 如果在 就加入 不在 就不加入
            ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
            String orgId = allInfo.getSubcompanyid1();
            boolean stringExist = isStringExist(thirdMappConfig.getSupportCompany(), orgId);

            String subcompanyid1 = "";

            if (StringUtils.hasText(orgId)) {
                flag = stringExist;
            }
            if (flag) {
                info("拿到当前人的subCompany: " + subcompanyid1);
                BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(subcompanyid1, 3);
                BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));

                info("企业详情查询 结果 : " + JSONUtil.toJsonStr(baseResp));
                joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
                joinOrgReq.setInviteUserId((String) resp.getData().get("userId"));
                joinOrgReq.setEnterpriseId(resp3.getData());
                info("邀请用户 进入企业 : " + JSONUtil.toJsonStr(joinOrgReq));
                integration.inviteUser(joinOrgReq);
            }


            if (resp.isStatus()) {
                BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping((String) resp.getData().get("userId"), createrCode, 2);
                info("resp1 : " + resp1.toString());
                if (!resp1.isStatus()) {
                    info("第三方系统数据插入 : 失败");
                    return MsgResp.error("第三方系统数据插入失败");
                }
                info("第三方系统数据插入 : 成功");
                realAccountSet.add(userId);
            }
        }

        return resp;
    }


    public static boolean isStringExist(String input, String number) {
        // 先检查输入字符串是否为空，若为空则直接返回 false
        if (input == null || input.isEmpty()) {
            return false;
        }
        // 将输入字符串按逗号分割成字符串数组
        String[] numbers = input.split(",");
        for (String numStr : numbers) {
            if (number.equals(numStr)) {
                return true;
            }
        }
        // 遍历完整个数组都没找到匹配数字，返回 false
        return false;
    }


    public static String encryptToMD5UpperCase(String input) {
        try {
            // 获取 MD5 算法的 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将输入字符串转换为字节数组并更新到 MessageDigest 中
            byte[] digest = md.digest(input.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 将字节转换为无符号整数
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    // 如果十六进制字符串长度为 1，前面补 0
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            // 将生成的十六进制字符串转换为大写形式
            return hexString.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            // 处理算法不可用的异常
            e.printStackTrace();
            return null;
        }
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public static void main(String[] args) {
        String ljHljh123 = encryptToMD5UpperCase("Asd710707&");
        String ljHljh1231 = encryptToMD5UpperCase("!Yzz67671057");
        String ljHljh1232 = encryptToMD5UpperCase("!&@CyitcE@2025..");
        System.out.println("ljHljh1232 = " + ljHljh123);

        System.out.println(ljHljh123.equals("5B0CBDCF22E10C6C7470513E3FB256AB"));
        System.out.println(ljHljh1231.equals("029D67DF1EB416ABEFBC93686027E513"));
        System.out.println(ljHljh1232.equals("E09EF10F64A050BDA7E03A824FACF2F0"));
        System.out.println(ljHljh1232);
        System.out.println("5b0cbdcf22e10c6c7470513e3fb256ab".equals("5B0CBDCF22E10C6C7470513E3FB256AB"));
    }


}