package com.api.cyitce.seal2.action.contract;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.WorkflowConfig;
import com.api.cyitce.seal2.enums.seal.SealFromEnum;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal2.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal2.util.FileUtils;
import com.api.cyitce.seal2.vo.req.contract.CompanyInfo;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;
import com.engine.common.util.ServiceUtil;
import com.weaver.general.BaseBean;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

import static com.api.cyitce.seal2.util.FileUtils.getDocName;
import static com.api.cyitce.seal2.util.FileUtils.getFjUrl;
import static com.api.cyitce.seal2.util.sql.SqlUtils.insertRequest;
import static com.api.cyitce.seal2.util.sql.SqlUtils.insertThirdMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/14 19:59
 * @describe
 */
public class ContaractAction extends BaseLog implements Action {
    public static final String CallBack = "https://oa.cyitce.com/api/Seal/web/update/con/file";
    // public static final String CallBack = "http://218.245.99.207:7001/api/Seal/web3/update/con/file";

    public String configId = "1";

    @Override
    public String execute(RequestInfo requestInfo) {
        String title = "";
        int sealType = -1;
        String lcbh = "默认-";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        int companyId = -1;
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("ContaractAction------------------> configId:" + configId);
        info("ContaractAction------------------> wconfig:" + JSONUtil.toJsonStr(wconfig));
        for (Property p : properties) {
            info("用户授权------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
            if (p.getName().equals(wconfig.getFsealType())) {
                if ("0".equals(p.getValue())) {
                    sealType = SealFromEnum.E_SEAL.getValue();
                } else if ("1".equals(p.getValue())) {
                    sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                }
            } else if (p.getName().equals(wconfig.getGszd())) {
                if (StringUtils.hasText(wconfig.getGszd())) {
                    companyId = Integer.parseInt(p.getValue());
                }
            } else if ("lcbh".equals(p.getName())) {
                if (StringUtils.hasText(p.getValue())) {
                    lcbh = p.getValue();
                }
            }
        }
        if (SealFromEnum.E_SEAL.getValue() == sealType) {
            Map<String, String> map = getInfo(requestInfo.getRequestManager().getBillTableName(), requestInfo.getRequestid(), wconfig.getFfile());
            String docId = map.get(wconfig.getFfile());
            if (!StringUtils.hasText(docId)) {
                requestInfo.getRequestManager().setMessagecontent("未查询到生成的合同文档");
                return Action.FAILURE_AND_CONTINUE;
            }

            // 创建合同

            info("ContaractAction------------> 文档id" + docId);
            BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
            if (!resp.isStatus()) {
                info("ContaractAction------------> 未查询到文档有对应文档 ,进行合同创建");
                Map<String, String> file = null;
                try {
                    file = FileUtils.oaFileInfo(docId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                info("ContaractAction------------> 拿到原始合同文档");
                byte[] fileBytes = file.get("bytes").getBytes();
                info("ContaractAction------------> 执行合同创建");
                // String person = obj.get(wconfig.getCreaterfieldname());
                // info("ContaractAction------------> 申请人: " + person);
                //
                // String code = OaHrmUtils.getworkCode(Integer.parseInt(person));
                // info("ContaractAction------------> 申请人code: " + code + " 三方表拿取 天威云对应id");
                // BaseResp<String> creator = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(code, 2);
                // info("ContaractAction------------> 申请人 天威云id: " + creator.getData());
                // BaseResp<String> company = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                // 根据企业 拿到企业管理员 id

                // 拿到对应的企业id
                BaseResp<String> com = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                info("ContaractAction------------> 拿到三方企业id: " + com.getData());

                if (!com.isStatus()) {
                    requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业id");
                    return Action.FAILURE_AND_CONTINUE;
                }
                BaseResp<JSONObject> baseResp = new CreateOrgIntegration().companyInfo(new CompanyInfo(com.getData()));
                // if (!baseResp.isStatus()) {
                //     requestInfo.getRequestManager().setMessagecontent("未查询到对应的企业信息");
                //     return Action.FAILURE_AND_CONTINUE;
                // }
                info("-----------------------> 企业信息" + JSONUtil.toJsonStr(baseResp));
                // info("ContaractAction------------> 申请人 天威云id: " + creator.getData());
                info("ContaractAction------------> 执行合同创建");
                CreateContractServiceImp ccsi = new CreateContractServiceImp();
                String docName = getDocName(docId);
                title = lcbh + docName;
                BaseResp<ContractApiVO> resp2 = ccsi.createContractCon(Base64.encode(docId), title, (String) baseResp.getData().get("creatorUuid"), fileBytes, com.getData(), getFjUrl(docId), CallBack);
                if (!resp2.isStatus()) {
                    requestInfo.getRequestManager().setMessagecontent("合同创建失败");
                    return Action.FAILURE_AND_CONTINUE;
                }

                Long contractId = resp2.getData().getContractId();
                info("ContaractAction------------> 拿到天威云合同id" + contractId);
                info("ContaractAction------------> 三方数据表 插入合同, request");
                // BaseResp<Void> resp1 = tmsi.insertThridDataRequestMapping(String.valueOf(contractId), Base64.encode(docId), 0, requestid);
                info("ContaractAction------------> 三方数据表 插入合同, request1");

                // if (!resp1.isStatus()) {
                //     info("ContaractAction------------> 三方数据表 插入合同, request失败");
                //     return Action.FAILURE_AND_CONTINUE;
                // }
                BaseResp<Void> request = insertRequest(String.valueOf(contractId), Base64.encode(docId), 0, requestInfo.getRequestid(), new ThirdMappConfig(), 0, wconfig.getDatasource(), configId);
                if (!request.isStatus()) {
                    info("ContaractAction------------> 三方数据表 插入合同, request失败");
                    return Action.FAILURE_AND_CONTINUE;
                }
                info("ContaractAction------------> 三方数据表 插入合同, request成功");

                Long contractDocId = resp2.getData().getDocId();
                info("ContaractAction------------> 合同文档id：" + contractDocId);
                info("ContaractAction------------> 三方数据表 插入合同文档数据");

                // BaseResp<Void> resp3 = tmsi.insertThridDataMapping(String.valueOf(contractDocId), Base64.encode(docId), 1);
                BaseResp<Void> resp3 = insertThirdMapping(String.valueOf(contractDocId), Base64.encode(docId), 1, new ThirdMappConfig());
                if (!resp3.isStatus()) {
                    info("ContaractAction------------> 三方数据表 插入合同文档数据失败");
                    return Action.FAILURE_AND_CONTINUE;
                }
                info("ContaractAction------------> 三方数据表 插入合同文档数据成功");
            }
            info("ContaractAction------------> 创建合同成功");
        }


        return Action.SUCCESS;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public static Map<String, String> getInfo(String formName, String requestid, String doc) {
        Map<String, String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        rs.next();
        try {
            String fileSql = "select * from " + formName + " where requestid=" + requestid;
            new BaseBean().writeLog(" getInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getInfo---------------------------> 执行成功sql " + rs.next());
            map.put(doc, rs.getString(doc));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getInfo ---------------------------> sql修改 方法报错");
            e.printStackTrace();
        }
        return map;
    }
}
