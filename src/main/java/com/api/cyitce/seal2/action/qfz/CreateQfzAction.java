package com.api.cyitce.seal2.action.qfz;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.integration.physicalSeal.seal.CreateSealTakIntegration;
import com.api.cyitce.seal2.util.sql.SqlUtils;
import com.api.cyitce.seal2.vo.req.seal.physical.SealUsePickApiReq;
import com.api.cyitce.seal2.vo.req.seal.physical.SealUsePickTaskVO;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.api.cyitce.seal2.util.sql.SqlUtils.yzInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17 10:39
 * @describe 创建取放章任务
 */
public class CreateQfzAction extends BaseLog implements Action {
    public static final String DefaultDepartment = "yzgls";
    private static final AtomicInteger counter = new AtomicInteger(0);
    private static final int MAX_COUNTER = 999;

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        String yzxz = "";
        String qfzlx = "0";
        String sqr = "0";
        String lcbh = "默认-";
        for (Property property : properties) {
            info("CreateQfzAction-------------->属性名称：" + property.getName());
            if ("yz".equals(property.getName())) {
                yzxz = property.getValue();
            }
            if ("qfzlx".equals(property.getName())) {
                qfzlx = property.getValue();
            }
            if ("sqr".equals(property.getName())) {
                sqr = property.getValue();
            }
            if ("lcbh".equals(property.getName())) {
                lcbh = property.getValue();
            }
        }
        if (!StringUtils.hasText(yzxz)) {
            requestInfo.getRequestManager().setMessagecontent("印章选择为空，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!StringUtils.hasText(qfzlx)) {
            requestInfo.getRequestManager().setMessagecontent("取放章未选择，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!StringUtils.hasText(sqr)) {
            requestInfo.getRequestManager().setMessagecontent("申请人为空，请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }

        String[] split = yzxz.split(",");
        // 将数组转换为 ArrayList
        List<String> yzlxList = new ArrayList<>(Arrays.asList(split));
        // 判断印章是否在库
        for (String s : yzlxList) {
            Map<String, String> map = yzInfo(s, new ThirdMappConfig());
            if (map.isEmpty()) {
                requestInfo.getRequestManager().setMessagecontent("未查询到对应的印章，请联系管理员");
                return Action.FAILURE_AND_CONTINUE;
            }
            if ("1".equals(map.get("zkzt")) && "0".equals(qfzlx)) {
                requestInfo.getRequestManager().setMessagecontent("【" + map.get("yzmc") + "】 该印章已被借出！");
                return Action.FAILURE_AND_CONTINUE;
            }
            if ("0".equals(map.get("zkzt")) && "1".equals(qfzlx)) {
                requestInfo.getRequestManager().setMessagecontent("【" + map.get("yzmc") + "】 该印章已在库！");
                return Action.FAILURE_AND_CONTINUE;
            }
        }
        CreateSealTakIntegration integration = new CreateSealTakIntegration();
        SealUsePickApiReq req1 = new SealUsePickApiReq();
        // 判断申请人是否在惠郎注册 如果没有则进行创建
        // String createrCode = OaHrmUtils.getworkCode(Integer.parseInt(sqr));
        // info("CreateQfzAction-------------->查询是否存在对应的用户 ");
        // BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 4);
        // HrmResourceApiReq req = null;
        // if (!resp2.isStatus()) {
        //     info("CreateQfzAction-------------->用户不存在 执行创建 ");
        //     List<String> departmengCodes = new ArrayList<>();
        //     // departmengCodes.add(DefaultDepartment);
        //
        //     info("CreateQfzAction-------------->查询 sqr 对应用户不存在，执行创建");
        //     req = OaHrmUtils.getAllInfo(Integer.parseInt(sqr));
        //
        //     if ("1985".equals(sqr) || "7289".equals(sqr) || "3810".equals(sqr)) {
        //         departmengCodes.add(DefaultDepartment);
        //     } else {
        //         departmengCodes.add(req.getSubcompanyid1());
        //     }
        //     req.setDepartCodes(departmengCodes);
        //     info("CreateQfzAction-------------->创建物理印章用户  请求参数:" + req.toString());
        //     // 创建申请人账号
        //     BaseResp<JSONObject> resp4 = ServiceUtil.getService(SealServiceImpl.class).createUser(req);
        //     info("CreateQfzAction-------------->创建用户返回值:" + JSONUtil.toJsonStr(resp4));
        //     if (!resp4.isStatus()) {
        //         requestInfo.getRequestManager().setMessagecontent("申请人账号创建失败，请联系管理员");
        //         return Action.FAILURE_AND_CONTINUE;
        //     }
        //     // 三方表不存在
        //     if (!resp2.isStatus()) {
        //         info("CreateQfzAction-------------->创建三方表 用户创建");
        //         BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(req.getUsername(), createrCode, 4);
        //         if (!resp1.isStatus()) {
        //             info("CreateQfzAction-------------->创建三方表 用户创建失败");
        //             requestInfo.getRequestManager().setMessagecontent("申请人三方数据加入失败，请联系管理员");
        //             return Action.FAILURE_AND_CONTINUE;
        //         }
        //     }
        //
        // }
        // info("CreateQfzAction-------------->查询对应用户：" + resp2.getData());
        // appId 1开头代表取，2开头代表放
        req1.setApplyStaffLoginName("ys1985");
        req1.setApplyTime(new Date());
        req1.setApplyReason(lcbh);
        Long applyId = System.currentTimeMillis();
        ;
        req1.setApplyId(applyId);

        req1.setApplyTitle(lcbh);
        List<SealUsePickTaskVO> list = new ArrayList<>();

        for (String yz : yzlxList) {
            SealUsePickTaskVO usePickTaskVO = new SealUsePickTaskVO();
            Map<String, String> map = SqlUtils.yzInfo(yz, new ThirdMappConfig());
            if (map.isEmpty()) {
                requestInfo.getRequestManager().setMessagecontent("未查询到对应的印章，请联系管理员");
                return Action.FAILURE_AND_CONTINUE;
            }
            usePickTaskVO.setInstrumentCode(map.get("yky"));
            usePickTaskVO.setPickType(qfzlx);
            usePickTaskVO.setSealName(map.get("yzmc"));
            usePickTaskVO.setSealCode(map.get("yzwbbh"));
            usePickTaskVO.setStaffLoginName("ys1985");
            list.add(usePickTaskVO);
        }
        req1.setPushSealPickTaskInfoList(list);
        info("CreateQfzAction-------------->创建取放章任务 请求参数：" + JSONUtil.toJsonStr(req1));
        BaseResp<JSONObject> sealPickTask = integration.createSealPickTask(req1);
        if (!sealPickTask.isStatus()) {
            info("CreateQfzAction-------------->创建取放章任务失败：" + String.valueOf(sealPickTask.getData().get("message")));
            requestInfo.getRequestManager().setMessagecontent("创建取放章任务失败: " + String.valueOf(sealPickTask.getData().get("message")) + ",请联系管理员");
            return Action.FAILURE_AND_CONTINUE;
        }
        SqlUtils.insertQfz(requestid, String.valueOf(applyId), 10, new ThirdMappConfig(), qfzlx);
        // BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(String.valueOf(applyId), requestid, 10);
        info("CreateQfzAction-------------->创建取放章任务成功");
        return Action.SUCCESS;
    }


}
