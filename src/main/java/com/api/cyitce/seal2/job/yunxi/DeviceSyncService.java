package com.api.cyitce.seal2.job.yunxi;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.yunxi.ApiException;
import com.api.cyitce.seal2.service.yunxi.YxygApiService;
import com.api.cyitce.seal2.util.DBUtil;
import com.api.cyitce.seal2.vo.req.yunxi.YxDevice;
import com.api.cyitce.seal2.vo.req.yunxi.YxDeviceListParams;
import com.api.cyitce.seal2.vo.req.yunxi.YxDeviceListResponseData;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class DeviceSyncService extends BaseLog {

    private final YxygApiService apiService;

    public DeviceSyncService(YxygApiService apiService) {
        this.apiService = apiService;
    }

    public void syncAllDevices() throws ApiException {
        info("--- 开始执行设备全量同步任务 ---");
        int currentPage = 1;
        final int pageSize = 100;
        long totalRecords;
        int totalPages;
        do {
            YxDeviceListParams params = new YxDeviceListParams();
            params.setPageNum(currentPage);
            params.setPageSize(pageSize);
            info("正在拉取第 " + currentPage + " 页设备数据...");
            YxDeviceListResponseData response = apiService.getDeviceList(params);
            if (response == null || response.getList() == null || response.getList().isEmpty()) {
                info("第 " + currentPage + " 页没有数据，或已到达末页。同步结束。");
                break;
            }
            List<YxDevice> devices = response.getList();
            info("成功拉取 " + devices.size() + " 条数据，开始处理...");
            for (YxDevice device : devices) {
                processSingleDevice(device);
            }
            totalRecords = response.getTotal();
            totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            currentPage++;
        } while (currentPage <= totalPages);

        info("--- 设备全量同步任务完成 ---");
    }

    private void processSingleDevice(YxDevice device) {
        try (Connection conn = DBUtil.getConnection()) {
            String checkSql = "SELECT Id FROM uf_yxwdt WHERE Uuid = ?";
            try (PreparedStatement checkStmt = conn.prepareStatement(checkSql)) {
                checkStmt.setString(1, device.getUuid());
                try (ResultSet rs = checkStmt.executeQuery()) {
                    if (rs.next()) {
                        info("设备 " + device.getUuid() + " 已存在，执行更新...");
                        updateDeviceInDb(conn, device);
                    } else {
                        info("设备 " + device.getUuid() + " 为新设备，执行插入...");
                        insertDeviceIntoDb(conn, device);
                    }
                }
            }
        } catch (SQLException e) {
            info("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void insertDeviceIntoDb(Connection conn, YxDevice device) throws SQLException {
        String fingersJson = (device.getFingers() != null) ? JSONUtil.toJsonStr(device.getFingers()) : "[]";
        String fencesJson = (device.getFences() != null) ? JSONUtil.toJsonStr(device.getFences()) : "[]";
        String sql = "INSERT INTO uf_yxwdt (Uuid, DeviceId, Name, UseCount,LastSyncTime) " +
                "VALUES (?, ?, ?, ?, ?)";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            setStatementParams(stmt, device, fingersJson, fencesJson);
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            stmt.setString(5, currentTime); // LastSyncTime参数
            stmt.executeUpdate();
        }
    }

    private void updateDeviceInDb(Connection conn, YxDevice device) throws SQLException {
        String fingersJson = (device.getFingers() != null) ? JSONUtil.toJsonStr(device.getFingers()) : "[]";
        String fencesJson = (device.getFences() != null) ? JSONUtil.toJsonStr(device.getFences()) : "[]";
        // String sql = "UPDATE uf_yxwdt SET Uuid = ?, DeviceId=?, Name=?, UseCount=?, Tenant=?, DeptId=?, DeptName=?, KeeperId=?, " +
        //         "AuditorId=?, Online=?, Network=?, SleepTime=?, FingerprintMode=?, Bluetooth=?, RemoteLock=?, Camera=?, " +
        //         "State=?, Location=?, JuridicalPerson=?, Type=?, BatteryLevel=?, RegisterDate=?, FingersJson=?, " +
        //         "FencesJson=?, LastSyncTime=? WHERE Uuid = ?";
        String sql = "UPDATE uf_yxwdt SET Uuid = ?, DeviceId=?, Name=?, UseCount=?, LastSyncTime=? WHERE Uuid = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            setStatementParams(stmt, device, fingersJson, fencesJson);
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            stmt.setString(5, currentTime); // LastSyncTime参数
            stmt.setString(6, device.getUuid()); // 第25个参数是 WHERE 条件的 uuid
            stmt.executeUpdate();
        }
    }

    private void setStatementParams(PreparedStatement stmt, YxDevice device, String fingersJson, String fencesJson) throws SQLException {
        stmt.setString(1, device.getUuid());
        stmt.setObject(2, device.getDeviceId());
        stmt.setString(3, device.getName());
        stmt.setObject(4, device.getCount());
        // stmt.setString(5, device.getTenant());
        // stmt.setString(6, device.getDeptId());
        // stmt.setString(7, device.getDeptName());
        // stmt.setString(8, device.getKeeperId());
        // stmt.setString(9, device.getAuditorId());
        // stmt.setObject(10, device.getOnline());
        // stmt.setString(11, device.getNetwork());
        // stmt.setObject(12, device.getSleepTime());
        // stmt.setObject(13, device.getFingerprintMode());
        // stmt.setObject(14, device.getBluetooth());
        // stmt.setObject(15, device.getRemoteLock());
        // stmt.setObject(16, device.getCamera());
        // stmt.setObject(17, device.getState());
        // stmt.setString(18, device.getLocation());
        // stmt.setString(19, device.getJuridicalPerson());
        // stmt.setString(20, device.getType());
        // stmt.setObject(21, device.getBatteryLevel());
        // stmt.setTimestamp(22, toTimestamp(device.getRegisterDate()));
        // stmt.setString(23, fingersJson);
        // stmt.setString(24, fencesJson);
    }

    private Timestamp toTimestamp(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;
        try {
            return new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    public static void main(String[] args) {

        String yxUuid = "0X1621SPK244402V00007318";
        String startTime="2023-08-01 15:15:15";
        String endTime="2023-08-01 20:00:00";
        // 假设这些是配置文件中的默认值
        String baseUrl = "http://183.230.9.103:18585/stamper/apis";
        String appKey = "cqxkgc";
        String tenant = "cqxkgc";
        String appSecret = "cn5o3JAG657Cjcs9";
        // ApiClient client = new ApiClient(baseUrl, appKey, tenant, appSecret, true);
        YxygApiService service = new YxygApiService(baseUrl, appKey, tenant, appSecret, true);
        DeviceSyncService service1 = new DeviceSyncService(service);
        try {
            service1.syncAllDevices();
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }
}