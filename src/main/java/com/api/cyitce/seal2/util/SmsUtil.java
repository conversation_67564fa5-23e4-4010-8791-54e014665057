package com.api.cyitce.seal2.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.realauth.RealAuthConfig;
import com.api.cyitce.seal2.constant.ESealApiUrl;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.MsgResp;
import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.client.api.ContentResponse;
import org.eclipse.jetty.client.api.Request;
import org.eclipse.jetty.client.util.BytesContentProvider;
import org.eclipse.jetty.util.HttpCookieStore;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import weaver.general.BaseBean;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * @ClassName: SmsUtil
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-21  15:05
 * @Version: 1.0
 */
public class SmsUtil extends BaseLog {
    // 创建对象
    public static HttpClient jettyhttpclient = null;

    private void init() {
        // 初始化 jettyhttpclient
        SslContextFactory sslContextFactory = new SslContextFactory();
        jettyhttpclient = new HttpClient(sslContextFactory);
        jettyhttpclient.setName("JettyHttpClient");
        jettyhttpclient.setTCPNoDelay(true);
        jettyhttpclient.setConnectBlocking(true);
        jettyhttpclient.setCookieStore(new HttpCookieStore.Empty());
        try {
            jettyhttpclient.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public BaseResp<JSONObject> sendSms(Map<String, Object> maprequest) {
        info("sendSms  ---------------------------> 进入主要方法");
        // 初始化服务连接
        init();
        // 构造请求参数
        RealAuthConfig rConfig = new RealAuthConfig();

        String url = rConfig.props("itruscloud.sms.api.url") + ESealApiUrl.SEND_SMS;
        info("sendSms  ---------------------------> 发送短信的url"+url);
        String autograph = rConfig.props("itruscloud.sms.api.autograph");
        info("sendSms  ---------------------------> itruscloud.sms.api.autograph： "+autograph);
        String appId = rConfig.getAppId();
        info("sendSms  ---------------------------> appId： "+appId);

        String serviceCode = "msg0001";
        info("sendSms  ---------------------------> serviceCode： "+serviceCode);
        String serviceKye = rConfig.getSecretKey();
        info("sendSms  ---------------------------> serviceCode： "+serviceCode);
        maprequest.put("appId", appId);
        maprequest.put("serviceCode", serviceCode);
        maprequest.put("autograph", autograph);

        info("sendSms  ---------------------------> 短信服务请求参数：{?}", JSONUtil.toJsonStr(maprequest));

        byte byterequest[] = JSON.toJSONBytes(maprequest);
        String signature = "";

        try {
            signature = Base64.getEncoder().encodeToString(
                    HmacSh1Util.getHmacSHA1(byterequest, serviceKye + serviceCode));

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return MsgResp.error("Base64 编码失败");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return MsgResp.error("加密算法不存在");
        }

        // 调用API服务
        Request request = jettyhttpclient.POST(url).header("appId", appId)
                .header("serviceCode", serviceCode).header("Content-Signature", "HMAC-SHA1 " + signature)
                .content(new BytesContentProvider(byterequest), "application/json;charset=UTF-8");
        info("sendSms  ---------------------------> 调用url"+url);
        try {
            // 调用服务端
            ContentResponse response = request.send();
            JSONObject jsonResp = JSONUtil.parseObj(response.getContentAsString());
            if (!"1".equals(jsonResp.get("status"))) {
                return MsgResp.error(jsonResp.get("message").toString());
            }

            return MsgResp.ok();
        } catch (InterruptedException e) {
            error("发送短信失败,{?}", e.getMessage(), e);
        } catch (TimeoutException e) {
            error("发送短信失败,{?}", e.getMessage(), e);
        } catch (ExecutionException e) {
            error("发送短信失败,{?}", e.getMessage(), e);
        } finally {
            // 关闭服务资源
            try {
                jettyhttpclient.stop();
            } catch (Exception e) {
                error("关闭服务资源失败,{?}", e.getMessage(), e);
            }
        }

        return MsgResp.error("发送短信失败");
    }

    public static BaseResp<JSONObject> sendSms(String phone, String content) {
        new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest12321312 ");
        Map<String, Object> maprequest = new HashMap<String, Object>();
        maprequest.put("phone", phone);
        maprequest.put("content", content);
        SmsUtil su = new SmsUtil();

        new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest " + maprequest.toString());

        return su.sendSms(maprequest);
    }
}