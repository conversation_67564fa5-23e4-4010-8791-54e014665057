package com.api.cyitce.seal2.util;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal2.util.http.PostUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: TokenUtil
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-02-16  17:27
 * @Version: 1.0
 */
public class TokenUtil {
    public static final String APPID = "e8a62801-588b-9590-8efb-2514c4b27c46";
    public static final String USERID = "pRjqbCcEpxZqXQNfdqGTlyqDh9YLmVLhMEnO9IXPJtwm08eGAcPM7MnXM9PfkCKdB7hd0kb4eguzRBgemhe/f+w5J3pCdagdj3GYtJOjVbaBq+EAo83G0hCU56TDQYyMfdQwZRzBxPQzISoW+aVoFDITbZi/+04zcHXmDcyfZmbRv8ShZDWODf1vEe6abOUU9XkUVty+LNHVgUOiwDXfwZ6srOi5jjMEaB4kF86Ekak7mYFNRvwFhe0Ok801gTA6DYQ/3ZuWGOi390g3L25YqgS11pZl4AFCToSI6kGYf3sjuxsHG6M3lHhVnzIEBw3VGPpwE2jjCGAWmI7zDecAtg==";
    public static final String SECRET = "Jzqcm4xZiEtvRiz0j31TtGzeotughthCnlBHwGYK/N+ccgWeqER9U1MfUB3avOIJyKWm94K/+dMeaG7WTLWQF1X1GJ7Gy3yQEMFxG4xuYpMQFeBZVhk0a7GLxA5CVBjehz0V4yCrLT8McIyQwh6w47XHpoe7LnTi7R8bV9GXrDyPeUI8L84PxhIZjigWr5DqwX4v0WU8PO4Phitc4I3tVSvBYvC75PVRfFf+f4g5o9nDtqL1vjRibLUl8c2gq6pwAcP5Gt7Le8RF0JPZ/63nZ3kK/4tkds7iL3XIZY5nViTJGeK1yyVfowPOznqP5Br545OL/BPp5wI8000tytv4Zw==";
    public static final String TOKEN_URL = "https://oa.cyitce.com/api/ec/dev/auth/applytoken";

    public static String getToken(){
        Map<String,String> headers = new HashMap<>();
        headers.put("appid",APPID);
        headers.put("secret",SECRET);

        JSONObject resp = PostUtil.toPost(TOKEN_URL,new JSONObject(),headers);

        return resp.getStr("token");
    }
}