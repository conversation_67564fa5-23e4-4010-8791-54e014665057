package com.api.cyitce.seal2.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @ClassName: HmacSh1Util
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-09  17:30
 * @Version: 1.0
 */
public class HmacSh1Util {

    public static String getSignature(String content, String key) {
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, key.getBytes());
        return Base64.encode(hMac.digest(content));
    }

    public static String getSignatureSha256(String content, String key) {
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, key.getBytes());
        return Base64.encode(hMac.digest(content));
    }

    public static byte[] getHmacSHA1(byte[] data, String key) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        byte[] ipadArray = new byte[64];
        byte[] opadArray = new byte[64];
        byte[] keyArray = new byte[64];
        int ex = 0;
        MessageDigest m=MessageDigest.getInstance("SHA1");
        if (key.length() > 64) {
            byte[] temp =m.digest(key.getBytes());
            ex = temp.length;
            System.arraycopy(temp, 0, keyArray, 0, temp.length);
        } else {
            byte[] temp = key.getBytes();
            ex = temp.length;
            System.arraycopy(temp, 0, keyArray, 0, temp.length);
        }
        for (int i = ex; i < 64; i++) {
            keyArray[i] = 0;
        }

        for (int j = 0; j < 64; j++) {
            ipadArray[j] = (byte) (keyArray[j] ^ 0x36);
            opadArray[j] = (byte) (keyArray[j] ^ 0x5C);
        }
        byte[] tempResult = m.digest(join(ipadArray, data));
        return  m.digest(join(opadArray, tempResult));
    }

    private static byte[] join(byte[] b1, byte[] b2) {
        int length = b1.length + b2.length;
        byte[] newer = new byte[length];
        System.arraycopy(b1, 0, newer, 0, b1.length);
        System.arraycopy(b2, 0, newer, b1.length,b2.length);
        return newer;
    }
}