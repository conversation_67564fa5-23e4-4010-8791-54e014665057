package com.api.cyitce.seal2.enums.contract;



/**
 * <AUTHOR>
 * @date 2023/8/17/0017
 */

public enum FontTypeEnum {

    /**
     * 签名字体
     */
    WEIRUANYAHEI(1, "微软雅黑（默认）"),
    HEITI(2, "黑体"),
    SONG(3, "宋体"),
    HUAWEN(4, "华文行楷"),
    FANGZHENG(5, "方正舒体"),
    ;

    FontTypeEnum(int index, String description) {
        this.index = index;
        this.description = description;
    }

    private final int index;
    private final String description;


    public int getIndex() {
        return index;
    }

    public String getDescription() {
        return description;
    }
}
