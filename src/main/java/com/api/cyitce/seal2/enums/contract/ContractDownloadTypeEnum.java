package com.api.cyitce.seal2.enums.contract;

public enum ContractDownloadTypeEnum {

    /**
     * 签名字体
     */
    SINGLE(1, "单个"),
    MULT(2, "多个"),
    ;

    private final int index;
    private final String description;

    public int getIndex() {
        return index;
    }

    public String getDescription() {
        return description;
    }

    ContractDownloadTypeEnum(int index, String description) {
        this.index = index;
        this.description = description;
    }
}
