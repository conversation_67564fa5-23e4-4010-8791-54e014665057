package com.api.cyitce.seal2.enums.signet;

import java.util.HashSet;
import java.util.Set;

public enum ControlTypeEnum {

    /**
     * 控件类型
     */
    TEXT(1,"text","文本控件"),
    AUTOGRAPH(2,"autograph","签名控件"),
    SIGNET(3,"signet","印章控件"),
    DATE(4,"date","日期控件"),
    AGENT(5,"agent","经办人控件"),
    LEGAL_PERSON(6,"legal","法定代表人控件"),
    ;

    private final int index;
    private final String code;
    private final String description;

    public static Set<String> getCodeSet(){
        ControlTypeEnum[] values = ControlTypeEnum.values();
        Set<String> codes = new HashSet<>();
        for (ControlTypeEnum value : values) {
            codes.add(value.code);
        }
        return codes;
    }

    ControlTypeEnum(int index, String code, String description) {
        this.index = index;
        this.code = code;
        this.description = description;
    }

    public int getIndex() {
        return index;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
