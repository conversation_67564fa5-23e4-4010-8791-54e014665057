package com.api.cyitce.seal2.enums.yunxi;

/**
 * 统一管理印管平台的所有接口标识 (apiKey)
 */
public enum YxygApiKey {

    // 系统管理 (YXYG01)
    SYNC_ORGANIZATION_INCREMENTAL("YXYG01_07", "增量同步组织"), //
    SYNC_ORGANIZATION_FULL("YXYG01_01", "同步组织(全量)"),
    SYNC_USER_INCREMENTAL("YXYG01_08", "增量同步用户"),
    DELETE_ORGANIZATION("YXYG01_02", "删除组织"),
    SYNC_PERSONNEL("YXYG01_03", "同步人员"),
    DELETE_PERSONNEL("YXYG01_04", "删除人员"),
    LOGIN_VALIDATE("YXYG01_05", "登录验证"),
    AUTH_VALIDATE("YXYG01_06", "鉴权验证"),

    // 设备管理 (YXYG02)
    CREATE_SEAL("YXYG02_02", "创建印章"),
    USE_SEAL_BY_SN("YXYG02_03", "sn号用印"),
    INSTALL_SEAL("YXYG02_04", "安装印章"),
    SEARCH_WIFI("YXYG02_12", "搜索WiFi"),
    SET_WIFI("YXYG02_05", "设置WiFi"),
    TOGGLE_FINGERPRINT_MODE("YXYG02_06", "启停指纹模式"),
    SET_FINGERPRINT("YXYG02_07", "设置指纹"),
    REMOTE_LOCK_UNLOCK("YXYG02_08", "远程解锁(锁定)"),
    SET_SLEEP_MODE("YXYG02_09", "设置休眠"),
    TOGGLE_CAMERA("YXYG02_10", "开关摄像头"),
    SET_BLUETOOTH("YXYG02_11", "设置蓝牙"),
    SET_GEO_FENCE("YXYG02_13", "设置电子围栏"),

    // 用印管理 (YXYG03)
    PUSH_APPLICATION("YXYG03_01", "推送申请单"),
    CLOSE_APPLICATION("YXYG03_02", "关闭申请单"),
    USE_SEAL_WITH_APPLICATION("YXYG03_03", "申请单用印"),
    GET_AUTHORIZATION_CODE("YXYG03_04", "获取授权码"),
    GET_PHYSICAL_SEAL_AUTHORIZATION_CODE("YXYG03_23", "获取授权码(实体章)"),
    QUERY_APPLICATION_FILE_TYPES("YXYG03_05", "查询申请文件类型"),
    GET_APPLICATION_USAGE_COUNT("YXYG03_06", "申请单用印次数"),
    CREATE_APPLICATION("YXYG03_07", "创建申请单"),
    CREATE_APPLICATION_WITH_BASE64("YXYG03_25", "创建申请单(附件为Base64)"),
    MANAGE_APPLICATION_FILE_TYPES("YXYG03_08", "增删申请文件类型"),
    SEAL_USAGE_ABNORMALITY_NOTIFICATION("YXYG03_09", "用印异常通知"),


    // 数据查询 (YXYG04)
    GET_DEVICE_LIST("YXYG04_01", "设备列表"),
    GET_SEAL_USAGE_RECORDS("YXYG04_02", "用印记录"),
    GET_GEO_FENCE_LIST("YXYG04_07", "电子围栏列表"),
    GET_FACE_IMAGE("YXYG04_09", "人脸图片"),
    GET_SEAL_USAGE_VIDEO("YXYG04_10", "用印视频");

    // 只保留设备同步所需的ApiKey

    private final String key;
    private final String description;

    /**
     * 枚举的构造函数
     *
     * @param key         接口的实际标识字符串
     * @param description 接口的中文描述
     */
    YxygApiKey(String key, String description) {
        this.key = key;
        this.description = description;
    }

    /**
     * 获取接口标识字符串
     *
     * @return apiKey, 例如 "YXYG01_01"
     */
    public String getKey() {
        return key;
    }

    /**
     * 获取接口的描述信息
     *
     * @return 描述, 例如 "同步组织"
     */
    public String getDescription() {
        return description;
    }

    /**
     * 重写 toString 方法，使其在需要字符串的上下文中直接返回 key
     *
     * @return apiKey
     */
    @Override
    public String toString() {
        return this.key;
    }
}