package com.api.cyitce.seal2.enums.account;

public enum UserTypeEnum {
    /**
     * 用户类型
     */
    PERSON(1, "个人用户",""),
    ENTERPRISE(2, "企业用户","ET_PE"),
    PER_ENTERPRISE(3, "个体工商户用户","ET_SE"),
    ET_OU(4, "政府机构/事业单位","ET_OU"),
    ;

    private final int index;
    private final String description;
    private final String type;

    UserTypeEnum(int index, String description, String type) {
        this.index = index;
        this.description = description;
        this.type = type;
    }

    public int getIndex() {
        return index;
    }

    public String getDescription() {
        return description;
    }

    public String getType() {
        return type;
    }
}
