package com.api.cyitce.seal2.enums;

public enum DocumentInfo {
    /**
     * 签字后附件
     */
    SIGNED_DOCUMENT(",,454"),
    /**
     * 签章后附件
     */
    SEALED_DOCUMENT(",,455"),
    /**
     * 可下载区域
     */
    DOWNLOADABLE_CONTENT(",,456");

    private final String value;

    DocumentInfo(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}