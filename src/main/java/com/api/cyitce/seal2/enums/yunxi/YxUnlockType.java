package com.api.cyitce.seal2.enums.yunxi;

/**
 * 解锁方式枚举
 * 用于 YxApplicationFileType 实体类中的 unlockType 字段。
 */
public enum YxUnlockType {

    /**
     * 指纹解锁 (默认)
     */
    DEFAULT(0, "default"),

    /**
     * 普通解锁
     */
    COMMON(1, "common"),

    /**
     * OCR比对解锁
     */
    OCR(2, "ocr"),

    /**
     * 二维码比对解锁
     */
    QRCODE(3, "qrcode");

    private final int id;
    private final String code;

    YxUnlockType(int id, String code) {
        this.id = id;
        this.code = code;
    }

    /**
     * 获取枚举的数字ID (从0开始)
     *
     * @return id
     */
    public int getId() {
        return id;
    }

    /**
     * 获取枚举对应的字符串标识 (例如："default", "common")
     *
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 【核心方法】通过 id 获取对应的枚举实例。
     *
     * @param id 要查找的数字ID
     * @return 对应的 YxUnlockType 枚举实例，如果找不到则返回 null (或可以抛出异常)。
     */
    public static YxUnlockType fromId(int id) {
        for (YxUnlockType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return YxUnlockType.DEFAULT; // 或者可以返回默认值 YxUnlockType.DEFAULT
    }

    /**
     * 【可选】通过 code 获取对应的枚举实例。
     *
     * @param code 要查找的字符串标识
     * @return 对应的 YxUnlockType 枚举实例，如果找不到则返回 null。
     */
    public static YxUnlockType fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (YxUnlockType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}