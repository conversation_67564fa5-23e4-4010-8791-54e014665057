package com.api.cyitce.seal2.enums.contract;

/**
 * @ClassName: LastFileTypeEnum
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:36
 * @Version: 1.0
 */
public enum LastFileTypeEnum {

    /**
     * 最终文件类型
     */
    PDF(1,"pdf","pdf"),
    OFD(2,"ofd","ofd")
    ;

    private final int index;
    private final String name;
    private final String description;

    LastFileTypeEnum(int i, String ofd, String ofd1) {
        this.index = i;
        this.name = ofd;
        this.description = ofd1;
    }

    public int getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}