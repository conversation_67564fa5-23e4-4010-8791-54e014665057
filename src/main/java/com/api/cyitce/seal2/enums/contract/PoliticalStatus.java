package com.api.cyitce.seal2.enums.contract;

public enum PoliticalStatus {
    MASSES(4, "群众"),
    CPC_MEMBER(1, "中共党员"),
    CPC_PROBATIONARY_MEMBER(6, "中共预备党员"),
    COMMUNIST_YOUTH_LEAGUE_MEMBER(2, "共青团员"),
    MINGUO_DANG_MEMBER(7, "民革党员"),
    CHINA_DEMOCRATIC_LEAGUE_MEMBER(8, "民盟盟员"),
    CHINA_DEMOCRATIC_NATIONAL_CONSTRUCTION_ASSOCIATION_MEMBER(9, "民建会员"),
    CHINA_ASSOCIATION_FOR_PROMOTION_OF_DEMOCRACY_MEMBER(10, "民进会员"),
    CHINA_PEOPLES_LIBERATION_ARMY_MEDICAL_PARTY_MEMBER(11, "农工党党员"),
    CHINA_ZHI_GONG_PARTY_MEMBER(12, "致公党党员"),
    JIU_SAN_SOCIETY_MEMBER(13, "九三学社社员"),
    TAIWAN_DEMOCRATIC_SELF_GOVERNMENT_LEAGUE_MEMBER(14, "台盟盟员"),
    INDEPENDENT_PERSON(15, "无党派人士"),
    OTHER(5, "其他");

    private final int id;
    private final String value;

    PoliticalStatus(int id, String value) {
        this.id = id;
        this.value = value;
    }

    public int getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    public static String getValueById(int id) {
        for (PoliticalStatus status : PoliticalStatus.values()) {
            if (status.getId() == id) {
                return status.getValue();
            }
        }
        return null;
    }
}    