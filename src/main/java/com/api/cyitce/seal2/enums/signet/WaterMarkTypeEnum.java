package com.api.cyitce.seal2.enums.signet;

public enum WaterMarkTypeEnum {
    /**
     * 水印类型
     */
    TEXT_MARK(1,"文本水印"),
    IMAGE_MARK(2,"图片水印"),
    QRCODE_MARK(3,"二维码水印"),
    TEXT_QRCODE_MARK(4,"文本和二维码水印")
    ;

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    WaterMarkTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
