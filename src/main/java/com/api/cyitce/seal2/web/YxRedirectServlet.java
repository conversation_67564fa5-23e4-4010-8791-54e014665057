package com.api.cyitce.seal2.web;

import com.api.cyitce.seal2.config.ThirdMappConfig;
import com.api.cyitce.seal2.config.YunxiConfig;


import com.engine.edc.biz.action.result.Result;

import com.weaver.general.BaseBean;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;

import static com.api.cyitce.seal2.util.AesUtils.encrypt;

@Path("/yx")
public class YxRedirectServlet extends HttpServlet {
    // private static String url = "https://openapi-fat3.fenbeijinfu.com/openapi/auth/web/v1/dispense";//测试服获取token
//    private static String appId = "67ce46ccf034e73305b6907d";
//    private static String appKey = "67ce473651b904288f70dcc7";


    private final HttpServletRequest request;
    private final HttpServletResponse response;


    public YxRedirectServlet(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        this.request = request;
        this.response = response;
        // this.user = HrmUserVarify.getUser(request, response);;
    }

    @GET
    @Path("login/silent")
    @Produces({"application/json"})
    public Result doPostToken(@QueryParam("id") String id) throws Exception {
        try {
            BaseBean baseBean = new BaseBean();
            baseBean.writeLog("YxRedirectServlet--------->拿到用户id  : " + id);
            // User user = HrmUserVarify.getUser(request, response);// 获取用户信息
            YunxiConfig config = new YunxiConfig();
            ThirdMappConfig mappConfig = new ThirdMappConfig();
            baseBean.writeLog("YxRedirectServlet--------->配置文件 config : " + config.toString());
            String baseUrl = mappConfig.getIpUrl() + "/h5/#/pages/tabBar/index?userId=%s&_tenant=%s";
            int userId = Integer.parseInt(id);
            String tenant = config.getTenant();
            String encrypt = encrypt(String.valueOf(userId), config.getAppSecret());
            baseBean.writeLog("YxRedirectServlet--------->userId : " + userId + " 加密前字符串: " + userId + " 加密后字符串: " + encrypt);
            // 填充占位符
            String redirectUrl = String.format(baseUrl, encrypt, tenant);
            baseBean.writeLog("YxRedirectServlet--------->跳转地址: " + redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }


}