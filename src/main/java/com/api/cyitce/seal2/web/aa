
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KZB773GP5L4', '0025', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 3, 'L021KZCZ85C5QCK', '6', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 3, 'L021KZCZA4D9SCA', '16', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KOILI8KJK3Q', '2947', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KI1KC53R06O', '1985', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KYQO3LXJ7V1', '5030', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KZB773GP5L4', '0025', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KPAGM1Z9T00', '3084', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [uf_seal_third_test] ( [requestId], [type], [sealid], [oaid], [requestid1], [htqzrsl], [htyqs], [qzrlj], [mxbid], [yymxb], [status]) VALUES ( NULL, 2, 'L021KUSFO1QAYCF', '0037', NULL, NULL, NULL, NULL, NULL, NULL, NULL);



import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowBase;
import java.util.List;

/**
 * 泛微E9流程退回工具类
 * 支持单个/批量流程退回到指定节点
 */
public class WorkflowReturnTool extends BaseBean {

    /**
     * 批量退回流程到指定节点
     * @param requestIds 流程实例ID列表
     * @param targetNodeId 目标节点ID
     * @param operatorId 操作人ID
     * @return 处理结果
     */
    public boolean batchReturnToNode(List<String> requestIds, String targetNodeId, String operatorId) {
        if (requestIds == null || requestIds.isEmpty()) {
            writeLog("流程ID列表不能为空");
            return false;
        }

        boolean allSuccess = true;
        for (String requestId : requestIds) {
            boolean success = returnToNode(requestId, targetNodeId, operatorId);
            if (!success) {
                writeLog("流程" + requestId + "退回失败");
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    /**
     * 将单个流程退回到指定节点
     * @param requestId 流程实例ID
     * @param targetNodeId 目标节点ID
     * @param operatorId 操作人ID
     * @return 操作是否成功
     */
    public boolean returnToNode(String requestId, String targetNodeId, String operatorId) {
        try {
            // 1. 验证流程和节点是否存在
            if (!validateRequestAndNode(requestId, targetNodeId)) {
                writeLog("流程" + requestId + "或节点" + targetNodeId + "不存在");
                return false;
            }

            // 2. 获取流程管理器实例
            RequestManager requestManager = new RequestManager();
            requestManager.setRequestid(requestId);

            // 3. 设置退回参数
            requestManager.setOperatorid(operatorId);
            requestManager.setIsbill(1); // 标记为流程操作

            // 4. 执行退回操作
            // 注意：退回操作需要特殊权限，确保操作人有相应权限
            boolean result = requestManager.backToNode(targetNodeId, "系统自动退回", "");

            // 5. 记录操作日志
            if (result) {
                writeLog("流程" + requestId + "成功退回到节点" + targetNodeId);
                recordReturnLog(requestId, targetNodeId, operatorId, "成功");
            } else {
                writeLog("流程" + requestId + "退回节点" + targetNodeId + "失败");
                recordReturnLog(requestId, targetNodeId, operatorId, "失败");
            }
            return result;
        } catch (Exception e) {
            writeLog("流程" + requestId + "退回发生异常：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 验证流程实例和节点是否有效
     */
    private boolean validateRequestAndNode(String requestId, String nodeId) {
        RecordSet rs = new RecordSet();

        // 验证流程实例是否存在
        rs.executeQuery("SELECT 1 FROM workflow_requestbase WHERE requestid = ?", requestId);
        if (!rs.next()) {
            return false;
        }

        // 获取流程ID
        rs.executeQuery("SELECT workflowid FROM workflow_requestbase WHERE requestid = ?", requestId);
        rs.next();
        String workflowId = rs.getString("workflowid");

        // 验证节点是否属于该流程
        rs.executeQuery("SELECT 1 FROM workflow_node WHERE id = ? AND workflowid = ?", nodeId, workflowId);
        return rs.next();
    }

    /**
     * 记录退回操作日志
     */
    private void recordReturnLog(String requestId, String nodeId, String operatorId, String status) {
        RecordSet rs = new RecordSet();
        String sql = "INSERT INTO workflow_return_log (requestid, targetnodeid, operatorid, operatetime, status) " +
                    "VALUES (?, ?, ?, GETDATE(), ?)";
        rs.executeUpdate(sql, requestId, nodeId, operatorId, status);
    }
}