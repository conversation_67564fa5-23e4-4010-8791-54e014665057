package com.api.cyitce.seal3.constant;

/**
 * @ClassName: ApiUrl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  11:12
 * @Version: 1.0
 */
public class ESealApiUrl {
    /**
     * 合同接口
     */
    public static final String CONTRACT_CREATE_BY_FILE = "/contract/createByFile";
    public static final String CONTRACT_ADD_SIGNER_BY_FILE = "/contract/addSignerByFile";
    public static final String CONTRACT_CREATE_BY_TEMPLATE = "/contract/createByTemplate";
    public static final String CONTRACT_ADD_SIGNER_BY_TEMPLATE = "/contract/addSignerByTemplate";
    public static final String CONTRACT_SIGN_BY_FILE = "/contract/signByFile";
    public static final String START_SIGN_BY_FILE = "/pageSign/startSignByFile";
    public static final String START_SIGN_BY_FILE_TEMPLATE = "/pageSign/startSignByTemplateFile";
    public static final String START_SIGN_BY_FILE_MUL_CONTRACT = "/pageSign/startSignMulContract";
    public static final String CONTRACT_UPLOAD_FILE = "/contract/uploadFile";
    public static final String CONTRACT_DOWNLOAD = "/contract/downloadContract";
    public static final String CONTRACT_SEARCH = "/contract/search";
    public static final String CONTRACT_OPERATE_CONTRACT_STATUS = "/contract/operateContractStatus";
    public static final String CONTRACT_URGE_SIGN = "/contract/urgeSign";
    public static final String CONTRACT_ADD_CC = "/contract/addContractCc";
    public static final String CONTRACT_VERIFY = "/contract/verifyContract";
    public static final String CONTRACT_DELETE_CONTRACT = "/contract/deleteContract";
    public static final String CONTRACT_LAUNCH_NULLIFY = "/contract/launchNullify";
    public static final String CONTRACT_CONFIRM_NULLIFY = "/contract/confirmNullify";
    public static final String CONTRACT_GET_CONFIRM_NULLIFY_URL = "/contract/getConfirmNullifyUrl";
    public static final String INTERNALVERIFICATION_URL = "/contract/internalVerification";
    public static final String VERIFICATIONINFO_URL = "/contract/verificationInfo";
    public static final String BATCH_SIGN = "/contract/batchSign";

    /**
     * 用户接口
     */
    public static final String CREATE_PERSON = "/user/create";
    public static final String UPDATE_PERSON = "/user/update";
    public static final String INITIATION = "/upgrade/initiation";
    public static final String USER_DETAILS = "/user/userDetails";
    public static final String USER_LIST = "/user/pageQuery";
    public static final String JOIN_ENTERPRISE = "/user/joinEnterprise";
    public static final String REMOVE_ENTERPRISE_USER = "/user/removeUserEnterprise";
    public static final String RESET_PASSWORD = "/user/resetPassword";
    public static final String DELETE = "/user/delete";
    public static final String USER_SYNCDATE = "/user/syncData";

    /**
     * 2、企业接口
     */
    public static final String CREATE_ENTERPRISE = "/enterprise/create";
    public static final String UPDATE_ENTERPRISE = "/enterprise/update";
    public static final String ADD_OR_MODIFY_ADMIN = "/enterprise/addOrModifyAdmin";
    public static final String ADD_SUB_COMPANY = "/enterprise/addSubCompany";
    public static final String ENTERPRISE_PAGE = "/enterprise/pageQuery";
    public static final String ENTERPRISE_DETAILS = "/enterprise/orgDetails";
    public static final String CHANGE_LEGAL = "/enterprise/changeLegal";
    public static final String BIND_LEGAL = "/enterprise/bindLegal";

    /**
     * 短信接口
     **/
    public static final String SEND_SMS = "/smsService/sendingSms";
    public static final String VOICE_SMS= "/smsService/voiceSms";


    /**
     * 8、模板接口
     */
    public static final String TEMPLATE_CREATE = "/template/createTemplate";
    public static final String TEMPLATE_SET_SIGN_CONTROLS = "/template/setSignControls";
    public static final String TEMPLATE_UPLOAD_FILE = "/template/uploadFile";
    public static final String TEMPLATE_STATUS = "/template/templateStatus";
    public static final String TEMPLATE_DETAILS = "/template/templateDetails";
    public static final String TEMPLATE_UPDATE = "/template/updateTemplate";
}