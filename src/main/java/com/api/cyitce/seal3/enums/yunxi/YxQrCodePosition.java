package com.api.cyitce.seal3.enums.yunxi;

/**
 * 二维码赋码位置枚举
 * 用于 YxApplicationFileType 实体类中的 qrCodePosition 字段。
 */
public enum YxQrCodePosition {

    /**
     * 左上
     */
    LEFT_UP(0, "left_up"),

    /**
     * 右上
     */
    RIGHT_UP(1, "right_up"),

    /**
     * 正上 (默认)
     */
    UP(2, "up"),

    /**
     * 左下
     */
    LEFT_DOWN(3, "left_down"),

    /**
     * 右下
     */
    RIGHT_DOWN(4, "right_down"),

    /**
     * 正下
     */
    DOWN(5, "down");

    private final int id;
    private final String code;

    YxQrCodePosition(int id, String code) {
        this.id = id;
        this.code = code;
    }

    /**
     * 获取枚举的数字ID (从0开始)
     * @return id
     */
    public int getId() {
        return id;
    }

    /**
     * 获取枚举对应的字符串标识 (例如："left_up", "up")
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 通过 id 获取对应的枚举实例。
     * @param id 要查找的数字ID
     * @return 对应的 YxQrCodePosition 枚举实例，如果找不到则返回 null。
     */
    public static YxQrCodePosition fromId(int id) {
        for (YxQrCodePosition position : values()) {
            if (position.getId() == id) {
                return position;
            }
        }
        return null;
    }

    /**
     * 通过 code 获取对应的枚举实例。
     * @param code 要查找的字符串标识
     * @return 对应的 YxQrCodePosition 枚举实例，如果找不到则返回 null。
     */
    public static YxQrCodePosition fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (YxQrCodePosition position : values()) {
            if (position.getCode().equalsIgnoreCase(code)) {
                return position;
            }
        }
        return null;
    }
}