package com.api.cyitce.seal3.enums.enterprise;

public enum EnterpriseTypeEnum {

    /**
     * 企业类型
     */
    ET_PE(0, "企业","ET_PE"),
    ET_SE(1, "个体工商户","ET_SE"),
    ET_OU(2, "政府机构/事业单位","ET_OU"),
    ;

    private final int index;
    private final String description;
    private final String code;

    EnterpriseTypeEnum(int index, String description,String code) {
        this.index = index;
        this.description = description;
        this.code = code;
    }

    public int getIndex() {
        return index;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }
}
