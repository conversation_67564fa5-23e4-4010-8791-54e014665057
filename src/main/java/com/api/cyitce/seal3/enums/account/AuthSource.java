package com.api.cyitce.seal3.enums.account;

/**
 * @description: 认证来源
 * @author: lijianpan
 **/
public enum AuthSource {

    CUSTOMER(true,"客户认证"),
    ITRUSCLOUD(false,"天威认证");

    private boolean code;
    private String desc;

    AuthSource(boolean code,String desc){
        this.code = code;
    }

    public boolean getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
