package com.api.cyitce.seal3.enums.signet;

public enum WaterMarkOpacityEnum {

    /**
     * 是否透明
     */
    TRUE(0f, "透明"),
    FALSE(1f, "不透明"),
    ;

    private final Float code;
    private final String name;

    WaterMarkOpacityEnum(Float code, String name) {
        this.code = code;
        this.name = name;
    }

    public Float getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
