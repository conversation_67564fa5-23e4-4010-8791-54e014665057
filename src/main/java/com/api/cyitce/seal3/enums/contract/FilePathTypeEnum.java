package com.api.cyitce.seal3.enums.contract;

/**
 * @ClassName: FilePathTypeEnum
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:31
 * @Version: 1.0
 */

public enum FilePathTypeEnum {

    /**
     * 路径类型
     */
    FTP(1, "ftp路径"),
    HTTP(2, "http路径")
    ;

    private final int index;
    private final String description;

    FilePathTypeEnum(int index, String description) {
        this.index = index;
        this.description = description;
    }

    public int getIndex() {
        return index;
    }

    public String getDescription() {
        return description;
    }
}