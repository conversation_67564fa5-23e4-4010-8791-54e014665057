package com.api.cyitce.seal3.enums.seal;

/**
 * @ClassName: 印章形式
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  14:16
 * @Version: 1.0
 */
public enum SealFromEnum {

    /**
     * 印章形式
     */
    E_SEAL(0,"电子印章"),
    PHYSICAL_SEAL(1,"物理印章"),
    OUT_SEAL(2,"便携式印章");

    private final int value;
    private final String name;

    SealFromEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}