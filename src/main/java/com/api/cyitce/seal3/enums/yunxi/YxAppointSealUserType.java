package com.api.cyitce.seal3.enums.yunxi;

/**
 * 专人盖章类型枚举
 * 用于 YxApplicationFileType 实体类中的 appointSealUserType 字段。
 */
public enum YxAppointSealUserType {

    /**
     * 所有人 (默认)
     */
    ALL(0, "all"),

    /**
     * 管章人
     */
    KEEPER(1, "keeper"),

    /**
     * 指定用户
     */
    USER(2, "user");

    private final int id;
    private final String code;

    YxAppointSealUserType(int id, String code) {
        this.id = id;
        this.code = code;
    }

    /**
     * 获取枚举的数字ID (从0开始)
     * @return id
     */
    public int getId() {
        return id;
    }

    /**
     * 获取枚举对应的字符串标识 (例如："all", "keeper")
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 通过 id 获取对应的枚举实例。
     * @param id 要查找的数字ID
     * @return 对应的 YxAppointSealUserType 枚举实例，如果找不到则返回 null。
     */
    public static YxAppointSealUserType fromId(int id) {
        for (YxAppointSealUserType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return null;
    }

    /**
     * 通过 code 获取对应的枚举实例。
     * @param code 要查找的字符串标识
     * @return 对应的 YxAppointSealUserType 枚举实例，如果找不到则返回 null。
     */
    public static YxAppointSealUserType fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (YxAppointSealUserType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}