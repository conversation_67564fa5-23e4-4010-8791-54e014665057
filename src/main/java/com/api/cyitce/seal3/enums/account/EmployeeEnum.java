package com.api.cyitce.seal3.enums.account;

public enum EmployeeEnum {
    CHONGQING_XINKE_COMMUNICATION_ENGINEERING_CO_LTD(6, "张三", 1001, 6),
    EMPLOYEE_2(2, "李四", 1002, 11),
    EMPLOYEE_3(3, "王五", 1003, 13);

    private final int id;
    private final String name;
    private final int userId;
    private final int companyId;

    EmployeeEnum(int id, String name, int userId, int companyId) {
        this.id = id;
        this.name = name;
        this.userId = userId;
        this.companyId = companyId;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getUserId() {
        return userId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public static EmployeeEnum getById(int id) {
        for (EmployeeEnum employee : values()) {
            if (employee.id == id) {
                return employee;
            }
        }
        return null;
    }
}