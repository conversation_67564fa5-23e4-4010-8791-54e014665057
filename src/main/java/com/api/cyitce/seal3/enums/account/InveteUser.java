package com.api.cyitce.seal3.enums.account;

/**
 * @description: 认证来源
 * @author: lijianpan
 **/
public enum InveteUser {

    ADMIN("L021JV7U36P59SE "),
    COMPANY("M011JRGICPKTWF7"),
    <PERSON>K_ADMIN("L021KI1KC53R06O"),
    XK_COMPANY("M011GYSR00YS8WP"),
    HL_ADMIN("L021KI1KC53R06O"),
    HL_COMPANY("M011GYSR00YS8WP");
    private String code;

    InveteUser(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
