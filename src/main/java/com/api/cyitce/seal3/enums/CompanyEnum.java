package com.api.cyitce.seal3.enums;

public enum CompanyEnum {
    CHONGQING_XINKE_COMMUNICATION_ENGINEERING_CO_LTD(6, "重庆信科通信工程有限公司", "重庆市渝北区黄山大道中段53号双鱼座A座17楼"),
    HRM_INPUT(8, "HrmInput", ""),
    THIRD_PARTY_BRANCH_AND_TRADE_UNION(10, "第三党支部&工会", ""),
    CHONGQING_ZHONGYOU_XINKE_GROUP_CO_LTD(11, "重庆中邮信科集团股份有限公司", "重庆市渝北区黄山大道中段53号双鱼座A座16楼"),
    CHONGQING_XINDE_DIGITAL_TECHNOLOGY_CO_LTD(12, "重庆信德数科企业管理咨询有限公司", ""),
    CHONGQING_XINKE_DESIGN_CO_LTD(13, "重庆信科设计有限公司", ""),
    CHONGQING_XINKE_COMMUNICATION_CONSTRUCTION_SUPERVISION_CONSULTING_CO_LTD(14, "重庆信科通信建设监理咨询有限公司", "重庆市两江新区黄山大道中段53号双鱼座A座16楼"),
    CHONGQING_HUALAI_INFORMATION_TECHNOLOGY_CO_LTD(16, "重庆华来信息科技有限公司", ""),
    CHONGQING_CHONGYOU_XINKE_COMMUNICATION_TECHNOLOGY_CO_LTD(20, "重庆重邮信科通信技术有限公司", ""),
    CHONGQING_SHUTENG_CLOUD_COMPUTING_CO_LTD(24, "重庆数藤云计算有限公司", "重庆市渝北区黄山大道中段53号双鱼座A座16楼");

    private final int id;
    private final String name;
    private final String address;

    CompanyEnum(int id, String name, String address) {
        this.id = id;
        this.name = name;
        this.address = address;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }

    public static CompanyEnum getById(int id) {
        for (CompanyEnum company : values()) {
            if (company.id == id) {
                return company;
            }
        }
        return null;
    }

    public static CompanyEnum getByName(String name) {
        for (CompanyEnum company : values()) {
            if (company.name.equals(name)) {
                return company;
            }
        }
        return null;
    }

    public static CompanyEnum getByAddress(String address) {
        for (CompanyEnum company : values()) {
            if (address != null && address.equals(company.address)) {
                return company;
            }
        }
        return null;
    }

    public static String getAddressById(int id) {
        CompanyEnum company = getById(id);
        return company != null ? company.getAddress() : "";
    }
}