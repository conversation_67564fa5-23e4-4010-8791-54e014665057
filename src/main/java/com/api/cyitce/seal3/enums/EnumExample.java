package com.api.cyitce.seal3.enums;

public class EnumExample {

    // 定义状态枚举类
    public enum WorkflowStatus {
        DRAFT(0, "草稿"),
        PENDING_SIGN(1, "待签署"),
        SIGNING(2, "签署中"),
        COMPLETED(3, "已完成"),
        EXPIRED(4, "已过期"),
        REVOKED(5, "已撤销"),
        REJECTED(6, "已拒签"),
        TERMINATED(11, "已解约");

        private final int id;
        private final String status;

        // 枚举构造函数
        WorkflowStatus(int id, String status) {
            this.id = id;
            this.status = status;
        }

        // 获取状态 ID
        public int getId() {
            return id;
        }

        // 获取状态描述
        public String getStatus() {
            return status;
        }

        // 根据 ID 获取对应的枚举实例
        public static WorkflowStatus getStatusById(int id) {
            for (WorkflowStatus status : values()) {
                if (status.getId() == id) {
                    return status;
                }
            }
            return null;
        }
    }


}