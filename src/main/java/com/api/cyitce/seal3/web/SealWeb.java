package com.api.cyitce.seal3.web;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.action.create.CreateContractAction;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.config.YunxiConfig;
import com.api.cyitce.seal3.config.eSeal.ESealConfig;
import com.api.cyitce.seal3.config.yunxi.ApiRequest;
import com.api.cyitce.seal3.enums.DocumentInfo;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal3.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal3.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal3.service.eSeal.impl.SignServiceImpl;
import com.api.cyitce.seal3.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal3.service.yunxi.CallbackService;
import com.api.cyitce.seal3.util.data.OaHrmUtils;
import com.api.cyitce.seal3.util.sql.SqlUtils;
import com.api.cyitce.seal3.vo.req.Cfzlc;
import com.api.cyitce.seal3.vo.req.SealUseCallBack;
import com.api.cyitce.seal3.vo.req.contract.addSignerByFile.SignFileApiVO;
import com.api.cyitce.seal3.vo.req.hrm.*;
import com.api.cyitce.seal3.vo.req.pagesign.AddContractSignerVO;
import com.api.cyitce.seal3.vo.req.pagesign.PageSignReq;
import com.api.cyitce.seal3.vo.req.updatefile.ContractCallback;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.api.cyitce.seal3.vo.resp.contract.ContractApiVO;
import com.api.cyitce.seal3.vo.resp.pagesign.PageSignResp;
import com.api.cyitce.seal3.vo.resp.template.DocumentApiVO;
import com.engine.common.util.ServiceUtil;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import com.obs.services.model.HttpMethodEnum;
import com.obs.services.model.TemporarySignatureRequest;
import com.obs.services.model.TemporarySignatureResponse;
import com.weaver.general.BaseBean;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.hrm.HrmUserVarify;

import weaver.hrm.User;
import weaver.mobile.webservices.workflow.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.api.cyitce.seal3.action.create.UserRealAuthAction.isStringExist;
import static com.api.cyitce.seal3.enums.EnumExample.WorkflowStatus.getStatusById;
import static com.api.cyitce.seal3.util.FileUtils.*;
import static com.api.cyitce.seal3.util.data.OaHrmUtils.getHrInfo;
import static com.api.cyitce.seal3.util.sql.SqlUtils.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/10 15:21
 * @describe oa路径/api/path
 */
@Path("/Seal/web3")
public class SealWeb extends BaseLog {

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @POST
    @Path("/yunxi/callback")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public String yunxiCallback(String record) {
        info("yunxi callback---------------------------->惠郎签署回调：");
        info("yunxi callback----------------------------> 回调数据" + record);
        YunxiConfig config = new YunxiConfig();
        CallbackService service = new CallbackService(config.getAppSecret());
        return service.handleCallback(record);
    }


    @GET
    @Path("/get/HW/url")
    @Produces(MediaType.TEXT_PLAIN)
    public Response getHWUrl(@QueryParam("docId") String docId) {
        if (!StringUtils.hasText(docId)) {
            return createJsonResponse(500, "附件id 为空", "");
        }
        Map<String, String> map = getDocIdInfoByDocId(5, docId, new ThirdMappConfig());
        if (map.isEmpty()) {
            return createJsonResponse(500, "未查询到对应数据的存储路径", "");
        }
        String cclj = map.get("cclj");
        if (!StringUtils.hasText(cclj)) {
            return createJsonResponse(500, "未查询到对应数据的存储路径,请联系管理员", "");
        }
        Map<String, String> map1 = splitFilePath(cclj);
        String huUrl = "";
        try {
            huUrl = getHUUrl(map1.get("bucket"), map.get("path"));
        } catch (Exception e) {
            return createJsonResponse(500, "下载路径生成出错，请联系管理员", "");
        }
        return createJsonResponse(200, "请求成功", huUrl);
    }

    @POST
    @Path("/Huilang/callback")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response huilangCallback(SealUseCallBack callback) {
        info("Huilang callback---------------------------->惠郎签署回调：");
        info("Huilang callback---------------------------->" + JSONUtil.toJsonStr(callback));
        ThirdMappConfig config = new ThirdMappConfig();
        String md5 = callback.getMd5();
        if (!StringUtils.hasText(md5)) {
            info("惠郎印章回调-----------------------> md5:" + md5);
            return createJsonResponse(401, "无权访问", "");
        } else {
            if (!md5.equals(encryptToMD5UpperCase(config.getCheckMD5()))) {
                info("惠郎印章回调-----------------------> config.getCheckMD5():" + config.getCheckMD5());
                info("惠郎印章回调-----------------------> md5:" + md5);
                return createJsonResponse(401, "无权访问", "");
            }
        }
        String fileUrl = callback.getSealUseCallBackFileUrl();
        if (StringUtils.hasText(fileUrl)) {
            // 存在值 则是回调
            // 存入对应 存储路径
            changecclj(5, callback.getApplyId(), new ThirdMappConfig(), fileUrl);
            // Map<String, String> map = splitFilePath(fileUrl);
            return createJsonResponse(200, "请求成功", "");

        }
        // String applyId = callback.getApplyId();
        // Map<String, String> map = getDocIdInfo(5, applyId, new ThirdMappConfig());
        // info("惠郎印章回调-----------------------> 拿到map信息:" + map.toString());
        // String requestId = map.get("requestid1");
        // String tableName = getFormName(requestId);
        //
        // updatestlj(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), fileUrl);
        return createJsonResponse(200, "请求成功", "");
    }

    /**
     * /Seal/web/send/sms
     * 发送签名,短信验证码
     */
    @GET
    @Path("/send/sms")
    @Produces(MediaType.TEXT_PLAIN)
    public Response sendToSms(@QueryParam("userId") Integer userId, @QueryParam("docId") String docId, @QueryParam("companyId") String companyId, @QueryParam(value = "flag") int flag, @Context HttpServletRequest request, @Context HttpServletResponse response) {
        info("sendToSms ---------------------------> 进入发送签名，接受到的参数: userId = " + userId + ", docId = " + docId + "flag=" + flag);

        int code = 200;
        String message = "成功";
        String data = "";
        String tag = "xynr";
        User user1 = null;
        int temp = userId;
        if (request != null && response != null) {
            user1 = HrmUserVarify.getUser(request, response);
            info("sendToSms ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user1));
            info("sendToSms ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user1.getUID()));
            info("sendToSms ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user1.getUsername()));
            userId = user1.getUID();
        }
        info("sendToSms ---------------------------> 进入发送签名，接受到的参数: userId = " + userId + ", docId = " + docId);
        if (flag == 1) {
            info("sendToSms --------------------------->temp id:" + temp);
            userId = temp;
        }
        if (userId == null || !StringUtils.hasText(docId)) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("请求参数错误").build();
        }
        // 校验当前人是否已签署该合同
        Map<String, String> map = getqsrInfo(0, Base64.encode(docId), new ThirdMappConfig());
        info("sendToSms ---------------------------> getqsrInfo:" + map.toString());
        if (map.isEmpty()) {
            return createJsonResponse(500, "合同查询失败,请联系管理员", "");
        }
        String qzrlj = findDomainByIds(map.get("qzrlj"), String.valueOf(userId));
        if (StringUtils.hasText(qzrlj)) {
            return createJsonResponse(200, "已发送短信链接", qzrlj);
        }
        // 判断签字人是否在天威云中认证


        // 这里发送短信 进行页面 签署
        // 实名认证
        info("sendToSms ---------------------------> 执行实名认证");

        // BaseResp<JSONObject> resp2 = realCreaterAccount(userId, 0);
        // if (!resp2.isStatus()) {
        //     return createJsonResponse(500, "个人实名认证失败,请联系管理员", "");
        // }
        //
        // info("sendToSms ---------------------------> 执行实名认证成功");
        RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
        info("sendToSms ---------------------------> realUser 用户信息: " + realUser.toString());
        ESealConfig config = new ESealConfig();
        String userCode = OaHrmUtils.getworkCode(userId);
        HrmResourceApiReq info = OaHrmUtils.getAllInfo(userId);
        BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
        String sendUserId = resp4.getData();
        if (!resp4.isStatus()) {
            info("sendToSms ---------------------------> 查询用户信息失败，去天威云查询");
            Map<String, String> hrInfo = getHrInfo(userId);
            if (hrInfo.isEmpty()) {
                return createJsonResponse(500, " 未查询到对应用户手机号，请联系管理员", "");
            }
            UserListReq userListReq = new UserListReq();
            userListReq.setPhone(hrInfo.get("phone"));
            BaseResp<UserListWrapper> phone = new CreatUserIntegration().queryUserList(userListReq);
            if (!phone.isStatus()) {
                return createJsonResponse(500, "查询用户请求错误，请联系管理员", "");
            }
            List<UserInfo> userList = phone.getData().getUserList();
            if (CollectionUtils.isEmpty(userList)) {
                // return createJsonResponse(500, "你尚未在天威云实名，请使用手机号[" + hrInfo.get("phone") + "] 去实名。", "");
                // return createJsonResponse(500, "您尚未在公司指定电子平台（天威诚信）实名，请使用手机号（" + hrInfo.get("phone") + "）前往天威诚信官网http://139.159.242.69:8070/#/signMountApp/register注册", "");
                // return createJsonResponse(500, "您尚未在公司指定电子平台（天威诚信）实名，请使用预留在北森人力系统上的手机号（" + hrInfo.get("phone") + "）前往天威诚信官网http://139.159.242.69:8070/#/signMountApp/register注册,", "");
                return createJsonResponse(500, "首次签署请先前往公司指定的电子平台“天威诚信”官网：http://139.159.242.69:8070/#/signMountApp/register并使用预留在北森人力系统上的手机号" + hrInfo.get("phone") + "完成实名注册，方可进行后续操作。", "");
            }
            // 存在 存入对应信息
            for (UserInfo user : userList) {
                sendUserId = user.getUserId();
                BaseResp<Void> voidBaseResp = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(sendUserId, userCode, 2);
                if (!voidBaseResp.isStatus()) {
                    info("sendToSms ---------------------------> 存入三方映射信息失败  用户id: " + userCode + "  三方id: " + sendUserId);
                    return createJsonResponse(500, "存入三方映射信息失败,请联系管理员。", "");
                }
            }
            // return createJsonResponse(500, " 查询用户 对应三方id 失败  不存在", " ");
        }

        info("sendToSms ---------------------------> userCode: " + userCode);
        SignServiceImpl ssi = new SignServiceImpl();
        try {
            PageSignReq psr = new PageSignReq();

            // BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
            //
            // if (!resp3.isStatus()) {
            //     info("sendToSms ---------------------------> resp3 error 未查询到对应文档: ");
            //     // 返回适当的响应或记录错误
            //     return createJsonResponse(500, " 未查询到对应文档", " ");
            // }
            String contractId = map.get("sealid");
            if (!StringUtils.hasText(contractId)) {
                return createJsonResponse(500, "未查询到对应的合同数据,请联系管理员", "");
            }

            psr.setContractId(Long.valueOf(contractId));
            info("sendToSms ---------------------------> 合同id: " + contractId);

            AddContractSignerVO acsv = new AddContractSignerVO();

            BaseResp<String> resp5 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(companyId, 3);

            if (!resp4.isStatus()) {
                return createJsonResponse(500, "未查询到对应的电子签章企业数据，请联系管理员", " ");
            }
            // acsv.setUserId(seal_signer);
            acsv.setUserId(sendUserId);
            acsv.setEnterpriseId(resp5.getData());
            info("sendToSms ---------------------------> CompanyUUID: " + resp5.getData());

            info("sendToSms ---------------------------> 查询合同对应文档");
            SignFileApiVO sfav = new SignFileApiVO();
            BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 1);
            info("sendToSms ---------------------------> 查询合同对应文档id:" + resp.getData());
            if (!resp.isStatus()) {
                info("sendToSms ---------------------------> 合同对应文档查询失败，不存在 : ");
                return createJsonResponse(500, " 未查询到对应文档,请联系管理员", " ");
            }
            sfav.setDocId(Long.valueOf(resp.getData()));
            List<SignFileApiVO> sfavList = new ArrayList<>();
            sfavList.add(sfav);
            acsv.setSignFiles(sfavList);
            psr.setSigner(acsv);
            psr.setExpire(1440);
            info("sendToSms ---------------------------> 准备发送个人页面会签  ");
            BaseResp<PageSignResp> resp1 = ssi.personPageSign(psr);

            if (!resp1.isStatus()) {
                info("sendToSms --------------------------->  页面签署发送失败:" + JSONUtil.toJsonStr(resp1));
                return createJsonResponse(500, " 页面签署发送失败,请联系管理员", " ");
            }
            // 修改合同对应签署人，
            info("sendToSms ---------------------------> 修改合同对应签署人   :" + Base64.encode(docId) + " :" + userId);
            int i = changeThirdNum(0, Base64.encode(docId), userId, new ThirdMappConfig(), userId + "@" + resp1.getData().getSignUrl() + "@" + DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            if (1 == i) {
                return createJsonResponse(500, " 修改合同对应签署人 失败,请联系管理员", " ");
            }
            // info("sendToSms ---------------------------> 修改对应链接地址的个人会签页面 :");
            // //
            // String tableName = getFormName(map.get("requestid1"));
            // WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(map.get("lcpzid")));
            //
            // if (!StringUtils.hasText(tableName)) {
            //     return createJsonResponse(500, " 主表数据名查询失败,请联系管理员", " ");
            // }
            // Map<String, String> tid = getTableDTid(Integer.parseInt(map.get("requestid1")), tableName, 2, docId, tag);
            // if (!StringUtils.hasText(map.get("mxbid"))) {
            //     return createJsonResponse(500, " 明细表查询失败,请联系管理员", " ");
            // }
            // String detailId = map.get("mxbid");
            // int res = updateQsym(Integer.parseInt(detailId), tableName, 2, resp1.getData().getSignUrl());
            // if (res == 1) {
            //     code = 500;
            //     message = "修改合同明细表签署页面 失败";
            //     String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \" \"}";
            //     // 返回适当的响应或记录错误
            //     return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
            // }


            code = 200;
            message = "发送成功，请注意短信";
            String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \"" + resp1.getData().getSignUrl() + "\"}";


            // 添加 CORS 响应头
            return Response.ok().header("Access-Control-Allow-Origin", "*") // 允许所有来源访问，生产环境建议指定具体来源
                    .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE").header("Access-Control-Allow-Headers", "Content-Type, Authorization").entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
        } catch (Exception e) {
            e.printStackTrace();
            // 返回适当的响应或记录错误
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Internal Server Error").build();
        }

    }

    @GET
    @Path("/send/GZ")
    @Produces(MediaType.TEXT_PLAIN)
    public Response sendToGZ(@QueryParam("userId") Integer userId, @QueryParam("docId") String docId, @QueryParam("companyId") String companyId, @Context HttpServletRequest request, @Context HttpServletResponse response) {
        int code = 200;
        User user = HrmUserVarify.getUser(request, response);
        info("sendtoGZ ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user));
        info("sendtoGZ ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user.getUID()));
        info("sendtoGZ ---------------------------> 拿到当前操作人:" + JSONUtil.toJsonStr(user.getUsername()));
        userId = user.getUID();
        String message = "成功";
        String data = "";
        String tag = "xynr";
        /**
         * 返回对应的签字链接
         */

        info("sendtoGZ ---------------------------> 进入发送签名，接受到的参数: userId = " + userId + ", docId = " + docId);
        if (userId == null || !StringUtils.hasText(docId) || !StringUtils.hasText(docId)) {
            return createJsonResponse(500, "请求参数错误", "");
        }

        // 校验当前人是否已签署该合同
        Map<String, String> map = getqsrInfo(0, Base64.encode(docId), new ThirdMappConfig());
        info("sendtoGZ ---------------------------> getqsrInfo:" + map.toString());
        if (map.isEmpty()) {
            return createJsonResponse(500, "合同查询失败,请联系管理员", "");
        }
        String configId = map.get("lcpzid");
        if (!StringUtils.hasText(configId)) {
            return createJsonResponse(500, "流程配置id为空格，请联系管理员", "");
        }
        WorkflowConfig workflowConfig = new WorkflowConfig(Integer.parseInt(configId));
        String gzr = workflowConfig.getGzr();
        // String gzr = map.get("gzr");
        boolean stringExist = isStringExist(gzr, String.valueOf(userId));
        if (!stringExist) {
            return createJsonResponse(500, "当前用户不具备盖章权限，请联系管理员", "");
        }
        String gzrlj = findDomainByIds(map.get("gzrlj"), docId);
        if (StringUtils.hasText(gzrlj)) {
            return createJsonResponse(200, "已发送短信链接", gzrlj);
        }

        SignServiceImpl ssi = new SignServiceImpl();
        try {
            String s = map.get("sfmb");
            if (!StringUtils.hasText(s) || !"1".equals(s)) {
                PageSignReq psr = new PageSignReq();

                // BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                //
                // if (!resp3.isStatus()) {
                //     info("sendToSms ---------------------------> resp3 error 未查询到对应文档: ");
                //     // 返回适当的响应或记录错误
                //     return createJsonResponse(500, " 未查询到对应文档", " ");
                // }
                String contractId = map.get("sealid");
                if (!StringUtils.hasText(contractId)) {
                    return createJsonResponse(500, "未查询到对应的合同数据,请联系管理员", "");
                }

                // // 查询合同详情 判断合同当前是否支持盖章 或者说是否处于签署中状态
                // ContractApiVO vo = searchInfo(Long.valueOf(map.get("sealid")));
                // if (vo.getStatus() != 2 ||vo.getStatus() == 1) {
                //     if (vo.getStatus() == 1) {
                //         return createJsonResponse(500, "存在他人未签署合同，暂不支持盖章", "");
                //     } else {
                //         info("合同状态异常");
                //         return createJsonResponse(500, "合同状态为:" + getStatusById(vo.getStatus()).getStatus() + "。 暂不支持盖章", "");
                //     }
                // }
                psr.setContractId(Long.valueOf(contractId));
                info("sendtoGZ ---------------------------> 合同id: " + contractId);


                psr.setContractId(Long.valueOf(contractId));

                AddContractSignerVO acsv = new AddContractSignerVO();

                // BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(seal_signer, 2);
                //
                // if (!resp4.isStatus()) {
                //     info("SignAction  ----------------------->  查询用户 对应三方id 失败  不存在: ");
                //     // 返回适当的响应或记录错误
                //     return Action.FAILURE_AND_CONTINUE;
                // }

                BaseResp<String> resp5 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);

                if (!resp5.isStatus()) {
                    return createJsonResponse(500, "未查询到对应的电子签章企业数据，请联系管理员", " ");

                }
                String userCode = OaHrmUtils.getworkCode(userId);
                // 设置当前签署人
                // String currentCode = OaHrmUtils.getworkCode(currentUserId);
                BaseResp<String> resp6 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                if (!resp6.isStatus()) {
                    info("sendtoGZ  -----------------------> 查询用户 对应三方id 失败  不存在,请联系管理员:");
                    return createJsonResponse(500, " 查询用户 对应三方id 失败  不存在,请联系管理员", " ");
                }
                acsv.setUserId(resp6.getData());
                // acsv.setUserId(jbrCode);
                // 企业签署
                acsv.setSignerType(2);
                // 根据企业对应的UUID 查询对应的企业id
                acsv.setEnterpriseId(resp5.getData());
                info("sendtoGZ  -----------------------> 企业固定UUID: " + resp5.getData());
                SignFileApiVO sfav = new SignFileApiVO();
                info("sendtoGZ  -----------------------> 拿到合同文档的id:");
                BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 1);
                if (!resp.isStatus()) {
                    info("sendtoGZ  -----------------------> 查询文档资料失败不存在,请联系管理员:");
                    return createJsonResponse(500, "查询文档资料失败不存在,请联系管理员", " ");
                }
                info("sendtoGZ  -----------------------> 合同文档的id: " + resp.getData());
                sfav.setDocId(Long.valueOf(resp.getData()));
                List<SignFileApiVO> sfavList = new ArrayList<>();
                sfavList.add(sfav);
                acsv.setSignFiles(sfavList);
                psr.setSigner(acsv);
                psr.setExpire(1440);
                info("sendtoGZ  -----------------------> 发送企业签署 psr " + JSONUtil.toJsonStr(psr));
                BaseResp<PageSignResp> resp1 = ssi.orgPageSign(psr);

                if (!resp1.isStatus()) {
                    info("sendtoGZ --------------------------->  页面签署发送失败:" + JSONUtil.toJsonStr(resp1));
                    return createJsonResponse(500, " 页面签署发送失败,请联系管理员", " ");
                }
                // 修改合同对应签署人，
                info("sendtoGZ ---------------------------> 修改合同对应签署人   :" + Base64.encode(docId) + " :" + userId);
                int i = changeThirdGZNum(0, Base64.encode(docId), userId, new ThirdMappConfig(), docId + "@" + resp1.getData().getSignUrl() + "@" + DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
                if (1 == i) {
                    return createJsonResponse(500, " 修改合同对应签署人 失败,请联系管理员", " ");
                }
                info("sendtoGZ ---------------------------> 修改对应链接地址的个人会签页面 :");
                code = 200;
                message = "发送成功，请注意短信";
                String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \"" + resp1.getData().getSignUrl() + "\"}";


                // 添加 CORS 响应头
                return Response.ok().header("Access-Control-Allow-Origin", "*") // 允许所有来源访问，生产环境建议指定具体来源
                        .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE").header("Access-Control-Allow-Headers", "Content-Type, Authorization").entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
            } else {
                String userCode = OaHrmUtils.getworkCode(userId);
                // 设置当前签署人
                // String currentCode = OaHrmUtils.getworkCode(currentUserId);
                BaseResp<String> resp6 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                if (!resp6.isStatus()) {
                    info("sendtoGZ  -----------------------> 查询用户 对应三方id 失败  不存在,请联系管理员:");
                    return createJsonResponse(500, " 查询用户 对应三方id 失败  不存在,请联系管理员", " ");
                }
                String contractId = map.get("sealid");
                if (!StringUtils.hasText(contractId)) {
                    return createJsonResponse(500, "未查询到对应的合同数据,请联系管理员", "");
                }
                BaseResp<String> com = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                info("CreateContractAction------------> 拿到三方企业id: " + com.getData());
                if (!com.isStatus()) {
                    return createJsonResponse(500, "添加模板,未查询到对应的企业联系人,请联系管理员", "");
                }
                // BaseResp<JSONObject> baseResp1 = new CreateOrgIntegration().companyInfo(new CompanyInfo(com.getData()));
                // if (baseResp1.isStatus()) {
                //     return createJsonResponse(500, "查询企业主管理员错误,请联系管理员", "");
                // }
                boolean b = new CreateContractAction().addSignerByTemplate(String.valueOf(contractId), "企业", 1, "", resp6.getData(), com.getData());
                if (!b) {
                    return createJsonResponse(500, "添加模板企业签署人失败,请联系管理员", "");
                }
                // info("sendtoGZ ---------------------------> 合同id: " + contractId);
                // BaseResp<String> resp5 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(String.valueOf(companyId), 3);
                //
                // if (!resp5.isStatus()) {
                //     return createJsonResponse(500, "未查询到对应的电子签章企业数据，请联系管理员", " ");
                // }
                String url = new CreateContractAction().startSignByTemplateFile(Long.parseLong(contractId), 2, com.getData(), "", resp6.getData());
                if (!StringUtils.hasText(url)) {
                    return createJsonResponse(500, " 发送页面签署失败，请联系管理员", " ");
                }
                info("sendtoGZ ---------------------------> 修改合同对应签署人   :" + Base64.encode(docId) + " :" + userId);
                int i = changeThirdGZNum(0, Base64.encode(docId), userId, new ThirdMappConfig(), docId + "@" + url + "@" + DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
                if (1 == i) {
                    return createJsonResponse(500, " 修改合同对应签署人 失败,请联系管理员", " ");
                }
                info("sendtoGZ ---------------------------> 修改对应链接地址的个人会签页面 :");
                code = 200;
                message = "发送成功，请注意短信";
                String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \"" + url + "\"}";


                // 添加 CORS 响应头
                return Response.ok().header("Access-Control-Allow-Origin", "*") // 允许所有来源访问，生产环境建议指定具体来源
                        .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE").header("Access-Control-Allow-Headers", "Content-Type, Authorization").entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
            }

        } catch (
                Exception e) {
            e.printStackTrace();
            // 返回适当的响应或记录错误
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Internal Server Error").build();
        }

    }


    /**
     * 更新对应流程的文件
     */
    @POST
    @Path("/update/con/file")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response registerConUser(@Context UriInfo uriInfo, ContractCallback contract) {
        // String clientIp = uriInfo.getRequestUri().getHost();
        // try {
        //     InetAddress address = InetAddress.getByName(clientIp);
        //     info("接受回调请求ip:" + address.toString());
        //     String hostAddress = address.getHostAddress();
        //     info("接受回调请求ip:" + hostAddress);
        //     byte[] ipBytes = address.getAddress();
        //     // 检查是否为内网 IP
        //     boolean privateIp = isPrivateIp(ipBytes);
        //     if (privateIp || "**********".equals(hostAddress)) {
        //         info("进入内网ip检测 :" + privateIp);
        //         info("进入外网ip检测 :" + hostAddress);
        //     } else {
        //         // 拒绝访问
        //         return Response.status(Response.Status.FORBIDDEN).entity("{\"code\": 403, \"message\": \"Access denied\"}").type(MediaType.APPLICATION_JSON).build();
        //     }
        // } catch (UnknownHostException e) {
        //     // 处理异常
        //     return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("{\"code\": 500, \"message\": \"Internal server error\"}").type(MediaType.APPLICATION_JSON).build();
        // }
        int code = 0;
        String message = null;
        try {
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的合同状态：" + contract.getContract().getStatus());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同id" + contract.getContractId());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的签署人数量：" + contract.getContract().getSignCount());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的签署人：" + contract.getSignerList().toString() + ":" + contract.getSignerList().get(0).getUserId());

            if (3 == contract.getContract().getStatus()) {
                info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同已完成 ");
                return Response.status(Response.Status.OK).type(MediaType.APPLICATION_JSON).build();
            }

            Map<String, String> map = getDocIdInfo(0, String.valueOf(contract.getContractId()), new ThirdMappConfig());
            // 修改对应明细表的qzhfj值
            code = 200;
            message = "成功";
            if (contract.getContract().getStatus() == 6) {
                info("签署回调---------------->合同被拒签");
                // 拒签 修改对应的status
                String tableName = getFormName(map.get("requestid1"));
                updateMainStatus(map.get("requestid1"), tableName, "已拒签");
            }
            if (contract.getContract().getStatus() == 2) {
                info("签署回调---------------->合同已被签署");
                Long contractId = contract.getContractId();
                info("签署回调 ---------------------------> 合同id:" + contractId);
                info("签署回调 ---------------------------> 根据合同id 和 type 0 去拿requestid:" + contractId);
                BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdBysealId(String.valueOf(contractId), 0);
                String requestId = resp.getData();
                info("签署回调 ---------------------------> requestid:" + requestId);
                String newDocId = "";
                String tableName = "";
                if (StringUtils.hasText(requestId)) {
                    // 拿到文件 存入oa流程
                    tableName = getFormName(requestId);
                    String configId = map.get("lcpzid");
                    WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
                    String tag = wconfig.getFfile();
                    if (StringUtils.hasText(tableName)) {
                        ContractApiVO vo = searchInfo(contractId);
                        info("签署回调 ---------------------------> 合同详情:");
                        List<DocumentApiVO> documents = vo.getDocuments();
                        if (!CollectionUtils.isEmpty(documents)) {
                            info("签署回调 ---------------------------> 合同文档:");
                            DocumentApiVO documentApiVO = documents.get(0);
                            String docBase64 = documentApiVO.getDocBase64();
                            info("签署回调 ---------------------------> 上传文件:");
                            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestId, DocumentInfo.SEALED_DOCUMENT.getValue());
                            info("签署回调 ---------------------------> 上传文件 返回的文档id:" + newDocId);
                        }
                        // updateFormFile(tableName, )
                    } else {
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).type(MediaType.APPLICATION_JSON).build();
                    }
                    Map<String, String> info = getDocIdInfo(0, String.valueOf(contractId), new ThirdMappConfig());
                    String oaid = info.get("oaid");
                    String oldDocId = Base64.decodeStr(oaid);

                    int i = updateMainqzfj(requestId, tableName, newDocId);
                    if (i == 1) {
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("{\"code\":500 , \"message\": \"更新附件 失败\"}").type(MediaType.APPLICATION_JSON).build();
                    }
                    // Map<String, String> file = getfileName(Integer.parseInt(newDocId));
                    // if (3 == contract.getContract().getStatus()) {
                    //     info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同已完成 ");
                    //     updatedzlj(Integer.parseInt(detailId), tableName, 2, DownFilePath(file.get("fileid"), false));
                    // }

                    // changeThirdNum(0, oaid, i);
                    // updateFormFile(tableName, newDocId, requestId);
                }
            }
        } catch (NumberFormatException e) {
            info("签署回调 --------------------------->回调失败  ");
            throw new RuntimeException(e);
        }
        String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\"}";
        return Response.status(Response.Status.OK).entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
    }


    @POST
    @Path("/update/file")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response registerUser(@Context UriInfo uriInfo, ContractCallback contract) {
        // String clientIp = uriInfo.getRequestUri().getHost();
        // try {
        //     InetAddress address = InetAddress.getByName(clientIp);
        //     info("接受回调请求ip:" + address.toString());
        //     String hostAddress = address.getHostAddress();
        //     info("接受回调请求ip:" + hostAddress);
        //     byte[] ipBytes = address.getAddress();
        //     // 检查是否为内网 IP
        //     boolean privateIp = isPrivateIp(ipBytes);
        //     if (privateIp || "**********".equals(hostAddress)) {
        //         info("进入内网ip检测 :" + privateIp);
        //         info("进入外网ip检测 :" + hostAddress);
        //     } else {
        //         // 拒绝访问
        //         return Response.status(Response.Status.FORBIDDEN).entity("{\"code\": 403, \"message\": \"Access denied\"}").type(MediaType.APPLICATION_JSON).build();
        //     }
        // } catch (UnknownHostException e) {
        //     // 处理异常
        //     return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("{\"code\": 500, \"message\": \"Internal server error\"}").type(MediaType.APPLICATION_JSON).build();
        // }
        int code = 0;
        String message = null;
        try {
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的合同状态：" + contract.getContract().getStatus());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同id" + contract.getContractId());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的签署人数量：" + contract.getContract().getSignCount());
            info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同的签署人：" + contract.getSignerList().toString() + ":" + contract.getSignerList().get(0).getUserId());

            // if (3 == contract.getContract().getStatus()) {
            //     info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同已完成 ");
            //     return Response.status(Response.Status.OK).entity("{\"code\":200 , \"message\": \"回调成功\"}").type(MediaType.APPLICATION_JSON).build();
            // }

            String tag = "xynr";
            Map<String, String> map = getDocIdInfo(0, String.valueOf(contract.getContractId()), new ThirdMappConfig());
            // 修改对应明细表的qzhfj值
            code = 200;
            message = "成功";
            if (contract.getContract().getStatus() != 2 && contract.getContract().getStatus() != 3) {
                info("签署回调---------------->合同不对劲");
                // 拒签 修改对应的status
                String tableName = getFormName(map.get("requestid1"));
                updateStatus(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), getStatusById(contract.getContract().getStatus()).getStatus());
            }
            if (contract.getContract().getStatus() == 2 || 3 == contract.getContract().getStatus()) {
                info("签署回调---------------->合同已被签署");
                Long contractId = contract.getContractId();
                info("签署回调 ---------------------------> 合同id:" + contractId);
                info("签署回调 ---------------------------> 根据合同id 和 type 0 去拿requestid:" + contractId);
                BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdBysealId(String.valueOf(contractId), 0);
                String requestId = resp.getData();
                info("签署回调 ---------------------------> requestid:" + requestId);
                String newDocId = "";
                String tableName = "";
                if (StringUtils.hasText(requestId)) {
                    // 拿到文件 存入oa流程
                    tableName = getFormName(requestId);
                    // if ("formtable_main_1928".equals(tableName)) {
                    //     tag = "shnryy";
                    // }
                    String configId = map.get("lcpzid");
                    WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
                    tag = wconfig.getFfile();
                    if (StringUtils.hasText(tableName)) {
                        ContractApiVO vo = searchInfo(contractId);
                        info("签署回调 ---------------------------> 合同详情:");
                        List<DocumentApiVO> documents = vo.getDocuments();
                        if (!CollectionUtils.isEmpty(documents)) {
                            info("签署回调 ---------------------------> 合同文档:");
                            DocumentApiVO documentApiVO = documents.get(0);
                            String docBase64 = documentApiVO.getDocBase64();
                            info("签署回调 ---------------------------> 上传文件:");
                            newDocId = uploadToOA(vo.getContractName(), java.util.Base64.getDecoder().decode(docBase64), requestId, DocumentInfo.SEALED_DOCUMENT.getValue());
                            info("签署回调 ---------------------------> 上传文件 返回的文档id:" + newDocId);
                        }
                        // updateFormFile(tableName, )
                    } else {
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).type(MediaType.APPLICATION_JSON).build();
                    }
                    Map<String, String> info = getDocIdInfo(0, String.valueOf(contractId), new ThirdMappConfig());
                    String oaid = info.get("oaid");
                    String oldDocId = Base64.decodeStr(oaid);
                    // Map<String, String> tid = getTableDTid(Integer.parseInt(info.get("requestid1")), tableName, wconfig.getDatasource(), oldDocId, tag);
                    String mxbid = map.get("mxbid");
                    if (!StringUtils.hasText(mxbid)) {
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("{\"code\":500 , \"message\": \" 明细表表单数据名查询失败\"}").type(MediaType.APPLICATION_JSON).build();
                    }
                    String detailId = mxbid;
                    int i = updateqzfj(Integer.parseInt(detailId), tableName, wconfig.getDatasource(), newDocId);
                    if (i == 1) {
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("{\"code\":500 , \"message\": \"更新附件 明细表表单数据名查询失败\"}").type(MediaType.APPLICATION_JSON).build();
                    }
                    // Map<String, String> file = getfileName(Integer.parseInt(newDocId));
                    // if (3 == contract.getContract().getStatus()) {
                    //     info("签署回调 ---------------------------> 进入签署回调接口 拿到当前合同已完成 ");
                    //     updatedzlj(Integer.parseInt(detailId), tableName, 2, DownFilePath(file.get("fileid"), false));
                    // }

                    // changeThirdNum(0, oaid, i);
                    // updateFormFile(tableName, newDocId, requestId);
                }
            }
        } catch (NumberFormatException e) {
            info("签署回调 --------------------------->回调失败  ");
            throw new RuntimeException(e);
        }
        // String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\"}";
        return Response.status(Response.Status.OK).entity("{\"code\":200 , \"message\": \"回调成功\"}").type(MediaType.APPLICATION_JSON).build();
    }

    // 检查是否为内网 IP
    private boolean isPrivateIp(byte[] ipBytes) {
        if (ipBytes.length == 4) {
            // IPv4
            if ((ipBytes[0] & 0xFF) == 10) {
                return true;
            } else if ((ipBytes[0] & 0xFF) == 172 && (ipBytes[1] & 0xF0) == 16) {
                return true;
            } else if ((ipBytes[0] & 0xFF) == 192 && (ipBytes[1] & 0xFF) == 168) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据合同id查询合同详情
     *
     * @param contractId
     * @return
     */
    public static ContractApiVO searchInfo(Long contractId) {
        new BaseBean().writeLog(" searchInfo---------------------------> 进入方法");
        // BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridoaIdBySealId(String.valueOf(contractId), 0);
        // if (!resp3.isStatus()) {
        //     // 返回适当的响应或记录错误
        //     new BaseBean().writeLog("searchInfo ---------------------------> 根据合同id查询合同详情 查询不到数据");
        //     throw new RuntimeException("根据合同id查询合同详情 查询不到数据");
        // }
        CreateContractServiceImp imp = new CreateContractServiceImp();
        // String data = resp3.getData();
        new BaseBean().writeLog("searchInfo ---------------------------> 天威云合同id :" + contractId);
        new BaseBean().writeLog("searchInfo ---------------------------> 查询合同详情 :");
        BaseResp<ContractApiVO> search = imp.search(contractId, true);
        new BaseBean().writeLog("searchInfo ---------------------------> 查询合同详情结果 :" + search.toString());
        return search.getData();
    }

    public BaseResp<JSONObject> realCreaterAccount(Integer userId, int companyId) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();
        CreateOrgIntegration orgIntegration = new CreateOrgIntegration();


        String createrCode = OaHrmUtils.getworkCode(userId);

        BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
        HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(userId);


        if (resp2.isStatus()) {
            // if (resp3.isStatus()) {
            //     info("企业详情查询1 : ");
            //     BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));
            //     if (!baseResp.isStatus()) {
            //         return new BaseResp<JSONObject>(false);
            //     }
            //     info("企业详情查询 结果1 : " + JSONUtil.toJsonStr(baseResp));
            //     RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
            //     JoinOrgReq joinOrgReq = new JoinOrgReq();
            //     joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
            //     joinOrgReq.setInviteUserId(resp2.getData());
            //     joinOrgReq.setEnterpriseId(resp3.getData());
            //     info("邀请用户 进入企业11 : " + JSONUtil.toJsonStr(joinOrgReq));
            //     integration.inviteUser(joinOrgReq);
            //     return MsgResp.ok();
            // }
            return MsgResp.ok();
        }
        RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
        CreateUserServiceImpl cusi = new CreateUserServiceImpl();
        resp = cusi.createUser(realUser.getName(), realUser.getIdCard(), realUser.getMobile(), createrCode);
        JoinOrgReq joinOrgReq = new JoinOrgReq();
        boolean flag = false;
        // 判断是否在已上线的公司下面 如果在 就加入 不在 就不加入
        // ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
        // String orgId = allInfo.getSubcompanyid1();
        // boolean stringExist = isStringExist(thirdMappConfig.getSupportCompany(), orgId);
        //
        // String subcompanyid1 = "";
        //
        // if (StringUtils.hasText(orgId)) {
        //     flag = stringExist;
        // }
        // if (flag) {
        //     info("拿到当前人的subCompany: " + subcompanyid1);
        //     BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(subcompanyid1, 3);
        //     BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));
        //
        //     info("企业详情查询 结果 : " + JSONUtil.toJsonStr(baseResp));
        //     joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
        //     joinOrgReq.setInviteUserId((String) resp.getData().get("userId"));
        //     joinOrgReq.setEnterpriseId(resp3.getData());
        //     info("邀请用户 进入企业 : " + JSONUtil.toJsonStr(joinOrgReq));
        //     integration.inviteUser(joinOrgReq);
        // }


        if (resp.isStatus()) {
            BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping((String) resp.getData().get("userId"), createrCode, 2);
            info("resp1 : " + resp1.toString());
            if (!resp1.isStatus()) {
                info("第三方系统数据插入 : 失败");
                return MsgResp.error("第三方系统数据插入失败");
            }
            info("第三方系统数据插入 : 成功");
        } else {
            info("创建用户失败 : " + resp.toString());
            return MsgResp.error();
        }

        return resp;
    }

    public static Response createJsonResponse(int code, String message, String data) {
        String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \"" + data + "\"}";
        return Response.ok().header("Access-Control-Allow-Origin", "*").header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE").header("Access-Control-Allow-Headers", "Content-Type, Authorization").entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
    }

    /**
     * 检查给定的 ID 是否存在于输入字符串中，并返回对应的域名
     *
     * @param input 输入的字符串，格式为 "id1@domain1,id2@domain2,..."
     * @param id    要检查的 ID
     * @return 如果 ID 存在，返回对应的域名；否则返回 null
     */
    public static String findDomainById(String input, String id) {
        if (input == null || input.isEmpty() || id == null || id.isEmpty()) {
            return null;
        }
        // 按逗号分割输入字符串
        String[] pairs = input.split(",");
        for (String pair : pairs) {
            // 按 @ 分割每一对 ID 和域名
            String[] parts = pair.split("@");
            if (parts.length == 2 && parts[0].equals(id)) {
                return parts[1];
            }
        }
        return null;
    }

    public static String encryptToMD5UpperCase(String input) {
        try {
            // 获取 MD5 算法的 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将输入字符串转换为字节数组并更新到 MessageDigest 中
            byte[] digest = md.digest(input.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 将字节转换为无符号整数
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    // 如果十六进制字符串长度为 1，前面补 0
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            // 将生成的十六进制字符串转换为大写形式
            return hexString.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            // 处理算法不可用的异常
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 检查给定的 ID 是否存在于输入字符串中，并返回对应的域名
     *
     * @param input 输入的字符串，格式为 "id1@domain1@2022,id2@domain2@2022"
     * @param id    要检查的 ID
     * @return 如果 ID 存在，返回对应的域名；否则返回 null
     */
    public static String findDomainByIds(String input, String id) {
        // 输入字符串为空或者要查找的 id 为空，直接返回 null
        if (input == null || input.isEmpty() || id == null || id.isEmpty()) {
            return null;
        }
        // 按逗号分割输入字符串，得到多个 id-域名-时间 组合的字符串
        String[] pairs = input.split(",");
        for (String pair : pairs) {
            // 按 @ 分割每一对 id、域名和时间
            String[] parts = pair.split("@");
            // 确保分割后有三个部分，并且第一个部分（id）和要查找的 id 相等
            if (parts.length == 3 && parts[0].equals(id)) {
                return parts[1];
            }
        }
        // 没有找到匹配的 id，返回 null
        return null;
    }

    public static Map<String, String> splitFilePath(String filePath) {
        Map<String, String> map = new HashMap<>();
        // 查找第一个 / 的索引
        int index = filePath.indexOf("/");
        if (index != -1) {
            // 获取第一个 / 前的字符串
            String bucket = filePath.substring(0, index);
            // 获取第一个 / 后的所有字符串
            String path = filePath.substring(index + 1);
            map.put("bucket", bucket);
            map.put("path", path);
        }
        return map;
    }


    public String getHUUrl(String bucletName, String objectKey) throws Exception {
        ObsClient obsClient = new ObsClient("DKYYDBTLG7OMINWELGRD", "aQ3ToJOFCNuehFvQl4ZBYPu9WIn7uG4Bxf5GUpg4", "obs.cn-south-1.myhuaweicloud.com");
        long expireSeconds = 300L;
        TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, expireSeconds);
        request.setBucketName(bucletName);
        request.setObjectKey(objectKey);
        request.setRequestDate(new Date());
        // 1分钟后链接失效
        request.setExpires(300);
        // ObsClient obsClient = obsBiz.getObsClient();
        // 通过临时授权,直接访问链接下载
        TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
        info("getHUUrl------------------------>  拿到华为云OBS 临时url: " + response.getSignedUrl());
        return response.getSignedUrl();
    }


    /**
     * 触发子流程
     * 拿到当前流程的数据后 在触发新的流程
     *
     * @return
     */
    @POST
    @Path("/cszlc")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response cszlc(@Context UriInfo uriInfo, Cfzlc cfzlc) {
        String requestId = null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar now = Calendar.getInstance();
        try {
            if (!StringUtils.hasText(cfzlc.getSzgs())) {
                return createJsonResponse(500, "业务办理公司为空，请联系管理员", "");
            }
            if (!StringUtils.hasText(cfzlc.getUserId())) {
                return createJsonResponse(500, "当前操作人为空，请联系管理员", "");
            }
            if (!StringUtils.hasText(cfzlc.getGdxs())) {
                return createJsonResponse(500, "归档形式为空，请联系管理员", "");
            }
            if (!StringUtils.hasText(cfzlc.getRequestId())) {
                return createJsonResponse(500, "流程请求id为空，请联系管理员", "");
            }

            info("触发子流程------------->拿到请求数据 = " + JSONUtil.toJsonStr(cfzlc));

            // 校验印章的选择
            if (cfzlc.getGdxs().equals("1")) {
                if ("1".equals(cfzlc.getQfzlx())) {
                    return createJsonResponse(500, "不支持放章操作，请联系管理员", "");
                }
                /**
                 * SELECT top 100  * FROM workflow_base where formid=-1921 and isvalid=1
                 * select * from uf_seal_third_test where  sfmb='0' and  type in (8,9) and like  order by id desc
                 * select * from uf_seal_third_test where oaId='50826604'  and type in (8,9)  and sfmb='0' and  sfmb='0' and   and like  order by id desc
                 * insert into
                 * uf_seal_third_test (type,oaId,sealId,sfmb) values (9,'50826604','16,15,14,13','0')
                 */
                // 先判断 requestId 如果包含未归还印章则提示
                // 如果不包含则根据印章库查看印章状态 是否在库  在判定是否已借出
                String yzxz = cfzlc.getYzxz();
                if (!StringUtils.hasText(yzxz)) {
                    return createJsonResponse(500, "请选择需要取的印章", "");
                }
                String[] split = yzxz.split(",");
                // 将数组转换为 ArrayList
                List<String> list = new ArrayList<>(Arrays.asList(split));
                Boolean flag = false;
                // 校验是否存在重复的提交申请
                for (String s : list) {
                    Map<String, String> qz = qz(cfzlc.getRequestId(), "8", "0", new ThirdMappConfig(), s);
                    info("触发子流程------------->qz " + qz.toString());
                    if (!qz.isEmpty()) {
                        Map<String, String> map = yzInfo(s, new ThirdMappConfig());
                        if (map.isEmpty()) {
                            return createJsonResponse(500, "未查询到对应的印章，请联系管理员", "");
                        }
                        return createJsonResponse(500, "【" + map.get("yzmc") + "】 该印章你已提交取章申请", "");
                    }
                }
                // 判断印章是否在库
                for (String s : list) {
                    Map<String, String> map = yzInfo(s, new ThirdMappConfig());
                    if (map.isEmpty()) {
                        return createJsonResponse(500, "未查询到对应的印章，请联系管理员。", "");
                    }
                    if (!"0".equals(map.get("zkzt"))) {
                        return createJsonResponse(500, "【" + map.get("yzmc") + "】 该印章已被借出！", "");
                    }

                }
                // 全部校验完毕
                // SqlUtils.getDocIdInfoByDocId()
            }

            WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();// 主表
            WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];// 主表字段只有一条记录
            WorkflowRequestInfo wri = new WorkflowRequestInfo();// 流程基本信息
            info("触发子流程------------->拿到请求数据 = 1");
            // 主字段的3个
            WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[11];
            // 申请人
            wrti[0] = new weaver.mobile.webservices.workflow.WorkflowRequestTableField();
            wrti[0].setFieldName("sqr");
            // wrti[0].setFieldValue(rs1.getString(2));
            wrti[0].setFieldValue(cfzlc.getUserId());
            wrti[0].setView(true);// 字段是否可见
            wrti[0].setEdit(false);// 字段是否可编辑

            info("触发子流程------------->拿到请求数据 = 2");
            // 所属公司
            wrti[1] = new WorkflowRequestTableField();
            wrti[1].setFieldName("szgs");
            // wrti[0].setFieldValue(rs1.getString(2));
            wrti[1].setFieldValue(cfzlc.getSzgs());
            wrti[1].setView(true);// 字段是否可见
            wrti[1].setEdit(false);// 字段是否可编辑
            info("触发子流程------------->拿到请求数据 = 3");
            wrti[2] = new WorkflowRequestTableField();
            wrti[2].setFieldName("sfxyyy");
            // wrti[2].setFieldValue(rs1.getString(3));
            wrti[2].setFieldValue("1");
            wrti[2].setView(true);// 字段是否可见
            wrti[2].setEdit(false);// 字段是否可编辑

            wrti[3] = new WorkflowRequestTableField();
            wrti[3].setFieldName("gdxs");
            wrti[3].setFieldValue(cfzlc.getGdxs());
            wrti[3].setView(true);// 字段是否可见
            wrti[3].setEdit(false);// 字段是否可编辑

            wrti[4] = new WorkflowRequestTableField();
            wrti[4].setFieldName("lcbh");
            wrti[4].setFieldValue(cfzlc.getLcbh());
            wrti[4].setView(true);// 字段是否可见
            wrti[4].setEdit(false);// 字段是否可编辑

            wrti[5] = new WorkflowRequestTableField();
            wrti[5].setFieldName("dyrid");
            wrti[5].setFieldValue(cfzlc.getRequestId());
            wrti[5].setView(true);// 字段是否可见
            wrti[5].setEdit(false);// 字段是否可编辑

            // 甲方   联系方式
            wrti[6] = new WorkflowRequestTableField();
            wrti[6].setFieldName("jf");
            wrti[6].setFieldValue("重庆信科通信工程有限公司");
            wrti[6].setView(true);// 字段是否可见
            wrti[6].setEdit(false);// 字段是否可编辑

            wrti[7] = new WorkflowRequestTableField();
            wrti[7].setFieldName("lxfs");
            wrti[7].setFieldValue("13752801718");
            wrti[7].setView(true);// 字段是否可见
            wrti[7].setEdit(false);// 字段是否可编辑

            String uuid = UUID.randomUUID().toString();
            String uuid1 = UUID.randomUUID().toString();
            if (cfzlc.getGdxs().equals("0")) {
                info("触发子流程------------->拿到请求数据 = 4");
                if (!StringUtils.hasText(cfzlc.getDzyyfj())) {
                    return createJsonResponse(500, "电子用印附件未上传", "");
                }
                wrti[8] = new WorkflowRequestTableField();
                wrti[8].setFieldName("fj");
                wrti[8].setFieldValue(cfzlc.getDzyyfj());
                wrti[8].setView(true);// 字段是否可见
                wrti[8].setEdit(false);// 字段是否可编辑
                info("触发子流程------------->拿到请求数据 = 5");
            } else {
                info("触发子流程------------->拿到请求数据 = 6");
                if (!StringUtils.hasText(cfzlc.getYzxz())) {
                    info("触发子流程------------->拿到请求数据 = 41");
                    return createJsonResponse(500, "请选择需要取放的印章", "");
                }
                if (!StringUtils.hasText(cfzlc.getQfzlx())) {
                    info("触发子流程------------->拿到请求数据 = 42");
                    return createJsonResponse(500, "请选择取放章类型", "");
                }
                wrti[8] = new WorkflowRequestTableField();
                wrti[8].setFieldName("yz");
                wrti[8].setFieldValue(cfzlc.getYzxz());
                wrti[8].setView(true);// 字段是否可见
                wrti[8].setEdit(false);// 字段是否可编辑

                wrti[9] = new WorkflowRequestTableField();
                wrti[9].setFieldName("qfzlx");
                wrti[9].setFieldValue(cfzlc.getQfzlx());
                wrti[9].setView(true);// 字段是否可见
                wrti[9].setEdit(false);// 字段是否可编辑


                wrti[10] = new WorkflowRequestTableField();
                wrti[10].setFieldName("uuid");
                wrti[10].setFieldValue(uuid);
                wrti[10].setView(true);// 字段是否可见
                wrti[10].setEdit(false);// 字段是否可编辑
            }
            info("触发子流程------------->拿到请求数据 = 46");
            Map<String, String> map = cfzlcSql("-1978");
            info("触发子流程------------->拿到请求数据 = 47");
            if (!StringUtils.hasText(map.get("id"))) {
                return createJsonResponse(500, "未查询到对应流程，请联系管理员", "");
            }
            info("触发子流程------------->拿到请求数据对应的mapp" + JSONUtil.toJsonStr(cfzlc));
            wrtri[0] = new WorkflowRequestTableRecord();
            wrtri[0].setWorkflowRequestTableFields(wrti);
            workflowMainTableInfo.setRequestRecords(wrtri);
            wri.setCreatorId(cfzlc.getUserId());
            wri.setRequestLevel("0");// 0 正常，1重要，2紧急
            String title = "";
            wri.setRequestName(map.get("workflowname") + " " + format.format(now.getTime()) + " " + userid(cfzlc.getUserId()) + " " + getTitle(cfzlc.getGdxs(), cfzlc.getQfzlx()));// 流程标题
            info("触发子流程------------->拿到请求数据 = 48");
            wri.setWorkflowMainTableInfo(workflowMainTableInfo);
            // 添加工作流id
            WorkflowExtInfo wbi = new WorkflowExtInfo();
            wbi.setWorkflowId(map.get("id"));
            wbi.setWorkflowName(map.get("workflowname"));
            // wbi.setWorkflowTypeId("29");// 流程类别id
            // wbi.setWorkflowTypeName("信科工程-人事管理类");// 流程类别名称
            wri.setWorkflowBaseInfo(wbi);

            info("触发子流程------------->拿到请求数据 = 50");
            try {
                info("触发子流程------------->拿到请求数据1 : " + JSONUtil.toJsonStr(wri));
                requestId = new WorkflowServiceImpl().doCreateWorkflowRequest(wri, Integer.parseInt(cfzlc.getUserId()), "", "");
                if (!"0".equals(cfzlc.getGdxs())) {
                    SqlUtils.insertZTB(8, cfzlc.getRequestId(), cfzlc.getYzxz(), "0", new ThirdMappConfig(), cfzlc.getUserId(), uuid);
                    // 执行过快好像 第一个流程创建会失败
                    Thread.sleep(500);
                    WorkflowMainTableInfo workflowMainTableInfo1 = new WorkflowMainTableInfo();// 主表
                    WorkflowRequestTableRecord[] wrtri1 = new WorkflowRequestTableRecord[1];// 主表字段只有一条记录
                    info("触发子流程------------->拿到请求数据 = 1");
                    // 主字段的3个
                    WorkflowRequestTableField[] wrti1 = new WorkflowRequestTableField[11];
                    WorkflowRequestInfo wri1 = new WorkflowRequestInfo();
                    // 申请人
                    wrti1[0] = new WorkflowRequestTableField();
                    wrti1[0].setFieldName("sqr");
                    // wrti1[0].setFieldValue(rs1.getString(2));
                    wrti1[0].setFieldValue(cfzlc.getUserId());
                    wrti1[0].setView(true);// 字段是否可见
                    wrti1[0].setEdit(false);// 字段是否可编辑

                    info("触发子流程------------->拿到请求数据 = 2");
                    // 所属公司
                    wrti1[1] = new WorkflowRequestTableField();
                    wrti1[1].setFieldName("szgs");
                    // wrti1[0].setFieldValue(rs1.getString(2));
                    wrti1[1].setFieldValue(cfzlc.getSzgs());
                    wrti1[1].setView(true);// 字段是否可见
                    wrti1[1].setEdit(false);// 字段是否可编辑
                    info("触发子流程------------->拿到请求数据 = 3");
                    wrti1[2] = new WorkflowRequestTableField();
                    wrti1[2].setFieldName("sfxyyy");
                    // wrti1[2].setFieldValue(rs1.getString(3));
                    wrti1[2].setFieldValue("1");
                    wrti1[2].setView(true);// 字段是否可见
                    wrti1[2].setEdit(false);// 字段是否可编辑

                    wrti1[3] = new WorkflowRequestTableField();
                    wrti1[3].setFieldName("gdxs");
                    wrti1[3].setFieldValue(cfzlc.getGdxs());
                    wrti1[3].setView(true);// 字段是否可见
                    wrti1[3].setEdit(false);// 字段是否可编辑

                    wrti1[4] = new WorkflowRequestTableField();
                    wrti1[4].setFieldName("lcbh");
                    wrti1[4].setFieldValue(cfzlc.getLcbh());
                    wrti1[4].setView(true);// 字段是否可见
                    wrti1[4].setEdit(false);// 字段是否可编辑

                    wrti1[5] = new WorkflowRequestTableField();
                    wrti1[5].setFieldName("dyrid");
                    wrti1[5].setFieldValue(cfzlc.getRequestId());
                    wrti1[5].setView(true);// 字段是否可见
                    wrti1[5].setEdit(false);// 字段是否可编辑

                    // 甲方   联系方式
                    wrti1[6] = new WorkflowRequestTableField();
                    wrti1[6].setFieldName("jf");
                    wrti1[6].setFieldValue("重庆信科通信工程有限公司");
                    wrti1[6].setView(true);// 字段是否可见
                    wrti1[6].setEdit(false);// 字段是否可编辑

                    wrti1[7] = new WorkflowRequestTableField();
                    wrti1[7].setFieldName("lxfs");
                    wrti1[7].setFieldValue("17602318730");
                    wrti1[7].setView(true);// 字段是否可见
                    wrti1[7].setEdit(false);// 字段是否可编辑

                    wrti1[8] = new WorkflowRequestTableField();
                    wrti1[8].setFieldName("yz");
                    wrti1[8].setFieldValue(cfzlc.getYzxz());
                    wrti1[8].setView(true);// 字段是否可见
                    wrti1[8].setEdit(false);// 字段是否可编辑

                    wrti1[9] = new WorkflowRequestTableField();
                    wrti1[9].setFieldName("qfzlx");
                    wrti1[9].setFieldValue(cfzlc.getQfzlx().equals("0") ? "1" : "0");
                    wrti1[9].setView(true);// 字段是否可见
                    wrti1[9].setEdit(false);// 字段是否可编辑

                    wrti1[10] = new WorkflowRequestTableField();
                    wrti1[10].setFieldName("uuid");
                    wrti1[10].setFieldValue(uuid1);
                    wrti1[10].setView(true);// 字段是否可见
                    wrti1[10].setEdit(false);// 字段是否可编辑
                    info("触发子流程------------->wrti1: " + wrti1.length);
                    wrtri1[0] = new WorkflowRequestTableRecord();
                    wrtri1[0].setWorkflowRequestTableFields(wrti1);
                    workflowMainTableInfo1.setRequestRecords(wrtri1);
                    wri1.setCreatorId(cfzlc.getUserId());
                    wri1.setRequestLevel("0");// 0 正常，1重要，2紧急
                    wri1.setRequestName(map.get("workflowname") + " " + format.format(now.getTime()) + " " + userid(cfzlc.getUserId()) + " " + getTitle(cfzlc.getGdxs(), cfzlc.getQfzlx().equals("0") ? "1" : "0"));// 流程标题
                    wri1.setWorkflowMainTableInfo(workflowMainTableInfo1);
                    // 添加工作流id
                    WorkflowExtInfo wbi1 = new WorkflowExtInfo();
                    wbi1.setWorkflowId(map.get("id"));
                    wbi1.setWorkflowName(map.get("workflowname"));
                    // wbi.setWorkflowTypeId("29");// 流程类别id
                    // wbi.setWorkflowTypeName("信科工程-人事管理类");// 流程类别名称
                    wri1.setWorkflowBaseInfo(wbi1);
                    info("触发子流程------------->拿到请求数据2 : " + JSONUtil.toJsonStr(wri1));
                    requestId = new WorkflowServiceImpl().doCreateWorkflowRequest(wri1, Integer.parseInt(cfzlc.getUserId()), "", "");
                    SqlUtils.insertZTB(9, cfzlc.getRequestId(), cfzlc.getYzxz(), "0", new ThirdMappConfig(), cfzlc.getUserId(), uuid1);
                }
            } catch (Exception e) {
                return createJsonResponse(500, "触发子流程失败", "");
            }
            info("------------->触发子流程成功，requestId = " + requestId);
        } catch (NumberFormatException e) {
            return createJsonResponse(500, "触发子流程失败", "");
        }
        // 存放取放章任务
        return createJsonResponse(200, "触发子流程成功", requestId);
    }

    public String userid(String id) {
        RecordSet con = new RecordSet();
        con.executeQuery("SELECT lastname FROM HrmResource where id=?", new Object[]{id});
        if (con.next()) {
            return con.getString(1);
        }

        return "";
    }

    public static void main1(String[] args) throws IOException {

        // long expireSeconds = 3600L;
        // TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, expireSeconds);
        // request.setBucketName("seal-management");
        // request.setObjectKey("aioScan/aioScanFiles/2025/3/10/15/202503101554147134096.pdf");
        // request.setRequestDate(new Date());
        // // 1分钟后链接失效
        // request.setExpires(60);
        // ObsClient obsClient = new ObsClient("DKYYDBTLG7OMINWELGRD", "aQ3ToJOFCNuehFvQl4ZBYPu9WIn7uG4Bxf5GUpg4", "obs.cn-south-1.myhuaweicloud.com");
        // // ObsClient obsClient = obsBiz.getObsClient();
        // // 通过临时授权,直接访问链接下载
        // TemporarySignatureResponse response  = obsClient.createTemporarySignature(request);
        //
        // System.out.println("signature = " + response );
        // Request.Builder builder = new Request.Builder();

        // String filePath = "seal-management/callBack/callBackPdf/2025/3/11/15/202503111556124794096.pdf";
        // Map<String, String> resultMap = splitFilePath(filePath);
        // System.out.println(resultMap);
        System.out.println("Base64.encode(\"1303685\") = " + Base64.encode("1303685"));
    }

    public static void main(String[] args) {

        ObsConfiguration config = new ObsConfiguration();
        config.setEndPoint("obs.cn-south-1.myhuaweicloud.com");

        // 设置连接超时时间（单位：毫秒），默认5000毫秒（5秒）
        config.setConnectionTimeout(10000); // 改为10秒

        // 设置Socket超时时间（单位：毫秒），默认30000毫秒（30秒）
        // 上传图片建议设置更长，例如5分钟（300000毫秒）
        config.setSocketTimeout(300000);
        ObsClient obsClient = new ObsClient("DKYYDBTLG7OMINWELGRD", "aQ3ToJOFCNuehFvQl4ZBYPu9WIn7uG4Bxf5GUpg4", config);
        long expireSeconds = 300L;
        TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, expireSeconds);
        request.setBucketName("seal-management-dev");
        request.setObjectKey("ec_contract/20250529/217127425601961985.pdf");
        request.setRequestDate(new Date());
        // 1分钟后链接失效
        request.setExpires(300);
        // ObsClient obsClient = obsBiz.getObsClient();
        // 通过临时授权,直接访问链接下载
        TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
        // info("getHUUrl------------------------>  拿到华为云OBS 临时url: " + response.getSignedUrl());
        System.out.println(response.getSignedUrl());

        // // --- 回调处理演示 ---
        // final String API_SECRET = "YourSecretKey12345"; // 必须与印管平台配置的密钥一致
        // CallbackService callbackService = new CallbackService(API_SECRET);
        //
        // // 模拟印管平台发送的回调请求JSON (来自接口示例)
        // String mockCallbackRequestBody = "{"
        //         + "\"appKey\": \"test\","
        //         + "\"apiKey\": \"YXYG04_02\","
        //         + "\"bizId\": \"2d23a78864ca4344976fa848594c27bb\","
        //         + "\"timestamp\": 1656056217000,"
        //         + "\"params\": \"{\\\"id\\\":3007981,\\\"tenant\\\":\\\"test\\\",\\\"deptId\\\":\\\"2\\\",\\\"deptName\\\":\\\"财务部\\\",\\\"useTime\\\":\\\"2024-03-12 11:31:49\\\",\\\"userId\\\":\\\"1\\\",\\\"userName\\\":\\\"张三\\\",\\\"uuid\\\":\\\"0X1821STE212401N00000107\\\",\\\"deviceName\\\":\\\"测试章\\\",\\\"applicationId\\\":\\\"0728-001\\\",\\\"useCount\\\":0,\\\"model\\\":\\\"审批模式\\\",\\\"camera\\\":true,\\\"location\\\":\\\"安徽省合肥市蜀山区望江西路靠近沉浸式媒体技术文化部重点实验室\\\",\\\"videoFileUrl\\\":\\\"https://xxxx/xxx.m3u8\\\",\\\"faceFileUrl\\\":\\\"https://xxx/xxx.jpg\\\",\\\"useFileUrl\\\":\\\"https://xxx/xxx.jpg\\\",\\\"auditFileUrls\\\":[\\\"https://xxx/xxx.jpg\\\",\\\"https://xxx/xxx.jpg\\\"],\\\"timeoutFileUrls\\\":[\\\"https://xxx/xxx.jpg\\\",\\\"https://xxx/xxx.jpg\\\"]}\""
        //         + "}";
        // ApiRequest bean = JSONUtil.toBean(mockCallbackRequestBody, ApiRequest.class);
        // // // // 调用我们的处理服务
        // // // callbackService.handleCallback(mockCallbackRequestBody);
        // // new SealWeb().yunxiCallback(bean);
        // // callbackService.handleCallback(mockCallbackRequestBody);
        // System.out.println(JSONUtil.toJsonStr(bean));
    }


    public String getTitle(String gdxs, String qfzlx) {
        if (("0").equals(gdxs)) {
            return "电子用印";
        } else if (("1").equals(gdxs) && "0".equals(qfzlx)) {
            return "取章";
        } else if (("1").equals(gdxs) && "1".equals(qfzlx)) {
            return "放章";
        } else {
            return "";
        }
    }

}
