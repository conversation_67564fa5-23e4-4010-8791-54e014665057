package com.api.cyitce.seal3.integration.eSeal.hrm;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.constant.ESealApiUrl;
import com.api.cyitce.seal3.integration.eSeal.ESealBaseIntergration;
import com.api.cyitce.seal3.vo.req.contract.BindLegalPerson;
import com.api.cyitce.seal3.vo.req.contract.CompanyInfo;
import com.api.cyitce.seal3.vo.req.contract.TemplateDetailsApiReq;
import com.api.cyitce.seal3.vo.req.hrm.CreateOrgApiReq;
import com.api.cyitce.seal3.vo.req.hrm.UpdateOrgApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;

public class CreateOrgIntegration extends ESealBaseIntergration {

    /**
     * 创建企业
     **/
    public BaseResp<JSONObject> createOrg(CreateOrgApiReq req) {
        req.setAuthentication(true);

        return sendPost(ESealApiUrl.CREATE_ENTERPRISE, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * 更新企业
     **/
    public BaseResp<JSONObject> updateOrg(UpdateOrgApiReq req) {
        return sendPost(ESealApiUrl.UPDATE_ENTERPRISE, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * 更新企业
     **/
    public BaseResp<JSONObject> bindLegalPerson(BindLegalPerson req) {
        return sendPost(ESealApiUrl.BIND_LEGAL, JSONUtil.parseObj(req), new JSONObject());
    }


    /**
     * 企业详情
     **/
    public BaseResp<JSONObject> companyInfo(CompanyInfo req) {
        return sendPost(ESealApiUrl.ENTERPRISE_DETAILS, JSONUtil.parseObj(req), new JSONObject());
    }


    /**
     * 模板详情
     **/
    public BaseResp<JSONObject> modelInfo(TemplateDetailsApiReq req) {
        return sendPost(ESealApiUrl.TEMPLATE_DETAILS, JSONUtil.parseObj(req), new JSONObject());
    }
}
