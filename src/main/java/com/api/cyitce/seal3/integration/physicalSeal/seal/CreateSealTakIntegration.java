package com.api.cyitce.seal3.integration.physicalSeal.seal;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.constant.PhysicalSealApiUrl;
import com.api.cyitce.seal3.integration.physicalSeal.PhysicalSealBaseIntergration;
import com.api.cyitce.seal3.vo.req.seal.physical.*;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.seal.physical.PhysicalSealInfoApiResp;

public class CreateSealTakIntegration extends PhysicalSealBaseIntergration {
    /**
     * @description: 获取印章列表
     * @author:
     **/
    public BaseResp<PhysicalSealInfoApiResp> getSealList(PhysicalSealListApiReq req) {
        return sendPost(PhysicalSealApiUrl.GET_SEAL_LIST, JSONUtil.parseObj(req), new PhysicalSealInfoApiResp());
    }

    /**
     * @description: 创建【开放、封闭、便携】盖章任务
     * @author:
     **/
    public BaseResp<JSONObject> createSealUseTask(SealUseApplyApiReq req) {
        return sendPost(PhysicalSealApiUrl.CREATE_SEAL_USE_TASK, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 创建取放章任务
     * @author:
     **/
    public BaseResp<JSONObject> createSealPickTask(SealUsePickApiReq req) {
        return sendPost(PhysicalSealApiUrl.CREATE_SEAL_USE_PICK_TASK, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 创建(打印扫描盖章机)盖章任务
     * @author:
     **/
    public BaseResp<JSONObject> createPrintSealUseTask(PrintSealUseTaskApiReq req) {
        return sendPost(PhysicalSealApiUrl.CREATE_SEAL_USE_PRINT_TASK, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 撤销接口
     * @author:
     **/
    public BaseResp<JSONObject> cancelTask(CancelInfoReq req) {
        return sendPost(PhysicalSealApiUrl.CANCEL_TASK, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * 更新密码信息
     *
     * @param
     */
    public BaseResp<JSONObject> updatePassword(LoginInfoReq req) {
        return sendPost(PhysicalSealApiUrl.UPDATE_PASSWORD, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 用户信息
     * @author:
     **/
    public BaseResp<JSONObject> userInfo(LoginInfoReq req) {
        return sendPost(PhysicalSealApiUrl.USER_INFO, JSONUtil.parseObj(req), new JSONObject());
    }


}
