package com.api.cyitce.seal3.integration.eSeal;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.eSeal.ESealBaseAuth;
import com.api.cyitce.seal3.util.http.PostUtil;
import com.api.cyitce.seal3.vo.resp.BaseResp;

/**
 * @ClassName: BaseIntergration
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  15:30
 * @Version: 1.0
 */
public class ESealBaseIntergration extends BaseLog {
    public static ESealBaseAuth config = Singleton.get(ESealBaseAuth.class);

    public static <T> BaseResp<T> sendPost(String url, JSONObject requestJson, T t) {
        JSONObject responseJson = PostUtil.toPost(url, requestJson, config);
        // 使用 TypeReference 精确转换 BaseResp<T>
        BaseResp<T> tBaseResp = JSONUtil.toBean(responseJson, new TypeReference<BaseResp<T>>() {}, false);
        Object o = JSONUtil.toBean(JSONUtil.parseObj(tBaseResp.getData()), t.getClass());
        t = (T) o;
        tBaseResp.setData(t);
        return tBaseResp;
    }

    public String getFileBase64(String url) {
        return Base64.encode(FileUtil.readBytes(System.getProperty("user.dir") + "/src/main/resources/" + url));
    }
}