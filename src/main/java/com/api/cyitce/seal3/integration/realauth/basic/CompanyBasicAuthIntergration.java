package com.api.cyitce.seal3.integration.realauth.basic;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.constant.RealAuthApiUrl;
import com.api.cyitce.seal3.integration.realauth.RealAuthBaseIntergration;
import com.api.cyitce.seal3.vo.req.hrm.RealOrgApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;

public class CompanyBasicAuthIntergration extends RealAuthBaseIntergration {
    /**
     * @description: 企业工商信息核验
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> companyAuth(RealOrgApiReq req) {
        JSONObject jsonReq = JSONUtil.parseObj(req);
        jsonReq.put("serviceCode","idb0011");
        config.setServiceCode("idb0011");
        return sendPost(RealAuthApiUrl.ENTERPRISE_AUTH_MOBILE,jsonReq);
    }
}
