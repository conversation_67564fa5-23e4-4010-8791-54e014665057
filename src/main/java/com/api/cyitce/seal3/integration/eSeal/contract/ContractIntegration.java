package com.api.cyitce.seal3.integration.eSeal.contract;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.eSeal.ESealDataSource;
import com.api.cyitce.seal3.constant.ESealApiUrl;
import com.api.cyitce.seal3.integration.eSeal.ESealBaseIntergration;
import com.api.cyitce.seal3.vo.req.SignByFileOrTemplateApiReq;
import com.api.cyitce.seal3.vo.req.contract.*;
import com.api.cyitce.seal3.vo.req.contract.signByFile.AddSignerByFileApiReq;
import com.api.cyitce.seal3.vo.req.contract.signByFile.DownloadApiVO;
import com.api.cyitce.seal3.vo.req.pagesign.PageSignReq;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.api.cyitce.seal3.vo.resp.contract.ContractApiVO;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.contract.AddSignerApiResp;
import com.api.cyitce.seal3.vo.resp.contract.DownloadResp;
import com.api.cyitce.seal3.vo.resp.pagesign.PageSignResp;

import java.sql.SQLException;

/**
 * @ClassName: ContractIntegration
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  09:59
 * @Version: 1.0
 */
public class ContractIntegration extends ESealBaseIntergration {

    /**
     * @description:创建合同
     * @author: lijianpan
     **/
    public BaseResp<ContractApiVO> createByFile(CreateByFileApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_CREATE_BY_FILE, JSONUtil.parseObj(req), new ContractApiVO());
    }

    /**
     * @description: 添加签署人
     * @author: lijianpan
     **/
    public BaseResp<AddSignerApiResp> addSignerByFile(AddSignerByFileApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_ADD_SIGNER_BY_FILE, JSONUtil.parseObj(req), new AddSignerApiResp());
    }

    /**
     * @description: 页面签署
     * @author: lijianpan
     **/
    public BaseResp<PageSignResp> startSignByFile(PageSignReq pageSignReq) {
        return sendPost(ESealApiUrl.START_SIGN_BY_FILE, JSONUtil.parseObj(pageSignReq), new PageSignResp());
    }

    /**
     * @description: 上传合同文件
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> uploadFile(UploadFileApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_UPLOAD_FILE, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 下载合同
     * @author: lijianpan
     **/
    public BaseResp<DownloadResp> downloadContract(DownloadApiVO req) {
        return sendPost(ESealApiUrl.CONTRACT_DOWNLOAD, JSONUtil.parseObj(req), new DownloadResp());
    }

    /**
     * @description: 合同发起、撤回、结束
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> operateContractStatus(ContractStatusApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_OPERATE_CONTRACT_STATUS, JSONUtil.parseObj(req), new JSONObject());
    }

    /**
     * @description: 合同操作删除
     * @author: lijianpan
     **/
    public BaseResp<JSONObject> contractDelete(ContractDeleteApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_DELETE_CONTRACT, JSONUtil.parseObj(req), new JSONObject());
    }


    /**
     * @description: 模板创建合同
     **/
    public BaseResp<ContractApiVO> createModelContract(CreateByTemplateApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_CREATE_BY_TEMPLATE, JSONUtil.parseObj(req), new ContractApiVO());
    }

    /**
     * 合同详情
     **/
    public BaseResp<ContractApiVO> search(ContractDetailsApiReq req, boolean responseContractFile) {
        req.setResponseContractFile(responseContractFile);
        return sendPost(ESealApiUrl.CONTRACT_SEARCH, JSONUtil.parseObj(req), new ContractApiVO());
    }

    /**
     * @description: 模板添加签署人
     **/
    public BaseResp<AddSignerApiResp> addSignerByTemplate(AddSignerByTemplateApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_ADD_SIGNER_BY_TEMPLATE, JSONUtil.parseObj(req), new AddSignerApiResp());
    }

    /**
     * @description: 模板发送页面签
     **/
    public BaseResp<PageSignResp> startSignByTemplateFile(StartSignByFileTemplateRequestVO req) {
        return sendPost(ESealApiUrl.START_SIGN_BY_FILE_TEMPLATE, JSONUtil.parseObj(req), new PageSignResp());
    }

    /**
     * @description: 合同签署
     **/
    public BaseResp<AddSignerApiResp> startSignerByFile(SignByFileOrTemplateApiReq req) {
        return sendPost(ESealApiUrl.CONTRACT_SIGN_BY_FILE, JSONUtil.parseObj(req), new AddSignerApiResp());
        // return sendPost(ESealApiUrl.START_SIGN_BY_FILE_TEMPLATE, JSONUtil.parseObj(req), new PageSignResp());
    }


    /**
     * 通过编号 查询合同id
     **/
    public BaseResp<String> searchByCode(String code) {
        ESealDataSource rs = new ESealDataSource();
        try {
            rs.executeQuery("select id from ec_contract where code=?", new String[]{code});
            if (rs.next()) {
                return MsgResp.ok(rs.getString(1));
            }

            return MsgResp.error("合同编号不存在");
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}