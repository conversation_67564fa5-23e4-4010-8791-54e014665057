package com.api.cyitce.seal3.action.create;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.enums.seal.SealFromEnum;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.Map;

import static com.api.cyitce.seal3.util.FileUtils.getDocName;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/24 11:07
 * @describe 校验文档数据，公司是否支持
 */
public class checkAction extends BaseLog implements Action {
    // 保密部门 id
    public static final String BMBM = "344";
    private String configId = "1";

    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        info("拿到当前操作人--------------->id:" + user.getUID());
        // 是否需要用印
        // String sfxyqz = "1";
        int sealType = -1;
        int signerId = 0;
        boolean flag = false;
        String jbrbm = "-1";
        int companyId = -1;
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("checkAction------------------> configId:" + configId);
        info("checkAction------------------> wconfig:" + JSONUtil.toJsonStr(wconfig));
        String jbr = "";
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            info("checkAction------------------> 进入明细表");
            for (Property p : properties) {
                if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if ("swszfb".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("szgs".equals(p.getName())) {
                    if (!StringUtils.hasText(String.valueOf(companyId))) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                }
            }
            // 校验是否支持当前业务公司
            String yqgs = wconfig.getYqgs();
            if (StringUtils.hasText(yqgs)) {
                ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
                boolean stringExist = isStringExist(yqgs, String.valueOf(companyId));
                info("checkAction------------------> 判断是否支持改公司companyId：" + companyId + "  res:" + stringExist);
                if (!stringExist) {
                    requestInfo.getRequestManager().setMessagecontent("该流程暂不支持该业务公司办理");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
            for (Property p : properties) {
                info("checkAction------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                } else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }


            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印 出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }

            // if (flag && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) {
            //     requestInfo.getRequestManager().setMessagecontent("保密文件只支持纸质鲜章件");
            //     return Action.FAILURE_AND_CONTINUE;
            // }

            // 实体章先不执行执行
            // if (!StringUtils.hasText(wconfig.getSfkqstyz()) || (!"1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue())) {
            //     info("CreateContractAction------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }
            // 实体章先不执行执行
            // if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue())) {
            //     info("CreateContractAction------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }
            // jbr = obj.get("jbr");
            //
            // // 法律  jbrbm 涉密项目
            // jbrbm = obj.get("jbrbm");
            //
            // if (StringUtils.hasText(jbrbm) && jbrbm.equals("344")) {
            //     flag = true;
            // }

            if (wconfig.isDetail()) {
                String docId = "-1";
                DetailTable[] detailTables1 = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt1 = detailTables1[wconfig.getDatasource() - 1];
                info("checkAction-------------------> 拿到当前明细表: " + (wconfig.getDatasource() - 1));
                Row[] row = dt1.getRow();
                if (flag) {
                    if (row.length <= 0) {
                        requestInfo.getRequestManager().setMessagecontent("保密办请至少添加一行用印明细");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                }
                for (Row r : row) {
                    String sfxyqz = "0";
                    String qzr = "";
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        info("明细表" + (wconfig.getDatasource() - 1) + "  属性名称:" + c.getName() + "属性值:" + c.getValue());
                        if (c.getName().equals(wconfig.getFfile())) {
                            info("jbrbm: " + jbrbm + ": c.value:" + c.getValue());
                            if (!flag) {
                                info("length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length != 1) {
                                    requestInfo.getRequestManager().setMessagecontent("用印,每行只能上传一个附件!");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                docId = c.getValue();
                                String docName = getDocName(docId);
                                info("checkAction----------------------> 文件名长度:" + docName.length());
                                if (docName.length() > 210 && sealType == SealFromEnum.E_SEAL.getValue()) {
                                    requestInfo.getRequestManager().setMessagecontent("文件名过长!");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                            }

                        } else if ("qzr".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                qzr = c.getValue();
                            }
                        } else if ("sfxyqz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sfxyqz = c.getValue();
                            }
                        }
                        if (SealFromEnum.E_SEAL.getValue() == sealType) {
                            // 判断是否是10
                            // if ("10".equals(configId)) {
                            //     info("checkAction ---------------------------> 签字查询");
                            //     // 判断当前是否需要签字
                            //     if ("1".equals(sfxyqz)) {
                            //         // 签字
                            //         // 判断操作人是否已经在天威云上面实名认证了
                            //         // 查看当前操作人是否需要签字
                            //         // 校验所有人是否已经在天威云上面实名
                            //         int userId = Integer.parseInt(qzr);
                            //         info("checkAction ---------------------------> 签字查询 ：是否签字：" + sfxyqz + "   签字人:" + qzr);
                            //         RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
                            //         info("checkAction ---------------------------> realUser 用户信息: " + realUser.toString());
                            //         ESealConfig config = new ESealConfig();
                            //         String userCode = OaHrmUtils.getworkCode(userId);
                            //         HrmResourceApiReq info = OaHrmUtils.getAllInfo(userId);
                            //         BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                            //         String sendUserId = resp4.getData();
                            //         if (!resp4.isStatus()) {
                            //             info("checkAction ---------------------------> 查询用户信息失败，去天威云查询");
                            //             Map<String, String> hrInfo = getHrInfo(userId);
                            //             if (hrInfo.isEmpty()) {
                            //                 requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + " : 未查询到对应用户手机号，请联系管理员!");
                            //                 return Action.FAILURE_AND_CONTINUE;
                            //             }
                            //             UserListReq userListReq = new UserListReq();
                            //             userListReq.setPhone(hrInfo.get("phone"));
                            //             BaseResp<UserListWrapper> phone = new CreatUserIntegration().queryUserList(userListReq);
                            //             info("checkAction ---------------------------> 天威云查询请求数据： " + JSONUtil.toJsonStr(userListReq));
                            //             if (!phone.isStatus()) {
                            //                 info("checkAction ---------------------------> 天威云查询请求数据失败 : " + JSONUtil.toJsonStr(phone));
                            //                 requestInfo.getRequestManager().setMessagecontent("  查询用户请求错误，请联系管理员!");
                            //                 return Action.FAILURE_AND_CONTINUE;
                            //             }
                            //             List<UserInfo> userList = phone.getData().getUserList();
                            //             if (CollectionUtils.isEmpty(userList)) {
                            //                 info("checkAction ---------------------------> 天威云查询查询数据为空 : " + JSONUtil.toJsonStr(userList));
                            //                 requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + " ：尚未在天威云实名，请使用手机号[" + hrInfo.get("phone") + "] 去实名。");
                            //                 return Action.FAILURE_AND_CONTINUE;
                            //             }
                            //             // 存在 存入对应信息
                            //             for (UserInfo user1 : userList) {
                            //                 sendUserId = user1.getUserId();
                            //                 BaseResp<Void> voidBaseResp = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(sendUserId, userCode, 2);
                            //                 if (!voidBaseResp.isStatus()) {
                            //                     info("sendToSms ---------------------------> 存入三方映射信息失败  用户id: " + userCode + "  三方id: " + sendUserId);
                            //                     requestInfo.getRequestManager().setMessagecontent("存入三方映射信息失败 请联系管理员");
                            //                     return Action.FAILURE_AND_CONTINUE;
                            //                 }
                            //             }
                            //         }
                            //     }
                            // }

                        }

                    }
                }

                // if ("4".equals(configId)) {
                //     info("checkAction----------------------> 模板进行校验:");
                //     if ("1".equals(obj.get("sfsymb"))) {
                //         info("checkAction----------------------> 模板进行校验,需要模板");
                //         String s = obj.get(wconfig.getMbzd());
                //         info("checkAction----------------------> 模板进行校验,拿到具体事项： " + s);
                //         if ("2".equals(s)) {
                //             String bsdh = obj.get("jcdh");
                //             Map<String, String> map = getcusDataInfo(bsdh, "1");
                //             info("checkAction----------------------> 模板进行校验,劳动解除 查询人手机号: " + bsdh);
                //             if (map.isEmpty()) {
                //                 requestInfo.getRequestManager().setMessagecontent("数藤平台未查询到此人信息，请核实手机号是否正确");
                //                 return Action.FAILURE_AND_CONTINUE;
                //             }
                //             info("checkAction----------------------> 模板进行校验,劳动解除 查询人id: " + map.get("id"));
                //             Integer userId = Integer.parseInt(map.get("id"));
                //             RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
                //             info("checkAction ---------------------------> realUser 用户信息: " + realUser.toString());
                //             String userCode = OaHrmUtils.getworkCode(userId);
                //             HrmResourceApiReq info = OaHrmUtils.getAllInfo(userId);
                //             BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                //             String sendUserId = resp4.getData();
                //             if (!resp4.isStatus()) {
                //                 info("checkAction ---------------------------> 查询用户信息失败，去天威云查询");
                //                 Map<String, String> hrInfo = getHrInfo(userId);
                //                 if (hrInfo.isEmpty()) {
                //                     requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + " : 未查询到对应用户手机号，请联系管理员!");
                //                     return Action.FAILURE_AND_CONTINUE;
                //                 }
                //                 UserListReq userListReq = new UserListReq();
                //                 userListReq.setPhone(hrInfo.get("phone"));
                //                 BaseResp<UserListWrapper> phone = new CreatUserIntegration().queryUserList(userListReq);
                //                 info("checkAction ---------------------------> 天威云查询请求数据： " + JSONUtil.toJsonStr(userListReq));
                //                 if (!phone.isStatus()) {
                //                     info("checkAction ---------------------------> 天威云查询请求数据失败 : " + JSONUtil.toJsonStr(phone));
                //                     requestInfo.getRequestManager().setMessagecontent("  查询用户请求错误，请联系管理员!");
                //                     return Action.FAILURE_AND_CONTINUE;
                //                 }
                //                 List<UserInfo> userList = phone.getData().getUserList();
                //                 if (CollectionUtils.isEmpty(userList)) {
                //                     info("checkAction ---------------------------> 天威云查询查询数据为空 : " + JSONUtil.toJsonStr(userList));
                //                     requestInfo.getRequestManager().setMessagecontent(info.getStaffName() + " ：尚未在天威云实名，请使用手机号[" + hrInfo.get("phone") + "] 去实名。");
                //                     return Action.FAILURE_AND_CONTINUE;
                //                 }
                //                 // 存在 存入对应信息
                //                 for (UserInfo user1 : userList) {
                //                     sendUserId = user1.getUserId();
                //                     BaseResp<Void> voidBaseResp = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(sendUserId, userCode, 2);
                //                     if (!voidBaseResp.isStatus()) {
                //                         info("checkAction ---------------------------> 存入三方映射信息失败  用户id: " + userCode + "  三方id: " + sendUserId);
                //                         requestInfo.getRequestManager().setMessagecontent("存入三方映射信息失败 请联系管理员");
                //                         return Action.FAILURE_AND_CONTINUE;
                //                     }
                //                 }
                //             }
                //         }
                //     }
                // }
            }

        } else {
            info("checkAction------------------> 进入主表");
            for (Property p : properties) {
                info("checkAction 回退开始执行------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("checkAction 回退开始执行-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if (configId.equals("7")) {
                            // 支出合同 0 纸质 1 电子
                            if ("0".equals(p.getValue())) {
                                sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                            } else if ("1".equals(p.getValue())) {
                                sealType = SealFromEnum.E_SEAL.getValue();
                            }
                        } else if (configId.equals("8")) {
                            // 采购结算单 1 纸质  0 电子
                            if ("0".equals(p.getValue())) {
                                sealType = SealFromEnum.E_SEAL.getValue();
                            } else if ("1".equals(p.getValue())) {
                                sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                            }
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                if (p.getName().equals(wconfig.getGszd())) {
                    if (StringUtils.hasText(wconfig.getGszd())) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                } else if ("swszfb".equals(p.getName())) {
                    companyId = Integer.parseInt(p.getValue());
                } else if ("szgs".equals(p.getName())) {
                    if (!StringUtils.hasText(String.valueOf(companyId))) {
                        companyId = Integer.parseInt(p.getValue());
                    }
                }
                obj.put(p.getName(), p.getValue());
            }
            // 校验是否支持当前业务公司
            String yqgs = wconfig.getYqgs();
            if (StringUtils.hasText(yqgs)) {
                ThirdMappConfig thirdMappConfig = new ThirdMappConfig();
                boolean stringExist = isStringExist(yqgs, String.valueOf(companyId));
                info("checkAction------------------> 判断是否支持改公司companyId：" + companyId + "  res:" + stringExist);
                if (!stringExist) {
                    requestInfo.getRequestManager().setMessagecontent("该流程暂不支持该业务公司办理");
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
        }


        return Action.SUCCESS;
    }

    public static boolean isStringExist(String input, String number) {
        // 先检查输入字符串是否为空，若为空则直接返回 false
        if (input == null || input.isEmpty()) {
            return false;
        }
        // 将输入字符串按逗号分割成字符串数组
        String[] numbers = input.split(",");
        for (String numStr : numbers) {
            if (number.equals(numStr)) {
                return true;
            }
        }
        // 遍历完整个数组都没找到匹配数字，返回 false
        return false;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public static void main(String[] args) {
        String str = "中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省综合类房屋维合同中国移动甘肃公司2025年至2026年全省.pdf";
        int length = str.length();
        System.out.println("字符串的字符数量为: " + length);
    }

}