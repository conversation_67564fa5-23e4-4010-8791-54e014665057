package com.api.cyitce.seal3.action.create;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.enums.seal.SealFromEnum;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreateOrgIntegration;
import com.api.cyitce.seal3.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal3.service.eSeal.impl.SignServiceImpl;
import com.api.cyitce.seal3.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal3.util.data.OaHrmUtils;
import com.api.cyitce.seal3.vo.req.contract.CompanyInfo;
import com.api.cyitce.seal3.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal3.vo.req.hrm.JoinOrgReq;
import com.api.cyitce.seal3.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.api.cyitce.seal3.web.SealWeb;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.StringUtils;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import javax.ws.rs.core.Response;
import java.util.*;

import static com.api.cyitce.seal3.action.create.checkAction.BMBM;
import static com.api.cyitce.seal3.util.sql.SqlUtils.getCompanyInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/12 16:32
 * @describe 给签字人发送页面签署
 */
public class jbrAction extends BaseLog implements Action {

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String configId = "1";
    private static Set<Integer> realAccountSet = new HashSet<>();

    /**
     * 保密部门 344 	jbrbm
     * 经办人 	jbr
     */

    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        info("拿到当前操作人--------------->", JSONUtil.toJsonStr(user));
        info("拿到当前操作人--------------->id:" + user.getUID());
        // 是否需要用印
        int sealType = -1;
        int signerId = 0;
        boolean flag = false;
        String jbrbm = "-1";
        int companyId = -1;
        String docId = "";

        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("checkAction------------------> configId:" + configId);
        info("checkAction------------------> wconfig:" + JSONUtil.toJsonStr(wconfig));
        String jbr = "";
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        for (Property p : properties) {
            if (p.getName().equals(wconfig.getFsealType())) {
                info("CreateContractAction-------------------> 主表 sealType: " + p.getValue());
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                }
            } else if (p.getName().equals(wconfig.getGszd())) {
                if (StringUtils.hasText(wconfig.getGszd())) {
                    companyId = Integer.parseInt(p.getValue());
                }
            } else if ("swszfb".equals(p.getName())) {
                companyId = Integer.parseInt(p.getValue());
            } else if ("szgs".equals(p.getName())) {
                companyId = Integer.parseInt(p.getValue());
            } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (!"0".equals(p.getValue())) {
                        info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (BMBM.equals(p.getValue())) {
                        flag = true;
                    }
                }
            } else if ("smdj".equals(p.getName())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                        flag = true;
                    }
                }
            }
            obj.put(p.getName(), p.getValue());
        }
        String sfxyyy = wconfig.getSfxyyy();
        if (!StringUtils.hasText(sfxyyy)) {
            requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!obj.get(sfxyyy).equals("1")) {
            info("执行 不盖章逻辑" + obj.get(sfxyyy));
            return Action.SUCCESS;
        }
        // 实体章先不执行执行
        if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            info("CreateContractAction------------------->  实体章先不执行执行");
            return Action.SUCCESS;
        }
        if (wconfig.isDetail()) {
            String sfxyqz = "0";
            String qzr = "";
            DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
            DetailTable dt = detailTables[wconfig.getDatasource() - 1];
            for (Row r : dt.getRow()) {
                String lwqzr = "";
                Cell[] cs = r.getCell();
                for (Cell c : cs) {
                    if (c.getName().equals(wconfig.getFfile())) {
                        if (!flag) {
                            info("jbrAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                        info("docId" + c.getValue());
                        docId = c.getValue();
                    } else if (wconfig.getFsigner().equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            info("docId1: " + c.getName());
                            signerId = Integer.parseInt(c.getValue());
                            info("docId1: " + signerId);
                        }
                    } else if ("shnryy".equals(c.getName())) {
                        if (!flag) {
                            info("jbrAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                        if (StringUtils.hasText(c.getValue())) {
                            info("docId" + c.getValue());
                            docId = c.getValue();
                        }
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    } else if ("lwqzr".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            lwqzr = c.getValue();
                        }
                    } else if ("qzr".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            qzr = c.getValue();
                        }
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    }
                }
                // boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                // info("jbrAction------------> ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
                if (SealFromEnum.PHYSICAL_SEAL.getValue() == sealType) {
                    info("jbrAction  ----------------------->  签字 实体章 不需要");
                    return Action.SUCCESS;
                }
                // 发送页面签署  盖章
                if (SealFromEnum.E_SEAL.getValue() == sealType) {
                    Map<String, String> companyInfo = getCompanyInfo(companyId);
                    // 校验当前是否需要签字
                    if ("1".equals(sfxyqz)) {
                        // 签字
                        // 判断操作人是否已经在天威云上面实名认证了
                        // 查看当前操作人是否需要签字
                        int userId = Integer.parseInt(qzr);
                        info("jbrAction  ----------------------->  签字 签字人: " + qzr);
                        String userCode = OaHrmUtils.getworkCode(userId);
                        HrmResourceApiReq info = OaHrmUtils.getAllInfo(userId);
                        BaseResp<String> resp4 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(userCode, 2);
                        if (!resp4.isStatus()) {
                            requestInfo.getRequestManager().setMessagecontent(info.getUsername() + " : 没有在天威云实名，请联系管理员!");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        Response response = new SealWeb().sendToSms(userId, docId, String.valueOf(companyId), 0, null, null);
                        info("jbrAction  ----------------------->  发送短信请求响应: " + JSONUtil.toJsonStr(JSONUtil.parseObj(response.getEntity())));
                        JSONObject jsonObject = JSONUtil.parseObj(response.getEntity());
                        if (!"200".equals(String.valueOf(jsonObject.get("code")))) {
                            info("jbrAction  ----------------------->  请求签名失败: " + JSONUtil.toJsonStr(jsonObject));
                            requestInfo.getRequestManager().setMessagecontent(info.getUsername() + " : " + String.valueOf(jsonObject.get("message")));
                            return Action.FAILURE_AND_CONTINUE;
                        } else {
                            info("jbrAction  ----------------------->  请求签名成功: " + JSONUtil.toJsonStr(jsonObject));
                            String content = "【" + companyInfo.get("subcompanyname") + "】您有一份待签署的订单，请前往 " + String.valueOf(jsonObject.get("data")) + " 进行查看并签署。进行查看并签署。注意：签署有效期为24小时，请尽快完成。";
                            // 请求成功发送数据
                            BaseResp<JSONObject> resp1 = new SignServiceImpl().sendSms(resp4.getData(), String.valueOf(jsonObject.get("data")), content);
                            if (!resp1.isStatus()) {
                                info("jbrAction  ----------------------->   发送短信失败: ");
                                requestInfo.getRequestManager().setMessagecontent(info.getUsername() + " : 发送短信失败");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                    }


                    // info("jbrAction  ----------------------->  经办人 实名开始");
                    // BaseResp<JSONObject> resp = new UserRealAuthAction().realCreaterAccount(Integer.valueOf(jbr), companyId);
                    // if (!resp.isStatus()) {
                    //     requestInfo.getRequestManager().setMessagecontent("经办人实名认证失败");
                    //     return Action.FAILURE_AND_CONTINUE;
                    // }
                    // 判断当前这个是否已经签了字
                    // 发送页面签署

                    // info("jbrAction  ----------------------->  电子印章  添加 签署人数量，签署人");
                    // int i = changeThirdNum(0, Base64.encode(docId), Integer.parseInt(jbr), new ThirdMappConfig());
                    // if (i == 1) {
                    //     requestInfo.getRequestManager().setMessagecontent("三方表修改失败");
                    //     return Action.FAILURE_AND_CONTINUE;
                    // }
                    info("jbrAction  ----------------------->  电子印章  添加 签署人数量，签署人成功");
                }


                // if (b && StringUtils.hasText(lwqzr)) {
                //     info("jbrAction  ----------------------->  实体印章 另外签字人 实名开始 ");
                //     BaseResp<JSONObject> resp = new UserRealAuthAction().realCreaterAccount(Integer.valueOf(lwqzr), companyId);
                //     if (!resp.isStatus()) {
                //         requestInfo.getRequestManager().setMessagecontent("实体印章 另外签字人实名认证失败");
                //         return Action.FAILURE_AND_CONTINUE;
                //     }
                //     // 发送页面签署
                //     Response response = new SealWeb().sendToSms(Integer.valueOf(lwqzr), docId, String.valueOf(companyId), null, null);
                //     if (response.getStatus() != 200) {
                //         requestInfo.getRequestManager().setMessagecontent("实体印章 另外签字人 发送页面签署失败");
                //         return Action.FAILURE_AND_CONTINUE;
                //     }
                //     // info("jbrAction  ----------------------->  实体印章 电子签字  添加 签署人数量，签署人：" + Base64.encode(docId));
                //     // int i = changeThirdNum(0, Base64.encode(docId), Integer.parseInt(lwqzr), new ThirdMappConfig());
                //     // if (i == 1) {
                //     //     requestInfo.getRequestManager().setMessagecontent("三方表修改失败");
                //     //     return Action.FAILURE_AND_CONTINUE;
                //     // }
                //     info("jbrAction  ----------------------->  实体印章 电子签字  添加 签署人数量，签署人成功：" + Base64.encode(docId));
                // }
            }
        }
        return Action.SUCCESS;
    }


    private BaseResp<JSONObject> realCreaterAccount(Integer userId, int companyId) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();
        CreateOrgIntegration orgIntegration = new CreateOrgIntegration();


        if (!realAccountSet.contains(userId)) {
            String createrCode = OaHrmUtils.getworkCode(userId);

            BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
            HrmResourceApiReq allInfo = OaHrmUtils.getAllInfo(userId);
            String subcompanyid1 = allInfo.getSubcompanyid1();
            info("拿到当前人的subCompany: " + subcompanyid1);
            BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(subcompanyid1, 3);

            if (resp2.isStatus()) {
                // if (resp3.isStatus()) {
                //     info("企业详情查询1 : ");
                //     BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));
                //     if (!baseResp.isStatus()) {
                //         return new BaseResp<JSONObject>(false);
                //     }
                //     info("企业详情查询 结果1 : " + JSONUtil.toJsonStr(baseResp));
                //     RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
                //     JoinOrgReq joinOrgReq = new JoinOrgReq();
                //     joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
                //     joinOrgReq.setInviteUserId(resp2.getData());
                //     joinOrgReq.setEnterpriseId(resp3.getData());
                //     info("邀请用户 进入企业11 : " + JSONUtil.toJsonStr(joinOrgReq));
                //     integration.inviteUser(joinOrgReq);
                //     return MsgResp.ok();
                // }
                return MsgResp.ok();
            }
            RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
            CreateUserServiceImpl cusi = new CreateUserServiceImpl();
            resp = cusi.createUser(realUser.getName(), realUser.getIdCard(), realUser.getMobile(), createrCode);
            JoinOrgReq joinOrgReq = new JoinOrgReq();

            BaseResp<JSONObject> baseResp = orgIntegration.companyInfo(new CompanyInfo(resp3.getData()));

            info("企业详情查询 结果 : " + JSONUtil.toJsonStr(baseResp));
            joinOrgReq.setUserId((String) baseResp.getData().get("creatorUuid"));
            joinOrgReq.setInviteUserId((String) resp.getData().get("userId"));
            joinOrgReq.setEnterpriseId(resp3.getData());
            info("邀请用户 进入企业 : " + JSONUtil.toJsonStr(joinOrgReq));
            integration.inviteUser(joinOrgReq);

            if (resp.isStatus()) {
                BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping((String) resp.getData().get("userId"), createrCode, 2);
                info("resp1 : " + resp1.toString());
                if (!resp1.isStatus()) {
                    info("第三方系统数据插入 : 失败");
                    return MsgResp.error("第三方系统数据插入失败");
                }
                info("第三方系统数据插入 : 成功");
                realAccountSet.add(userId);
            }
        }

        return resp;
    }

}