package com.api.cyitce.seal3.action.contract;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.config.eSeal.ESealConfig;
import com.api.cyitce.seal3.enums.contract.ContractCheckStatusEnum;
import com.api.cyitce.seal3.enums.seal.SealFromEnum;
import com.api.cyitce.seal3.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal3.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal3.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal3.vo.req.contract.ContractStatusApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.Map;

import static com.api.cyitce.seal3.action.create.checkAction.BMBM;

/**
 * @ClassName: 统一印控中心 结束签署
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  10:21
 * @Version: 1.0
 */
public class FinishConAction extends BaseLog implements Action {
    public String configId = "1";



    @Override
    public String execute(RequestInfo requestInfo) {
        String sfxyqz = "1";
        int sealType = -1;
        String docId = "";
        String title = "";
        String jbrbm = "-1";
        boolean flag = false;
        info("FinishContractAction------------------------> 进行合同结束Action");

        CreateContractServiceImp ccsi = new CreateContractServiceImp();
        ESealConfig config = new ESealConfig();
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();
        ContractIntegration ci = new ContractIntegration();
        info("FinishContractAction------------------------> 开始执行");
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        // 是否使用模板0 否 1 是
        String isModel = "0";
        for (Property p : properties) {
            if (p.getName().equals(wconfig.getFsealType())) {
                info("CreateContractAction-------------------> 主表 sealType: " + p.getValue());
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        sealType = SealFromEnum.E_SEAL.getValue();
                    } else if ("1".equals(p.getValue())) {
                        sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                    }
                }
            } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (!"0".equals(p.getValue())) {
                        info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                if (StringUtils.hasText(p.getValue())) {
                    if ("0".equals(p.getValue())) {
                        info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                        return Action.SUCCESS;
                    }
                }
            }else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                if (StringUtils.hasText(p.getValue())) {
                    if (BMBM.equals(p.getValue())) {
                        flag = true;
                    }
                }
            }
            obj.put(p.getName(), p.getValue());
        }

        // 是否需要用印
        String sfxyyy = wconfig.getSfxyyy();
        if (!StringUtils.hasText(sfxyyy)) {
            requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (!obj.get(sfxyyy).equals("1")) {
            info("执行 不盖章逻辑" + obj.get(sfxyyy));
            return Action.SUCCESS;
        }

        // 实体章先不执行执行
        if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            info("FinishContractAction------------------->  实体章先不执行执行");
            return Action.SUCCESS;
        }


        if (wconfig.isDetail()) {
            DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
            DetailTable dt = detailTables[wconfig.getDatasource() - 1];
            for (Row r : dt.getRow()) {
                Cell[] cs = r.getCell();
                for (Cell c : cs) {
                    if (c.getName().equals(wconfig.getFfile())) {
                        if (!flag) {
                            info("FinishContractAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                            if (c.getValue().split(",").length > 1) {
                                requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                return Action.FAILURE_AND_CONTINUE;
                            }
                        }
                        docId = c.getValue();
                    } else if (c.getName().equals(wconfig.getFtitle())) {
                        if (StringUtils.hasText(c.getValue())) {
                            title = c.getValue();
                        }
                    } else if ("sfxyqz".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            sfxyqz = c.getValue();
                        }
                    } else if ("shnryy".equals(c.getName())) {
                        if (StringUtils.hasText(c.getValue())) {
                            docId = c.getValue();
                        }
                        // 是否需要签字
                    }
                }
                boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                info("FinishContractAction  ----------------------->  ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
                // 电子合同 上传合同附件
                if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
                    info("FinishContractAction  -----------------------> 结束合同电子合同 开始执行");
                    // 结束合同签署。
                    try {
                        BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                        if (resp.isStatus()) {
                            info("FinishContractAction  -----------------------> 天威云合同id: " + resp.getData());
                            // 没找到数据
                            ContractStatusApiReq contractStatusApiReq = new ContractStatusApiReq();
                            contractStatusApiReq.setContractId(Long.parseLong(resp.getData()));
                            contractStatusApiReq.setOperationType(ContractCheckStatusEnum.FINISHED.getIndex());
                            info("FinishContractAction  -----------------------> 请求参数: " + contractStatusApiReq.toString());
                            BaseResp<JSONObject> resp1 = ci.operateContractStatus(contractStatusApiReq);
                            JSONObject data = resp1.getData();
                            data.get("status");
                            if ("1".equals(resp1.getData().get("status"))) {
                                info("FinishContractAction  -----------------------> 执行结束合同方法错误:" + resp1.getData().get("message"));
                                requestInfo.getRequestManager().setMessagecontent(resp1.getData().get("message").toString());
                                return Action.FAILURE_AND_CONTINUE;
                            }
                            info("FinishContractAction  -----------------------> 执行结束合同方法");


                            // 修改对应的docId对应的签署人信息


                            // Map<String, String> file = FileUtils.oaFileInfo(docId);
                            //
                            // byte[] fileBytes = file.get("bytes").getBytes();
                            //
                            // BaseResp<ContractApiVO> resp2 = ccsi.createContract(Base64.encode(docId), title, OaHrmUtils.getworkCode(Integer.parseInt(obj.get(wconfig.getCreaterfieldname()))), fileBytes, config.getCompanyUUID());
                            //
                            // if (!resp2.isStatus()) {
                            //     throw new Exception(resp2.getMessage());
                            // }
                            //
                            // Long contractId = resp2.getData().getContractId();
                            //
                            // BaseResp<Void> resp1 = tmsi.insertThridDataMapping(String.valueOf(contractId), Base64.encode(docId), 0);
                            //
                            // if (resp1.isStatus()) {
                            //     throw new Exception(resp1.getMessage());
                            // }

                        }

                    } catch (Exception e) {
                        requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                        return Action.FAILURE_AND_CONTINUE;
                    }

                }
            }
        }
        return Action.SUCCESS;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }
}