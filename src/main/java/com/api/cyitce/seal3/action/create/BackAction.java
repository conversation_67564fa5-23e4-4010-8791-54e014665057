package com.api.cyitce.seal3.action.create;


import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.config.eSeal.ESealConfig;
import com.api.cyitce.seal3.enums.seal.SealFromEnum;
import com.api.cyitce.seal3.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal3.service.eSeal.impl.CreateContractServiceImp;
import com.api.cyitce.seal3.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal3.vo.req.contract.ContractDeleteApiReq;
import com.api.cyitce.seal3.vo.req.contract.ContractStatusApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.engine.common.util.ServiceUtil;
import com.weaver.general.BaseBean;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.Map;

import static com.api.cyitce.seal3.action.contract.ContaractAction.getInfo;
import static com.api.cyitce.seal3.action.create.checkAction.BMBM;
import static com.api.cyitce.seal3.util.sql.SqlUtils.*;

/**
 * @ClassName: 统一印控中心 回退按钮
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  10:21
 * @Version: 1.0
 */
public class BackAction extends BaseLog implements Action {
    public String configId = "1";

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        info("BackAction -------------> 进入方法");
        String sfxyqz = "1";
        int sealType = -1;
        String docId = "";
        String title = "";
        String src = requestInfo.getRequestManager().getSrc();
        boolean flag = false;
        CreateContractServiceImp ccsi = new CreateContractServiceImp();
        ESealConfig config = new ESealConfig();
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        ThridMappingServiceImpl tmsi = new ThridMappingServiceImpl();
        ContractIntegration ci = new ContractIntegration();
        info("BackAction 回退开始执行-------------> 开始执行");
        info("BackAction 回退开始执行-------------> 当前执行操作:" + src);
        // if ("submit".equals(src)) {
        //     return Action.SUCCESS;
        // }
        // obj 主表
        Map<String, String> obj = new HashMap<>();
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        if (wconfig.getDatasource() == null) {
            requestInfo.getRequestManager().setMessagecontent("流程配置查询失败");
            return Action.FAILURE_AND_CONTINUE;
        }
        if (wconfig.getDatasource() != 0) {
            for (Property p : properties) {
                info("BackAction 回退开始执行------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("BackAction 回退开始执行-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            sealType = SealFromEnum.E_SEAL.getValue();
                        } else if ("1".equals(p.getValue())) {
                            sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getDzyypt()) && p.getName().equals(wconfig.getDzyypt())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (!"0".equals(p.getValue())) {
                            info("项目资料用印电子平台  不是公司自有 不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getYysp()) && p.getName().equals(wconfig.getYysp())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("0".equals(p.getValue())) {
                            info("项目资料用印审批  部门  不处理 ：" + p.getValue());
                            return Action.SUCCESS;
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                } else if ("smdj".equals(p.getName())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if ("1".equals(p.getValue()) || "2".equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }
            // if (!wconfig.getDzyypt().equals("0")) {
            //     return Action.SUCCESS;
            // }
            String sfxyyy = wconfig.getSfxyyy();
            if (!StringUtils.hasText(sfxyyy)) {
                requestInfo.getRequestManager().setMessagecontent("是否用印出错,请联系管理员配置!");
                return Action.FAILURE_AND_CONTINUE;
            }
            if (!obj.get(sfxyyy).equals("1")) {
                info("执行 不盖章逻辑" + obj.get(sfxyyy));
                return Action.SUCCESS;
            }

            // // 实体章先不执行执行
            // if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
            //     info("BackAction 回退开始执行------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }
            // if (("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) || sealType == SealFromEnum.E_SEAL.getValue()) {
            // } else {
            //     info("CreateContractAction------------------->  实体章先不执行执行");
            //     return Action.SUCCESS;
            // }


            if (wconfig.isDetail()) {
                DetailTable[] detailTables = requestInfo.getDetailTableInfo().getDetailTable();
                DetailTable dt = detailTables[wconfig.getDatasource() - 1];
                for (Row r : dt.getRow()) {
                    Cell[] cs = r.getCell();
                    for (Cell c : cs) {
                        if (c.getName().equals(wconfig.getFfile())) {
                            if (!flag) {
                                info("jbrAction  ----------------------->  文件长度 length:" + c.getValue().split(",").length);
                                if (c.getValue().split(",").length > 1) {
                                    requestInfo.getRequestManager().setMessagecontent("明细表" + wconfig.getDatasource() + ".第" + r.getId() + "行：请上传单个文件");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                docId = c.getValue();
                            }
                        } else if (c.getName().equals(wconfig.getFtitle())) {
                            title = c.getValue();
                        } else if ("sfxyqz".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                sfxyqz = c.getValue();
                            }
                        } else if ("shnryy".equals(c.getName())) {
                            if (StringUtils.hasText(c.getValue())) {
                                docId = c.getValue();
                            }
                            // 是否需要签字
                        }
                    }
                    boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
                    info("BackAction------------> ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
                    //
                    if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
                        info("BackAction 回退开始执行-------------> 进入电子合同方法 删除合同对应 合同 以及文档数据,删除对应合同草稿");
                        // 结束合同签署。
                        try {
                            BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                            if (resp.isStatus()) {
                                info("BackAction 回退开始执行------------->天威云合同id: " + resp.getData());
                                // 找到数据
                                ContractStatusApiReq contractStatusApiReq = new ContractStatusApiReq();
                                contractStatusApiReq.setContractId(Long.parseLong(resp.getData()));
                                contractStatusApiReq.setOperationType(5);
                                ci.operateContractStatus(contractStatusApiReq);
                                info("BackAction 回退开始执行-------------> 执行结束合同方法 参数: ", contractStatusApiReq.toString());
                                ContractDeleteApiReq contractDeleteApiReq = new ContractDeleteApiReq();
                                contractDeleteApiReq.setContractId(Long.parseLong(resp.getData()));
                                info("BackAction 回退开始执行-------------> 执行删除合同方法 参数: ", contractDeleteApiReq.toString());
                                BaseResp<JSONObject> resp1 = ci.contractDelete(contractDeleteApiReq);
                                info("BackAction 删除合同 详情 :" + resp1.getStatus() + " , 消息: " + resp1.getMessage());
                                info("BackAction 删除合同 详情 :" + resp1.getData().toString());
                                info("BackAction 回退开始执行-------------> 执行删除合同方法");
                                // String tableName = getFormName(requestInfo.getRequestid());
                                String billTableName = requestInfo.getRequestManager().getBillTableName();
                                Map<String, String> map = getqsrInfo(0, Base64.encode(docId), new ThirdMappConfig());
                                info("BackAction 回退开始执行-------------> 删除明细表数据 ， 表名：" + billTableName);
                                updateFile(Integer.parseInt(map.get("mxbid")), billTableName, Integer.parseInt(map.get("yymxb")));
                                BaseResp<Void> resp2 = deleteThrid(Base64.encode(docId), 0, new ThirdMappConfig());
                                if (!resp2.isStatus()) {
                                    info("BackAction 回退开始执行-------------> 删除合同出错");
                                    requestInfo.getRequestManager().setMessagecontent("回退错误，请联系管理员");
                                    return Action.FAILURE_AND_CONTINUE;
                                }
                                BaseResp<Void> resp3 = deleteThrid(Base64.encode(docId), 1, new ThirdMappConfig());
                                if (!resp3.isStatus()) {
                                    info("BackAction 回退开始执行-------------> 删除合同文档出错");
                                    // requestInfo.getRequestManager().setMessagecontent("回退错误，请联系管理员");
                                    // return Action.FAILURE_AND_CONTINUE;
                                    // 可以不用管理 因为文档删除失败不影响合同删除
                                }

                                // 删除对应的签署后文件。
                                // 删除对应文档的映射关系
                                // BaseResp<String> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 1);
                                info("BackAction 回退开始执行-------------> 相关数据删除完成");
                            }
                        } catch (Exception e) {
                            writeLog("BackAction 回退开始执行-------------> 执行出错");
                            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                            return Action.FAILURE_AND_CONTINUE;
                        }

                    }
                }
            }
            // if (configId.equals("7")&&sealType == SealFromEnum.E_SEAL.getValue()) {
            //     // 支出合同  删除对应的明细表
            //     String billTableName = requestInfo.getRequestManager().getBillTableName();
            //     getDocIdInfoByDocId()
            // }

        } else {
            info("updatedzljAction  ----------------------->进入主表");
            Map<String, String> map = getInfo(requestInfo.getRequestManager().getBillTableName(), requestInfo.getRequestid(), wconfig.getFfile());
            docId = map.get(wconfig.getFfile());
            if (!StringUtils.hasText(docId)) {
                requestInfo.getRequestManager().setMessagecontent("未查询到生成的合同文档");
                return Action.FAILURE_AND_CONTINUE;
            }
            for (Property p : properties) {
                info("BackAction 回退开始执行------------------>主表数据 属性：" + p.getName() + " 值：" + p.getValue());
                if (p.getName().equals(wconfig.getFsealType())) {
                    info("BackAction 回退开始执行-------------------> 主表 sealType: " + p.getValue());
                    if (StringUtils.hasText(p.getValue())) {
                        info("updatedzljAction-------------------> 主表 sealType: " + p.getValue());
                        if (StringUtils.hasText(p.getValue())) {
                            if (configId.equals("7")) {
                                // 支出合同 0 纸质 1 电子
                                if ("0".equals(p.getValue())) {
                                    sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                                } else if ("1".equals(p.getValue())) {
                                    sealType = SealFromEnum.E_SEAL.getValue();
                                }
                            } else if (configId.equals("8")) {
                                // 采购结算单 1 纸质  0 电子
                                if ("0".equals(p.getValue())) {
                                    sealType = SealFromEnum.E_SEAL.getValue();
                                } else if ("1".equals(p.getValue())) {
                                    sealType = SealFromEnum.PHYSICAL_SEAL.getValue();
                                }
                            }
                        }
                    }
                } else if (StringUtils.hasText(wconfig.getJbrbm()) && p.getName().equals(wconfig.getJbrbm())) {
                    if (StringUtils.hasText(p.getValue())) {
                        if (BMBM.equals(p.getValue())) {
                            flag = true;
                        }
                    }
                }
                obj.put(p.getName(), p.getValue());
            }
            // 实体章先不执行执行
            if (!("1".equals(wconfig.getSfkqstyz()) && sealType == SealFromEnum.PHYSICAL_SEAL.getValue()) && sealType != SealFromEnum.E_SEAL.getValue()) {
                info("FinishContractAction------------------->  实体章先不执行执行");
                return Action.SUCCESS;
            }
            // boolean b = SealFromEnum.PHYSICAL_SEAL.getValue() == sealType && "1".equals(sfxyqz);
            // info("BackAction------------> ,b:" + b + ", sealType:" + sealType + " , sfxyqz:" + sfxyqz);
            //
            // if (SealFromEnum.E_SEAL.getValue() == sealType || b) {
            if (SealFromEnum.E_SEAL.getValue() == sealType) {
                info("BackAction 回退开始执行-------------> 进入电子合同方法 删除合同对应 合同 以及文档数据,删除对应合同草稿");
                // 结束合同签署。
                try {
                    BaseResp<String> resp = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 0);
                    if (resp.isStatus()) {
                        info("BackAction 回退开始执行------------->天威云合同id: " + resp.getData());
                        // 找到数据
                        ContractStatusApiReq contractStatusApiReq = new ContractStatusApiReq();
                        contractStatusApiReq.setContractId(Long.parseLong(resp.getData()));
                        contractStatusApiReq.setOperationType(5);
                        ci.operateContractStatus(contractStatusApiReq);
                        info("BackAction 回退开始执行-------------> 执行结束合同方法 参数: ", contractStatusApiReq.toString());
                        ContractDeleteApiReq contractDeleteApiReq = new ContractDeleteApiReq();
                        contractDeleteApiReq.setContractId(Long.parseLong(resp.getData()));
                        info("BackAction 回退开始执行-------------> 执行删除合同方法 参数: ", contractDeleteApiReq.toString());
                        BaseResp<JSONObject> resp1 = ci.contractDelete(contractDeleteApiReq);
                        info("BackAction 删除合同 详情 :" + resp1.getStatus() + " , 消息: " + resp1.getMessage());
                        info("BackAction 删除合同 详情 :" + resp1.getData().toString());
                        info("BackAction 回退开始执行-------------> 执行删除合同方法");
                        BaseResp<Void> resp2 = deleteThrid(Base64.encode(docId), 0, new ThirdMappConfig());
                        if (!resp2.isStatus()) {
                            info("BackAction 回退开始执行-------------> 删除合同出错");
                            requestInfo.getRequestManager().setMessagecontent("回退错误，请联系管理员");
                            return Action.FAILURE_AND_CONTINUE;
                        }
                        BaseResp<Void> resp3 = deleteThrid(Base64.encode(docId), 1, new ThirdMappConfig());
                        if (!resp3.isStatus()) {
                            info("BackAction 回退开始执行-------------> 删除合同文档出错");
                            // requestInfo.getRequestManager().setMessagecontent("回退错误，请联系管理员");
                            // return Action.FAILURE_AND_CONTINUE;
                            // 可以不用管理 因为文档删除失败不影响合同删除
                        }
                        // 删除对应文档的映射关系
                        // BaseResp<String> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(Base64.encode(docId), 1);
                        info("BackAction 回退开始执行-------------> 相关数据删除完成");
                    }
                } catch (Exception e) {
                    writeLog("BackAction 回退开始执行-------------> 执行出错");
                    requestInfo.getRequestManager().setMessagecontent(e.getMessage());
                    return Action.FAILURE_AND_CONTINUE;
                }

            }
        }


        return Action.SUCCESS;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }
}