package com.api.cyitce.seal3.action.create;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.WorkflowConfig;
import com.api.cyitce.seal3.config.eSeal.ESealConfig;
import com.api.cyitce.seal3.enums.CompanyEnum;
import com.api.cyitce.seal3.enums.enterprise.EnterpriseTypeEnum;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal3.integration.realauth.basic.CompanyBasicAuthIntergration;
import com.api.cyitce.seal3.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal3.service.oa.impl.ThridMappingServiceImpl;
import com.api.cyitce.seal3.util.HmacSh1Util;
import com.api.cyitce.seal3.util.data.OaHrmUtils;
import com.api.cyitce.seal3.vo.req.hrm.JoinOrgReq;
import com.api.cyitce.seal3.vo.req.hrm.RealOrgApiReq;
import com.api.cyitce.seal3.vo.req.hrm.RealPersonApiReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.engine.common.util.ServiceUtil;
import org.springframework.util.StringUtils;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 统一印控中心 回退按钮
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  10:21
 * @Version: 1.0
 */
public class TestAction extends BaseLog implements Action {


    public String configId = "1";


    @Override
    public String execute(RequestInfo requestInfo) {
        WorkflowConfig wconfig = new WorkflowConfig(Integer.parseInt(configId));
        info("configId=" + configId);

        // 0 信科 1 华来 userId 就是法人id
        // realCreaterAccount(1215, 0, "L021KI1KC53R06O");
        // realCreaterAccount(1215, 0, "L021KI1KC53R06O");
        // realCreaterAccount(1215, 0, "M011K7PNSCAJOQY");
        realCreaterAccount(1215, 1, "M011K7PNSCAJOQY");

        // 创建企业
        return Action.SUCCESS;
    }


    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    private BaseResp<JSONObject> realCreaterAccount(Integer userId, int type, String YSID) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();


        String createrCode = OaHrmUtils.getworkCode(userId);

        BaseResp<String> resp2 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
        String orgUserId = "";
        if (!resp2.isStatus()) {
            RealPersonApiReq realUser = OaHrmUtils.getRealInfo(userId);
            CreateUserServiceImpl cusi = new CreateUserServiceImpl();
            resp = cusi.createUser(realUser.getName(), realUser.getIdCard(), realUser.getMobile(), createrCode);
            info("个人实名结果: " + JSONUtil.toJsonStr(resp));
            orgUserId = (String) resp.getData().get("userId");
        } else {
            orgUserId = resp2.getData();
        }


        String oaid = "";
        if (type == 0) {
            oaid = String.valueOf(CompanyEnum.CHONGQING_XINKE_COMMUNICATION_ENGINEERING_CO_LTD.getId());
        } else if (type == 1) {
            oaid = String.valueOf(CompanyEnum.CHONGQING_HUALAI_INFORMATION_TECHNOLOGY_CO_LTD.getId());
        }
        BaseResp<JSONObject> company = realCreaterOrg(YSID, type);
        String enterpriseId = "";
        if (StringUtils.hasText((String) company.getData().get("enterpriseId"))) {
            enterpriseId = (String) company.getData().get("enterpriseId");
            info("插入企业三方映射数据");
            BaseResp<Void> resp7 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping(enterpriseId, oaid, 3);

        } else {
            enterpriseId = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(oaid, 3).getData();
        }
        JoinOrgReq joinOrgReq = new JoinOrgReq();

        joinOrgReq.setUserId(YSID);
        joinOrgReq.setInviteUserId(orgUserId);
        joinOrgReq.setEnterpriseId(enterpriseId);
        info("邀请用户 进入企业 : " + joinOrgReq.toString());
        BaseResp<JSONObject> baseResp = integration.inviteUser(joinOrgReq);

        info("邀请用户 进入企业  结果: " + JSONUtil.toJsonStr(joinOrgReq.toString()));
        // BaseResp<String> resp3 = ServiceUtil.getService(ThridMappingServiceImpl.class).getThridIdByOaId(createrCode, 2);
        // if (!resp3.isStatus()) {
        //     BaseResp<Void> resp1 = ServiceUtil.getService(ThridMappingServiceImpl.class).insertThridDataMapping((String) resp.getData().get("userId"), createrCode, 2);
        //     info("resp1 : " + resp1.toString());
        //     if (!resp1.isStatus()) {
        //         info("第三方系统数据插入 : 失败");
        //         return MsgResp.error("第三方系统数据插入失败");
        //     }
        //     info("第三方系统数据插入 : 成功");
        //
        // }
        // 绑定企业法定人
        BaseResp<JSONObject> baseResp1 = bindFDPERSON(enterpriseId, orgUserId);
        info("绑定企业法定人响应结果: " + JSONUtil.toJsonStr(baseResp1));

        return resp;
    }

    private BaseResp<JSONObject> bindFDPERSON(String enterpriseId, String legalUserId) {
        CreateUserServiceImpl service = new CreateUserServiceImpl();
        return service.bindLegalPerson(enterpriseId, legalUserId);
    }

    private BaseResp<JSONObject> realCreaterOrg(String orgUserId, int type) {
        BaseResp<JSONObject> resp = new BaseResp<JSONObject>(true);
        CreatUserIntegration integration = new CreatUserIntegration();
        CompanyBasicAuthIntergration ca = new CompanyBasicAuthIntergration();
        CreateUserServiceImpl createUserService = new CreateUserServiceImpl();
        RealOrgApiReq orgApiReq = new RealOrgApiReq();
        List<RealOrgApiReq> list = new ArrayList<>();
        RealOrgApiReq orgApiReq1 = new RealOrgApiReq();
        if (type == 0) {
            // 信科
            orgApiReq1 = new RealOrgApiReq(EnterpriseTypeEnum.ET_PE.getCode(), "重庆信科通信工程有限公司", "9150010878745315XL", "肖海秋", "51022219770516041X");
        } else if (type == 1) {
            // 华来
            orgApiReq1 = new RealOrgApiReq(EnterpriseTypeEnum.ET_PE.getCode(), "重庆华来信息科技有限公司", "91500000MAABY05X2B", "肖海秋", "51022219770516041X");
        }
        // list.add(orgApiReq1);
        // list.add(orgApiReq2);
        // for (RealOrgApiReq realOrgApiReq : list) {
        // BaseResp<JSONObject> baseResp = ca.companyAuth(realOrgApiReq);
        // info("企业实名--------------------> baseResp:" + JSONUtil.toJsonStr(baseResp));
        // if (baseResp.getStatus() != 1) {
        //     info("企业实名错误");
        // }
        BaseResp<JSONObject> company = createUserService.createCompany(EnterpriseTypeEnum.ET_PE.getCode(), orgUserId, orgApiReq1.getName(), orgApiReq1.getIdCode(), orgApiReq1.getLegalName(), orgApiReq1.getLegalId(), null);
        info("compant--------------------> :" + JSONUtil.toJsonStr(company));
        //
        return company;
    }

    public static void main(String[] args) {
        // 生成签名密钥
        String requestJsonStr = "";
        ESealConfig config = new ESealConfig();

        // 生成签名，签名秘钥为：secretKey + serviceCode  contract003固定接口  idc0012  个人实名idb0011
        String key = config.getSecretKey() + "contract003";
        String signature = HmacSh1Util.getSignature(requestJsonStr, key);
        // Content-Signature 为固定值 HMAC-SHA1 + 英文空格 + signature
        String ContentSignature = "HMAC-SHA1 " + signature;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Signature", ContentSignature);
        headers.put("appId", config.getAppId());
        headers.put("serviceCode", "contract003");
        headers.put("companyUUID", config.getCompanyUUID());
        headers.put("Accept", "application/json;charset=utf-8");
        System.out.println(headers.toString());

    }

}