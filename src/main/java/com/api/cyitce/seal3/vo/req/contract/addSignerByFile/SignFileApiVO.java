package com.api.cyitce.seal3.vo.req.contract.addSignerByFile;

import java.util.List;

public class SignFileApiVO {

    /**
     * 合同文档id
     */
    private Long docId;

    /**
     * 可选 坐标系类型: 1:左下角为原点,单位为像素(默认) 2:左上角为原点,单位为百分比
     */
    private Integer axisType = 1;

    /**
     * 坐标轴控件
     */
    private List<XYControlApiVO> xySignControls;

    /**
     * 关键字控件
     */
    private List<KeywordControlApiVO> keywordSignControls;

    /**
     * 骑缝章控件
     */
    private List<CrossControlApiVO> crossSignControls;

    public SignFileApiVO() {
    }

    public SignFileApiVO(Long docId, List<XYControlApiVO> xySignControls, List<KeywordControlApiVO> keywordSignControls, List<CrossControlApiVO> crossSignControls) {
        this.docId = docId;
        this.xySignControls = xySignControls;
        this.keywordSignControls = keywordSignControls;
        this.crossSignControls = crossSignControls;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Integer getAxisType() {
        return axisType;
    }

    public void setAxisType(Integer axisType) {
        this.axisType = axisType;
    }

    public List<XYControlApiVO> getXySignControls() {
        return xySignControls;
    }

    public void setXySignControls(List<XYControlApiVO> xySignControls) {
        this.xySignControls = xySignControls;
    }

    public List<KeywordControlApiVO> getKeywordSignControls() {
        return keywordSignControls;
    }

    public void setKeywordSignControls(List<KeywordControlApiVO> keywordSignControls) {
        this.keywordSignControls = keywordSignControls;
    }

    public List<CrossControlApiVO> getCrossSignControls() {
        return crossSignControls;
    }

    public void setCrossSignControls(List<CrossControlApiVO> crossSignControls) {
        this.crossSignControls = crossSignControls;
    }

    @Override
    public String toString() {
        return "SignFileApiVO{" +
                "docId=" + docId +
                ", axisType=" + axisType +
                ", xySignControls=" + xySignControls +
                ", keywordSignControls=" + keywordSignControls +
                ", crossSignControls=" + crossSignControls +
                '}';
    }
}
