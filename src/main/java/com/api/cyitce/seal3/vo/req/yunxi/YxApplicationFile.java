package com.api.cyitce.seal3.vo.req.yunxi;

// params.files 数组中的对象模型
public class YxApplicationFile {
    /**
     * 合同附件名称
     */
    private String fileName;

    /**
     * 合同附件URL, 需可直接下载
     */
    private String fileUrl;

    public YxApplicationFile() {}

    public YxApplicationFile(String fileName, String fileUrl) {
        this.fileName = fileName;
        this.fileUrl = fileUrl;
    }
    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    public String getFileUrl() { return fileUrl; }
    public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }
}