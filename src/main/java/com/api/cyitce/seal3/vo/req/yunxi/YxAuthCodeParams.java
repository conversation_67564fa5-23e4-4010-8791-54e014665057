package com.api.cyitce.seal3.vo.req.yunxi;

/**
 * 获取授权码接口的参数实体类 (用于 YXYG03_04)
 */
public class YxAuthCodeParams {
    /**
     * 申请单id, 第三方对接系统的申请单id (必填)
     */
    private String applicationId;

    /**
     * 设备唯一标识 (必填)
     */
    private String uuid;

    /**
     * 过期时间, 单位：秒，默认3600秒
     */
    private Long expireTime;

    public YxAuthCodeParams() {}

    public YxAuthCodeParams(String applicationId, String uuid) {
        this.applicationId = applicationId;
        this.uuid = uuid;
    }

    public YxAuthCodeParams(String applicationId, String uuid, Long expireTime) {
        this.applicationId = applicationId;
        this.uuid = uuid;
        this.expireTime = expireTime;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    public String getUuid() {
        return uuid;
    }
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    public Long getExpireTime() {
        return expireTime;
    }
    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }
}