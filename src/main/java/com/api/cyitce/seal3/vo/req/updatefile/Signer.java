package com.api.cyitce.seal3.vo.req.updatefile;

public class Signer {

    // 合同ID（非必填）
    private Long contractId;

    // 签约方类型（非必填）
    // 1=个人, 2=企业
    private Integer signerType;

    // 签署人状态（非必填）
    private Integer status;

    // 签署状态（非必填，注意与status区分，可能表示合同整体的签署状态）
    private Integer contractStatus;

    // 签署人姓名（非必填）
    private String name;

    // 签署人用户ID（非必填）
    private String userId;

    // 签署人三方用户ID（非必填）
    private String thirdUserId;

    // 企业名称（非必填，当signerType为企业时使用）
    private String enterpriseName;

    // 签署人企业ID（非必填）
    private String enterpriseId;

    // 签署人三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 签署人角色（非必填）
    private String signerRole;

    // 是否为有序签署（非必填）
    private Boolean signSortable;

    // 签署顺序（非必填，当signSortable为true时使用）
    private Integer sequenceNumber;

    // 是否意愿认证（非必填）
    private Boolean desireType;

    // 是否发短信通知（非必填）
    private Boolean sendMsg;

    // 是否发邮件通知（非必填）
    private Boolean sendMail;

    // 签署方式（非必填）
    private Integer signMode;

    // 是否需要审批（非必填）
    private Boolean approved;

    // 审批状态（非必填）
    private Integer approveStatus;

    // 审批工作流实例ID（非必填）
    private String processInstanceId;

    // 审批缓存签署数据json（非必填）
    private String signData;

    // 解约状态（非必填）
    private Integer nullifyStatus;

    // 业务类型签署策略（非必填）
    private String signStrategy;

    // 是否允许补传文件（非必填）
    private Boolean allowAppendFile;

    // 是否指定控件位置（非必填）
    private Boolean setting;

    // 控件类型范围（非必填）
    private String controlsType;

    // 固化数据（非必填）
    private String extendData;

    // 接收时间（非必填，格式：yyyy-MM-dd HH:mm:ss）
    // 注意：这里使用String类型，但您可能需要自定义解析逻辑来匹配特定格式
    private String receiveTime;

    // 签署时间（非必填，格式：yyyy-MM-dd HH:mm:ss）
    // 注意：同样使用String类型，需要自定义解析逻辑
    private String signTime;

    private Integer signStatus;


    public Integer getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(Integer signStatus) {
        this.signStatus = signStatus;
    }


    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Integer getSignerType() {
        return signerType;
    }

    public void setSignerType(Integer signerType) {
        this.signerType = signerType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(Integer contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getSignerRole() {
        return signerRole;
    }

    public void setSignerRole(String signerRole) {
        this.signerRole = signerRole;
    }

    public Boolean getSignSortable() {
        return signSortable;
    }

    public void setSignSortable(Boolean signSortable) {
        this.signSortable = signSortable;
    }

    public Integer getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(Integer sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public Boolean getDesireType() {
        return desireType;
    }

    public void setDesireType(Boolean desireType) {
        this.desireType = desireType;
    }

    public Boolean getSendMsg() {
        return sendMsg;
    }

    public void setSendMsg(Boolean sendMsg) {
        this.sendMsg = sendMsg;
    }

    public Boolean getSendMail() {
        return sendMail;
    }

    public void setSendMail(Boolean sendMail) {
        this.sendMail = sendMail;
    }

    public Integer getSignMode() {
        return signMode;
    }

    public void setSignMode(Integer signMode) {
        this.signMode = signMode;
    }

    public Boolean getApproved() {
        return approved;
    }

    public void setApproved(Boolean approved) {
        this.approved = approved;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public Integer getNullifyStatus() {
        return nullifyStatus;
    }

    public void setNullifyStatus(Integer nullifyStatus) {
        this.nullifyStatus = nullifyStatus;
    }

    public String getSignStrategy() {
        return signStrategy;
    }

    public void setSignStrategy(String signStrategy) {
        this.signStrategy = signStrategy;
    }

    public Boolean getAllowAppendFile() {
        return allowAppendFile;
    }

    public void setAllowAppendFile(Boolean allowAppendFile) {
        this.allowAppendFile = allowAppendFile;
    }

    public Boolean getSetting() {
        return setting;
    }

    public void setSetting(Boolean setting) {
        this.setting = setting;
    }

    public String getControlsType() {
        return controlsType;
    }

    public void setControlsType(String controlsType) {
        this.controlsType = controlsType;
    }

    public String getExtendData() {
        return extendData;
    }

    public void setExtendData(String extendData) {
        this.extendData = extendData;
    }

    public String getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(String receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    @Override
    public String toString() {
        return "Signer{" +
                "contractId=" + contractId +
                ", signerType=" + signerType +
                ", status=" + status +
                ", contractStatus=" + contractStatus +
                ", name='" + name + '\'' +
                ", userId='" + userId + '\'' +
                ", thirdUserId='" + thirdUserId + '\'' +
                ", enterpriseName='" + enterpriseName + '\'' +
                ", enterpriseId='" + enterpriseId + '\'' +
                ", thirdEnterpriseId='" + thirdEnterpriseId + '\'' +
                ", signerRole='" + signerRole + '\'' +
                ", signSortable=" + signSortable +
                ", sequenceNumber=" + sequenceNumber +
                ", desireType=" + desireType +
                ", sendMsg=" + sendMsg +
                ", sendMail=" + sendMail +
                ", signMode=" + signMode +
                ", approved=" + approved +
                ", approveStatus=" + approveStatus +
                ", processInstanceId='" + processInstanceId + '\'' +
                ", signData='" + signData + '\'' +
                ", nullifyStatus=" + nullifyStatus +
                ", signStrategy='" + signStrategy + '\'' +
                ", allowAppendFile=" + allowAppendFile +
                ", setting=" + setting +
                ", controlsType='" + controlsType + '\'' +
                ", extendData='" + extendData + '\'' +
                ", receiveTime='" + receiveTime + '\'' +
                ", signTime='" + signTime + '\'' +
                ", signStatus=" + signStatus +
                '}';
    }

    // Getter和Setter方法（通常使用IDE自动生成）
    // ...

    // 示例：生成getter和setter方法（通常这部分代码由IDE自动生成）
    // public Long getContractId() {
    //     return contractId;
    // }
    //
    // public void setContractId(Long contractId) {
    //     this.contractId = contractId;
    // }
    //
    // ... (其他字段的getter和setter方法类似)

    // 注意：对于日期字段，您可能需要提供自定义的getter和setter方法，
    // 或者在解析JSON到对象时使用自定义的日期格式解析器。
}