package com.api.cyitce.seal3.vo.req.updatefile;

import java.util.Date; // 或者使用java.time.LocalDateTime，取决于您的日期处理需求

public class NullifyFlow {

    // 合同标题（非必填）
    private String contractTitle;

    // 是否是解约申请人（非必填）
    private Boolean creator;

    public String getContractTitle() {
        return contractTitle;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }

    public Boolean getCreator() {
        return creator;
    }

    public void setCreator(Boolean creator) {
        this.creator = creator;
    }

    public Integer getNullifyType() {
        return nullifyType;
    }

    public void setNullifyType(Integer nullifyType) {
        this.nullifyType = nullifyType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getNullifyStatus() {
        return nullifyStatus;
    }

    public void setNullifyStatus(Integer nullifyStatus) {
        this.nullifyStatus = nullifyStatus;
    }

    public String getNullifyReason() {
        return nullifyReason;
    }

    public void setNullifyReason(String nullifyReason) {
        this.nullifyReason = nullifyReason;
    }

    public Date getNullifyTime() {
        return nullifyTime;
    }

    public void setNullifyTime(Date nullifyTime) {
        this.nullifyTime = nullifyTime;
    }

    // 解约人类型（非必填）
    // 1=个人, 2=企业
    private Integer nullifyType;

    // 用户ID（非必填）
    private String userId;

    // 用户三方ID（非必填）
    private String thirdUserId;

    // 名称（非必填）
    private String userName;

    // 企业ID（非必填）
    private String enterpriseId;

    // 三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 企业名称（非必填）
    private String enterpriseName;

    // 手机号（非必填）
    private String phone;

    // 解约状态（非必填）
    private Integer nullifyStatus;

    // 解约理由（非必填）
    private String nullifyReason;

    // 解约时间（非必填，格式：yyyy-MM-dd HH:mm:ss，但这里应该使用Date或LocalDateTime类型）
    // 注意：由于格式说明中出现了重复，我假设您想要的只是标准的日期时间格式
    // 如果您确实需要将日期时间存储为字符串，请忽略下面的注释并保留String类型
    // private String nullifyTime; // 不推荐，因为这会丢失日期时间的许多内置功能
    private Date nullifyTime; // 推荐使用Date，或者更好的是LocalDateTime（需要Java 8及以上）

    // 通常这里会有getter和setter方法，但由于您的要求是不写具体实现，所以这里省略
    // ... (省略getter和setter方法)

    // 注意：在实际应用中，建议使用Java 8引入的java.time包来处理日期和时间，
    // 因为它提供了更好的时区支持和更清晰的API。如果您使用LocalDateTime，
    // 那么您需要相应地调整任何依赖于日期时间格式化的代码。
}