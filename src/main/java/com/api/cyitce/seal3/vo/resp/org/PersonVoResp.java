package com.api.cyitce.seal3.vo.resp.org;


import java.util.Arrays;

/**
 * @ClassName: PersonVo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-22  14:35
 * @Version: 1.0
 */
public class PersonVoResp {
    /**
     * 用户标识
     **/
    private String userId;

    /**
     * 三方用户id 数藤平台人员的workCode
     **/
    private String thirdDataId;
    
    /**
     * 账号类型
     * 1=手机号
     * 2=账号
     * 3=邮箱
     **/
    private String accountType;
    
    /**
     * 手机号
     **/
    private String phone;

    /**
     * 邮箱
     **/
    private String email;

    /**
     * 用户状态
     **/
    private String status;

    /**
     * 实名类型
     * 1：手机号三要素
     * 3：银行卡四要素
     * 5：眨眼
     **/
    private String authType;

    /**
     * 实名认证结果
     * 0：未实名
     * 1：已实名
     * 2：实名中
     * 3：已拒绝
     * 4：验证中
     **/
    private String authResult;

    /**
     * 用户加入的企业列表
     **/
    private String[] companyUuidList;

    /**
     * 名称
     **/
    private String displayName;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdDataId() {
        return thirdDataId;
    }

    public void setThirdDataId(String thirdDataId) {
        this.thirdDataId = thirdDataId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getAuthResult() {
        return authResult;
    }

    public void setAuthResult(String authResult) {
        this.authResult = authResult;
    }

    public String[] getCompanyUuidList() {
        return companyUuidList;
    }

    public void setCompanyUuidList(String[] companyUuidList) {
        this.companyUuidList = companyUuidList;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String toString() {
        return "PersonVoResp{" +
                "userId='" + userId + '\'' +
                ", thirdDataId='" + thirdDataId + '\'' +
                ", accountType='" + accountType + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status='" + status + '\'' +
                ", authType='" + authType + '\'' +
                ", authResult='" + authResult + '\'' +
                ", companyUuidList=" + Arrays.toString(companyUuidList) +
                ", displayName='" + displayName + '\'' +
                '}';
    }
}