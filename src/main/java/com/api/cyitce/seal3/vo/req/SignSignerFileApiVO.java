package com.api.cyitce.seal3.vo.req;



import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class SignSignerFileApiVO {

    /**
     * 合同文档id
     */
    private Long docId;

    /**
     * 控件参数值
     */
    private List<ControlValueApiVO> controlValues;

    /**
     * 印章、签名控件下方是否加盖时间戳,默认false不显示
     */
    private Boolean showTimestamp;

    /**
     * 印章、签名控件时间戳参数,当showTimestamp为true时,此参数必填
     */
    private SealTimestampApiVO sealTimestamp;

    /**
     * 文本控件字体属性
     */
    private List<TextControlsFontVO> fonts;

    /**
     * 控件参数值
     */
    private Map<String, Object> signControl;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public List<ControlValueApiVO> getControlValues() {
        return controlValues;
    }

    public void setControlValues(List<ControlValueApiVO> controlValues) {
        this.controlValues = controlValues;
    }

    public Boolean getShowTimestamp() {
        return showTimestamp;
    }

    public void setShowTimestamp(Boolean showTimestamp) {
        this.showTimestamp = showTimestamp;
    }

    public SealTimestampApiVO getSealTimestamp() {
        return sealTimestamp;
    }

    public void setSealTimestamp(SealTimestampApiVO sealTimestamp) {
        this.sealTimestamp = sealTimestamp;
    }

    public List<TextControlsFontVO> getFonts() {
        return fonts;
    }

    public void setFonts(List<TextControlsFontVO> fonts) {
        this.fonts = fonts;
    }

    public Map<String, Object> getSignControl() {
        return signControl;
    }

    public void setSignControl(Map<String, Object> signControl) {
        this.signControl = signControl;
    }
}
