package com.api.cyitce.seal3.vo.req.updatefile;

public class Attachment {

    // 合同ID（非必填）
    private Long contractId;

    // 附件名称（非必填）
    private String attachName;

    // 文件名（非必填）
    private String fileName;

    // 文件存储ID（非必填）
    private Long fileId;

    // 附件类型（非必填）
    // 1=签署人附件
    private Integer attachType;

    // 签署人ID（如果attachType为1时可能必填，否则非必填）
    private Long signerId;

    // 上传人用户ID（非必填）
    private String userId;

    // 上传人用户三方ID（非必填）
    private String thirdUserId;

    // 上传人企业ID（非必填）
    private String enterpriseId;

    // 上传人三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 注意：您的参数列表中似乎截断了一个参数，我假设这里没有遗漏重要的字段。
    // 如果有其他字段，请按照相同的模式添加到这个类中。

    // 通常这里会有getter和setter方法，但由于您的要求是不写具体实现，所以这里省略。
    // ... (省略getter和setter方法)

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getAttachName() {
        return attachName;
    }

    public void setAttachName(String attachName) {
        this.attachName = attachName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public Integer getAttachType() {
        return attachType;
    }

    public void setAttachType(Integer attachType) {
        this.attachType = attachType;
    }

    public Long getSignerId() {
        return signerId;
    }

    public void setSignerId(Long signerId) {
        this.signerId = signerId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    // 构造函数（可选，根据需求添加）
    // public Attachment() {}
    // public Attachment(Long contractId, String attachName, String fileName, Long fileId, Integer attachType, ...) {}

    // 其他业务逻辑方法（可选，根据需求添加）
    // ...
}