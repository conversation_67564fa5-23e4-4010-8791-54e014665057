package com.api.cyitce.seal3.vo.req.contract.addSignerByFile;

public class ControlApiVO {

    /**
     * 控件id，不可重复
     */
    private Integer id;

    /**
     * 控件类型
     * autograph:签名类型，个人控件
     * signet:印章类型，企业控件
     * text:文本类型， value为要填写的文本内容
     * date:日期类型，非必传， 系统会取当前系统时间来填写，传值的话，value为空即可
     * pic:图片类型
     */
    private String type;

    private String value;

    /**
     * 控件宽(单位:cm),印章、图片控件,可不填,默认为图片的宽度
     */
    private Float width;

    /**
     * 控件高(单位:cm),印章、图片控件,可不填,默认为图片的高度
     */
    private Float height;

    /**
     * 控件key：模板补充变量时必填
     */
    private String key;

    public ControlApiVO(Integer id, String type, String value, Float width, Float height, String key) {
        this.id = id;
        this.type = type;
        this.value = value;
        this.width = width;
        this.height = height;
        this.key = key;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Float getWidth() {
        return width;
    }

    public void setWidth(Float width) {
        this.width = width;
    }

    public Float getHeight() {
        return height;
    }

    public void setHeight(Float height) {
        this.height = height;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public ControlApiVO() {
    }

    @Override
    public String toString() {
        return "ControlApiVO{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", value='" + value + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", key='" + key + '\'' +
                '}';
    }
}
