package com.api.cyitce.seal3.vo.req.hrm;

public class UserInfo {
    private String userId;
    private String displayName;
    private String accountType;
    private String phone;
    private String email;
    private String status;
    private boolean isJoinCompany;
    private String idCardNum;
    private String idCardType;
    private String authType;
    private String authTypeStr;
    private String authResult;
    private String authResultStr;
    private String sourceType;
    private String sourceCode;
    private String createTime;
    private String userUuid;
    private CertInfo certInfo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isJoinCompany() {
        return isJoinCompany;
    }

    public void setJoinCompany(boolean joinCompany) {
        isJoinCompany = joinCompany;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public String getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(String idCardType) {
        this.idCardType = idCardType;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getAuthTypeStr() {
        return authTypeStr;
    }

    public void setAuthTypeStr(String authTypeStr) {
        this.authTypeStr = authTypeStr;
    }

    public String getAuthResult() {
        return authResult;
    }

    public void setAuthResult(String authResult) {
        this.authResult = authResult;
    }

    public String getAuthResultStr() {
        return authResultStr;
    }

    public void setAuthResultStr(String authResultStr) {
        this.authResultStr = authResultStr;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUserUuid() {
        return userUuid;
    }

    public void setUserUuid(String userUuid) {
        this.userUuid = userUuid;
    }

    public CertInfo getCertInfo() {
        return certInfo;
    }

    public void setCertInfo(CertInfo certInfo) {
        this.certInfo = certInfo;
    }
}    