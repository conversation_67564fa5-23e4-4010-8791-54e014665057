package com.api.cyitce.seal3.vo.req.seal.physical;

import java.util.Date;
import java.util.List;

public class SealUsePickApiReq {
    /**
     * 取放章申请 ID 必填，用印申请单 ID
     **/
    private Long applyId;

    /**
     * 申请人登录名 必填
     **/
    private String applyStaffLoginName;

    /**
     * 申请时间 必填
     **/
    private Date applyTime;

    /**
     * 申请事由 必填
     **/
    private String applyReason;

    /**
     * 申请标题 必填
     **/
    private String applyTitle;

    /**
     * 取放章任务数据 批量的取放章任务数据
     **/
    private List<SealUsePickTaskVO> pushSealPickTaskInfoList;

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public String getApplyStaffLoginName() {
        return applyStaffLoginName;
    }

    public void setApplyStaffLoginName(String applyStaffLoginName) {
        this.applyStaffLoginName = applyStaffLoginName;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getApplyTitle() {
        return applyTitle;
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

    public List<SealUsePickTaskVO> getPushSealPickTaskInfoList() {
        return pushSealPickTaskInfoList;
    }

    public void setPushSealPickTaskInfoList(List<SealUsePickTaskVO> pushSealPickTaskInfoList) {
        this.pushSealPickTaskInfoList = pushSealPickTaskInfoList;
    }
}
