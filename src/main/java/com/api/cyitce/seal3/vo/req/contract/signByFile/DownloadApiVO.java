package com.api.cyitce.seal3.vo.req.contract.signByFile;

import java.util.List;

public class DownloadApiVO {

    /**
     * 下载类型  1 单个 2 批量
     */
    private Integer type;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同id集合 type为2时必填
     */
    private List<Long> contractIds;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public List<Long> getContractIds() {
        return contractIds;
    }

    public void setContractIds(List<Long> contractIds) {
        this.contractIds = contractIds;
    }
}
