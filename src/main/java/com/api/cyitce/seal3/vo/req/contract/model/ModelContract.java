package com.api.cyitce.seal3.vo.req.contract.model;

import java.util.List;

// 模板合同类
public class ModelContract {
    private String code;
    private String name;
    private String templateCode;
    private String creator;
    private String enterpriseId;
    private List<ModelDoc> docList;
    private String syncUrl;
    private String asyncUrl;

    // Getters 和 Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public List<ModelDoc> getDocList() {
        return docList;
    }

    public void setDocList(List<ModelDoc> docList) {
        this.docList = docList;
    }

    public String getSyncUrl() {
        return syncUrl;
    }

    public void setSyncUrl(String syncUrl) {
        this.syncUrl = syncUrl;
    }

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }
}

