package com.api.cyitce.seal3.vo.req.updatefile;

import java.util.List;

public class NullifyLog {

    // 合同ID（必填）
    private Long contractId;

    // 解约状态（非必填）
    private Integer status;

    // 拒绝解约原因（非必填）
    private String refuseReason;

    // 申请解约序号（非必填）
    private Integer applyNo;

    // 解约流程（非必填，包含多个解约流程步骤）
    // 注意：这里使用List<NullifyFlow>来表示解约流程的多个步骤
    private List<NullifyFlow> nullifyFlow;

}