package com.api.cyitce.seal3.vo.req.contract;

import com.api.cyitce.seal3.vo.req.contract.addSignerByFile.FileApiVO;

import java.util.List;

/**
 * @ClassName: CreateByFileApiReq
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:04
 * @Version: 1.0
 */
public class CreateByFileApiReq {

    /**
     * 合同编号，如果用户没有传递系统会自动生成合同编号。合同编号不能重复（1-64字符）
     */
    private String code;

    /**
     * 合同名称 1-64字符，支持中文、英文大小写，数字、特殊字符
     */
    private String name;

    /**
     * 签约人数 0-10人，0代表不限制签署人个数。需要调用强制结束接口结束合同签署。
     */
    private Integer signCount;

    /**
     * 签署有效期（天）默认90天。范围1-365天
     */
    private Integer signValidDays;

    /**
     * 是否顺序签署:false无序(默认)、true有序
     */
    private Boolean signSortable;

    /**
     * 是否立即发起合同:false不发起、true立即发起(默认)
     */
    private Boolean send;

    /**
     * 页面同步通知地址;页面签有效
     */
    private String syncUrl;

    /**
     * 后台异步通知地址
     */
    private String asyncUrl;

    /**
     * 最终文件类型 1:PDF 2:OFD
     */
    private Integer lastFileType;

    /**
     * 创建人（发起用户id）（创建人必须实名，并且加入到此企业）
     */
    private String creator;

    /**
     * 发起方企业id
     */
    private String enterpriseId;

    /**
     * 业务类型 默认：common
     */
    private String bizCode;

    /**
     * 合同文档名称1-64字符，支持中文、英文大小写，数字、特殊字符
     */
    private String docName;

    /**
     * 合同文件base64，最大不超过5MB，支持pdf/doc/docx/ofd文件格式
     */
    private String base64;

    /**
     * 合同文档
     */
    private List<FileApiVO> files;

    /**
     * 是否检查业务类型
     */
    private Boolean checkSysBizType;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSignCount() {
        return signCount;
    }

    public void setSignCount(Integer signCount) {
        this.signCount = signCount;
    }

    public Integer getSignValidDays() {
        return signValidDays;
    }

    public void setSignValidDays(Integer signValidDays) {
        this.signValidDays = signValidDays;
    }

    public Boolean getSignSortable() {
        return signSortable;
    }

    public void setSignSortable(Boolean signSortable) {
        this.signSortable = signSortable;
    }

    public Boolean getSend() {
        return send;
    }

    public void setSend(Boolean send) {
        this.send = send;
    }

    public String getSyncUrl() {
        return syncUrl;
    }

    public void setSyncUrl(String syncUrl) {
        this.syncUrl = syncUrl;
    }

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }

    public Integer getLastFileType() {
        return lastFileType;
    }

    public void setLastFileType(Integer lastFileType) {
        this.lastFileType = lastFileType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getBase64() {
        return base64;
    }

    public void setBase64(String base64) {
        this.base64 = base64;
    }

    public List<FileApiVO> getFiles() {
        return files;
    }

    public void setFiles(List<FileApiVO> files) {
        this.files = files;
    }

    public Boolean getCheckSysBizType() {
        return checkSysBizType;
    }

    public void setCheckSysBizType(Boolean checkSysBizType) {
        this.checkSysBizType = checkSysBizType;
    }

    @Override
    public String toString() {
        return "CreateByFileApiReq{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", signCount=" + signCount +
                ", signValidDays=" + signValidDays +
                ", signSortable=" + signSortable +
                ", send=" + send +
                ", syncUrl='" + syncUrl + '\'' +
                ", asyncUrl='" + asyncUrl + '\'' +
                ", lastFileType=" + lastFileType +
                ", creator='" + creator + '\'' +
                ", enterpriseId='" + enterpriseId + '\'' +
                ", bizCode='" + bizCode + '\'' +
                ", docName='" + docName + '\'' +
                ", base64='" + base64 + '\'' +
                ", files=" + files +
                ", checkSysBizType=" + checkSysBizType +
                '}';
    }
}