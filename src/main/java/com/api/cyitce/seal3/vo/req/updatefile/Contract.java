package com.api.cyitce.seal3.vo.req.updatefile;

public class Contract {

    // 签名记录代码
    private long signRecordCode;

    // 签名值，以base64编码
    private String signValue;

    // 合同内容
    private String content;

    // 合同名称（非必填）
    private String name;

    // 合同编号（非必填）
    private String code;

    // 业务类型（非必填）
    private String bizCode;

    // 签署人数量（非必填）
    private Integer signCount;

    // 模板ID（非必填）
    private Long templateId;

    // 待签署有效天数（非必填）
    private Integer waitSignValidDays;

    // 合同状态（非必填）
    private Integer status;

    // 是否顺序签署（非必填）
    private Boolean signSortable;

    // 企业ID（非必填）
    private String enterpriseId;

    // 三方企业ID（非必填）
    private String thirdEnterpriseId;

    // 企业名称（非必填）
    private String enterpriseName;

    // 发起人ID（非必填）
    private String creator;

    // 发起人三方ID（非必填）
    private String thirdCreator;

    // 发起人姓名（非必填）
    private String createName;

    // 页面同步通知地址（非必填）
    private String syncUrl;

    // 后台异步通知地址（非必填）
    private String asyncUrl;

    // 是否需要审批（非必填）
    private Boolean approved;

    // 合同发起审批状态（非必填）
    private Integer approveStatus;

    // 审批工作流实例ID（非必填）
    private String processInstanceId;

    // 解约状态（非必填）
    private Integer applyNullify;

    // 是否存在附件（非必填）
    private Boolean existAttached;

    // 归档状态（非必填）
    private Boolean archivedStatus;

    // 是否自动签署（非必填）
    private Boolean autoSign;

    // 文件类型（非必填）
    private Integer fileType;

    // 数据来源（非必填）
    private Integer dataSource;

    // 生效日期（非必填），格式：yyyy-MM-dd HH:mm:ss
    private String effectString;

    // 发送日期（非必填），格式：yyyy-MM-dd HH:mm:ss
    private String sendTime;

    // 最后签署时间（非必填），格式：yyyy-MM-dd HH:mm:ss
    private String lastSignedTime;

    // 过期时间（非必填），格式：yyyy-MM-dd HH:mm:ss
    private String expiredTime;

    // 完成时间（非必填），格式：yyyy-MM-dd HH:mm:ss
    private String finishedTime;

    public long getSignRecordCode() {
        return signRecordCode;
    }

    public void setSignRecordCode(long signRecordCode) {
        this.signRecordCode = signRecordCode;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public Integer getSignCount() {
        return signCount;
    }

    public void setSignCount(Integer signCount) {
        this.signCount = signCount;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getWaitSignValidDays() {
        return waitSignValidDays;
    }

    public void setWaitSignValidDays(Integer waitSignValidDays) {
        this.waitSignValidDays = waitSignValidDays;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getSignSortable() {
        return signSortable;
    }

    public void setSignSortable(Boolean signSortable) {
        this.signSortable = signSortable;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getThirdEnterpriseId() {
        return thirdEnterpriseId;
    }

    public void setThirdEnterpriseId(String thirdEnterpriseId) {
        this.thirdEnterpriseId = thirdEnterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getThirdCreator() {
        return thirdCreator;
    }

    public void setThirdCreator(String thirdCreator) {
        this.thirdCreator = thirdCreator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getSyncUrl() {
        return syncUrl;
    }

    public void setSyncUrl(String syncUrl) {
        this.syncUrl = syncUrl;
    }

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }

    public Boolean getApproved() {
        return approved;
    }

    public void setApproved(Boolean approved) {
        this.approved = approved;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public Integer getApplyNullify() {
        return applyNullify;
    }

    public void setApplyNullify(Integer applyNullify) {
        this.applyNullify = applyNullify;
    }

    public Boolean getExistAttached() {
        return existAttached;
    }

    public void setExistAttached(Boolean existAttached) {
        this.existAttached = existAttached;
    }

    public Boolean getArchivedStatus() {
        return archivedStatus;
    }

    public void setArchivedStatus(Boolean archivedStatus) {
        this.archivedStatus = archivedStatus;
    }

    public Boolean getAutoSign() {
        return autoSign;
    }

    public void setAutoSign(Boolean autoSign) {
        this.autoSign = autoSign;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public String getEffectString() {
        return effectString;
    }

    public void setEffectString(String effectString) {
        this.effectString = effectString;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getLastSignedTime() {
        return lastSignedTime;
    }

    public void setLastSignedTime(String lastSignedTime) {
        this.lastSignedTime = lastSignedTime;
    }

    public String getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(String expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(String finishedTime) {
        this.finishedTime = finishedTime;
    }

    // Getter和Setter方法省略，可以通过IDE自动生成
    // ...

    // 示例：生成getter和setter方法（通常使用IDE自动生成）
    // public long getSignRecordCode() {
    //     return signRecordCode;
    // }
    //
    // public void setSignRecordCode(long signRecordCode) {
    //     this.signRecordCode = signRecordCode;
    // }
    //
    // ... (其他字段的getter和setter方法类似)
}