package com.api.cyitce.seal3.vo.resp;

/**
 * @ClassName: 返回值消息
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-13  10:23
 * @Version: 1.0
 */
public class MsgResp<T> extends BaseResp<T> {
    public String getMsg() {
        return getMessage();
    }

    public void setMsg(String msg) {
        setMessage(msg);
    }

    public static MsgResp ok(){
        MsgResp msgResp = new MsgResp();
        msgResp.setStatus(1);
        msgResp.setMsg("成功");
        return msgResp;
    }

    public static <T> MsgResp<T> ok(Object data){
        MsgResp msgResp = new MsgResp();
        msgResp.setStatus(1);
        msgResp.setMsg("成功");
        msgResp.setData(data);
        return msgResp;
    }

    public static <T> MsgResp<T> error(){
        MsgResp msgResp = new MsgResp();
        msgResp.setStatus(0);
        msgResp.setMsg("失败");
        return msgResp;
    }

    public static <T> MsgResp<T> error(String msg){
        MsgResp msgResp = new MsgResp();
        msgResp.setStatus(0);
        msgResp.setMsg(msg);
        return msgResp;
    }
}