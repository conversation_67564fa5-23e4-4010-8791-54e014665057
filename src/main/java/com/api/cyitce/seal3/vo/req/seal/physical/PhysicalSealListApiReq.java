package com.api.cyitce.seal3.vo.req.seal.physical;

/**
 * @ClassName: SealListApiReq
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  17:45
 * @Version: 1.0
 */
public class PhysicalSealListApiReq {
    
    /**
     * 印控仪编号 为空时则获取所有印控仪中的印章
     **/
    private String instrumentCode;

    /**
     * 每页查询条数 默认是 0，不分页
     **/
    private Integer pageSize;

    /**
     * 查询第几页 默认 1
     **/
    private Integer pageNum;

    public String getInstrumentCode() {
        return instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode) {
        this.instrumentCode = instrumentCode;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
}