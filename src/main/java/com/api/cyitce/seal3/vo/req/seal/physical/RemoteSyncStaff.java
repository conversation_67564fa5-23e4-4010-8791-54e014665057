package com.api.cyitce.seal3.vo.req.seal.physical;

import java.util.List;

// 远程同步人员信息类
public class RemoteSyncStaff {
    // 人员唯一编码，必填项
    private String staffUnicode;
    // 登录名，必填项
    private String username;
    // 同步人员的密码(明文)，密文提供算法，非必填
    private String password;
    // 人员名称，必填项
    private String staffName;
    // 身份证号，必填，15 或 18 位有效身份证号
    private String staffIdCard;
    // 手机号，非必填，国内 11 位有效手机号
    private String cellphone;
    // 性别，非必填，0/1 女/男
    private Short sex;
    // 邮箱，非必填，正常格式的邮箱地址
    private String email;
    // 员工岗位，非必填
    private String post;
    // 所在部门编码，必填
    private List<String> departCodes;
    // 员工状态，0 停用，1 启用，默认 0
    private String state;

    // 构造函数，用于初始化人员信息
    public RemoteSyncStaff(String staffUnicode, String username, String staffName, String staffIdCard, List<String> departCodes) {
        this.staffUnicode = staffUnicode;
        this.username = username;
        this.staffName = staffName;
        this.staffIdCard = staffIdCard;
        this.departCodes = departCodes;
        this.state = "0"; // 默认停用
    }

    // 获取人员唯一编码
    public String getStaffUnicode() {
        return staffUnicode;
    }

    // 设置人员唯一编码
    public void setStaffUnicode(String staffUnicode) {
        this.staffUnicode = staffUnicode;
    }

    // 获取登录名
    public String getUsername() {
        return username;
    }

    // 设置登录名
    public void setUsername(String username) {
        this.username = username;
    }

    // 获取密码
    public String getPassword() {
        return password;
    }

    // 设置密码
    public void setPassword(String password) {
        this.password = password;
    }

    // 获取人员名称
    public String getStaffName() {
        return staffName;
    }

    // 设置人员名称
    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    // 获取身份证号
    public String getStaffIdCard() {
        return staffIdCard;
    }

    // 设置身份证号
    public void setStaffIdCard(String staffIdCard) {
        this.staffIdCard = staffIdCard;
    }

    // 获取手机号
    public String getCellphone() {
        return cellphone;
    }

    // 设置手机号
    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    // 获取性别
    public Short getSex() {
        return sex;
    }

    // 设置性别
    public void setSex(Short sex) {
        this.sex = sex;
    }

    // 获取邮箱
    public String getEmail() {
        return email;
    }

    // 设置邮箱
    public void setEmail(String email) {
        this.email = email;
    }

    // 获取员工岗位
    public String getPost() {
        return post;
    }

    // 设置员工岗位
    public void setPost(String post) {
        this.post = post;
    }

    // 获取所在部门编码
    public List<String> getDepartCodes() {
        return departCodes;
    }

    // 设置所在部门编码
    public void setDepartCodes(List<String> departCodes) {
        this.departCodes = departCodes;
    }

    // 获取员工状态
    public String getState() {
        return state;
    }

    // 设置员工状态
    public void setState(String state) {
        this.state = state;
    }
}
