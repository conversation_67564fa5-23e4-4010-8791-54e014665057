package com.api.cyitce.seal3.vo.req.seal.physical;

import java.util.Date;
import java.util.List;

public class SealUseApplyApiReq {

    /**
     * 申请 ID 必填，用印申请单 ID
     **/
    private String applyId;

    /**
     * 申请人登录名 必填
     **/
    private String applyStaffLoginName;

    /**
     * 申请时间 必填
     **/
    private Date applyTime;

    /**
     * 申请事由 必填
     **/
    private String applyReason;

    /**
     * 申请标题 必填
     **/
    private String applyTitle;

    /**
     * 业务类型 非必填，默认是“用印业务”
     **/
    private String useType;

    /**
     * 文件名 必填，多个文件以”;”分割
     **/
    private String fileName;

    /**
     * 文件全路径 必填，多个文件路径以“;”分割
     **/
    private String filePath;

    /**
     * 限时盖章开始时间 非必填
     **/
    private Date startTIme;

    /**
     * 限时盖章结束时间 非必填
     **/
    private Date endTime;

    /**
     * 用印任务数据 批量的盖章任务数据
     **/
    private List<SealUseTaskVo> data;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getApplyStaffLoginName() {
        return applyStaffLoginName;
    }

    public void setApplyStaffLoginName(String applyStaffLoginName) {
        this.applyStaffLoginName = applyStaffLoginName;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getApplyTitle() {
        return applyTitle;
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Date getStartTIme() {
        return startTIme;
    }

    public void setStartTIme(Date startTIme) {
        this.startTIme = startTIme;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<SealUseTaskVo> getData() {
        return data;
    }

    public void setData(List<SealUseTaskVo> data) {
        this.data = data;
    }
}
