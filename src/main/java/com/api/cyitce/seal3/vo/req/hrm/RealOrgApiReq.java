package com.api.cyitce.seal3.vo.req.hrm;

import com.api.cyitce.seal3.enums.account.UserTypeEnum;

public class RealOrgApiReq {
    
    /**
     * 企业类型 必填 ET_PE：企业  ET_SE：个体工商户 ET_OU：政府机构/事业单位
     **/
    private String type = UserTypeEnum.ENTERPRISE.getType();

    /**
     * 企业名称 必填
     **/
    private String name;

    /**
     * 统一社会信用代码或营业执照注册号 选填
     */
    private String idCode;

    /**
     * 法人姓名 必填
     **/
    private String legalName;

    /**
     * 法人证件号 必填
     **/
    private String legalId;

    public RealOrgApiReq(String type, String name, String idCode, String legalName, String legalId) {
        this.type = type;
        this.name = name;
        this.idCode = idCode;
        this.legalName = legalName;
        this.legalId = legalId;
    }

    public RealOrgApiReq() {
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getLegalId() {
        return legalId;
    }

    public void setLegalId(String legalId) {
        this.legalId = legalId;
    }

    @Override
    public String toString() {
        return "RealOrgApiReq{" +
                "type='" + type + '\'' +
                ", name='" + name + '\'' +
                ", idCode='" + idCode + '\'' +
                ", legalName='" + legalName + '\'' +
                ", legalId='" + legalId + '\'' +
                '}';
    }
}
