package com.api.cyitce.seal3.vo.resp;

/**
 * @ClassName: BaseResp
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  16:49
 * @Version: 1.0
 */
public class BaseResp<T> {
    private Integer status;
    private String message;
    private T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean isStatus() {
        return status==1?true:false;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setStatus(boolean status) {
        this.status = status?1:0;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BaseResp() {
        this.status = 0;
        this.data = null;
        this.message = null;
    }

    public BaseResp(boolean status) {
        this.status = status?1:0;
        this.data = null;
        this.message = null;
    }
}