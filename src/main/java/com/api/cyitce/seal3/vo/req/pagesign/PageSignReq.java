package com.api.cyitce.seal3.vo.req.pagesign;

public class PageSignReq {

    /**
     * 合同id 必填
     */
    private Long contractId;

    /**
     * 签署人 必填
     */
    private AddContractSignerVO signer;

    /**
     * 签署类型 非必填 1：页面云端签署 2：页面ukey签署
     */
    private Integer signType = 1;

    /**
     * 链接有效期,单位分钟;默认30分钟,最小5分钟, 最大60*24分钟(24小时)
     */
    private Integer expire = 30;

    /**
     * 同步地址 非必填 1-500个字符
     * 会覆盖创建合同时传的地址
     */
    private String syncUrl;


    public boolean getIsUserWishes() {
        return isUserWishes;
    }

    public void setIsUserWishes(boolean userWishes) {
        isUserWishes = userWishes;
    }

    public Integer[] getUserWishesWay() {
        return userWishesWay;
    }

    public void setUserWishesWay(Integer[] userWishesWay) {
        this.userWishesWay = userWishesWay;
    }

    private boolean isUserWishes;

    private Integer[] userWishesWay;

    /**
     * 异步地址 非必填 1-500个字符
     * 会覆盖创建合同时传的地址
     */
    private String asyncUrl;


    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public AddContractSignerVO getSigner() {
        return signer;
    }

    public void setSigner(AddContractSignerVO signer) {
        this.signer = signer;
    }

    public Integer getSignType() {
        return signType;
    }

    public void setSignType(Integer signType) {
        this.signType = signType;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    public String getSyncUrl() {
        return syncUrl;
    }

    public void setSyncUrl(String syncUrl) {
        this.syncUrl = syncUrl;
    }

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }

    @Override
    public String toString() {
        return "PageSignReq{" +
                "contractId=" + contractId +
                ", signer=" + signer +
                ", signType=" + signType +
                ", expire=" + expire +
                ", syncUrl='" + syncUrl + '\'' +
                ", asyncUrl='" + asyncUrl + '\'' +
                '}';
    }
}
