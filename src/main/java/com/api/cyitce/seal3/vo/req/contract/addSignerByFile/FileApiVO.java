package com.api.cyitce.seal3.vo.req.contract.addSignerByFile;

import com.api.cyitce.seal3.vo.req.template.create.WaterMarkBaseVO;

import java.util.List;

/**
 * @ClassName: FileApiVO
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:06
 * @Version: 1.0
 */
public class FileApiVO {

    /**
     * 合同文档名称1-64字符，支持中文、英文大小写，数字、特殊字符
     */
    private String docName;

    /**
     * 合同文件base64，最大不超过5MB，支持pdf/doc/docx/ofd文件格式
     */
    private String base64;

    /**
     * 文件绝对路径（带文件名）(base64与filePath二选一必填)
     */
    private String filePath;

    /**
     * 文件绝对路径类型:1ftp路径(默认)、2http路径
     */
    private Integer pathType;

    /**
     * 是否开启水印(0:不开启;1:开启)
     */
    private Boolean waterMarkOff;

    /**
     * 水印集合设置参数
     */
    private List<WaterMarkBaseVO> waterMarkParams;

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getBase64() {
        return base64;
    }

    public void setBase64(String base64) {
        this.base64 = base64;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getPathType() {
        return pathType;
    }

    public void setPathType(Integer pathType) {
        this.pathType = pathType;
    }

    public Boolean getWaterMarkOff() {
        return waterMarkOff;
    }

    public void setWaterMarkOff(Boolean waterMarkOff) {
        this.waterMarkOff = waterMarkOff;
    }

    public List<WaterMarkBaseVO> getWaterMarkParams() {
        return waterMarkParams;
    }

    public void setWaterMarkParams(List<WaterMarkBaseVO> waterMarkParams) {
        this.waterMarkParams = waterMarkParams;
    }

    @Override
    public String toString() {
        return "FileApiVO{" +
                "docName='" + docName + '\'' +
                ", base64='" + base64 + '\'' +
                ", filePath='" + filePath + '\'' +
                ", pathType=" + pathType +
                ", waterMarkOff=" + waterMarkOff +
                ", waterMarkParams=" + waterMarkParams +
                '}';
    }
}