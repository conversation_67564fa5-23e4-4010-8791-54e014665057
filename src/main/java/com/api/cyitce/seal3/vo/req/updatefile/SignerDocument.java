package com.api.cyitce.seal3.vo.req.updatefile;

public class SignerDocument {

    // 合同ID（非必填）
    private Long contractId;

    // 签署人ID（非必填）
    private Long signerId;

    // 文档ID（非必填）
    private Long docId;

    // 文档名称（非必填）
    private String docName;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getSignerId() {
        return signerId;
    }

    public void setSignerId(Long signerId) {
        this.signerId = signerId;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public Integer getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(Integer docStatus) {
        this.docStatus = docStatus;
    }

    public Boolean getReaded() {
        return readed;
    }

    public void setReaded(Boolean readed) {
        this.readed = readed;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    // 用户ID（非必填）
    private String userId;

    // 用户三方ID（非必填）
    private String thirdUserId;

    // 文档状态（非必填）
    private Integer docStatus;

    // 是否已阅读合同（非必填）
    private Boolean readed;

    // 签署人文档类型（非必填）
    // 1=签署, 2=阅读
    private String docType;
}