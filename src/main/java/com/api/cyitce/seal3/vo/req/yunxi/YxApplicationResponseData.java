package com.api.cyitce.seal3.vo.req.yunxi;

// "创建申请单" 接口成功响应时，data数组中对象的模型
public class YxApplicationResponseData {
    /**
     * 合同附件名称
     */
    private String fileName;

    /**
     * 赋码后的合同附件url地址
     */
    private String fileUrl;

    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    public String getFileUrl() { return fileUrl; }
    public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }
}