package com.api.cyitce.seal3.vo.req.yunxi;

// params.ApplicationEleFences 数组中的对象模型
public class YxApplicationEleFence {
    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 半径范围, 单位:千米 (0.01-10.00)
     */
    private Double range;

    /**
     * 开始时间, 格式: yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间, 格式: yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    public YxApplicationEleFence() {}

    public YxApplicationEleFence(String longitude, String latitude, Double range, String startTime, String endTime) {
        this.longitude = longitude;
        this.latitude = latitude;
        this.range = range;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    // Getters and Setters
    public String getLongitude() { return longitude; }
    public void setLongitude(String longitude) { this.longitude = longitude; }
    public String getLatitude() { return latitude; }
    public void setLatitude(String latitude) { this.latitude = latitude; }
    public Double getRange() { return range; }
    public void setRange(Double range) { this.range = range; }
    public String getStartTime() { return startTime; }
    public void setStartTime(String startTime) { this.startTime = startTime; }
    public String getEndTime() { return endTime; }
    public void setEndTime(String endTime) { this.endTime = endTime; }
}