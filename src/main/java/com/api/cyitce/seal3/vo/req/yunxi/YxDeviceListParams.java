package com.api.cyitce.seal3.vo.req.yunxi;

/**
 * 获取智能印章列表接口的参数实体类 (用于 YXYG04_01)
 */
public class YxDeviceListParams {
    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * 查询页码, 默认第1页 (必填)
     */
    private int pageNum;

    /**
     * 每页条数, 默认10条/页,最大2000条/页 (必填)
     */
    private int pageSize;
    
    /**
     * 组织id, 第三方对接系统的组织id
     */
    private String deptId;
    
    /**
     * 设备名称, 支持模糊查询
     */
    private String name;
    
    /**
     * 设备标识, 支持模糊查询
     */
    private String uuid;
    
    // Getters & Setters...
}