package com.api.cyitce.seal3.vo.req.contract.addSignerByFile;

import java.util.Objects;

public class XYControlApiVO extends ControlApiVO {

    /**
     * 签章页码范围，格式：“1~5,8,-3~-1”，说明如下：0：所有页（默认）,1~5：第1页至第5页,-3~-1：倒数第3页至倒数第1页
     */
    private String pageNum;

    /**
     * 签署控件x坐标,pdf位置比例
     */
    private Float x;

    /**
     * 签署控件y坐标,pdf位置比例
     */
    private Float y;

    public XYControlApiVO() {
    }

    public XYControlApiVO(Integer id, String type, String pageNum, Float x, Float y, Float width, Float height) {
        super.setId(id);
        super.setType(type);
        super.setWidth(width);
        super.setHeight(height);
        this.pageNum = pageNum;
        this.x = x;
        this.y = y;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        XYControlApiVO that = (XYControlApiVO) o;
        return Objects.equals(pageNum, that.pageNum) && Objects.equals(x, that.x) && Objects.equals(y, that.y);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pageNum, x, y);
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public Float getX() {
        return x;
    }

    public void setX(Float x) {
        this.x = x;
    }

    public Float getY() {
        return y;
    }

    public void setY(Float y) {
        this.y = y;
    }
}
