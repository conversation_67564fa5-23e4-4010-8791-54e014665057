package com.api.cyitce.seal3.vo.req.seal.physical;

import java.util.List;

// 同步请求的主类，包含部门信息和人员信息
public class SyncRequest {
    // 部门信息数组
    private List<RemoteSyncDepart> syncDeparts;
    // 人员信息数组
    private List<RemoteSyncStaff> syncStaffs;

    // 构造函数，用于初始化部门信息和人员信息
    public SyncRequest(List<RemoteSyncDepart> syncDeparts, List<RemoteSyncStaff> syncStaffs) {
        this.syncDeparts = syncDeparts;
        this.syncStaffs = syncStaffs;
    }

    // 获取部门信息数组
    public List<RemoteSyncDepart> getSyncDeparts() {
        return syncDeparts;
    }

    // 设置部门信息数组
    public void setSyncDeparts(List<RemoteSyncDepart> syncDeparts) {
        this.syncDeparts = syncDeparts;
    }

    // 获取人员信息数组
    public List<RemoteSyncStaff> getSyncStaffs() {
        return syncStaffs;
    }

    // 设置人员信息数组
    public void setSyncStaffs(List<RemoteSyncStaff> syncStaffs) {
        this.syncStaffs = syncStaffs;
    }
}

