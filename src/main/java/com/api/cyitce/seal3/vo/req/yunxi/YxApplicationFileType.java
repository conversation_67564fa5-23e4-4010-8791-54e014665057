package com.api.cyitce.seal3.vo.req.yunxi;

import java.util.List;

public class YxApplicationFileType {
    /**
     * 操作类型 (必填), 0:删除 1:新增或更新
     */
    private Integer mode;

    /**
     * 组织id, 如果为空，则自动匹配到根组织下
     */
    private String deptId;

    /**
     * 唯一标识, 如果为空，则自动分配
     */
    private String code;

    /**
     * 名称 (必填)
     */
    private String name;

    /**
     * 启用状态, true:启用(默认) false:禁用
     */
    private Boolean enabled;

    /**
     * 是否拍照, true:启用(默认) false:禁用
     */
    private Boolean takePictureEnabled;

    /**
     * 解锁方式: default, common, ocr, qrcode
     */
    private String unlockType;

    /**
     * 解锁阈值, 0:不限制(默认)
     */
    private Integer unlockThreshold;

    /**
     * 赋码位置: left_up, right_up, up(默认), left_down, right_down, down
     */
    private String qrCodePosition;

    /**
     * 是否专人盖章, true:是 false:否(默认)
     */
    private Boolean appointSealUserEnabled;

    /**
     * 专人盖章类型: all(默认), keeper, user
     */
    private String appointSealUserType;

    /**
     * 指定用户id列表 (当appointSealUserType为user时)
     */
    private List<String> appointSealUserIds;

    /**
     * 事前比对阈值, 0:关闭(默认)
     */
    private Integer beforeRatioThreshold;

    /**
     * 事中比对阈值, 0:关闭(默认)
     */
    private Integer usingRatioThreshold;

    /**
     * 事后比对阈值, 0:关闭(默认)
     */
    private Integer afterRatioThreshold;

    public YxApplicationFileType() {
    }

    @Override
    public String toString() {
        return "YxApplicationFileType{" +
                "mode=" + mode +
                ", deptId='" + deptId + '\'' +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", enabled=" + enabled +
                ", takePictureEnabled=" + takePictureEnabled +
                ", unlockType='" + unlockType + '\'' +
                ", unlockThreshold=" + unlockThreshold +
                ", qrCodePosition='" + qrCodePosition + '\'' +
                ", appointSealUserEnabled=" + appointSealUserEnabled +
                ", appointSealUserType='" + appointSealUserType + '\'' +
                ", appointSealUserIds=" + appointSealUserIds +
                ", beforeRatioThreshold=" + beforeRatioThreshold +
                ", usingRatioThreshold=" + usingRatioThreshold +
                ", afterRatioThreshold=" + afterRatioThreshold +
                '}';
    }

    public YxApplicationFileType(Integer mode, String deptId, String code, String name, Boolean enabled, Boolean takePictureEnabled, String unlockType, Integer unlockThreshold, String qrCodePosition, Boolean appointSealUserEnabled, String appointSealUserType, List<String> appointSealUserIds, Integer beforeRatioThreshold, Integer usingRatioThreshold, Integer afterRatioThreshold) {
        this.mode = mode;
        this.deptId = deptId;
        this.code = code;
        this.name = name;
        this.enabled = enabled;
        this.takePictureEnabled = takePictureEnabled;
        this.unlockType = unlockType;
        this.unlockThreshold = unlockThreshold;
        this.qrCodePosition = qrCodePosition;
        this.appointSealUserEnabled = appointSealUserEnabled;
        this.appointSealUserType = appointSealUserType;
        this.appointSealUserIds = appointSealUserIds;
        this.beforeRatioThreshold = beforeRatioThreshold;
        this.usingRatioThreshold = usingRatioThreshold;
        this.afterRatioThreshold = afterRatioThreshold;
    }

    // Getters and Setters
    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getTakePictureEnabled() {
        return takePictureEnabled;
    }

    public void setTakePictureEnabled(Boolean takePictureEnabled) {
        this.takePictureEnabled = takePictureEnabled;
    }

    public String getUnlockType() {
        return unlockType;
    }

    public void setUnlockType(String unlockType) {
        this.unlockType = unlockType;
    }

    public Integer getUnlockThreshold() {
        return unlockThreshold;
    }

    public void setUnlockThreshold(Integer unlockThreshold) {
        this.unlockThreshold = unlockThreshold;
    }

    public String getQrCodePosition() {
        return qrCodePosition;
    }

    public void setQrCodePosition(String qrCodePosition) {
        this.qrCodePosition = qrCodePosition;
    }

    public Boolean getAppointSealUserEnabled() {
        return appointSealUserEnabled;
    }

    public void setAppointSealUserEnabled(Boolean appointSealUserEnabled) {
        this.appointSealUserEnabled = appointSealUserEnabled;
    }

    public String getAppointSealUserType() {
        return appointSealUserType;
    }

    public void setAppointSealUserType(String appointSealUserType) {
        this.appointSealUserType = appointSealUserType;
    }

    public List<String> getAppointSealUserIds() {
        return appointSealUserIds;
    }

    public void setAppointSealUserIds(List<String> appointSealUserIds) {
        this.appointSealUserIds = appointSealUserIds;
    }

    public Integer getBeforeRatioThreshold() {
        return beforeRatioThreshold;
    }

    public void setBeforeRatioThreshold(Integer beforeRatioThreshold) {
        this.beforeRatioThreshold = beforeRatioThreshold;
    }

    public Integer getUsingRatioThreshold() {
        return usingRatioThreshold;
    }

    public void setUsingRatioThreshold(Integer usingRatioThreshold) {
        this.usingRatioThreshold = usingRatioThreshold;
    }

    public Integer getAfterRatioThreshold() {
        return afterRatioThreshold;
    }

    public void setAfterRatioThreshold(Integer afterRatioThreshold) {
        this.afterRatioThreshold = afterRatioThreshold;
    }
}