package com.api.cyitce.seal3.vo.req.template.create;

/**
 * @ClassName: WaterMarkBaseVO
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:08
 * @Version: 1.0
 */
public class WaterMarkBaseVO {

    /**
     * 水印名称
     */
    private String markName;

    /**
     * 水印类型:1文字2图片3二维码
     */
    private Integer markType;

    /**
     * 水印位置:1左上,2右上,3 左下 ,4右下, 5居中,6平铺,7填充(默认1左上)(注意二维码仅支持1左上,2右上,3 左下 ,4右下)
     */
    private Integer position = 1;

    /**
     * 文本水印
     */
    private TextWaterMarkVO pdfTextMarkParams;

    /**
     * 图片水印
     */
    private ImageWaterMarkVO pdfImageMarkParams;

    /**
     * 二维码水印
     */
    private QrWaterMarkVO pdfQrCodeMarkParams;

    public String getMarkName() {
        return markName;
    }

    public void setMarkName(String markName) {
        this.markName = markName;
    }

    public Integer getMarkType() {
        return markType;
    }

    public void setMarkType(Integer markType) {
        this.markType = markType;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public TextWaterMarkVO getPdfTextMarkParams() {
        return pdfTextMarkParams;
    }

    public void setPdfTextMarkParams(TextWaterMarkVO pdfTextMarkParams) {
        this.pdfTextMarkParams = pdfTextMarkParams;
    }

    public ImageWaterMarkVO getPdfImageMarkParams() {
        return pdfImageMarkParams;
    }

    public void setPdfImageMarkParams(ImageWaterMarkVO pdfImageMarkParams) {
        this.pdfImageMarkParams = pdfImageMarkParams;
    }

    public QrWaterMarkVO getPdfQrCodeMarkParams() {
        return pdfQrCodeMarkParams;
    }

    public void setPdfQrCodeMarkParams(QrWaterMarkVO pdfQrCodeMarkParams) {
        this.pdfQrCodeMarkParams = pdfQrCodeMarkParams;
    }
}