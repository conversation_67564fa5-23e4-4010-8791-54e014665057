package com.api.cyitce.seal3.vo.req.hrm;

public class CreateOrgApiReq {
    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 企业全称（1-32位字符）
     */
    private String orgName;

    /**
     * 证件类型（"N"，社会统一信用代码（支持多种）） authentication=true时必填
     */
    private String idType = "N";

    /**
     * 证件号（1-32） authentication=true时必填
     */
    private String orgCode;

    /**
     * 其他证件类型名称
     */
    private String otherCardName;

    /**
     * 企业类型:ET_PE:企业 (默认),  ET_SE:个体工商户,  OU:政府机构/事业单位
     */
    private String enterpriseType;

    /**
     * 实名认证来源(客户认证true,天威认证false)
     */
    private Boolean authentication = true;

    /**
     * 法定代表人姓名,(1-32位长度)
     */
    private String legalName;

    /**
     * 法定代表人手机号
     */
    private String legalPhone;

    /**
     * 法定代表人证件类型
     */
    private String legalIdCardType;

    /**
     * 法定代表人证件号(身份证号15、18位校验,其它1-64位校验)
     */
    private String legalIdCard;

    /**
     * 扩展数据,数据格式为json(key/value形式)例如:{"name":"张三"}
     */
    private String extendData;

    /**
     * 企业标签
     */
    private String enterpriseLabel;

    /**
     * 企业三方id
     */
    private String thirdUniqueId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOtherCardName() {
        return otherCardName;
    }

    public void setOtherCardName(String otherCardName) {
        this.otherCardName = otherCardName;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public Boolean getAuthentication() {
        return authentication;
    }

    public void setAuthentication(Boolean authentication) {
        this.authentication = authentication;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }

    public String getLegalIdCardType() {
        return legalIdCardType;
    }

    public void setLegalIdCardType(String legalIdCardType) {
        this.legalIdCardType = legalIdCardType;
    }

    public String getLegalIdCard() {
        return legalIdCard;
    }

    public void setLegalIdCard(String legalIdCard) {
        this.legalIdCard = legalIdCard;
    }

    public String getExtendData() {
        return extendData;
    }

    public void setExtendData(String extendData) {
        this.extendData = extendData;
    }

    public String getEnterpriseLabel() {
        return enterpriseLabel;
    }

    public void setEnterpriseLabel(String enterpriseLabel) {
        this.enterpriseLabel = enterpriseLabel;
    }

    public String getThirdUniqueId() {
        return thirdUniqueId;
    }

    public void setThirdUniqueId(String thirdUniqueId) {
        this.thirdUniqueId = thirdUniqueId;
    }
}
