package com.api.cyitce.seal3.vo.req.hrm;

import java.util.List;

/**
 * @ClassName: HrmResourceVo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-15  15:21
 * @Version: 1.0
 */
public class HrmResourceApiReq {

    /**
     * 人员唯一编码 必填
     **/
    private String staffUnicode;

    /**
     * 登录名 必填
     **/
    private String username;


    /**
     * 密码 必填
     **/
    private String password;


    /**
     * 人员名称 必填
     **/
    private String staffName;

    /**
     * 身份证号 非必填 15或18位有效身份证号
     **/
    private String staffIdCard;

    /**
     * 手机号 非必填 国内11位有效手机号
     **/
    private String cellphone;

    /**
     * 性别 非必填 0/1 女/男
     **/
    private Short sex;

    /**
     * 邮箱 非必填 正常格式的邮箱地址
     **/
    private String email;

    /**
     * 员工岗位 非必填
     **/
    private String post;

    /**
     * 所在部门编码 必填
     **/
    private List<String> departCodes;

    /**
     * 部门名称 非必填
     **/
    private String departmentName;

    /**
     * 员工状态 0停用 1启用 默认0
     **/
    private String state = "1";

    public String getId() {
        return oaid;
    }

    public void setId(String id) {
        this.oaid = id;
    }

    private String oaid;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSubcompanyid1() {
        return subcompanyid1;
    }

    public void setSubcompanyid1(String subcompanyid1) {
        this.subcompanyid1 = subcompanyid1;
    }

    // 临时字段
    private String subcompanyid1;

    public String getStaffUnicode() {
        return staffUnicode;
    }

    public void setStaffUnicode(String staffUnicode) {
        this.staffUnicode = staffUnicode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getStaffIdCard() {
        return staffIdCard;
    }

    public void setStaffIdCard(String staffIdCard) {
        this.staffIdCard = staffIdCard;
    }

    public String getCellphone() {
        return cellphone;
    }

    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    public Short getSex() {
        return sex;
    }

    public void setSex(Short sex) {
        this.sex = sex;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public List<String> getDepartCodes() {
        return departCodes;
    }

    public void setDepartCodes(List<String> departCodes) {
        this.departCodes = departCodes;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "HrmResourceApiReq{" +
                "staffUnicode='" + staffUnicode + '\'' +
                ", username='" + username + '\'' +
                ", staffName='" + staffName + '\'' +
                ", staffIdCard='" + staffIdCard + '\'' +
                ", cellphone='" + cellphone + '\'' +
                ", sex=" + sex +
                ", email='" + email + '\'' +
                ", post='" + post + '\'' +
                ", departCodes=" + departCodes +
                ", departmentName='" + departmentName + '\'' +
                ", state='" + state + '\'' +
                '}';
    }
}