package com.api.cyitce.seal3.vo.req.yunxi;

/**
 * 设备关联的电子围栏信息实体 (YxDevice的嵌套对象)
 */
public class YxDeviceFence {
    /**
     * 围栏名称
     */
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Float getRange() {
        return range;
    }

    public void setRange(Float range) {
        this.range = range;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 围栏标识
     */
    private String code;
    
    /**
     * 经度
     */
    private String longitude;
    
    /**
     * 维度
     */
    private String latitude;
    
    /**
     * 半径, 单位：千米
     */
    private Float range;
    
    /**
     * 地址
     */
    private String address;

    // Getters & Setters...
}