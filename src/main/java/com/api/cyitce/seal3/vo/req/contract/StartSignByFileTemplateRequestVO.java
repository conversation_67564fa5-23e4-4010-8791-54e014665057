package com.api.cyitce.seal3.vo.req.contract;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/11 0011
 * @description
 */

public class StartSignByFileTemplateRequestVO implements Serializable {
    /**
     * 1 页面云端签署 2 页面ukey签署
     */
    private Integer signType = 1;

    /**
     * 合同id
     */
    private Long contractId;
    /**
     * 合同签署人
     */
    private AddTemplateSignerVO signer;
    /**
     * 链接有效期,单位分钟;默认30分钟,最小5分钟,<br />最大60*24分钟(24小时);
     */
    private Integer expire = 30;

    /**
     * 是否发短信
     * 20231121
     */
    private Boolean sendMsg;

    public String getAsyncUrl() {
        return asyncUrl;
    }

    public void setAsyncUrl(String asyncUrl) {
        this.asyncUrl = asyncUrl;
    }

    private  String asyncUrl;


    public Integer getSignType() {
        return signType;
    }

    public void setSignType(Integer signType) {
        this.signType = signType;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public AddTemplateSignerVO getSigner() {
        return signer;
    }

    public void setSigner(AddTemplateSignerVO signer) {
        this.signer = signer;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    public Boolean getSendMsg() {
        return sendMsg;
    }

    public void setSendMsg(Boolean sendMsg) {
        this.sendMsg = sendMsg;
    }
}
