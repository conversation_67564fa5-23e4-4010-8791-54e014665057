package com.api.cyitce.seal3.vo.req.contract.addSignerByFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class AddSignerApiVO {

    /**
     * 签署人类型：1个人、2企业
     */
    private Integer signerType;

    /**
     * 签署人用户id，签署人可以未实名
     */
    private String userId;

    /**
     * 签署人公司ID（当签署人类型为企业时，必填）
     */
    private String enterpriseId;

    /**
     * 是否要做意愿认证
     */
    private Boolean isUserWishes;


    private List<Integer> userWishesWay = new ArrayList<>();

    /**
     * 签署人顺序（签署顺序1-100之间）
     */
    private Integer sequence;

    /**
     * 是否发送短信通知到诚信签-前置系统签署,false不发送(默认)、true发送
     */
    private Boolean sendMsg;

    /**
     * 是否指定签署位置,设置签署控件
     */
    private Boolean setting;

    /**
     * 不设置控件,签署时的控件类型范围;当setting为false时,该参数为必填
     */
    private Set<String> controlsType;

    /**
     * 签署人要签署的合同文件
     */
    private List<SignFileApiVO> signFiles;

    /**
     * 签署时是否可编辑控件
     */
    private Boolean updateControlAtSign;

    /**
     * 签署时是否需要弹窗提示信息
     */
    private Boolean toastRead;

    public Integer getSignerType() {
        return signerType;
    }

    public void setSignerType(Integer signerType) {
        this.signerType = signerType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Boolean getUserWishes() {
        return isUserWishes;
    }

    public void setUserWishes(Boolean userWishes) {
        isUserWishes = userWishes;
    }

    public List<Integer> getUserWishesWay() {
        return userWishesWay;
    }

    public void setUserWishesWay(List<Integer> userWishesWay) {
        this.userWishesWay = userWishesWay;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Boolean getSendMsg() {
        return sendMsg;
    }

    public void setSendMsg(Boolean sendMsg) {
        this.sendMsg = sendMsg;
    }

    public Boolean getSetting() {
        return setting;
    }

    public void setSetting(Boolean setting) {
        this.setting = setting;
    }

    public Set<String> getControlsType() {
        return controlsType;
    }

    public void setControlsType(Set<String> controlsType) {
        this.controlsType = controlsType;
    }

    public List<SignFileApiVO> getSignFiles() {
        return signFiles;
    }

    public void setSignFiles(List<SignFileApiVO> signFiles) {
        this.signFiles = signFiles;
    }

    public Boolean getUpdateControlAtSign() {
        return updateControlAtSign;
    }

    public void setUpdateControlAtSign(Boolean updateControlAtSign) {
        this.updateControlAtSign = updateControlAtSign;
    }

    public Boolean getToastRead() {
        return toastRead;
    }

    public void setToastRead(Boolean toastRead) {
        this.toastRead = toastRead;
    }
}
