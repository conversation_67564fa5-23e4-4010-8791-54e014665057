package com.api.cyitce.seal3.vo.req.yunxi;

import java.util.List;
/**
 * 智能印章设备信息实体 (列表中的核心对象)
 */
public class YxDevice {
    /** 设备标识 */
    private String uuid;

    /** 设备id */
    private Integer deviceId;
    /** 设备名称 */
    private String name;
    /** 用印次数 */
    private Integer count;
    /** 租户标识 */
    private String tenant;
    /** 组织id */
    private String deptId;
    /** 组织, 示例：xx公司/xx部/综合办 */
    private String deptName;
    /** 管章人id */
    private String keeperId;
    /** 审计人id */
    private String auditorId;
    /** 在线状态 */
    private Boolean online;
    /** 网络 */
    private String network;
    /** 休眠时间, 单位：分钟 */
    private Integer sleepTime;
    /** 指纹模式状态, true:开启 false:关闭 */
    private Boolean fingerprintMode;
    /** 蓝牙开关状态, true:开启 false:关闭 */
    private Boolean bluetooth;
    /** 远程锁定状态, true:锁定 false:解锁 */
    private Boolean remoteLock;
    /** 摄像头状态, true:开启 false:关闭 */
    private Boolean camera;
    /** 印章设备状态, true:启用 false:禁用 */
    private Boolean state;
    /** 设备地址 */
    private String location;
    /** 法人, 如：张三 */
    private String juridicalPerson;
    /** 印章设备类型, 如：公章 */
    private String type;
    /** 印章设备电量, 如：97 */
    private Integer batteryLevel;
    /** 印章注册时间 */
    private String registerDate;
    /** 指纹列表 */
    private List<YxDeviceFinger> fingers;
    /** 电子围栏列表 */
    private List<YxDeviceFence> fences;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getKeeperId() {
        return keeperId;
    }

    public void setKeeperId(String keeperId) {
        this.keeperId = keeperId;
    }

    public String getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(String auditorId) {
        this.auditorId = auditorId;
    }

    public Boolean getOnline() {
        return online;
    }

    public void setOnline(Boolean online) {
        this.online = online;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public Integer getSleepTime() {
        return sleepTime;
    }

    public void setSleepTime(Integer sleepTime) {
        this.sleepTime = sleepTime;
    }

    public Boolean getFingerprintMode() {
        return fingerprintMode;
    }

    public void setFingerprintMode(Boolean fingerprintMode) {
        this.fingerprintMode = fingerprintMode;
    }

    public Boolean getBluetooth() {
        return bluetooth;
    }

    public void setBluetooth(Boolean bluetooth) {
        this.bluetooth = bluetooth;
    }

    public Boolean getRemoteLock() {
        return remoteLock;
    }

    public void setRemoteLock(Boolean remoteLock) {
        this.remoteLock = remoteLock;
    }

    public Boolean getCamera() {
        return camera;
    }

    public void setCamera(Boolean camera) {
        this.camera = camera;
    }

    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getJuridicalPerson() {
        return juridicalPerson;
    }

    public void setJuridicalPerson(String juridicalPerson) {
        this.juridicalPerson = juridicalPerson;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getBatteryLevel() {
        return batteryLevel;
    }

    public void setBatteryLevel(Integer batteryLevel) {
        this.batteryLevel = batteryLevel;
    }

    public String getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public List<YxDeviceFinger> getFingers() {
        return fingers;
    }

    public void setFingers(List<YxDeviceFinger> fingers) {
        this.fingers = fingers;
    }

    public List<YxDeviceFence> getFences() {
        return fences;
    }

    public void setFences(List<YxDeviceFence> fences) {
        this.fences = fences;
    }


}