package com.api.cyitce.seal3.service.oa.impl;

import com.api.cyitce.seal3.cmd.thrid.*;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.service.oa.ThridMappingService;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.engine.core.impl.Service;
import com.weaver.general.BaseBean;

public class ThridMappingServiceImpl extends Service implements ThridMappingService {

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @Override
    public BaseResp<Void> insertThridDataRequestMapping(String thridId, String oaId, int type, String requestid) {
        writeLog("CreateContractAction------------> 三方数据表 插入合同, request123");
        InsertThridRequestDataCmd dataCmd = new InsertThridRequestDataCmd(type, oaId, thridId, requestid,new ThirdMappConfig());
        writeLog("CreateContractAction------------> 三方数据表 插入合同, request123 :" + dataCmd.toString());

        boolean boo = false;
        try {
            if (this.commandExecutor == null) {
                writeLog("commandExecutor:" + this.commandExecutor);
            }
            boo = this.commandExecutor.execute(dataCmd);
        } catch (Exception e) {

            throw new RuntimeException(e);
        }
        writeLog("boo:" + boo);
        // if (!boo) {
        //     return MsgResp.error("创建合同 三方数据插入失败");
        // }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<Void> insertThridDataMapping(String thridId, String oaId, int type) {
        boolean boo = this.commandExecutor.execute(new InsertThridDataCmd(type, oaId, thridId,new ThirdMappConfig()));
        writeLog("boo :" + boo);
        // if (!boo) {
        //     return MsgResp.error("三方系统数据映射插入失败");
        // }

        return MsgResp.ok();
    }

    @Override
    public BaseResp<String> getThridIdByOaId(String oaId, int type) {
        writeLog("aaaa" + this.commandExecutor);
        String thridId = this.commandExecutor.execute(new GetThridDataCmd(oaId, type,new ThirdMappConfig()));
        writeLog("getThridIdByOaId---------------------> 执行成功返回 thridId :" + thridId);

        if ("".equals(thridId) || thridId == null) {
            return MsgResp.error("未找到对应的三方系统数据映射");
        }
        return MsgResp.ok(thridId);
    }

    @Override
    public BaseResp<String> getThridIdBysealId(String sealid, int type) {
        String thridId = this.commandExecutor.execute(new GetRequestIdDataCmd(sealid, type,new ThirdMappConfig()));

        if ("".equals(thridId) || thridId == null) {
            return MsgResp.error("未找到对应的系统requestid数据映射");
        }

        return MsgResp.ok(thridId);
    }


    @Override
    public BaseResp<String> getThridoaIdBySealId(String sealid, int type) {
        String thridId = this.commandExecutor.execute(new GetDocIdDataCmd(sealid, type,new ThirdMappConfig()));

        if ("".equals(thridId) || thridId == null) {
            return MsgResp.error("未找到对应的系统oa数据映射");
        }

        return MsgResp.ok(thridId);
    }


    @Override
    public BaseResp<String> deleteThridBySealId(String oaid, int type) {
        String thridId = this.commandExecutor.execute(new DeleteIdDataCmd(oaid, type,new ThirdMappConfig()));

        if ("".equals(thridId) || thridId == null) {
            return MsgResp.error("删除文档对应合同");
        }
        return MsgResp.ok(thridId);
    }
}

