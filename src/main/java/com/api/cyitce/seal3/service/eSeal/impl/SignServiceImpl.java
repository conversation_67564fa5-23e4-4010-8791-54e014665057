package com.api.cyitce.seal3.service.eSeal.impl;

import cn.hutool.json.JSONObject;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.enums.account.UserTypeEnum;
import com.api.cyitce.seal3.enums.signet.ControlTypeEnum;
import com.api.cyitce.seal3.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal3.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal3.service.eSeal.SignService;
import com.api.cyitce.seal3.service.oa.impl.UserInfoServiceImpl;
import com.api.cyitce.seal3.util.SmsTest;
import com.api.cyitce.seal3.vo.req.hrm.QueryPersonApiReq;
import com.api.cyitce.seal3.vo.req.pagesign.PageSignReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.api.cyitce.seal3.vo.resp.org.PersonVoResp;
import com.api.cyitce.seal3.vo.resp.pagesign.PageSignResp;
import weaver.general.BaseBean;

import java.util.*;

import static com.api.cyitce.seal3.service.eSeal.impl.CreateContractServiceImp.CallBack;

public class SignServiceImpl extends BaseLog implements SignService {
    ContractIntegration ci = new ContractIntegration();
    UserInfoServiceImpl uisi = new UserInfoServiceImpl();
    CreatUserIntegration cui = new CreatUserIntegration();

    public static String ENTERPRISE_ID = "M011JRGICPKTWF7";

    @Override
    public BaseResp<PageSignResp> personPageSign(PageSignReq req) {
        req.getSigner().setSignerType(UserTypeEnum.PERSON.getIndex());
        req.getSigner().getControlsType().add(ControlTypeEnum.AUTOGRAPH.getCode());
        req.getSigner().getControlsType().add(ControlTypeEnum.DATE.getCode());
        req.getSigner().getControlsType().add(ControlTypeEnum.TEXT.getCode());
        req.setAsyncUrl(CallBack);
        info("personPageSign ------------------->发送个人页面签署  ：" + req.toString());
        BaseResp<PageSignResp> resp = ci.startSignByFile(req);
        // 暂时不需要发送短信
        // if (resp.isStatus()) {
        //     info("personPageSign ------------------->发送个人页面签署 拿到的结果 url  ：" + resp.getData().getSignUrl());
        //     info("personPageSign ------------------->req.getSigner().getUserId()  ：" + req.getSigner().getUserId());
        // String content = "您有一份待签署的合同，请前往" + resp.getData().getSignUrl() + "进行查看并签署。";
        // BaseResp<JSONObject> resp1 = sendSms(req.getSigner().getUserId(), resp.getData().getSignUrl(), content);
        //     if (!resp1.isStatus()) {
        //         return MsgResp.error(resp1.getMessage());
        //     }
        // }

        return resp;
    }

    @Override
    public BaseResp<PageSignResp> orgPageSign(PageSignReq req) {
        req.getSigner().setSignerType(UserTypeEnum.ENTERPRISE.getIndex());
        req.getSigner().getControlsType().add(ControlTypeEnum.SIGNET.getCode());
        req.getSigner().getControlsType().add(ControlTypeEnum.TEXT.getCode());
        req.getSigner().getControlsType().add(ControlTypeEnum.LEGAL_PERSON.getCode());
        // req.getSigner().getControlsType().add(ControlTypeEnum.AGENT.getCode());
        // req.getSigner().getControlsType().add(ControlTypeEnum.AUTOGRAPH.getCode());
        req.getSigner().getControlsType().add(ControlTypeEnum.DATE.getCode());
        info("发送企业页面签署---------------> 请求值: " + req.toString());
        BaseResp<PageSignResp> resp = ci.startSignByFile(req);
        // 暂时不需要电子签字
        // if (resp.isStatus()) {
        //     info("发送企业页面签署---------------> 页面发送成功 返回值:" + resp.getData().toString());
        //     BaseResp<JSONObject> resp1 = sendSms(req.getSigner().getUserId(), resp.getData().getSignUrl());
        //     if (!resp1.isStatus()) {
        //         info("发送企业页面签署---------------> 发送短信失败");
        //         return MsgResp.error(resp1.getMessage());
        //     }
        // }
        return resp;
    }

    public BaseResp<JSONObject> sendSms(String userId, String signUrl, String content) {
        QueryPersonApiReq qpar = new QueryPersonApiReq();
        qpar.setUserId(userId);
        BaseResp<PersonVoResp> resp1 = cui.queryPerson(qpar);
        info(" sendSms  ---------------------------> 拿到天威云用户详情 : " + resp1.getData().toString());

        if (!resp1.isStatus()) {
            return MsgResp.error(resp1.getMessage());
        }

        String phone = resp1.getData().getPhone();

        info(" sendSms  ---------------------------> 发送短信内容 : " + content + "  手机号：" + phone);
        // BaseResp<JSONObject> resp2 = SmsUtil.sendSms(phone, content);
        new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest12321312 ");
        Map<String, Object> maprequest = new HashMap<String, Object>();
        maprequest.put("phone", phone);
        maprequest.put("content", content);
        // SmsUtil su = new SmsUtil();

        new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest " + maprequest.toString());

        BaseResp<JSONObject> resp2 = new SmsTest().sendSmsTest(maprequest);
        if (!resp2.isStatus()) {
            info("短信发送成功！");
        }

        return resp2;
    }
}
