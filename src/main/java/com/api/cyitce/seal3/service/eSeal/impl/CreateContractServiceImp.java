package com.api.cyitce.seal3.service.eSeal.impl;

import com.api.cyitce.seal3.enums.contract.LastFileTypeEnum;
import com.api.cyitce.seal3.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal3.service.eSeal.CreateContractService;
import com.api.cyitce.seal3.vo.req.contract.ContractDetailsApiReq;
import com.api.cyitce.seal3.vo.req.contract.CreateByFileApiReq;
import com.api.cyitce.seal3.vo.req.contract.addSignerByFile.FileApiVO;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.contract.ContractApiVO;
import com.weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.List;

public class CreateContractServiceImp implements CreateContractService {
    ContractIntegration ci = new ContractIntegration();
    // public static final String CallBack = "http://218.245.99.207:7001/api/Seal/web3/update/file";
    // public static final String CallBack = "http://192.168.1.178:7001/api/Seal/web/update/file";
    public static final String CallBack = "https://oa.cyitce.com/api/Seal/web/update/file";


    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    @Override
    public BaseResp<ContractApiVO> createContract(String contractCode, String contractName, String creator, byte[] bytes, String enterpriseId, String file1) {
        CreateByFileApiReq req = new CreateByFileApiReq();
        // req.setCode(contractCode);
        req.setName(contractName);
        req.setCreator(creator);
        req.setEnterpriseId(enterpriseId);

        req.setLastFileType(LastFileTypeEnum.PDF.getIndex());
        // 这里设置0 标识不确定签署人数量，后续再确定
        req.setSignCount(0);

        // 回调地址
        req.setAsyncUrl(CallBack);
        req.setSignValidDays(90);
        req.setSignSortable(false);
        req.setSend(true);
        req.setBizCode("common");
        req.setCheckSysBizType(true);
        req.setDocName(contractName);
        List<FileApiVO> v = new ArrayList<>();
        FileApiVO fileApiVO = new FileApiVO();
        fileApiVO.setDocName(contractName);
        // fileApiVO.setBase64(Base64.getEncoder().encodeToString(bytes));
        fileApiVO.setBase64(file1);
        v.add(fileApiVO);
        req.setFiles(v);
        writeLog("创建合同--------->  成功: ");

        // req.setBase64("base64:"+Base64.getEncoder().encodeToString(bytes));
        // req.getFiles().add(fileApiVO);
        return ci.createByFile(req);
    }

    @Override
    public BaseResp<ContractApiVO> createContractCon(String contractCode, String contractName, String creator, byte[] bytes, String enterpriseId, String file1,String callback) {
        CreateByFileApiReq req = new CreateByFileApiReq();
        // req.setCode(contractCode);
        req.setName(contractName);
        req.setCreator(creator);
        req.setEnterpriseId(enterpriseId);

        req.setLastFileType(LastFileTypeEnum.PDF.getIndex());
        // 这里设置0 标识不确定签署人数量，后续再确定
        req.setSignCount(0);

        // 回调地址
        req.setAsyncUrl(callback);
        req.setSignValidDays(90);
        req.setSignSortable(false);
        req.setSend(true);
        req.setBizCode("common");
        req.setCheckSysBizType(true);
        req.setDocName(contractName);
        List<FileApiVO> v = new ArrayList<>();
        FileApiVO fileApiVO = new FileApiVO();
        fileApiVO.setDocName(contractName);
        // fileApiVO.setBase64(Base64.getEncoder().encodeToString(bytes));
        fileApiVO.setBase64(file1);
        v.add(fileApiVO);
        req.setFiles(v);
        writeLog("创建合同--------->  成功: ");

        // req.setBase64("base64:"+Base64.getEncoder().encodeToString(bytes));
        // req.getFiles().add(fileApiVO);
        return ci.createByFile(req);
    }

    @Override
    public BaseResp<ContractApiVO> search(Long contractId, boolean responseContractFile) {
        new BaseBean().writeLog("searchInfo ---------------------------> 进入search方法 :");
        ContractDetailsApiReq req = new ContractDetailsApiReq();
        req.setContractId(contractId);
        new BaseBean().writeLog("searchInfo ---------------------------> 查询合同详情 调用接口 请求参数： :" + req.toString());
        return ci.search(req, responseContractFile);
    }
}
