package com.api.cyitce.seal3.service.eSeal;

import com.api.cyitce.seal3.vo.req.pagesign.PageSignReq;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.pagesign.PageSignResp;

public interface SignService {
    /**
     * 个人页面签署
     **/
    BaseResp<PageSignResp> personPageSign(PageSignReq req);

    /**
     * 企业页面签署
     **/
    BaseResp<PageSignResp> orgPageSign(PageSignReq req);
}
