package com.api.cyitce.seal3.service.oa;

import com.api.cyitce.seal3.vo.resp.BaseResp;

public interface UserInfoService {
    /**
     * 通过工号获取用户手机号
     **/
    BaseResp<String> getUserPhoneByWorkCode(String workCode);
    
    /**
     * 查看三方系统数据是否存在
     **/
    BaseResp<String> isHave(String OaId,int type);

    /**
     * 插入三方系统数据
     **/
    BaseResp<Boolean> insert(String OaId,String thirdId,int type);
}
