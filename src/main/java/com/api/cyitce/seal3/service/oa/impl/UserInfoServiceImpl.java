package com.api.cyitce.seal3.service.oa.impl;

import com.api.cyitce.seal3.cmd.hrm.GetOAUserMobileCmd;
import com.api.cyitce.seal3.cmd.thrid.GetThridDataCmd;
import com.api.cyitce.seal3.cmd.thrid.InsertThridDataCmd;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.service.oa.UserInfoService;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.engine.core.impl.Service;

/**
 * @ClassName: UserInfoServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-22  14:12
 * @Version: 1.0
 */
public class UserInfoServiceImpl extends Service implements UserInfoService {
    @Override
    public BaseResp<String> getUserPhoneByWorkCode(String workCode) {
        String mobile = this.commandExecutor.execute(new GetOAUserMobileCmd(workCode));

        if (null == mobile) {
            return MsgResp.error("未查询到该用户手机号或用户工号错误！");
        }

        return MsgResp.ok(mobile);
    }

    @Override
    public BaseResp<String> isHave(String OaId, int type) {
        String third = this.commandExecutor.execute(new GetThridDataCmd(OaId, type, new ThirdMappConfig()));

        if ("".equals(third) || third == null) {
            return MsgResp.error("三方数据不存在！");
        }

        return MsgResp.ok(third);
    }

    @Override
    public BaseResp<Boolean> insert(String OaId, String thirdId, int type) {
        boolean boo = this.commandExecutor.execute(new InsertThridDataCmd(type, OaId, thirdId,new ThirdMappConfig()));

        if (boo) {
            return MsgResp.ok();
        }

        return MsgResp.error();
    }
}