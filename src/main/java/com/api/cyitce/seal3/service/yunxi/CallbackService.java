package com.api.cyitce.seal3.service.yunxi;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.config.yunxi.ApiRequest;
import com.api.cyitce.seal3.config.yunxi.ApiResponse;
import com.api.cyitce.seal3.enums.yunxi.YxygApiKey;
import com.api.cyitce.seal3.util.AesUtils;
import com.api.cyitce.seal3.util.sql.SqlUtils;
import com.api.cyitce.seal3.vo.req.yunxi.YxSealUsageRecord;
import org.springframework.util.CollectionUtils;

import java.util.Map;

import static com.api.cyitce.seal3.util.FileUtils.getFormName;
import static com.api.cyitce.seal3.util.sql.SqlUtils.updateyxInfo;

/**
 * 模拟处理印管平台回调请求的服务
 */
public class CallbackService extends BaseLog {
    private static final String DELIMITER = "|||";
    private final String apiSecret; // 用于解密的密钥

    public CallbackService(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    /**
     * 模拟的Web Endpoint方法，接收回调请求的完整JSON体。
     *
     * @param requestBody 印管平台POST过来的原始JSON字符串
     * @return 响应给印管平台的JSON字符串
     */
    public String handleCallback(String requestBody) {
        info("接收到回调请求: " + requestBody);

        try {
            // 1. 解析外层信封
            ApiRequest callbackRequest = JSONUtil.toBean(requestBody, ApiRequest.class);

            // 2. 验证apiKey是否是我们期望的
            if (!YxygApiKey.GET_SEAL_USAGE_RECORDS.getKey().equals(callbackRequest.getApiKey())) {
                return buildResponse(400, "错误的apiKey: " + callbackRequest.getApiKey());
            }

            String paramsStr = callbackRequest.getParams();

            // 3. (重要) 判断 params 是否加密，并进行解密
            // 简单的判断：如果params不是以'{'开头，就认为是加密的
            if (!paramsStr.trim().startsWith("{")) {
                info("检测到加密的params，正在解密...");
                try {
                    paramsStr = AesUtils.decrypt(paramsStr, this.apiSecret);
                    info("解密后的params: " + paramsStr);
                } catch (Exception e) {
                    return buildResponse(400, "解密失败: " + e.getMessage());
                }
            }

            // 4. 解析 params 字符串到实体类
            YxSealUsageRecord record = JSONUtil.toBean(paramsStr, YxSealUsageRecord.class);

            // 5. 在这里处理业务逻辑
            processSealUsageRecord(record);

            // 6. 如果一切顺利，返回成功的响应
            return buildResponse(0, "success");

        } catch (Exception e) {
            // 捕获所有可能的异常，确保总能返回一个标准的响应
            return buildResponse(400, "处理请求时发生内部错误: " + e.getMessage());
        }
    }

    /**
     * 具体的业务处理方法
     *
     * @param record 解析后的用印记录对象
     */
    private void processSealUsageRecord(YxSealUsageRecord record) {
        info("--- 开始处理用印记录 ---");
        String applicationId = record.getApplicationId();
        Map<String, String> map = SqlUtils.getDocIdInfo(9, applicationId, new ThirdMappConfig());
        if (CollectionUtils.isEmpty(map)) {
            info("未找到对应的申请单信息");
            throw new RuntimeException("系统逻辑错误");
        }
        String tableName = getFormName(map.get("requestid1"));
        int i = updateyxInfo(Integer.parseInt(map.get("mxbid")), tableName, Integer.parseInt(map.get("yymxb")), record.getUseFileUrl() + DELIMITER + record.getAuditFileUrls(), JSONUtil.toJsonStr(record));
        if (0 != i) {
            info("修改申请单信息失败");
            throw new RuntimeException("系统逻辑错误");
        }
        info("--- 用印记录处理完毕 ---");
    }

    /**
     * 构建返回给印管平台的响应JSON
     *
     * @param code    状态码 (0为成功)
     * @param message 响应信息
     * @return JSON字符串
     */
    private String buildResponse(int code, String message) {
        ApiResponse response = new ApiResponse();
        response.setCode(code);
        response.setMessage(message);
        // bizId可以不设置，或返回原始请求的bizId
        String responseJson = JSONUtil.toJsonStr(response);
        info("响应给回调方: " + responseJson);
        return responseJson;
    }
}