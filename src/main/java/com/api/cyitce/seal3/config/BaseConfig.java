package com.api.cyitce.seal3.config;


import weaver.general.GCONST;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * @ClassName: BaseConfig
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-21  16:49
 * @Version: 1.0
 */
public class BaseConfig extends BaseLog{
    // public static final String THIRD_PART_PROP = "ecology_seal_prop.properties";
    // public static final String THIRD_PART_PROP = "ecology_seal_prop_N.properties";
    public static final String THIRD_PART_PROP = "ecology_seal.properties";
    // public final static String CLASS_PATH = BaseConfig.class.getClassLoader().getResource("").getPath();
    //获取项目所在目录1
   protected final static String CLASS_PATH = GCONST.getRootPath() + "WEB-INF/prop/";

    private Properties prop(){
        Properties prop = new Properties();
        try {
            info("配置文件路径:{?}",CLASS_PATH +THIRD_PART_PROP);
            prop.load(new FileInputStream(CLASS_PATH +THIRD_PART_PROP));
            return prop;
        } catch (IOException e) {
            error("配置文件不存在",e);
            return null;
        }
    }

    public String props(String param){
        try {
            return prop().getProperty(param);
        }catch (Exception e){
            error("属性【{?}】不存在",param,e);
            return null;
        }
    }
}