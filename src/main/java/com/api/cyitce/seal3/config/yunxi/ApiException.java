package com.api.cyitce.seal3.config.yunxi;

public class ApiException extends Exception {
    private final int code;
    private final String bizId;

    public ApiException(int code, String message, String bizId) {
        super(message);
        this.code = code;
        this.bizId = bizId;
    }

    public int getCode() { return code; }
    public String getBizId() { return bizId; }

    @Override
    public String toString() {
        return String.format("ApiException{code=%d, message='%s', bizId='%s'}", code, getMessage(), bizId);
    }
}