package com.api.cyitce.seal3.config.eSeal;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseAuth;
import com.api.cyitce.seal3.util.HmacSh1Util;
import com.api.cyitce.seal3.vo.req.contract.ContractDeleteApiReq;
import okhttp3.*;


import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: StampBaseAuth
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  10:57
 * @Version: 1.0
 */
public class ESealBaseAuth extends BaseAuth {
    private ESealConfig config = new ESealConfig();
    private String CONTENT_SIGNATURE = "Content-Signature";
    private String APP_ID_KEY = "appId";
    private String SERVICE_CODE_KEY = "serviceCode";
    private String COMPANY_UUID = "companyUUID";

    public ESealConfig getConfig() {
        return config;
    }

    @Override
    public String getBaseUrl() {
        return config.getUrl();
    }

    @Override
    public Map<String, String> getRequestHeaders(String requestJsonStr) {
        // 生成签名，签名秘钥为：secretKey + serviceCode
        String key = config.getSecretKey() + config.getServiceCode();
        String signature = HmacSh1Util.getSignature(requestJsonStr, key);
        // Content-Signature 为固定值 HMAC-SHA1 + 英文空格 + signature
        String ContentSignature = "HMAC-SHA1 " + signature;

        Map<String, String> headers = new HashMap<>();
        headers.put(CONTENT_SIGNATURE, ContentSignature);
        headers.put(APP_ID_KEY, config.getAppId());
        headers.put(SERVICE_CODE_KEY, config.getServiceCode());
        headers.put(COMPANY_UUID, config.getCompanyUUID());
        headers.put("Accept", "application/json;charset=utf-8");

        return headers;
    }


    public static void main(String[] args) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS).build();
        String CONTENT_SIGNATURE = "Content-Signature";
        String APP_ID_KEY = "appId";
        String SERVICE_CODE_KEY = "serviceCode";
        String COMPANY_UUID = "companyUUID";
        ContractDeleteApiReq contractDeleteApiReq = new ContractDeleteApiReq();
        contractDeleteApiReq.setContractId(208164333480313234L);

        String jsonStr = JSONUtil.toJsonStr(contractDeleteApiReq);
        System.out.println("jsonStr = " + jsonStr);

        MediaType mediaType = MediaType.parse("application/json;charset=utf-8");
        // 封装 http 请求
        RequestBody body = RequestBody.create(mediaType, jsonStr);
        Request.Builder requestBuilder = new Request.Builder(); // /apigate/contractapi
        HttpUrl parse = HttpUrl.parse("http://139.159.242.69:8070/apigate/contractapi/contract/deleteContract");
        requestBuilder.url(parse);
        // requestBuilder.url("http://192.168.1.146:8070/apigate/contractapi/contract/deleteContract");
        // 获取请求头部参数
        Map<String, String> headers = new HashMap<>();
        String ContentSignature = HmacSh1Util.getSignature(jsonStr, "c471e66f68a642e5984e0d0c2d1c7e1d" + "contract003");
        System.out.println("ContentSignature：  " + ContentSignature);
        headers.put(CONTENT_SIGNATURE, "HMAC-SHA1 " + ContentSignature);
        headers.put(APP_ID_KEY, "a5cc224cbe1f4b");
        headers.put(SERVICE_CODE_KEY, "contract003");
        headers.put(COMPANY_UUID, "M011GYSR00YS8WP");
        headers.put("Accept", "application/json;charset=utf-8");

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());

        }

        // 发送 http 请求
        Request request = requestBuilder.post(body).build();

        Response response = client.newCall(request).execute();

        if (response.isSuccessful()) {
            JSONObject result = JSONUtil.parseObj(response.body().string());
            System.out.println(JSONUtil.toJsonStr(result));
        }


        // HMAC-SHA1 fTOioW+bQNt+jm8Qk08a7pNixUY=
    }
}


