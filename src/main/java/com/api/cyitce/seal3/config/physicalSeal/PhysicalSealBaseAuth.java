package com.api.cyitce.seal3.config.physicalSeal;

import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseAuth;
import com.api.cyitce.seal3.vo.req.LoginRequest;

import java.io.DataOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: StampBaseAuth
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  10:57
 * @Version: 1.0
 */
public class PhysicalSealBaseAuth extends BaseAuth {
    private PhysicalSealConfig config = new PhysicalSealConfig();

    public PhysicalSealConfig getConfig() {
        return config;
    }

    @Override
    public String getBaseUrl() {
        return config.getUrl();
    }

    @Override
    public Map<String, String> getRequestHeaders(String requestJsonStr) {

        Map<String, String> headers = new HashMap<>();
        // todo  物理印章接口权限  加白名单
        headers.put("Authorization", getAuthorizationHeader("yzqx001",
                "LJHljh12311"));
        return headers;
    }

    private static final String LOGIN_URL = "/login";

    public String getAuthorizationHeader(String username, String password) {
        HttpURLConnection connection = null;
        try {
            info("PhysicalSealBaseAuth-------------------->  执行登录请求");
            // 创建 URL 对象
            // URL url = new URL(new PhysicalSealConfig().getUrl() + LOGIN_URL);
            URL url = new URL("http://139.159.242.69:9080/SealCustomerApi" + LOGIN_URL);
            // 打开连接
            connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为 POST
            connection.setRequestMethod("POST");
            // 允许输出
            connection.setDoOutput(true);

            // 创建请求的 JSON 对象
            LoginRequest request = new LoginRequest(username, password);
            String jsonStr = JSONUtil.toJsonStr(request);

            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Content-Length", String.valueOf(jsonStr.getBytes(StandardCharsets.UTF_8).length));

            // 获取输出流并写入 JSON 请求体
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.write(jsonStr.getBytes(StandardCharsets.UTF_8));
            }
            info("PhysicalSealBaseAuth-------------------->  connection :" + JSONUtil.toJsonStr(connection));
            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                info("PhysicalSealBaseAuth-------------------->  拿到token返回值 :" + connection.getHeaderField("authorization"));
                // 获取响应头部的 Authorization 值
                return connection.getHeaderField("authorization");
            } else {
                System.err.println("请求失败，响应码: " + responseCode);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        String username = "hkadmin";
        String password = "Hk123456";
        String authorization = new PhysicalSealBaseAuth().getAuthorizationHeader("yzqx001",
                "LJHljh12311");
        if (authorization != null) {
            System.out.println("Authorization: " + authorization);
        } else {
            System.out.println("未能获取到 Authorization 头部信息。");
        }
    }


}