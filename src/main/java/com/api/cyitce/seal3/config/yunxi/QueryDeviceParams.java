package com.api.cyitce.seal3.config.yunxi;

import java.util.List;

// 查询设备接口的业务参数 (无变化)
class QueryDeviceParams {
    private boolean page = true;
    public boolean isPage() { return page; }
    public void setPage(boolean page) { this.page = page; }
}

// 单个设备信息 (无变化)
class Device {
    public int deviceId;
    public String uuid;
    public String simNum;
    public String name;
    public int count;
    public int orgId;
    public String location;
    public boolean online;
}

// 查询设备接口成功响应时 data 字段的结构
class QueryDeviceResponse {
    public int pageNum;
    public int pageSize;
    public int pages;
    public long total;
    // **重要**: 字段名从 deviceList 改为 list，以直接匹配JSON中的 "list" 字段。
    // Hutool的JSON工具默认按字段名匹配，这样最简单可靠。
    public List<Device> list;
}