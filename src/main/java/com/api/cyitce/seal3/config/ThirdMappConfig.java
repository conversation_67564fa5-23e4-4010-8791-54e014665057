package com.api.cyitce.seal3.config;

/**
 * @ClassName: StampConfig
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  11:21
 * @Version: 1.0
 */
public class ThirdMappConfig extends BaseConfig {
    String thirdTable = props("third.table"); // 三方映射表
    String thirdyzTable = props("third.yz.table"); // 实体章库
    String supportCompany = props("support.company"); // 实体章库

    String baseUrl = props("yunxi.baseurl"); //
    String appKey = props("yunxi.appKey"); //
    String appSecret = props("yunxi.appSecret"); //
    String tenant = props("yunxi.tenant"); //
    String topId = props("yunxi.topId"); //

    @Override
    public String toString() {
        return "ThirdMappConfig{" +
                "thirdTable='" + thirdTable + '\'' +
                ", thirdyzTable='" + thirdyzTable + '\'' +
                ", supportCompany='" + supportCompany + '\'' +
                ", baseUrl='" + baseUrl + '\'' +
                ", appKey='" + appKey + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", tenant='" + tenant + '\'' +
                ", topId='" + topId + '\'' +
                ", ipUrl='" + ipUrl + '\'' +
                ", checkMD5='" + checkMD5 + '\'' +
                '}';
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getTopId() {
        return topId;
    }

    public void setTopId(String topId) {
        this.topId = topId;
    }

    public String getIpUrl() {
        return ipUrl;
    }

    public void setIpUrl(String ipUrl) {
        this.ipUrl = ipUrl;
    }

    String ipUrl = props("yunxi.ipUrl"); //

    public String getCheckMD5() {
        return checkMD5;
    }

    public void setCheckMD5(String checkMD5) {
        this.checkMD5 = checkMD5;
    }

    String checkMD5 = props("check.MD5"); // 实体章库

    public void setThirdTable(String thirdTable) {
        this.thirdTable = thirdTable;
    }

    public String getThirdyzTable() {
        return thirdyzTable;
    }

    public void setThirdyzTable(String thirdyzTable) {
        this.thirdyzTable = thirdyzTable;
    }


    public String getThirdTable() {
        return thirdTable;
    }

    public String getSupportCompany() {
        return supportCompany;
    }

    public void setSupportCompany(String supportCompany) {
        this.supportCompany = supportCompany;
    }
}