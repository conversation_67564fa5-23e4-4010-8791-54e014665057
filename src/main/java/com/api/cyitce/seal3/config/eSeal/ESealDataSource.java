package com.api.cyitce.seal3.config.eSeal;

import weaver.general.StaticObj;
import weaver.interfaces.datasource.DataSource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @ClassName: DataSource
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-22  15:02
 * @Version: 1.0
 */
public class ESealDataSource {
    private Connection connection;
    private PreparedStatement ps;
    ResultSet resultSet;

    public ESealDataSource() {
        dataSource();
    }

    public void dataSource() {
        DataSource ds = (DataSource) StaticObj.getServiceByFullname(("datasource.eSeal"), DataSource.class);
        Connection conn = ds.getConnection();

        connection = conn;
    }

    public void executeQuery(String sql) throws SQLException {
        //预编译SQL
        ps = connection.prepareStatement(sql);
        resultSet = ps.executeQuery();
    }

    public void executeQuery(String sql,String[] param) throws SQLException {
        sql = parameterReplace(sql,param);

        //预编译SQL
        ps = connection.prepareStatement(sql);
        resultSet = ps.executeQuery();
    }

    public int executeUpdate(String sql) throws SQLException {
        //预编译SQL
        ps = connection.prepareStatement(sql);
        return ps.executeUpdate();
    }

    public boolean execute(String sql) throws SQLException {
        boolean result = false;
        //预编译SQL
        ps = connection.prepareStatement(sql);
        result = ps.execute();
        resultSet = ps.getResultSet();
        return result;
    }

    public String getString(int i) throws SQLException {
        return resultSet.getString(i);
    }

    public String getString(String str) throws SQLException {
        return resultSet.getString(str);
    }

    public String getInt(int i) throws SQLException {
        return resultSet.getString(i);
    }

    public String getInt(String str) throws SQLException {
        return resultSet.getString(str);
    }

    public Boolean next() throws SQLException {
        return resultSet.next();
    }

    public void close() throws SQLException {
        if(connection!=null){
            connection.close();
        }
        if(resultSet!=null){
            resultSet.close();
        }
        if(ps!=null){
            ps.close();
        }
    }

    public String parameterReplace(String sql,String[] params) {
        StringBuilder result = new StringBuilder(sql);
        int index = 0;
        int start;
        while ((start=result.indexOf("?"))!= -1 && index < params.length) {
            // 替换占位符为 Parameters 中的元素
            if (index < params.length) {
                result.replace(start, start + 1, "'"+params[index]+"'");
                index++;
            } else {
                result.replace(start, start + 1, "");
            }
        }

        return result.toString();
    }
}