package com.api.cyitce.seal3.config.realauth;

import cn.hutool.core.util.ObjectUtil;
import com.api.cyitce.seal3.config.BaseAuth;

import com.api.cyitce.seal3.util.HmacSh1Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 天威云基础认证类
 * @Author:lijianpan
 */
public class RealAuthBaseAuth extends BaseAuth {
    private RealAuthConfig config = new RealAuthConfig();
    private String AUTHORIZATION = "Authorization";
    private String APP_ID_KEY = "appId";
    private String SERVICE_CODE_KEY = "serviceCode";

    private String serviceCode;

    public RealAuthBaseAuth() {
        if (ObjectUtil.isEmpty(config)) {
            config = new RealAuthConfig();
        }
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public RealAuthConfig getConfig() {
        return config;
    }

    @Override
    public String getBaseUrl() {
        return config.getUrl();
    }

    @Override
    public Map<String, String> getRequestHeaders(String requestJsonStr) {
        // 生成签名，签名秘钥为：secretKey + serviceCode
        String key = config.getSecretKey() + getServiceCode();
        String signature = HmacSh1Util.getSignature(requestJsonStr, key);
        // AUTHORIZATION 为固定值 COMMON + 英文空格 + signature
        String authorization = "COMMON " + signature;

        Map<String, String> headers = new HashMap<>();
        headers.put(AUTHORIZATION, authorization);
        headers.put(APP_ID_KEY, config.getAppId());
        headers.put(SERVICE_CODE_KEY, getServiceCode());
        headers.put("orderNo", "ZX120230328165537000189");

        return headers;
    }

    @Override
    public String toString() {
        return "RealAuthBaseAuth{" +
                "config=" + config.toString() +
                ", AUTHORIZATION='" + AUTHORIZATION + '\'' +
                ", APP_ID_KEY='" + APP_ID_KEY + '\'' +
                ", SERVICE_CODE_KEY='" + SERVICE_CODE_KEY + '\'' +
                ", serviceCode='" + serviceCode + '\'' +
                '}';
    }
}
