package com.api.cyitce.seal3.cmd.thrid;

import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

public class InsertThridSealDataCmd extends AbstractCommonCommand<Boolean> {
    private int type;
    private String oaId;
    private String thridId;
    private ThirdMappConfig config;

    public InsertThridSealDataCmd(int type, String oaId, String thridId, ThirdMappConfig config) {
        this.type = type;
        this.oaId = oaId;
        this.thridId = thridId;
        this.config = config;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        rs.execute("insert into " + config.getThirdyzTable() + "(type,yzmc,yzwbbh) values(" + type + ",'" + oaId + "','" + thridId + "')");

        return rs.next();
    }
}
