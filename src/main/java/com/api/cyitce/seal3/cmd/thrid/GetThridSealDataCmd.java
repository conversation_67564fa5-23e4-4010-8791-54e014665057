package com.api.cyitce.seal3.cmd.thrid;

import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

public class GetThridSealDataCmd extends AbstractCommonCommand<String> {
    private String oaId;
    private int type;
    private ThirdMappConfig config;

    public GetThridSealDataCmd(String oaId, int type, ThirdMappConfig config) {
        this.oaId = oaId;
        this.type = type;
        this.config = config;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        String sql = "select yzwbbh from " + config.getThirdyzTable() + "  where id=" + type;
        rs.executeQuery(sql);
        new BaseBean().writeLog("执行sql:" + sql);
        rs.next();
        String string = rs.getString(0);
        new BaseBean().writeLog("执行sql 结果:" + string);
        return string;
    }
}
