package com.api.cyitce.seal3.cmd.thrid;

import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

public class DeleteIdDataCmd extends AbstractCommonCommand<String> {
    private String oaId;
    private int type;
    private ThirdMappConfig config;

    public DeleteIdDataCmd(String oaId, int type, ThirdMappConfig config) {
        this.oaId = oaId;
        this.type = type;
        this.config = config;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("delete  from  ?  where type=? and oaid=?", new Object[]{config.getThirdTable(), type, oaId});

        if (rs.next()) {
            return Util.null2String(rs.getString(1));
        }

        return "";
    }
}
