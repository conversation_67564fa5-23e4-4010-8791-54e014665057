package com.api.cyitce.seal3.cmd.thrid;

import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;

public class InsertThridRequestDataCmd extends AbstractCommonCommand<Boolean> {
    private int type;
    private String oaId;
    private String thridId;
    private String requestId;
    private ThirdMappConfig config;

    private void writeLog(String var) {
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(), var);
    }

    public InsertThridRequestDataCmd(int type, String oaId, String thridId, String requestId, ThirdMappConfig config) {
        this.type = type;
        this.oaId = oaId;
        this.thridId = thridId;
        this.requestId = requestId;
        this.config = config;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        writeLog("CreateContractAction------------> 三方数据表 插入合同, request123123213");
        RecordSet rs = new RecordSet();
        String sql = "insert into " + config.getThirdTable() + "(type,oaid,sealid,requestid1) values(" + type + ",'" + oaId + "','" + thridId + "','" + requestId + "')";
        writeLog("插入requestid  sql:" + sql);
        rs.execute(sql);

        return rs.next();
    }

    @Override
    public String toString() {
        return "InsertThridRequestDataCmd{" +
                "type=" + type +
                ", oaId='" + oaId + '\'' +
                ", thridId='" + thridId + '\'' +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
