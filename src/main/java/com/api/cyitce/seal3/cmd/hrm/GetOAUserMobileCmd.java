package com.api.cyitce.seal3.cmd.hrm;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

/**
 * @ClassName: GetOAUserInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-22  13:51
 * @Version: 1.0
 */
public class GetOAUserMobileCmd extends AbstractCommonCommand<String> {
    private String workcode;

    public GetOAUserMobileCmd(String workcode) {
        this.workcode = workcode;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select mobile from hrmresource where workcode=?",new Object[]{workcode});

        if(rs.next()){
            return rs.getString(1);
        }

        return null;
    }
}