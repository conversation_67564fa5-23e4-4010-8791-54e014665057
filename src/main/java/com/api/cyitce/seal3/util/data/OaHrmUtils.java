package com.api.cyitce.seal3.util.data;

import com.api.cyitce.seal3.vo.req.hrm.HrmResourceApiReq;
import com.api.cyitce.seal3.vo.req.hrm.RealPersonApiReq;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: OaHrmUtils
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-21  10:28
 * @Version: 1.0
 */
public class OaHrmUtils {
    /**
     * 获取人员工号
     **/
    public static String getworkCode(int userId) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select workCode from hrmresource where id=?", userId);

        if (rs.next()) {
            return rs.getString(1);
        }

        return "";
    }

    /**
     * 获取人员工号
     **/
    public static Map<String, String> getHrInfo(int userId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        rs.executeQuery("select * from cus_fielddata where   scopeid=1 and id=?", userId);
        if (rs.next()) {
            map.put("phone", rs.getString("field49"));
            return map;
        } else {
            return map;
        }
    }

    /**
     * 获取人员认证信息
     **/
    public static RealPersonApiReq getRealInfo(int userId) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select lastName,mobile,certificatenum from hrmresource where id=?", userId);

        RealPersonApiReq realPersonApiReq = new RealPersonApiReq();
        if (rs.next()) {
            realPersonApiReq.setName(extractChinese(rs.getString(1)));
            realPersonApiReq.setMobile(rs.getString(2));
            realPersonApiReq.setIdCard(rs.getString(3));
        }

        return realPersonApiReq;
    }

    /**
     * 获取人员认证信息
     **/
    public static HrmResourceApiReq getAllInfo(int userId) {
        RecordSet rs = new RecordSet();
        String sql = "select * from hrmresource where id=" + userId;
        new BaseBean().writeLog("执行sql: " + sql);
        rs.executeQuery(sql);
        HrmResourceApiReq req = new HrmResourceApiReq();
        // new BaseBean().writeLog("执行sql: 3" + rs.next());
        boolean next = rs.next();
        if (next) {
            req.setStaffUnicode(String.valueOf(userId));
            req.setUsername(rs.getString("loginid"));
            req.setStaffName(extractChinese(rs.getString("lastName")));
            // req.setStaffIdCard(rs.getString("certificatenum"));
            req.setCellphone(rs.getString("mobile"));
            req.setSubcompanyid1(rs.getString("subcompanyid1"));
            req.setPassword(rs.getString("password"));
            req.setId(rs.getString("id"));
        }
        return req;
    }

    /**
     * 提取名字
     **/
    public static String extractChinese(String input) {
        // 定义匹配中文字符的正则表达式
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]+");
        Matcher matcher = pattern.matcher(input);
        StringBuilder result = new StringBuilder();

        // 查找匹配的中文部分并添加到结果中
        while (matcher.find()) {
            result.append(matcher.group());
        }
        return result.toString();
    }

    public static void main(String[] args) {
        String s = extractChinese("李俊鸿2947");
        System.out.println("s = " + s);
    }
}
