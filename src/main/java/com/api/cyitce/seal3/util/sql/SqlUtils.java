package com.api.cyitce.seal3.util.sql;

import cn.hutool.core.codec.Base64;
import com.api.cyitce.seal3.config.ThirdMappConfig;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

import static com.api.cyitce.seal3.util.data.OaHrmUtils.extractChinese;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/15 13:13
 * @describe 执行sql
 */
public class SqlUtils {
    /**
     * sfmb 0 取 1 放
     *
     * <AUTHOR>
     */

    public static BaseResp<Void> insertQfz(String thridId, String oaId, int type, ThirdMappConfig config, String sfmb) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,oaid,sealid,sfmb) values(" + type + ",'" + oaId + "','" + thridId + "','" + sfmb + "')";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertQfz---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertQfz 执行错误");
            return new BaseResp<Void>(false);
        }
    }

    public static int updateFile(int id, String tableName, int i) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set qzhfj=''  where id =" + id;
            new BaseBean().writeLog(" updateQsym---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" updateQsym---------------------------> 执行成功sql " + rs.next());
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateQsym ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    // sfmb 0 未完成 1 完成
    public static int updateRepect(String oaid, int type, ThirdMappConfig config, String requestid, String sealid) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = " update  " + config.getThirdTable() + " set sealid='" + sealid + "' where type=" + type + " and oaid='" + oaid + "' and requestid1='" + requestid + "'";
            new BaseBean().writeLog(" updateRepect---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" updateRepect---------------------------> 执行成功sql " + rs.next());
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateRepect ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    // sfmb 0 未完成 1 完成
    public static int updateZTB(String uuid, String isComplete, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + config.getThirdTable() + "  set sfmb='" + isComplete + "'  where rlsxid='" + uuid + "'";
            new BaseBean().writeLog(" updateZTB---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" updateZTB---------------------------> 执行成功sql " + rs.next());
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateZTB ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    public static int updateZKZT(String id, String zkzt, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + config.getThirdyzTable() + " set  zkzt='" + zkzt + "'  where id=" + id;
            new BaseBean().writeLog(" updateZKZT---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" updateZKZT---------------------------> 执行成功sql " + rs.next());
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateZKZT ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }


    /**
     * 获取印章信息
     *
     * @return
     */
    public static Map<String, String> yzInfo(String id, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            // select * from    uf_seal_yz  where id = 16
            fileSql = "select  * from  " + config.getThirdyzTable() + "  where id=" + id;
            new BaseBean().writeLog(" yzInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            if (rs.next()) {
                map.put("id", rs.getString("id"));
                map.put("yzmc", rs.getString("yzmc"));
                map.put("zkzt", rs.getString("zkzt"));
                map.put("yzlx", rs.getString("yzlx"));
                map.put("yky", rs.getString("yky"));
                map.put("yzwbbh", rs.getString("yzwbbh"));
            } else {
                return map;
            }
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" yzInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }


    /**
     * 获取印章桶信息
     *
     * @return
     */
    public static Map<String, String> yztInfo(String id) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            // select * from    uf_seal_yz  where id = 16
            fileSql = "select  * from  uf_yxwdt where id=" + id;
            new BaseBean().writeLog(" yztInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            if (rs.next()) {
                map.put("id", rs.getString("id"));
                map.put("Uuid", rs.getString("Uuid"));
            } else {
                return map;
            }
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" yztInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    /**
     * 取放章数据查询
     *
     * @param requestId
     * @param type
     * @param sfmb
     * @return
     */
    public static Map<String, String> qz(String requestId, String type, String sfmb, ThirdMappConfig config, String yz) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            // select top 1 * from uf_seal_third_test where oaId='50826604'  and  sealid like '%16%'  and type =8 and sfmb='0' order by id desc
            fileSql = "select top 1 * from  " + config.getThirdTable() + "  where type=" + type + "  and oaid='" + requestId + "' and sfmb='" + sfmb + "' and sealid like '%" + yz + "%' order by id desc  ";
            new BaseBean().writeLog(" qz---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            if (rs.next()) {
                map.put("id", rs.getString("id"));
            } else {
                return map;
            }
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" qz ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }


    /**
     * 触发子流程的sql
     *
     * @return
     */
    public static Map<String, String> cfzlcSql(String formId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "SELECT * FROM workflow_base where formid=" + formId + " and isvalid=1";
            new BaseBean().writeLog(" cfzlcSql---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" cfzlcSql---------------------------> 执行成功sql " + rs.next());
            map.put("id", rs.getString("id"));
            map.put("workflowname", rs.getString("workflowname"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" cfzlcSql ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    public static Map<String, String> getcusDataInfo(String phone, String scopeid) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from cus_fielddata where field49='" + phone + "' and scopeid=" + scopeid;
            new BaseBean().writeLog(" getcusDataInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getcusDataInfo---------------------------> 执行成功sql " + rs.next());
            map.put("id", rs.getString("id"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getcusDataInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    // 根据requestid拿到主表信息
    public static Map<String, String> getMainTableInfo(String requestId, String tablename) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from  " + tablename + "  where requestid=" + requestId;
            new BaseBean().writeLog(" getMainTableInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getMainTableInfo---------------------------> 执行成功sql " + rs.next());
            map.put("id", rs.getString("id"));
            // map.put("htyqs", rs.getString("htyqs"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getMainTableInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    public static int insertMXB(String wjbt, String docId, int mainId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "insert into formtable_main_1921_dt1(wjbt,mainid,shnrfyy) values('" + wjbt + " '," + mainId + ",'" + docId + "')";
            new BaseBean().writeLog(" insertMXB---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertMXB---------------------------> 执行成功sql " + rs.next());
            return 1;
        } catch (Exception e) {
            new BaseBean().writeLog(" insertMXB ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return 0;
        }

    }

    public static Map<String, String> getJobInfo(int id) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from hrmjobtitles where id=" + id;
            new BaseBean().writeLog(" getJobInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getJobInfo---------------------------> 执行成功sql " + rs.next());
            map.put("zw", extractChinese(rs.getString("jobtitlename")));
            // map.put("htyqs", rs.getString("htyqs"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getJobInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    public static Map<String, String> getSchoolInfo(int userId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from cus_fielddata where id=" + userId + " and scopeid=1";
            new BaseBean().writeLog(" getSchoolInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getSchoolInfo---------------------------> 执行成功sql " + rs.next());

            map.put("xx", extractChinese(rs.getString("field4")));
            map.put("zy", extractChinese(rs.getString("field3")));
            // map.put("htyqs", rs.getString("htyqs"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getModelInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }


    public static Map<String, String> getModelInfo(int userId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from hrmresource where id=" + userId;
            new BaseBean().writeLog(" getModelInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getModelInfo---------------------------> 执行成功sql " + rs.next());

            map.put("xm", extractChinese(rs.getString("lastname")));
            map.put("sfz", rs.getString("certificatenum"));
            map.put("companystartdate", rs.getString("companystartdate")); // 入职日期
            map.put("telephone", rs.getString("telephone")); // 入职日期
            map.put("mobile", rs.getString("mobile")); // 入职日期
            map.put("enddate", rs.getString("enddate")); // 合同结束时间可以看成离职日期
            map.put("subcompanyid1", rs.getString("subcompanyid1")); //
            map.put("sex", rs.getString("sex")); //
            // map.put("htyqs", rs.getString("htyqs"));

            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getModelInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    public static Map<String, String> getCompanyInfo(int id) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from hrmsubcompany where id=" + id;
            new BaseBean().writeLog(" getCompanyInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getCompanyInfo---------------------------> 执行成功sql " + rs.next());
            map.put("subcompanyname", rs.getString("subcompanyname"));
            map.put("id", rs.getString("id"));

            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getModelInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    /**
     * RecordSet rs =new RecordSet();
     * rs.executeQuery("select requestid1 from uf_seal_thrid_mapp where type=? and sealid=?", new Object[]{type,oaId});
     * <p>
     * if(rs.next()){
     * return Util.null2String(rs.getString(1));
     * }
     * <p>
     * return "";
     * <p>
     * <p>
     * update formtable_main_1930_dt2 set qsym ='http://www.baidu.com' where id =84
     */
    public static int updateQsym(int id, String tableName, int i, String url) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set qsym ='" + url + "' where id =" + id;
            new BaseBean().writeLog(" updateQsym---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" updateQsym---------------------------> 执行成功sql " + rs.next());
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateQsym ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    public static int updateqzfj(int id, String tableName, int i, String docId) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set qzhfj ='" + docId + "' where id =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }


    public static int updateMainqzfj(String id, String tableName, String docId) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + " set qzhfj ='" + docId + "' where requestid =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    /**
     * 修改 电子印章 点击链接的下载地址
     *
     * @param id
     * @param tableName
     * @param
     * @return
     */
    public static int updateMaindzlj(int id, String tableName, String dzlj) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + " set dzlj ='" + dzlj + "' where requestid =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    /**
     * 修改 电子印章 点击链接的下载地址
     *
     * @param id
     * @param tableName
     * @param i
     * @return
     */
    public static int updatedzlj(int id, String tableName, int i, String dzlj) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set dzlj ='" + dzlj + "' where id =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    /**
     * 修改 实体印章点击链接的下载地址
     *
     * @param id
     * @param tableName
     * @param i
     * @return
     */
    public static int updatestlj(int id, String tableName, int i, String stlj) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set stlj ='" + stlj + "' where id =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }


    // 修改对应状态
    public static int updateStatus(int id, String tableName, int i, String status) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set status ='" + status + "' where id =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    // 修改对应状态
    public static int updateyxInfo(int id, String tableName, int i, String url, String info) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + "_dt" + i + " set yxtpurl ='" + url + "', yxyyjlbf ='" + info + "' where id =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    // 修改对应状态
    public static int updateMainStatus(String id, String tableName, String status) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "update " + tableName + " set status ='" + status + "' where requestid =" + id;
            new BaseBean().writeLog(" updateqzfj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" updateqzfj ---------------------------> sql修改 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    /**
     * 实体推送任务时判断是否重复推送任务
     *
     * @param type
     * @param oaid
     * @param config
     * @return
     */
    public static Map<String, String> getRepect(int type, String oaid, ThirdMappConfig config, String requestid) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from  " + config.getThirdTable() + "  where type=" + type + "  and oaid='" + oaid + "' and requestid1='" + requestid + "'";
            new BaseBean().writeLog(" getDocIdInfoByDocId---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            if (rs.next()) {
                new BaseBean().writeLog(" getDocIdInfoByDocId---------------------------> 执行成功sql " + rs.next());
                map.put("oaid", rs.getString("oaid"));
                // map.put("htyqs", rs.getString("htyqs"));
                map.put("requestid1", rs.getString("requestid1"));
                map.put("mxbid", rs.getString("mxbid"));
                map.put("yymxb", rs.getString("yymxb"));
                map.put("sealid", rs.getString("sealid"));
                map.put("mbdywd", rs.getString("mbdywd")); // 模板对应文档
                map.put("cclj", rs.getString("cclj")); // 回廊对应存储路径
                return map;
            } else {
                return map;
            }

        } catch (Exception e) {
            new BaseBean().writeLog(" getDocIdInfoByDocId ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    /**
     * 根据主表名
     */
    public static Map<String, String> getDocIdInfo(int type, String contaractId, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from  " + config.getThirdTable() + "  where type=" + type + "  and sealid='" + contaractId + "' ";
            new BaseBean().writeLog(" getDocIdInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getDocIdInfo---------------------------> 执行成功sql " + rs.next());
            map.put("oaid", rs.getString("oaid"));
            // map.put("htyqs", rs.getString("htyqs"));
            map.put("htqzrsl", rs.getString("htqzrsl"));
            map.put("htyqs", rs.getString("htyqs"));
            map.put("requestid1", rs.getString("requestid1"));
            map.put("qzrlj", rs.getString("qzrlj"));
            map.put("mxbid", rs.getString("mxbid"));
            map.put("lcpzid", rs.getString("lcpzid"));
            map.put("yymxb", rs.getString("yymxb"));
            map.put("gzrlj", rs.getString("gzrlj"));
            map.put("sealid", rs.getString("sealid"));
            map.put("sfmb", rs.getString("sfmb"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getDocIdInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    public static Map<String, String> getDocIdInfoByDocId(int type, String oaid, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from  " + config.getThirdTable() + "  where type=" + type + "  and oaid='" + oaid + "' ";
            new BaseBean().writeLog(" getDocIdInfoByDocId---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getDocIdInfoByDocId---------------------------> 执行成功sql " + rs.next());
            map.put("oaid", rs.getString("oaid"));
            // map.put("htyqs", rs.getString("htyqs"));
            map.put("requestid1", rs.getString("requestid1"));
            map.put("mxbid", rs.getString("mxbid"));
            map.put("yymxb", rs.getString("yymxb"));
            map.put("sealid", rs.getString("sealid"));
            map.put("mbdywd", rs.getString("mbdywd")); // 模板对应文档
            map.put("cclj", rs.getString("cclj")); // 回廊对应存储路径
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getDocIdInfoByDocId ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }


    /**
     * 根据主表名
     * i： 主表_dt+i
     *
     * <AUTHOR>
     * @date 2025/2/19 19:33
     */
    public static Map<String, String> getqsrInfo1(int type, String docId, ThirdMappConfig config, int i) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from " + config.getThirdTable() + "  where type=" + type + "  and oaid='" + docId + "' and  mxbid='" + i + "'";
            new BaseBean().writeLog(" getqsrInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getqsrInfo---------------------------> 执行成功sql " + rs.next());
            map.put("htqzrsl", rs.getString("htqzrsl"));
            map.put("htyqs", rs.getString("htyqs"));
            map.put("requestid1", rs.getString("requestid1"));
            map.put("qzrlj", rs.getString("qzrlj"));
            map.put("mxbid", rs.getString("mxbid"));
            map.put("lcpzid", rs.getString("lcpzid"));
            map.put("yymxb", rs.getString("yymxb"));
            map.put("gzrlj", rs.getString("gzrlj"));
            map.put("sealid", rs.getString("sealid"));
            map.put("mbdywd", rs.getString("mbdywd"));
            map.put("mbdyht", rs.getString("mbdyht"));
            map.put("rlsxid", rs.getString("rlsxid"));
            map.put("sfmb", rs.getString("sfmb"));
            map.put("gzr", rs.getString("gzr"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getqsrInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    /**
     * 根据主表名
     * i： 主表_dt+i
     *
     * <AUTHOR>
     * @date 2025/2/19 19:33
     */
    public static Map<String, String> getqsrInfo(int type, String docId, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from " + config.getThirdTable() + "  where type=" + type + "  and oaid='" + docId + "'";
            new BaseBean().writeLog(" getqsrInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getqsrInfo---------------------------> 执行成功sql " + rs.next());
            map.put("htqzrsl", rs.getString("htqzrsl"));
            map.put("htyqs", rs.getString("htyqs"));
            map.put("requestid1", rs.getString("requestid1"));
            map.put("qzrlj", rs.getString("qzrlj"));
            map.put("mxbid", rs.getString("mxbid"));
            map.put("lcpzid", rs.getString("lcpzid"));
            map.put("yymxb", rs.getString("yymxb"));
            map.put("gzrlj", rs.getString("gzrlj"));
            map.put("sealid", rs.getString("sealid"));
            map.put("mbdywd", rs.getString("mbdywd"));
            map.put("mbdyht", rs.getString("mbdyht"));
            map.put("rlsxid", rs.getString("rlsxid"));
            map.put("sfmb", rs.getString("sfmb"));
            map.put("gzr", rs.getString("gzr"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getqsrInfo ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }


    /**
     * 根据主表名
     * i： 主表_dt+i
     *
     * <AUTHOR>
     * @date 2025/2/19 19:33
     */
    public static Map<String, String> getTableDTid(int requestId, String mainTableName, int i, String docId, String tag) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            // select * from formtable_main_1930_dt2 where mainid=122 and CONVERT(varchar(10), xynr) = 1259505 ;
            fileSql = "select * from " + mainTableName + "_dt" + i + "  where mainid = (select id from " + mainTableName + " where requestid=" + requestId + ")" + "  and CONVERT(varchar(255)," + tag + ") =  '" + docId + "'";
            new BaseBean().writeLog(" getTableDTid---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getTableDTid---------------------------> 执行成功sql " + rs.next());
            map.put("id", rs.getString("id"));
            map.put("qzhfj", rs.getString("qzhfj"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" getTableDTid ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return map;
        }

    }

    /**
     * 根据主表名
     * i： 主表_dt+i
     * update uf_seal_thrid_mapp set
     * htqzrsl = CASE
     * WHEN htqzrsl IS NULL OR htqzrsl = '' THEN '1'
     * ELSE htqzrsl+1
     * END,
     * htyqs = CASE
     * WHEN htyqs IS NULL OR htyqs = '' THEN '55'
     * ELSE htyqs + ',55'
     * END
     * where oaid='MTI1OTAyNQ==' and type=0;
     *
     * <AUTHOR>
     * @date 2025/2/19 19:33
     */
    public static int changeThirdNum(int type, String oaId, int i, ThirdMappConfig config, String qzrlj) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = " update  " + config.getThirdTable() + "  set htqzrsl = CASE WHEN htqzrsl IS NULL OR htqzrsl = '' THEN '1' ELSE htqzrsl + 1 END, " + "htyqs = CASE WHEN htyqs IS NULL OR htyqs = '' THEN '" + i + "'  ELSE htyqs + '," + i + "' " + "  END , qzrlj = CASE WHEN qzrlj IS NULL OR qzrlj = '' THEN '" + qzrlj + "'  ELSE qzrlj + '," + qzrlj + "' END " + " where oaid='" + oaId + "' and type=" + type;
            new BaseBean().writeLog(" changeThirdNum---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" changeThirdNum ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return 1;
        }

    }


    public static int changeThirdGZNum(int type, String oaId, int i, ThirdMappConfig config, String gzrlj) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = " update  " + config.getThirdTable() + " set  gzrlj = CASE WHEN gzrlj IS NULL OR gzrlj = '' THEN '" + gzrlj + "'  ELSE gzrlj + '," + gzrlj + "' END " + "  where oaid='" + oaId + "' and type=" + type;
            new BaseBean().writeLog(" changeThirdNum---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" changeThirdNum ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    public static int changecclj(int type, String appId, ThirdMappConfig config, String cclj) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = " update  " + config.getThirdTable() + " set  cclj = '" + cclj + "'  where sealid='" + appId + "' and type=" + type;
            new BaseBean().writeLog(" changecclj---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            return 0;
        } catch (Exception e) {
            new BaseBean().writeLog(" changecclj ---------------------------> sql查询 方法报错");
            e.printStackTrace();
            return 1;
        }

    }

    /**
     * 根据request 表名 拿到对应数据
     *
     * @param tableName
     * @param requestId
     * <AUTHOR>
     * @date 2025/2/19 19:33
     */
    public static Map<String, String> getTableInfo(String tableName, String requestId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select * from  " + tableName + " where requestid=" + requestId;
            new BaseBean().writeLog(" getTableInfo---------------------------> 执行sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getTableInfo---------------------------> 执行成功sql " + rs.next() + ":" + rs.getString("qzhfj"));
            map.put("qzhfj", rs.getString("qzhfj"));
            map.put("id", rs.getString("id"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertRequest 执行错误");
            return map;
        }

    }

    /**
     * 根据requestid拿到主表 表名
     * yymxb 用印明细表
     *
     * @return
     */
    public static BaseResp<Void> insertRequest(String thridId, String oaId, int type, String requestId, ThirdMappConfig config, int rowId, int yymxb, String configId) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,oaid,sealid,requestid1,mxbid,yymxb,lcpzid) values(" + type + ",'" + oaId + "','" + thridId + "','" + requestId + "','" + rowId + "','" + yymxb + "'," + configId + ")";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertRequest---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertRequest 执行错误");
            return new BaseResp<Void>(false);
        }
    }

    /**
     * 招投标insert
     */
    public static BaseResp<Void> insertZTB(int type, String oaId, String sealId, String sfmb, ThirdMappConfig config, String userId, String uuid) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = " insert into  " + config.getThirdTable() + "  (type,oaId,sealId,sfmb,qzrlj,rlsxid) values (" + type + ",'" + oaId + "','" + sealId + "','" + sfmb + "','" + userId + "','" + uuid + "')";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertZTB---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> insertZTB 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertZTB 执行错误");
            return new BaseResp<Void>(false);
        }
    }

    /**
     * 根据requestid拿到主表 表名
     * yymxb 用印明细表
     *
     * @return
     */
    public static BaseResp<Void> insertRequestModel(String thridId, String oaId, int type, String requestId, ThirdMappConfig config, int rowId, int yymxb, String configId, int sfmb) {
        RecordSet rs = new RecordSet();
        oaId = Base64.encode(oaId);
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,oaid,sealid,requestid1,mxbid,yymxb,lcpzid,sfmb) values(" + type + ",'" + oaId + "','" + thridId + "','" + requestId + "','" + rowId + "','" + yymxb + "'," + configId + "," + sfmb + ")";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertRequest---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertRequest 执行错误");
            return new BaseResp<Void>(false);
        }
    }


    public static BaseResp<Void> insertRequestModel(String thridId, String mbdyht, int type, String requestId, ThirdMappConfig config, String mbdywd) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,mbdyht,sealid,requestid1,mbdywd) values(" + type + ",'" + mbdyht + "','" + thridId + "','" + requestId + "','" + mbdywd + "')";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertRequest---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("insertRequest 执行错误");
            return new BaseResp<Void>(false);
        }

    }

    /**
     * 根据requestid拿到主表 表名
     *
     * @return
     */
    public static Map<String, String> getfileName(int docId) {
        RecordSet rs = new RecordSet();
        Map<String, String> map = new HashMap<>();
        String fileSql = "";
        try {
            fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid =" + docId + " order by imagefileid desc";
            rs.execute(fileSql);
            new BaseBean().writeLog(" getfileName---------------------------> 执行成功sql " + rs.next() + ":" + rs.getString("imagefilename"));
            new BaseBean().writeLog(" getfileName---------------------------> 执行成功sql " + rs.next() + ":" + rs.getString("imagefileid"));
            map.put("filename", rs.getString("imagefilename"));
            map.put("fileid", rs.getString("imagefileid"));
            return map;
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> getfileName 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("getSealid 执行错误");
            return map;
        }

    }

    /**
     * 根据requestid拿到主表 表名
     *
     * @return
     */
    public static String getSealid(int id, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "select * from " + config.getThirdyzTable() + "  where id='" + id + "'";
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + rs.next() + ":" + rs.getString("yzwbbh"));
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + rs.getInt("yzwbbh"));
            return rs.getString("yzwbbh");
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("getSealid 执行错误");
            return "";
        }

    }

    public static String getSealid1(int id, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "select * from " + config.getThirdyzTable() + "  where id='" + id + "'";
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + rs.next() + ":" + rs.getString("yzwbbh"));
            new BaseBean().writeLog(" getSealid---------------------------> 执行成功sql " + rs.getInt("yzwbbh"));
            return rs.getString("sffrykj");
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");
            e.printStackTrace();
            new BaseBean().writeLog("getSealid 执行错误");
            return "";
        }

    }


    public static BaseResp<Void> insertThirdMapping(String thridId, String oaId, int type, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,oaid,sealid) values(" + type + ",'" + oaId + "','" + thridId + "')";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertThirdMapping---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            e.printStackTrace();
            new BaseBean().writeLog("insertThirdMapping 执行错误");
            return new BaseResp<Void>(false);
        }

    }

    public static BaseResp<Void> insertThirdCodeMapping(String thridId, String oaId, int type, ThirdMappConfig config, String code, String mxbid, String requestid, String configId, String yymxb) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "insert into " + config.getThirdTable() + "(type,oaid,sealid,sfmb,mxbid,requestid1,lcpzid,yymxb) values(" + type + ",'" + oaId + "','" + thridId + "','" + code + "','" + mxbid + "','" + requestid + "','" + configId + "','" + yymxb + "')";
            rs.execute(fileSql);
            new BaseBean().writeLog(" insertThirdCodeMapping---------------------------> 执行成功 ");
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            e.printStackTrace();
            new BaseBean().writeLog("insertThirdMapping 执行错误");
            return new BaseResp<Void>(false);
        }

    }


    /**
     * 删除三方对应表数据
     *
     * @return
     */
    public static BaseResp<Void> deleteThrid(String oaId, int type, ThirdMappConfig config) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "delete  from " + config.getThirdTable() + "  WHERE type=" + type + " and oaid='" + oaId + "'";
            rs.execute(fileSql);
            new BaseBean().writeLog(" deleteThrid---------------------------> 执行sql ：" + fileSql);
            return new BaseResp<Void>(true);
        } catch (Exception e) {
            new BaseBean().writeLog("deleteThrid---------------------------> 执行sql  方法报错");
            e.printStackTrace();
            return new BaseResp<Void>(false);
        }

    }


}
