package com.api.cyitce.seal3.util.http;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal3.config.BaseAuth;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.vo.resp.MsgResp;
import com.weaver.general.BaseBean;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: PostUtil
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-10  17:17
 * @Version: 1.0
 */
public class PostUtil {
    private BaseLog log = new BaseLog(PostUtil.class);

    private OkHttpClient client = new OkHttpClient().newBuilder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS).build();

    private MediaType mediaType = MediaType.parse("application/json;charset=utf-8");

    public static JSONObject toPost(String url, JSONObject requestJson, BaseAuth config) {
        PostUtil postUtil = new PostUtil();
        new BaseBean().writeLog(PostUtil.class.getName(), " 请求config:" + config.getBaseUrl());
        return postUtil.sendPost(config.getBaseUrl(), url, JSONUtil.toJsonStr(requestJson), config);
    }

    public JSONObject sendPost(String baseUrl, String url, String requestJson, BaseAuth config) {
        return sendPost(baseUrl + url, requestJson, config);
    }

    public JSONObject sendPost(String url, String requestBodyStr, BaseAuth config) {
        log.info("执行请求 ，url=" + url);
        // 封装 http 请求
        RequestBody body = RequestBody.create(mediaType, requestBodyStr);
        Request.Builder requestBuilder = new Request.Builder();
        try {
            requestBuilder.url(url);
            // 获取请求头部参数
            Map<String, String> headers = config.getRequestHeaders(requestBodyStr);
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
                log.info("头部参数，{?}：【{?}】", entry.getKey(), entry.getValue());
            }

            // 发送 http 请求
            Request request = requestBuilder.post(body).build();

            Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                JSONObject result = JSONUtil.parseObj(response.body().string());
                if (url.contains("/contract/search")) {
                    //  查询合同详情 不打印base64
                    log.info("拿到合同详情结果 +" + result.get("code"));

                } else {
                    log.info("请求响应：【{?}】", result.toString());
                }
                return result;
            } else {
                throw new IOException("错误 code " + response);
            }
        } catch (IOException e) {
            log.error("请求异常，requestBody：【{?}】", requestBodyStr, e);
            e.printStackTrace();
            return JSONUtil.parseObj(MsgResp.error("请求异常"));
        }
    }

    public static JSONObject toPost(String url, JSONObject requestJson, Map<String, String> headers) {
        PostUtil postUtil = new PostUtil();
        new BaseBean().writeLog(PostUtil.class.getName(), " 请求头部:" + headers);
        return postUtil.sendPost(url, JSONUtil.toJsonStr(requestJson), headers);
    }

    public JSONObject sendPost(String url, String requestBodyStr, Map<String, String> headers) {
        // 封装 http 请求
        RequestBody body = RequestBody.create(mediaType, requestBodyStr);
        Request.Builder requestBuilder = new Request.Builder();
        try {
            requestBuilder.url(url);
            // 获取请求头部参数
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
                log.info("头部参数，{?}：【{?}】", entry.getKey(), entry.getValue());
            }

            // 发送 http 请求
            Request request = requestBuilder.post(body).build();

            Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                JSONObject result = JSONUtil.parseObj(response.body().string());
                log.info("请求响应：【{?}】", result.toString());

                return result;
            } else {
                throw new IOException("错误 code " + response);
            }
        } catch (IOException e) {
            log.error("请求异常，requestBody：【{?}】", requestBodyStr, e);
            e.printStackTrace();
            return JSONUtil.parseObj(MsgResp.error("请求异常"));
        }
    }
}