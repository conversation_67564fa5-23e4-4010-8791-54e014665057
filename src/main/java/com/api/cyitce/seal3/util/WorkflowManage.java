package com.api.cyitce.seal3.util;

import weaver.conn.RecordSet;

/**
 * @Description: 流程信息管理
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/6/27 14:45
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/6/27      HONOR          v1.0.0               修改原因
 */
public class WorkflowManage {

    /**
     * @Description: 获取流程附件存放目录
     * @param formid
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/27 14:48
     */
    public static String getDocPath(int formid){
        RecordSet rs = new RecordSet();
        rs.execute("select docCategory from workflow_base where formid='"+formid+"' and isvalid=1");
        rs.next();
        try {
            String path = rs.getString(1);
            if(path==null){
                throw new Exception("流程未设置附件存放目录");
            }
            return path;
        }catch (Exception e){
            return "";
        }
    }

    /**
     * @description:获取流程workflowid
     * @author: lijianpan
     * @date: 2023/8/11
     * @param: [requestid]
     * @return: int
     **/
    public static int getWorkflowid(String requestid){
        int path;
        RecordSet rs = new RecordSet();
        rs.executeQuery("select workflowid from workflow_requestbase where requestid=?",requestid);
        if(rs.next()){
            path = rs.getInt(1);
        }else {
            throw new NullPointerException("requestid:"+requestid+",不存在的流程！");
        }

        return path;
    }

    /**
     * @description:获取流程附件存放目录
     * @author: lijianpan
     * @date: 2023/8/11
     * @param: [formid]
     * @return: java.lang.String
     **/
    public static String getDocCategory(String requestid){
        String docCategory = "";
        RecordSet rs = new RecordSet();
        rs.executeQuery("SELECT b.docCategory FROM workflow_form a inner join workflow_base b on a.billformid=b.formid where a.requestid=? " +
                "and b.isvalid in (?,?)",requestid,"1","2");
        if(rs.next()){
            docCategory = rs.getString(1);
        }else {
            throw new NullPointerException("requestid："+requestid+",流程没有设置附件存放目录！");
        }

        return docCategory;
    }

    /**
     * @description:获取流程创建人
     * @author: lijianpan
     * @date: 2023/8/11
     * @param: [requestid]
     * @return: int
     **/
    public static int getRequestCreater(String requestid){
        int creater;
        RecordSet rs = new RecordSet();
        rs.executeQuery("select creater from workflow_requestbase where requestid=?",requestid);
        if(rs.next()){
            creater = rs.getInt(1);
        }else {
            throw new NullPointerException("requestid："+requestid+",此流程不存在！");
        }

        return creater;
    }
}
