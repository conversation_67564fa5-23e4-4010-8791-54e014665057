package com.api.cyitce.seal3.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.api.cyitce.seal3.config.BaseLog;
import com.api.cyitce.seal3.config.realauth.RealAuthConfig;
import com.api.cyitce.seal3.constant.ESealApiUrl;
import com.api.cyitce.seal3.vo.resp.BaseResp;
import com.api.cyitce.seal3.vo.resp.MsgResp;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/15 14:55
 * @describe
 */
public class SmsTest extends BaseLog {

    public BaseResp<JSONObject> sendSmsTest(Map<String, Object> maprequest) {
        info("sendSms  ---------------------------> 进入主要方法");
        // 构造请求参数
        RealAuthConfig rConfig = new RealAuthConfig();

        String url = rConfig.props("itruscloud.sms.api.url") + ESealApiUrl.SEND_SMS;
        info("sendSms  ---------------------------> 发送短信的url" + url);
        String autograph = rConfig.props("itruscloud.sms.api.autograph");
        info("sendSms  ---------------------------> itruscloud.sms.api.autograph： " + autograph);
        String appId = rConfig.getAppId();
        info("sendSms  ---------------------------> appId： " + appId);

        String serviceCode = "msg0001";
        info("sendSms  ---------------------------> serviceCode： " + serviceCode);
        String serviceKye = rConfig.getSecretKey();
        info("sendSms  ---------------------------> serviceCode： " + serviceCode);
        maprequest.put("appId", appId);
        maprequest.put("serviceCode", serviceCode);
        maprequest.put("autograph", autograph);

        info("sendSms  ---------------------------> 短信服务请求参数：{?}", JSON.toJSONString(maprequest));

        byte[] byterequest = JSON.toJSONBytes(maprequest);
        String signature = "";

        try {
            signature = Base64.getEncoder().encodeToString(
                    HmacSh1Util.getHmacSHA1(byterequest, serviceKye + serviceCode));

        } catch (Exception e) {
            e.printStackTrace();
            return MsgResp.error("加密算法不存在");
        }

        try {
            // 创建 URL 对象
            URL apiUrl = new URL(url);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
            // 设置请求方法为 POST
            connection.setRequestMethod("POST");
            // 设置请求头
            connection.setRequestProperty("appId", appId);
            connection.setRequestProperty("serviceCode", serviceCode);
            connection.setRequestProperty("Content-Signature", "HMAC-SHA1 " + signature);
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            // 允许输出
            connection.setDoOutput(true);

            // 写入请求体
            try (OutputStream os = connection.getOutputStream()) {
                os.write(byterequest);
                os.flush();
            }

            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应内容
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line);
                    }
                    String responseStr = response.toString();
                    // 打印响应内容
                    info("sendSms  ---------------------------> 响应内容: {?}", responseStr);
                    JSONObject jsonResp = JSONUtil.parseObj(response.toString());
                    if (!"1".equals(jsonResp.getStr("status"))) {
                        info("sendSms  ---------------------------> 发送短信失败: {?}", responseStr);
                        return MsgResp.error(jsonResp.getStr("message"));
                    }
                    return MsgResp.ok();
                }
            } else {
                error("发送短信失败，响应码: {?}", responseCode);
            }
        } catch (Exception e) {
            error("发送短信失败,{?}", e.getMessage(), e);
        }

        return MsgResp.error("发送短信失败");
    }

    // public static BaseResp<JSONObject> sendSms(String phone, String content) {
    //     new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest12321312 ");
    //     Map<String, Object> maprequest = new HashMap<>();
    //     maprequest.put("phone", phone);
    //     maprequest.put("content", content);
    //     SmsUtil su = new SmsUtil();
    //
    //     new BaseBean().writeLog("sendSms  --------------------------->  进行sendSms方法maprequest " + maprequest.toString());
    //
    //     return su.sendSms(maprequest);
    // }
}
