package com.api.cyitce.seal3.util;

import com.api.cyitce.seal3.config.OaConfig;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;

/**
 * @ClassName: Cookie
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-02-11  23:08
 * @Version: 1.0
 */
public class Cookie {
    public static OaConfig OA_CONFIG = new OaConfig();

    public static String URL = OA_CONFIG.getUrl()+"/hrm/login/checkLogin";

    /**
     *
     * @description 获取cookie
     * @param username 登录名
     * @param password 登录密码
     * @return
     */
    public static String getCookies(String username, String password) {
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            client = HttpClients.createDefault();
            post = new HttpPost();
            post.addHeader("Content-Type", "application/json");
            builder = new URIBuilder(URL);
            builder.addParameter("loginid",username);
            builder.addParameter("userpassword",password);
            post = new HttpPost(builder.build());
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }
            headers = response.getHeaders("Set-Cookie");
            for (int i = 0;i<headers.length;++i){
                if("ecology_JSessionId".equals(headers[i].getElements()[0].getName())){
                    cookieStr = headers[i].getElements()[0].getValue();
                }
            }
            return cookieStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}