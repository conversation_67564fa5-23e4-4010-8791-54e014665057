package com.api.cyitce.seal3.util.data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName: Now
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-14  10:12
 * @Version: 1.0
 */
public class Now {
    static String DATE_FORMAT = "yyyyMMddHHmmssSSS";

    @Override
    public String toString() {
        SimpleDateFormat time = new SimpleDateFormat(DATE_FORMAT);
        String now = time.format(new Date());
        return now;
    }
}