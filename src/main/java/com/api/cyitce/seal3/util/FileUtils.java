package com.api.cyitce.seal3.util;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.cyitce.seal3.config.OaConfig;
import com.weaver.general.BaseBean;
import com.weaver.general.Util;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.file.ImageFileManager;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

public class FileUtils {
    public static OaConfig OA_CONFIG = new OaConfig();

    public static String UPLOAD_URL = OA_CONFIG.getUrl() + "/doc/upload/uploadFile2Doc";

    /**
     * @description:OA文件信息
     * @author: lijianpan
     * @date: 2023/5/6
     * @param: [docid]
     * @return: Map<String, Object>
     **/
    public static Map<String, String> oaFileInfo(String docid) throws Exception {
        if ("".equals(docid) || docid == null) {
            throw new Exception("文档id为空");
        }
        RecordSet rs = new RecordSet();
        String filename = "";// 文件名
        String filepath = "";// 文件路径
        String fileSql = "";
        Map<String, String> map = new HashMap<>();

        FileInputStream fis = null;
        ZipInputStream zis = null;
        ZipEntry entry = null;
        ByteArrayOutputStream bos = null;

        try {
            fileSql = "select c.imagefileid,c.filerealpath,c.iszip,c.imagefilename,c.imagefile,c.filesize from DocImageFile b ,imagefile c where b.imagefileid = c.imagefileid and b.docid = "
                    + docid + " order by imagefileid desc";
            rs.execute(fileSql);

            if (rs.next()) {
                filename = Util.null2String(rs.getString("imagefilename"));
                filepath = Util.null2String(rs.getString("filerealpath"));
                new BaseBean().writeLog("imagefilename:" + rs.getString("imagefilename") + "filerealpath" + rs.getString("filerealpath"));
                map.put("filename", filename);
                map.put("type", filename.substring(filename.lastIndexOf(".")));
                map.put("imagefileid", Util.null2String(rs.getString("imagefileid")));
                File file = new File(filepath);
                if (file.exists()) {
                    fis = new FileInputStream(file);   //  读取文件流
                }
                Map<String, String> stringStringMap = unzipAndEncode(filepath);
                for (String s : stringStringMap.keySet()) {
                    map.put("file1", stringStringMap.get("s"));
                }
                zis = new ZipInputStream(new BufferedInputStream(fis));  // 该类实现了以ZIP文件格式读取文件的输入流过滤器。 包括对压缩和未压缩条目的支持(读取到缓冲流)。
                bos = new ByteArrayOutputStream();

                while ((entry = zis.getNextEntry()) != null) {   // 文件实例是否为空
                    byte[] data = new byte[2048];
                    int len;

                    while ((len = zis.read(data)) != -1) {
                        bos.write(data, 0, len);
                    }
                }
                map.put("bytes", new String(bos.toByteArray(), StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            bos.close();
            fis.close();
            zis.close();
        }

        return map;
    }

    public static String inputStreamToBase64(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            // 从 InputStream 中读取数据并写入 ByteArrayOutputStream
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            // 获取 ByteArrayOutputStream 中的字节数组
            byte[] bytes = outputStream.toByteArray();
            // 使用 Base64 编码字节数组
            return Base64.getEncoder().encodeToString(bytes);
        }
    }

    // 通过fjid查询附件的路径，并且通过流方式读取转换成base64，存放数组中
    public static String getFjUrl(String fjids) {
        RecordSet rs = new RecordSet();
        JSONArray jsonArray = new JSONArray();
        try {
            if (StringUtils.isBlank(fjids)) return null;
            // 一个附件
            int number = Integer.parseInt(fjids);
            // 通过附件id查询存放路径
            rs.execute("select IMAGEFILEID from docimagefile where docid = " + number);
            while (rs.next()) {
                String imagefileid = inputStreamToBase64(ImageFileManager.getInputStreamById(rs.getInt("IMAGEFILEID")));
                return imagefileid;
            }
        } catch (Exception e) {

            throw new RuntimeException(e);
        }
        return "";
    }

    /**
     * 将文件转换为 Base64 编码字符串
     *
     * @param file 文件对象
     * @return Base64 编码字符串
     */
    public static String fileToBase64(File file) {
        new BaseBean().writeLog("正在转换文件名: " + file.getName());
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            byte[] fileBytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解压 ZIP 文件并将解压后的文件转换为 Base64 编码
     *
     * @param zipFilePath ZIP 文件路径
     * @return 文件名到 Base64 编码字符串的映射
     */
    public static java.util.Map<String, String> unzipAndEncode(String zipFilePath) {
        java.util.Map<String, String> fileBase64Map = new java.util.HashMap<>();
        try (ZipFile zipFile = new ZipFile(zipFilePath)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                if (!entry.isDirectory()) {
                    File tempFile = File.createTempFile("unzip_", entry.getName());
                    try (BufferedInputStream bis = new BufferedInputStream(zipFile.getInputStream(entry));
                         FileOutputStream fos = new FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = bis.read(buffer)) != -1) {
                            fos.write(buffer, 0, bytesRead);
                        }
                        String base64Encoded = fileToBase64(tempFile);
                        fileBase64Map.put(entry.getName(), base64Encoded);
                        // 删除临时文件
                        tempFile.delete();
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return fileBase64Map;
    }

    /**
     * @description:上传文件（OA）
     * @author: lijianpan
     * @date: 2023/8/11
     * @param:
     * @return:
     **/
    public static String uploadToOA(String fileName, byte[] bytes, String requestid, String content) {
        // 上传附件接口参数设置
        HttpRequestManage http = new HttpRequestManage();
        new BaseBean().writeLog("传附件接口参数设置---------------->  fileName:" + fileName);
        String s = uploadEntity(requestid, content);
        new BaseBean().writeLog("传附件接口参数设置---------------->  uploadEntity 返回值:" + s);
        String wordId = http.raise(fileName + ".pdf", bytes, UPLOAD_URL, s);

        return wordId;
    }


    /**
     * @description:文件上传（OA）：流程上传文件所需参数
     * @author: lijianpan
     * @date: 2023/8/11
     * @param: [formid, workflowid, creatorid]
     * @return: com.alibaba.fastjson.JSONObject
     **/
    public static String uploadEntity(String requestid, String content) {
        JSONObject entity_json = new JSONObject();
        entity_json.put("category", content);// 存放目录
        entity_json.put("isFirstUploadFile", "9");// 附件状态
        entity_json.put("workflowid", WorkflowManage.getWorkflowid(requestid));// 流程workflowid
        entity_json.put("listType", "list");
        entity_json.put("f_weaver_belongto_userid", WorkflowManage.getRequestCreater(requestid));// 附件归属人
        entity_json.put("f_weaver_belongto_usertype", "0");
        return entity_json.toString();
    }

    /**
     * 根据文档id拿到文档名称
     *
     * @param docid
     * <AUTHOR>
     * @date 2025/2/15 9:53
     */
    public static String getDocName(String docid) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "select imagefilename  from DocImageFile where docid=" + docid;
            new BaseBean().writeLog(" 根据文档id 拿到文档名称--------------------------->  sql :" + fileSql);
            rs.execute(fileSql);
            new BaseBean().writeLog(" 根据文档id 拿到文档名称--------------------------->  sql rs.next() :" + rs.next());
            new BaseBean().writeLog(" 根据文档id 拿到文档名称--------------------------->  sql rs.next() :" + rs.getString("imagefilename"));
            return rs.getString("imagefilename") == null ? "" : rs.getString("imagefilename");
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> getDocName 方法报错");
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 根据requestid拿到主表 表名
     *
     * @param requestid
     * @return
     */
    public static String getFormName(String requestid) {
        new BaseBean().writeLog("getFormName ---------------------------> 进入方法");
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "SELECT\n" +
                    "  tablename \n" +
                    "FROM\n" +
                    "  workflow_bill \n" +
                    "WHERE\n" +
                    "  id = ( SELECT formid FROM workflow_base WHERE id = ( SELECT workflowid FROM workflow_requestbase WHERE requestid = " + requestid + " ) )";
            new BaseBean().writeLog("getFormName ---------------------------> 执行sql: " + fileSql);
            rs.execute(fileSql);
            if (rs.next()) {
                new BaseBean().writeLog("getFormName ---------------------------> tablename: " + rs.getString("tablename"));
                return weaver.general.Util.null2String(rs.getString("tablename"));
            }
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 没查到表名");
        } catch (Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据requestid拿到主表表名 方法报错");

            e.printStackTrace();
        }
        return "";
    }


    /**
     * 根据表名修改qzhfj字段
     * UPDATE formtable_main_1594_dt1
     * SET qzhfj=
     * WHERE requestid=50325803;
     */

    public static String updateFormFile(String tableName, String docId, String requestid) {
        RecordSet rs = new RecordSet();
        String fileSql = "";
        try {
            fileSql = "UPDATE " + tableName;
            if (!org.springframework.util.StringUtils.hasText(docId)) {
                fileSql += " SET qzhfj=NULL";
            } else {
                fileSql += " SET qzhfj='" + docId + "'";
            }
            fileSql += "  WHERE requestid=" + requestid;

            rs.execute(fileSql);
            new BaseBean().writeLog(" ---------------------------> 根据表名修改qzhfj字段  sql语句:" + fileSql);
        } catch (
                Exception e) {
            new BaseBean().writeLog(" ---------------------------> 根据表名修改qzhfj字段 方法报错");

            e.printStackTrace();
        }
        return "";
    }


    /**
     * 将 Base64 编码的字符串转换为文件
     *
     * @param base64String   Base64 编码的字符串
     * @param outputFilePath 输出文件的路径
     * @return 如果转换成功返回 true，否则返回 false
     */
    public static boolean base64ToFile(String base64String, String outputFilePath) {
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(base64String);
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                fos.write(decodedBytes);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }


}
