import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.api.cyitce.seal2.config.BaseLog;
import com.api.cyitce.seal2.config.eSeal.ESealBaseAuth;
import com.api.cyitce.seal2.config.eSeal.ESealConfig;
import com.api.cyitce.seal2.integration.eSeal.contract.ContractIntegration;
import com.api.cyitce.seal2.integration.eSeal.hrm.CreatUserIntegration;
import com.api.cyitce.seal2.service.eSeal.impl.CreateUserServiceImpl;
import com.api.cyitce.seal2.service.eSeal.impl.SignServiceImpl;
import com.api.cyitce.seal2.util.HmacSh1Util;
import com.api.cyitce.seal2.vo.req.contract.ContractDetailsApiReq;
import com.api.cyitce.seal2.vo.req.contract.addSignerByFile.SignFileApiVO;
import com.api.cyitce.seal2.vo.req.hrm.QueryPersonApiReq;
import com.api.cyitce.seal2.vo.req.pagesign.AddContractSignerVO;
import com.api.cyitce.seal2.vo.req.pagesign.PageSignReq;
import com.api.cyitce.seal2.vo.resp.BaseResp;
import com.api.cyitce.seal2.vo.resp.contract.ContractApiVO;
import com.api.cyitce.seal2.vo.resp.org.PersonVoResp;
import com.api.cyitce.seal2.vo.resp.pagesign.PageSignResp;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: Auth
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-01-16  11:38
 * @Version: 1.0
 */
public class Auth extends BaseLog {
    public static void main(String[] args) {
        Auth a = new Auth();

//        a.real();
//        a.execut();
//        a.queryPerson();
//        a.contractInfo();

        a.ContentSignature("{\"contractId\":\"206591851463971050\"}");
    }

    private static String getFileBase64(File file) {
        try {
//            return Base64.getEncoder().encodeToString(FileReader.create(file).readBytes());
        }catch (Exception e){
            System.out.println("Error reading file: ");
            return null;
        }

        return null;
    }

    public void contractInfo(){
        ContractDetailsApiReq cdap = new ContractDetailsApiReq();
        cdap.setContractId(Long.valueOf("205180743180091417"));

        ContractIntegration ci = new ContractIntegration();
        BaseResp<ContractApiVO> resp = ci.search(cdap);

        info("合同详情：{?}",JSONUtil.toJsonStr(resp));
    }

    public void queryPerson(){
        QueryPersonApiReq qpa = new QueryPersonApiReq();
        qpa.setUserId("2946");

        CreatUserIntegration cut = new CreatUserIntegration();
        BaseResp<PersonVoResp> resp = cut.queryPerson(qpa);

        info("用户信息：{?}",JSONUtil.toJsonStr(resp));
    }

    public void real(){
        String dispalyName = "吴晗";
        String idCardNum = "500230199702458524";
        String mobile = "15696862204";
        String thirdDataId = "2946";

        CreateUserServiceImpl cusi = new CreateUserServiceImpl();
        BaseResp<JSONObject> resp = cusi.createUser(dispalyName,idCardNum,mobile,thirdDataId);

        info("认证结果：{?}",JSONUtil.toJsonStr(resp));
    }

    public void execut(){
        PageSignReq pageSignReq = new PageSignReq();
        pageSignReq.setContractId(Long.valueOf("205180743180091417"));
        AddContractSignerVO addContractSignerVO = new AddContractSignerVO();
        addContractSignerVO.setUserId("M011JRGICPK7FBK");
        addContractSignerVO.setEnterpriseId("M011JRGICPKTWF7");

        SignFileApiVO signFileApiVO = new SignFileApiVO();
        signFileApiVO.setDocId(Long.valueOf("205180744253833242"));

        List<SignFileApiVO> signFiles = new ArrayList<>();
        signFiles.add(signFileApiVO);

        addContractSignerVO.setSignFiles(signFiles);
        pageSignReq.setSigner(addContractSignerVO);

        info(JSONUtil.parseObj(pageSignReq).toString());

        SignServiceImpl ssi = new SignServiceImpl();
//        BaseResp<PageSignResp> resp = ssi.personPageSign(pageSignReq);
        BaseResp<PageSignResp> resp = ssi.orgPageSign(pageSignReq);

        info(JSONUtil.toJsonStr(resp));
    }

    public void ContentSignature(String requestJsonStr){
        ESealConfig config = new ESealBaseAuth().getConfig();;

        // 生成签名，签名秘钥为：secretKey + serviceCode
        String key = config.getSecretKey() + config.getServiceCode();
        String signature = HmacSh1Util.getSignature(requestJsonStr, key);
        // Content-Signature 为固定值 HMAC-SHA1 + 英文空格 + signature
        String ContentSignature = "HMAC-SHA1 " + signature;

        info("Content-Signature: {?}", ContentSignature);
    }
}