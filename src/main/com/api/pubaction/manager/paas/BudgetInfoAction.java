package com.api.pubaction.manager.paas;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.util.ParamUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.cyitce.manager.paasspace.PaasSpaceToken;
import weaver.interfaces.workflow.action.cyitce.util.HttpUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: IncomeAndCostInfoAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-11-28  11:04
 * @Version: 1.0
 */
@Path("BudgetInfoAction")
public class BudgetInfoAction extends BaseBean {
    private String ADDR = "https://cyitdigital.cqcyit.com:10000/gsapi";

    @Path("getBudgetCostSum")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Result getBudgetCostSum(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String projectNum = String.valueOf(p.get("projectNum"));
        String projectId = String.valueOf(p.get("projectId"));
        String companyId = String.valueOf(p.get("companyId"));
        String projectType = String.valueOf(p.get("projectType"));

        StringBuffer params = new StringBuffer();
        params.append("projectNum="+projectNum)
                .append("&")
                .append("projectId="+projectId)
                .append("&")
                .append("companyId="+companyId)
                .append("&")
                .append("projectType="+projectType);

        User user = HrmUserVarify.getUser(request,response);
        PaasSpaceToken paasSpaceToken = new PaasSpaceToken();
        Map headers = new HashMap();
        try {
            String url = ADDR + "/external/oaGetCost?"+params.toString();
            headers.put("Authorization",paasSpaceToken.token(user.getMobile()));
            ReturnData rd = HttpUtil.httpGet(url,headers);
            if(!rd.getSuccess()){
                return MsgResult.error(rd.getMsg());
            }
            return MsgResult.ok(rd.getData());
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
}