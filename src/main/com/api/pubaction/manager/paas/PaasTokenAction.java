package com.api.pubaction.manager.paas;

import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.cyitce.manager.paasspace.PaasSpaceToken;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * @ClassName: Token
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-11-28  10:52
 * @Version: 1.0
 */
@Path("PaasTokenAction")
public class PaasTokenAction extends BaseBean {

    /**
     * @description:获取PAAS平台token信息
     * @author: lijianpan
     * @date: 2024/11/28
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @Path("getToken")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result getToken(@Context HttpServletRequest request, @Context HttpServletResponse response){
        User user = HrmUserVarify.getUser(request,response);
        PaasSpaceToken paasSpaceToken = new PaasSpaceToken();
        try {
            String token = paasSpaceToken.token(user.getMobile());

            return MsgResult.ok(token);
        }catch (Exception e){
            e.printStackTrace();
            return MsgResult.error("token获取失败，e:"+e.getMessage());
        }
    }
}