package com.api.pubaction.manager;


import com.alibaba.fastjson.JSONObject;
import com.weaver.general.BaseBean;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.*;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 请求天擎专用httpclient工具类
 * <AUTHOR>
 * @date 2022/10/13 16:12
 */
public class HttpClientProviderTQ extends BaseBean {

    private  String appId = "IBIoZVc1TK";

    private  String appSecret = "5NmeLCkYtry8cBnp64IOfrx8rduYVhVB";


    /**
     * 请求
     * <AUTHOR>
     * @date 2022/10/13 16:12
     * @param url
     * @param apiREQ
     * @param apiRSP
     * @param contentMap 
     * @return cn.chinaunicom.sdsi.common.HttpSend.ReturnData
     */
    public ReturnData client(String url, String apiREQ, String apiRSP, Object contentMap) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        //设置报文头信息
        Map headMap = getHeadMap();
        //设置报文体
        Map bodyMap = new HashMap<>();
        bodyMap.put(apiREQ, contentMap);
        //封装请求体
        Map map = new HashMap<>();
        map.put("UNI_BSS_HEAD", headMap);
        map.put("UNI_BSS_BODY", bodyMap);
        try {
            HttpPost post = new HttpPost(url);
            post.addHeader("Content-Type", "");
            post.addHeader("Accept-Encoding", "");
            post.addHeader("Accept", "application/json");
			//添加请求体
			if ( map != null && map.size() > 0 ) {
                StringEntity requestentity=new StringEntity(JSONObject.toJSONString(map),Charset.forName("UTF-8"));
                requestentity.setContentType(ContentType.APPLICATION_JSON.toString());
                post.setEntity(requestentity);
			}


            writeLog("与天擎对接日志"+apiREQ+JSONObject.toJSONString(map));
			//创建客户端
            httpClient = createSSLClient();
            //发送请求并接收返回数据
            response = httpClient.execute(post);
            writeLog("与天擎对接响应状态码"
                    +response.getStatusLine().getStatusCode()+"  "
                    +response.getStatusLine().getReasonPhrase()+"    "
                    +response.getStatusLine().getProtocolVersion());
            if (response == null || response.getStatusLine().getStatusCode() != 200) {
                writeLog("与天擎对接响应异常response:{}",response);
                return new ReturnData(false,
                        String.valueOf(response.getStatusLine().getStatusCode()),
                        "与天擎对接响应异常:"+response.getStatusLine().getReasonPhrase());
            }
            //获取response的body部分
            HttpEntity entity = response.getEntity();
            //读取reponse的body部分并转化成字符串
            String result = EntityUtils.toString(entity);
            writeLog("与天擎对接响应日志"+apiRSP+result);
//			Map mapResponse = JSONUtil.toBean(result,Map.class);
//			Map mapResponseHEAD = (Map) mapResponse.get("UNI_BSS_HEAD");
//			Map mapResponseBODY = (Map) mapResponse.get("UNI_BSS_BODY");
//			//00000代表成功，其他代表失败
//            String RESP_CODE = (String)mapResponseHEAD.get("RESP_CODE");
//            String RESP_DESC = (String) mapResponseHEAD.get("RESP_DESC");
            // 调用天擎返回状态
//			if (!"00000".equals(RESP_CODE)) {
//                return new ReturnData(false, RESP_CODE, RESP_DESC);
//            }
			// 业务接口服务返回内容解析
//            Object obj = mapResponseBODY.get(apiRSP);
//            return JSONUtil.toBean(JSONUtil.parseObj(obj),ReturnData.class);
            return null;
        } catch (Exception e) {
//            log.error("请求失败",e);
            return new ReturnData(false,"500","请求失败"+e.getMessage());
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 组装请求头
     * <AUTHOR>
     * @date 2022/10/13 16:24
     * @return java.util.Map
     * APP_IDYsKuvrRKi9TIMESTAMP2022-10-13 16:02:46 998TRANS_ID20221013160246998wLyhtRx51QZVp0qpGHTmg50xr5qp0OUZ
     */
   	public Map getHeadMap() {
        //生成6位随机数，不足补零
        Random ran = new Random();
        int num = ran.nextInt(999999);
        String random =  String.format("%06d", num);
        Date date = new Date();
        //时间戳
        String TIMESTAMP = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS").format(date);
		//序列号
		String TRANS_ID = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(date) + random;
        //拼接APP_ID
		String plainText = "APP_ID" + appId + "TIMESTAMP" +TIMESTAMP + "TRANS_ID" + TRANS_ID + appSecret;
        Map headMap = new HashMap();
		headMap.put("APP_ID", appId);
		headMap.put("TIMESTAMP", TIMESTAMP);
		headMap.put("TRANS_ID", TRANS_ID);
		headMap.put("TOKEN", encryption(plainText));
		return headMap;
	}

    /**
     * MD5加密(32位小写)
     * <AUTHOR>
     * @date 2022/10/13 15:56
     * @param plainText
     * @return java.lang.String
     */
    public String encryption(String plainText) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        md.update(plainText.getBytes());
        byte[] b = md.digest();
        int i;
        StringBuffer buf = new StringBuffer("");
        for (int offset = 0; offset < b.length; offset++) {
            i = b[offset];
            if (i < 0) {
                i += 256;
            }
            if (i < 16) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }
        return buf.toString();
    }


    /**
     * 忽略SSL证书校验的CloseableHttpClient
     * @return
     */
    public CloseableHttpClient createSSLClient(){
        // 创建SSLContext对象，并使用我们指定的信任管理器初始化,信任所有
        X509TrustManager x509mgr = new X509TrustManager() {
            // 该方法检查客户端的证书，若不信任该证书则抛出异常
            @Override
            public void checkClientTrusted(X509Certificate[] xcs, String string) {
            }
            // 该方法检查服务端的证书，若不信任该证书则抛出异常
            @Override
            public void checkServerTrusted(X509Certificate[] xcs, String string) {
            }
            // 返回受信任的X509证书数组。
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };
        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[] { x509mgr }, null);
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession sslSession) {
                    // 对hostname不做验证信任所有
                    return true;
                }
            });
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (NoSuchAlgorithmException e) {
//            log.error(e.getMessage(), e);
        } catch (KeyManagementException e) {
//            log.error(e.getMessage(), e);
        }
        return HttpClients.createDefault();
    }


    public static class ReturnData {
        // 是否成功（true or false）
        private boolean success;
        // 响应码
        private String resultCode;
        // 返回数据信息
        private String resultMessage;
        // 返回接口信息
        private Object result;

        public ReturnData(boolean success,String resultCode,String resultMessage){
            this.success = success;
            this.resultCode = resultCode;
            this.resultMessage = resultMessage;
        }

        public ReturnData(boolean success,String resultCode,String resultMessage,Object result){
            this.success = success;
            this.resultCode = resultCode;
            this.resultMessage = resultMessage;
            this.result = result;
        }
    }
}