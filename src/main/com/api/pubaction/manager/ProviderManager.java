package com.api.pubaction.manager;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import com.weaver.general.BaseBean;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.integration.util.JSONUtil;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * @ClassName: provider
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-12-15  16:22
 * @Version: 1.0
 */
public class ProviderManager extends BaseBean {
    private  String appId = "IBIoZVc1TK";

    private  String appSecret = "5NmeLCkYtry8cBnp64IOfrx8rduYVhVB";

    public String token() {
        String token = "";

        Map map = new HashMap();
        map.put("response_type", "token");
        map.put("client_id", "dzUZNx5Ixa");
        map.put("client_secret", "tWP04rW3s3ZO8odSkryx2d");
        map.put("corp_id", "c1f2a3427d286bece28bdf1654e60cf1");
        map.put("state", "123");
        map.put("scope", "");
        // 天擎测试环境外网地址
        String url = "https://open.chinaunicom.cn:443/api/chinaUnicom/manageCenter/eshop/accessToken/v1";
        String apiREQ = "ACCESS_TOKEN_REQ";
        String apiRSP = "ACCESS_TOKEN_RSP";

        ReturnData rs = getReturnDataSuning(map, apiREQ, apiRSP, url);

        if (rs.getSuccess()) {
            Map obj = (Map)rs.getData();
            token = (String)obj.get("access_token");
        }
        return token;
    }

    public ReturnData getReturnDataSuning(Map<String,Object> p,String apiREQ,String apiRSP,String url) {
        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String info = null;
        JSONObject jsonObject = null;

        //报文附加信息
        Map attachedMap = new HashMap<>();
        attachedMap.put("MEDIA_INFO","");
        //设置报文头信息
        Map headMap = getHeadMap();
        //设置报文体
        Map bodyMap = new HashMap<>();
        bodyMap.put(apiREQ, p);
        //封装请求体
        Map map = new HashMap<>();
        map.put("UNI_BSS_ATTACHED", attachedMap);
        map.put("UNI_BSS_HEAD", headMap);
        map.put("UNI_BSS_BODY", bodyMap);
        try {
            client = HttpClients.createDefault();
            post = new HttpPost(url);
            post.addHeader("Content-Type", "");
            post.addHeader("Accept", "application/json");
            post.addHeader("Accept-Encoding", "");
            //添加请求体
            if ( map != null && map.size() > 0 ) {
                StringEntity requestentity=new StringEntity(JSONObject.toJSONString(map), Charset.forName("UTF-8"));
                requestentity.setContentType(ContentType.APPLICATION_JSON.toString());
                post.setEntity(requestentity);
            }
            writeLog("与天擎对接日志"+apiREQ+JSONObject.toJSONString(map));
            response = client.execute(post);

            writeLog("与天擎对接响应状态码"
                    +response.getStatusLine().getStatusCode()+"  "
                    +response.getStatusLine().getReasonPhrase()+"    "
                    +response.getStatusLine().getProtocolVersion());

            if (response == null || response.getStatusLine().getStatusCode() != 200) {
                writeLog("与天擎对接响应异常response:{}",response);
                return new ReturnData(false,
                        String.valueOf(response.getStatusLine().getStatusCode()),
                        "与天擎对接响应异常:"+response.getStatusLine().getReasonPhrase());
            }

            entity = response.getEntity();
            info = EntityUtils.toString(entity);
            jsonObject = JSONObject.parseObject(info);
            writeLog("与天擎对接响应日志"+info);

            JSONObject mapResponseHEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
            JSONObject mapResponseBODY = jsonObject.getJSONObject("UNI_BSS_BODY");
			//00000代表成功，其他代表失败
            String RESP_CODE = (String)mapResponseHEAD.get("RESP_CODE");
            String RESP_DESC = (String) mapResponseHEAD.get("RESP_DESC");
            //调用天擎返回状态
			if (!"00000".equals(RESP_CODE)) {
                return new ReturnData(false, RESP_CODE, RESP_DESC);
            }

            //业务接口服务返回内容解析
            JSONObject obj = mapResponseBODY.getJSONObject(apiRSP);
            String resultMessage = (String)obj.get("resultMessage");
            String resultCode = (String) obj.get("resultCode");
            Object result = obj.get("result");

            return new ReturnData(true, resultCode, resultMessage,result);
        } catch (Exception e) {
            writeLog("请求失败",e);
            return new ReturnData(false,"500","请求失败"+e.getMessage());
        }
    }

    /**
     * 组装请求头
     * <AUTHOR>
     * @date 2022/10/13 16:24
     * @return java.util.Map
     * APP_IDYsKuvrRKi9TIMESTAMP2022-10-13 16:02:46 998TRANS_ID20221013160246998wLyhtRx51QZVp0qpGHTmg50xr5qp0OUZ
     */
    public Map getHeadMap() {
        //生成6位随机数，不足补零
        Random ran = new Random();
        int num = ran.nextInt(999999);
        String random =  String.format("%06d", num);
        Date date = new Date();
        //时间戳
        String TIMESTAMP = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS").format(date);
        //序列号
        String TRANS_ID = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(date) + random;
        //拼接APP_ID
        String plainText = "APP_ID" + appId + "TIMESTAMP" +TIMESTAMP + "TRANS_ID" + TRANS_ID + appSecret;
        Map headMap = new HashMap();
        headMap.put("APP_ID", appId);
        headMap.put("TIMESTAMP", TIMESTAMP);
        headMap.put("TRANS_ID", TRANS_ID);
        headMap.put("TOKEN", encryption(plainText));
        return headMap;
    }

    /**
     * MD5加密(32位小写)
     * <AUTHOR>
     * @date 2022/10/13 15:56
     * @param plainText
     * @return java.lang.String
     */
    public String encryption(String plainText) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        md.update(plainText.getBytes());
        byte[] b = md.digest();
        int i;
        StringBuffer buf = new StringBuffer("");
        for (int offset = 0; offset < b.length; offset++) {
            i = b[offset];
            if (i < 0) {
                i += 256;
            }
            if (i < 16) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }
        return buf.toString();
    }
}