package com.api.pubaction.web;

import com.alibaba.fastjson.JSONArray;
import com.api.pubaction.config.DocSys;
import com.api.pubaction.service.impl.*;
import com.api.pubaction.util.CookieUtil;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.*;
import java.util.*;

@Path("/customerWorkFlow")
public class docsManagerActuon {
    BaseBean log = new BaseBean();

    /**
     * 模糊查询表单字段
     */
    @GET
    @Path("getFieldList")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getFieldName(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);

        String formid = (String)p.get("formid");
        String fieldName = (String)p.get("fieldName");

        JSONArray jsonArray = new JSONArray();

        try {
            jsonArray = ServiceUtil.getService(CoustomerFieldServiceImpl.class).getFieldList(fieldName,formid);
            return MsgResult.ok(jsonArray);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * 获取文件管理系统 token
     */
    @GET
    @Path("getToken")
    public String getToken(@Context HttpServletRequest request, @Context HttpServletResponse response){
        User user = HrmUserVarify.getUser(request, response);

        try {
            return CookieUtil.getToken(user.getLoginid(),user.getLastname());
        }catch (Exception e){
            return "";
        }
    }

    /**
     * 获取文件管理系统ip
     */
    @GET
    @Path("getUrl")
    public String getUrl(@Context HttpServletRequest request, @Context HttpServletResponse response){
        return DocSys.url;
    }

    /**
     * 获取根据表单formid获取自定义浏览按钮字段id
     */
    @GET
    @Path("getFieldIdList")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getFieldIdList(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String field = "";
        String type = (String)p.get("type");

        try {
            if("browser".equals(type)){//formid获取自定义浏览按钮字段id
                field = ServiceUtil.getService(CustomBrowserServiceImpl.class).getFieldIdList(p);
            }
            return MsgResult.ok(field);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * 获取一级项目信息
     */
    @GET
    @Path("getProjectInfo")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getProjectInfo(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        Map<String,String> map;
        String id = (String)p.get("id");

        try {
            map = ServiceUtil.getService(ProcjectInfoServiceImpl.class).getProjectInfo(id);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:根据流程请求id获取流程信息
     * @author: lijianpan
     * @date: 2023/2/15
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getWorkflowInfo")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getWorkflowInfo(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        Map<String,String> map;
        User user = HrmUserVarify.getUser(request,response);

        try {
            map = ServiceUtil.getService(CoustomerFieldServiceImpl.class).getWorkflowInfo(p);
            map.put("user",user.getLastname());
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:根据二级项目id获取二级项目信息
     * @author: lijianpan
     * @date: 2023/2/15
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getProjectTwoInfo")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getProjectTwoInfo(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        Map<String,String> map;
        String id = (String)p.get("id");

        try {
            map = ServiceUtil.getService(CoustomerFieldServiceImpl.class).getProTwoInfo(id);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:更新模块布局的代码块
     * @author: lijianpan
     * @date: 2023/3/16
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("modeHtmlLayoutScriptUpdate")
    @Produces(MediaType.APPLICATION_JSON)
    public Result modeHtmlLayoutScriptUpdate(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {
        String jsSrc = "/u01/ecology/workflow/coustom/customFile/mode/file.js";
        String cssSrc = "/u01/ecology/workflow/coustom/customFile/mode/file.css";
        String error = "";
        //模块id
        Map<String, Object> p = ParamUtil.request2Map(request);
        String ids = Util.null2String((String)p.get("ids"));
        Properties prop = null;
        try {
            if("".equals(ids)){
                prop = new Properties();
                prop.load(new FileInputStream(GCONST.getRootPath() + "WEB-INF/prop/pubaction.properties"));
                ids = prop.getProperty("filesystem.mode.id");
                log.writeLog("filesystem.mode.id="+ids);
                String cus_jsPath = prop.getProperty("filesystem.mode.js.path");
                log.writeLog("filesystem.mode.js.path="+cus_jsPath);
                if(!"".equals(Util.null2String(cus_jsPath))){
                    jsSrc = cus_jsPath;
                }
                String cus_cssPath = prop.getProperty("filesystem.mode.css.path");
                if(!"".equals(Util.null2String(cus_cssPath))){
                    cssSrc = cus_cssPath;
                }
                log.writeLog("filesystem.mode.css.path="+cus_cssPath);
            }

            boolean boo = ServiceUtil.getService(ModeHtmlLayoutUpdateServiceImpl.class).scriptUpdate(jsSrc,cssSrc,ids);

            if(boo){
                return MsgResult.ok();
            }
        } catch (Exception e) {
            e.printStackTrace();
            error = e.getMessage();
        }

        return MsgResult.error(error);
    }

    /**
     * @description:项目获取标签
     * @author: lijianpan
     * @date: 2023/4/3
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getProTag")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getProTag(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        Map<String,String> map;
        String pid = (String)p.get("pid");
        String error;

        try {
            Map<String ,String > res = ServiceUtil.getService(CoustomerFieldServiceImpl.class).getProTagByPid(pid);
            return MsgResult.ok(res);
        } catch (Exception e) {
            e.printStackTrace();
            error = e.getMessage();
            return MsgResult.error(error);
        }
    }

//
//    @GET
//    @Path("test")
//    @Produces(MediaType.APPLICATION_JSON)
//    public Result test(@Context HttpServletRequest request, @Context HttpServletResponse response){
//        RecordSet rs = new RecordSet();
//        RecordSet rs2 = new RecordSet();
//        List<Map<String,String>> list = new ArrayList<>();
//        rs.execute("select xmbh,xmfzr,cyry FROM uf_yjxmlxjm where zt=5");
//        while (rs.next()){
//            Map<String,String > map = new HashMap<>();
//            map.put("code",rs.getString(1));
//
//            String id = rs.getString(2);
//            if(!"".equals(Util.null2String(id))){
//                String[] idss = id.split(",");
//                String loginidss = "";
//                for (String s:idss){
//                    rs2.execute("select loginid from hrmresource where id="+s);
//                    rs2.next();
//                    loginidss+=rs2.getString(1)+",";
//                }
//                if(loginidss.length()>0){
//                    loginidss=loginidss.substring(0,loginidss.length()-1);
//                }
//                map.put("fzr",loginidss);
//
//                String idsStr = rs.getString(3);
//                String loginids = "";
//                if (!"".equals(Util.null2String(idsStr))){
//                    String[] ids = idsStr.split(",");
//                    for (String s:ids){
//                        rs2.execute("select loginid from hrmresource where id="+s);
//                        rs2.next();
//                        loginids+=rs2.getString(1)+",";
//                    }
//                    if(loginids.length()>0){
//                        loginids=loginids.substring(0,loginids.length()-1);
//                    }
//                }
//                map.put("cyry",loginids);
//
//                list.add(map);
//                System.out.println(map.toString());
//            }
//        }
//
//        return MsgResult.ok(list);
//    }

    //项目同步
    @GET
    @Path("projectInsert")
    @Produces(MediaType.APPLICATION_JSON)
    public Result projectInsert(@Context HttpServletRequest request, @Context HttpServletResponse response) {

        try {
            Result boo = ServiceUtil.getService(JobServiceImpl.class).projectInsert();
            return boo;
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            log.writeLog(sw.toString());
            return MsgResult.error(e.getMessage());
        }
    }
    //文件所属一级项目同步
    @GET
    @Path("updataFileProject")
    @Produces(MediaType.APPLICATION_JSON)
    public Result updataFileProject(@Context HttpServletRequest request, @Context HttpServletResponse response){
        try {
            Result boo = ServiceUtil.getService(JobServiceImpl.class).updataFileProject();
            return boo;
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            log.writeLog(sw.toString());
            return MsgResult.error(e.getMessage());
        }
    }

    //文件的标签同步
    @GET
    @Path("updataFileTag")
    @Produces(MediaType.APPLICATION_JSON)
    public Result updataFileTag(@Context HttpServletRequest request, @Context HttpServletResponse response){
        try {
            Result boo = ServiceUtil.getService(JobServiceImpl.class).updataFileTag();
            return boo;
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            log.writeLog(sw.toString());
            return MsgResult.error(e.getMessage());
        }
    }
}
