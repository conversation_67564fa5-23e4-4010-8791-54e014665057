package com.api.pubaction.web;

import com.api.pubaction.service.impl.WorkFlowServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: modeAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-15  17:24
 * @Version: 1.0
 */
@Path("modeAction")
public class modeAction {
    /**
     * @description:查询建模表数据
     * @author: lijianpan
     * @date: 2024/7/11
     * @param: tableName 流程表名称
     * @param: fields 查询的字段
     * @param: sqlwhere where条件
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @Path("modeInfoBySelect")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Result modeInfoBySelect(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String tableName = String.valueOf(p.get("tableName"));
        String fields = String.valueOf(p.get("fields"));
        String sqlwhere = String.valueOf(p.get("sqlwhere"));
        List<Map<String,String>> list = null;
        try {
            list = ServiceUtil.getService(WorkFlowServiceImpl.class).modeInfoBySelect(tableName,fields,sqlwhere);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }

        return MsgResult.ok(list);
    }
}