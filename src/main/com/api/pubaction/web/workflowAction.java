package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.SupplierServiceImpl;
import com.api.pubaction.service.impl.WorkFlowServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: 工作流接口
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-07-10  15:03
 * @Version: 1.0
 */
@Path("workflowAction")
public class workflowAction extends BaseBean {
    /**
     * @description:查询流程表数据
     * @author: lijianpan
     * @date: 2024/7/11
     * @param: tableName 流程表名称
     * @param: fields 查询的字段
     * @param: sqlwhere where条件
     * @param: currentnodetype 流程节点类型（创建，提交，审核，归档）
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @Path("workflowInfoBySelect")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Result workflowInfoBySelect(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String tableName = String.valueOf(p.get("tableName"));
        String fields = String.valueOf(p.get("fields"));
        String sqlwhere = String.valueOf(p.get("sqlwhere"));
        String currentnodetype = String.valueOf(p.get("currentnodetype"));
        List<Map<String,String>> list = null;
        try {
            list = ServiceUtil.getService(WorkFlowServiceImpl.class).workflowInfoBySelect(tableName,fields,sqlwhere,currentnodetype);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }

        return MsgResult.ok(list);
    }
}