package com.api.pubaction.web;

import cn.hutool.json.JSONUtil;
import org.springframework.util.CollectionUtils;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.cyitce.job.fbt.FBTJob;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.Data;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.STYProjectDetailInfo;
import weaver.interfaces.workflow.action.cyitce.job.fbt.service.new1.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static weaver.interfaces.workflow.action.cyitce.job.fbt.service.FlowUtil.queryCompanyIdsByProjectIds;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9 15:21
 * @describe
 */
@Path("/fbt/request/web")
public class RequestWeb {

    /**
     * 接受消费账单的请求
     * 工程0(明细表1)  集团1 树藤2 监理3 华来4
     *
     * @return
     */
    @POST
    @Path("/cszlc")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    // , @Context HttpServletRequest request, @Context HttpServletResponse response
    public Response cszlc(List<Integer> data, @Context HttpServletRequest request, @Context HttpServletResponse response) {
        if (CollectionUtils.isEmpty(data)) {
            return createJsonResponse(500, "请勾选要发起的台账", null);
        }
        BaseBean log = new BaseBean();
        List<String> companyIds = queryCompanyIdsByProjectIds(data);
        if (companyIds.size() >= 2) {
            return createJsonResponse(500, "勾选错误(已勾选多个公司台账)，请勾选同一公司下的台账后再发起！", null);
        }
        if (companyIds.size() < 1) {
            return createJsonResponse(500, "未查询到勾选台账的公司，请联系管理员！", null);
        }
        log.writeLog("拿到请求的id: " + data.toString());
        List<Map<String, String>> list = new FBTJob().getProjectInfoByIds(data);
        log.writeLog("查询到的数据: " + list.size());
        newAbstractDetailInfoGenerator detailInfoImp = null;
        User user = HrmUserVarify.getUser(request, response);
        int userId = user.getUID();
        // int userId = 3810;
        log.writeLog("拿到发起人的id: " + userId);

        int companyId = Integer.parseInt(companyIds.get(0));
        log.writeLog("拿到公司的id: " + companyId);
        // int userId = 3810;
        switch (companyId) {
            case 6:
                detailInfoImp = new newGCgenerateDetailInfoImpl();
                break;
            case 16:
                detailInfoImp = new newHLgenerateDetailInfoImpl();
                break;
            case 14:
                detailInfoImp = new newJLgenerateDetailInfoImpl();
                break;
            case 11:
                detailInfoImp = new newJTgenerateDetailInfoImpl();
                break;
            case 24:
                detailInfoImp = new newSTYgenerateDetailInfoImpl();
                break;
            default:
                log.writeLog("不支持的公司代码: " + companyId);
                throw new IllegalArgumentException("不支持的公司代码: " + companyId);
        }
        String requestId = "";
        // detailInfoImpl.generateDetailInfo(convertToMapList(data));
        List<STYProjectDetailInfo> infoList = detailInfoImp.generateDetailInfo(list);
        log.writeLog("infoList数据  : " + JSONUtil.toJsonStr(infoList));
        if (CollectionUtils.isEmpty(infoList)) {
            log.writeLog("没有对应公司的数据");
            return createJsonResponse(500, "没有对应公司的数据", null);
        }
        log.writeLog("开始执行请求: ");
        try {
            requestId = detailInfoImp.generateFlowData(infoList, String.valueOf(userId), String.valueOf(companyId), 0);
        } catch (Exception e) {
            log.writeLog("执行出错: ");
            throw new RuntimeException(e);
        }
        log.writeLog("开始执行请求成功: ");
        // detailInfoImpl.generateDetailInfo()
        return createJsonResponse(200, "请求成功", requestId);
    }

    // /**
    //  * 根据公司代码获取对应的项目详情实现类
    //  * GC对应6，HL对应14，JL对应15，JT对应16，STY对应24
    //  *
    //  * @param companyCode 公司代码
    //  * @return 对应的项目详情实现类
    //  */
    // public static generateDetailInfo getDetailInfoImplementation(int companyCode) {
    //
    // }

    public static Response createJsonResponse(int code, String message, String data) {
        String jsonResponse = "{\"code\": " + code + ", \"message\": \"" + message + "\",\"data\": \"" + data + "\"}";
        return Response.ok().header("Access-Control-Allow-Origin", "*").header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE").header("Access-Control-Allow-Headers", "Content-Type, Authorization").entity(jsonResponse).type(MediaType.APPLICATION_JSON).build();
    }

    public static List<Map<String, String>> convertToMapList(List<Data> dataList) {
        List<Map<String, String>> resultList = new ArrayList<>();

        if (dataList == null) {
            return resultList;
        }

        for (Data data : dataList) {
            Map<String, String> map = new HashMap<>();

            // 获取所有声明的字段（包括私有字段）
            Field[] fields = data.getClass().getDeclaredFields();

            for (Field field : fields) {
                // 设置可访问私有字段
                field.setAccessible(true);

                try {
                    // 获取字段名和对应的值
                    String fieldName = field.getName();
                    Object value = field.get(data);

                    // 将值转换为字符串（如果为null则存储为null）
                    map.put(fieldName, value != null ? value.toString() : "");
                } catch (IllegalAccessException e) {
                    // 处理反射访问异常
                    System.err.println("Error accessing field: " + field.getName() + " - " + e.getMessage());
                }
            }

            resultList.add(map);
        }

        return resultList;
    }

    public static void main(String[] args) {
        // new RequestWeb().cszlc(new ArrayList<Integer>() {{
        //     add(205);
        //     add(148);
        //     // add(146);
        //     // add(150);
        //     // add(153);
        //     // add(157);
        //     // add(158);
        //     // add(159);
        //     // add(160);
        //     // add(161);
        //     // add(205);
        // }});
        // List<String> strings = queryCompanyIdsByProjectIds(new ArrayList<Integer>() {{
        //     add(1461);
        //     // add(150);
        //     // add(153);
        //     // add(157);
        //     // add(158);
        //     // add(159);
        //     // add(160);
        //     // add(161);
        //     // add(205);
        //     // add(2);
        //     // add(3);
        //     // add(5);
        // }});
        // System.out.println("strings = " + strings);
    }
}
