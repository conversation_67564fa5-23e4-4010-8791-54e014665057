package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.entity.Supplier;
import com.api.pubaction.service.impl.SupplierServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


/**
 * @ClassName: supplierAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-21  11:08
 * @Version: 1.0
 */
@Path("supplierOperate")
public class supplierAction extends BaseBean {

    //添加供应商银行信息
    @Path("saveBankInfo")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result insertBankInfo(@Context HttpServletRequest request, @Context HttpServletResponse response, Supplier data){
        try {
            ServiceUtil.getService(SupplierServiceImpl.class).addBankInfo(data);

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
    //修改供应商银行信息
    @Path("updateBankInfo")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result updateBankInfo(@Context HttpServletRequest request, @Context HttpServletResponse response, Supplier data){
        try {
            ServiceUtil.getService(SupplierServiceImpl.class).updataBankInfo(data);

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
    //添加供应商资质
    @Path("saveQualificationInfo")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result addQualificationInfo(@Context HttpServletRequest request, @Context HttpServletResponse response, Supplier data){
        try {
            ServiceUtil.getService(SupplierServiceImpl.class).addQualificationInfo(data);

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
    //修改供应商资质
    @Path("updataQualificationInfo")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result updataQualificationInfo(@Context HttpServletRequest request, @Context HttpServletResponse response, Supplier data){
        try {
            ServiceUtil.getService(SupplierServiceImpl.class).updataQualificationInfo(data);

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
    //供应商信息修改
    @Path("updataSupplierInfo")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Result updataSupplierInfo(@Context HttpServletRequest request, @Context HttpServletResponse response, Supplier data){
        try {
            ServiceUtil.getService(SupplierServiceImpl.class).updataSupplierInfo(data);

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    //OA附件上传到供应商平台并返回附件id
    @Path("getSupplierSystemFileId")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSupplierSystemFileId(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String ids = (String) p.get("ids");

        ReturnData rd = null;
        try {
            rd = ServiceUtil.getService(SupplierServiceImpl.class).upLoadSupplierSystemFile(ids);

            if(!rd.getSuccess()){
                throw new Exception(rd.getMsg());
            }

        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }

        return MsgResult.ok(rd.getData());
    }
}