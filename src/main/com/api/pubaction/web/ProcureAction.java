package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.SupplierServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


/**
 * @ClassName: 采购需求
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-07-30  14:46
 * @Version: 1.0
 */
@Path("procure")
public class ProcureAction {
    BaseBean log = new BaseBean();

    /**
     * 供应商报名
     */
    @GET
    @Path("supplierApplication")
    @Produces(MediaType.APPLICATION_JSON)
    public Result supplierApplication(@Context HttpServletRequest request, @Context HttpServletResponse response){
        User user = HrmUserVarify.getUser(request,response);
        Map<String, Object> p = ParamUtil.request2Map(request);
        p.put("user",user);

        try {
            ReturnData rd = ServiceUtil.getService(SupplierServiceImpl.class).procureSupplierApplication(p);

            if(!rd.getSuccess()){
                return MsgResult.error(rd.getMsg());
            }

            return MsgResult.ok();
        }catch (Exception e){
            return MsgResult.error(e);
        }
    }
}