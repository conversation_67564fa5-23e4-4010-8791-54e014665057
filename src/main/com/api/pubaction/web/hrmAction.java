package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.KQServiceImpl;
import com.api.pubaction.service.impl.PasswordServiceImpl;
import com.api.pubaction.service.impl.UserInfoServerImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.webbeans.In;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: hrmAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-11-23  19:56
 * @Version: 1.0
 */

@Path("/hrmAction")
public class hrmAction {
    /**
     * @description:获取人员信息（参数：loginid）
     * @author: lijianpan
     * @date: 2022/11/23
     * @param: [request, response]
     * @return: java.lang.String
     **/
    @GET
    @Path("usrByLoginid")
    @Produces(MediaType.APPLICATION_JSON)
    public Result userInfoByLoginid(@Context HttpServletRequest request, @Context HttpServletResponse response){
        String result = "";
        try {
            Map<String, Object> p = ParamUtil.request2Map(request);
            String loginid = Util.null2String(p.get("loginid"));
            result = ServiceUtil.getService(UserInfoServerImpl.class).GetMobil(loginid);

            return MsgResult.ok(result);
        }catch (Exception e){
            e.printStackTrace();
            return MsgResult.error(e.getMessage());
        }
    }


    /**
     * @description:获取考勤公众节假日
     * @author: lijianpan
     * @date: 2023/1/28
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getKQHoliday")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getKQHoliday(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String year = Util.null2String(p.get("year"));
        String type = Util.null2String(p.get("type"));
        List<String> list = new ArrayList<>();

        try {
            list = ServiceUtil.getService(KQServiceImpl.class).getHoliday(year,type);
            return MsgResult.ok(list);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:获取人员信息（param：id）
     * @author: lijianpan
     * @date: 2023/2/2
     * @param: []
     * @return: java.lang.String
     **/
    @GET
    @Path("getHrmInfoById")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getHrmInfoById(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        int id = -1;

        if(Util.null2String(p.get("id"))!=""){
            id = Integer.parseInt((String)p.get("id"));
        }

        User u = new User(id);

        return MsgResult.ok(u);
    }

    /**
     * @description:获取人员信息（param：loginid）
     * @author: lijianpan
     * @date: 2023/2/2
     * @param: []
     * @return: java.lang.String
     **/
    @GET
    @Path("getHrmInfoByLoginid")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getHrmInfoByLoginid(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String loginid;


        try {
            if(Util.null2String(p.get("loginid"))==""){
                return MsgResult.error("参数loginid 不能为空");

            }
            loginid = (String)p.get("loginid");

            ReturnData r = ServiceUtil.getService(UserInfoServerImpl.class).GetUserInfo(loginid);

            if(!r.getSuccess()){
                MsgResult.error(r.getMsg());
            }
            return MsgResult.ok(r.getData());
        }catch (Exception e){
            return  MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:验证用户密码是否正确，并返回用户手机号
     * @author: lijianpan
     * @date: 2024/11/19
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("isPasswordAndPhone")
    @Produces(MediaType.APPLICATION_JSON)
    public Result isPasswordAndPhone(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        boolean result;

        try {
            String loginid = (String) p.get("loginid");
            String password = (String) p.get("password");

            result = ServiceUtil.getService(PasswordServiceImpl.class).isPasswordByLoginid(loginid,password);
            if(result){
                ReturnData rd = ServiceUtil.getService(UserInfoServerImpl.class).GetUserInfo(loginid);
                if(!rd.getSuccess()){
                    return MsgResult.error(rd.getMsg());
                }

                User user = (User)rd.getData();
                return MsgResult.ok(user.getMobile());
            }else {
                return MsgResult.error("密码错误");
            }
        }catch (Exception e){
            e.printStackTrace();
            return MsgResult.error(e.getMessage());
        }
    }
}