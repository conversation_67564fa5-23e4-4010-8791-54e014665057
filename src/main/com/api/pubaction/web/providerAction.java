package com.api.pubaction.web;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.manager.ProviderManager;

import com.engine.common.util.ParamUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: providerAction
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-12-16  19:15
 * @Version: 1.0
 */
@Path("/provider")
public class providerAction {
    ProviderManager pm = new ProviderManager();

    //测试环境入口
    String tokent_url = "https://open1.chinaunicom.cn/api/chinaUnicom/manageCenter/eshop/accessToken/v1";
    String api_url = "https://open1.chinaunicom.cn/api/chinaUnicom/manageCenter/eshop/providerApi/v1";
    //生成环境入口
//    String tokent_url = "https://open.chinaunicom.cn:443/api/chinaUnicom/manageCenter/eshop/accessToken/v1";
//    String api_url = "https://open.chinaunicom.cn:443/api/chinaUnicom/manageCenter/eshop/providerApi/v1";

    String apiRSP = "PROVIDER_API_REQ";
    String apiREQ = "PROVIDER_API_RSP";

    /**
     * @description:获取 Access token（24 小时内调用一次）
     * @author: lijianpan
     * @date: 2022/12/15
     * @param:
     * @return:
     **/
    @GET
    @Path("/getToken")
    @Produces(MediaType.APPLICATION_JSON)
    public ReturnData token(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String,Object> p = ParamUtil.request2Map(request);
        p.remove("param_ip");
        p.remove("request_header_user_agent");

        return pm.getReturnDataSuning(p,"ACCESS_TOKEN_REQ","ACCESS_TOKEN_RSP",tokent_url);
    }

    /**
     * @description:api接口
     * @author: lijianpan
     * @date: 2022/12/15
     * @param:
     * @return:
     **/
    @POST
    @Path("/api")
    @Produces(MediaType.APPLICATION_JSON)
    public ReturnData api(@Context HttpServletRequest request, @Context HttpServletResponse response,JSONObject data) {
        return pm.getReturnDataSuning(data.getInnerMap(),apiRSP,apiREQ,api_url);
    }

    /**
     * @description:api接口
     * @author: lijianpan
     * @date: 2022/12/15
     * @param:
     * @return:
     **/
    @GET
    @Path("/wl")
    @Produces(MediaType.APPLICATION_JSON)
    public ReturnData wl(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String,Object> p = ParamUtil.request2Map(request);
        p.remove("param_ip");
        p.remove("request_header_user_agent");

        List<Map> list = new ArrayList<>();
        Map orderTrack = new HashMap();
        orderTrack.put("msgTime","2022-12-12 12:12:12");
        orderTrack.put("content","订单已发货");
        list.add(orderTrack);

        Map map = new HashMap();
        map.put("sendOrderNo",p.get("sendOrderNo"));
        map.put("orderTrack",list);

        return new ReturnData(true,"","",map);
    }
}