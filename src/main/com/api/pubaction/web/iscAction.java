package com.api.pubaction.web;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.RequestTableField;
import com.api.pubaction.service.impl.OneProjectModeAuthorityServiceImpl;
import com.api.pubaction.service.impl.PasswordServiceImpl;
import com.api.pubaction.service.impl.ProcjectInfoServiceImpl;
import com.api.pubaction.service.impl.TableFieldUpdateServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

@Path("/project")
public class iscAction {
    BaseBean log = new BaseBean();


    /**
     * 根据人员权限获取项目
     */
    @GET
    @Path("list")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getOrderInfo(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);

        try {
            List<Integer> project = ServiceUtil.getService(OneProjectModeAuthorityServiceImpl.class).getIdOfList((String)p.get("loginid"));
            return MsgResult.ok(project);
        }catch (Exception e){
            return MsgResult.error(e);
        }
    }

    /**
     * 判断用户id的密码是否正确
     */
    @GET
    @Path("isPassword")
    @Produces(MediaType.APPLICATION_JSON)
    public String isPassword(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String result;

        try {
            int userid = Integer.parseInt((String) p.get("userid"));
            String password = (String) p.get("password");

            result = ServiceUtil.getService(PasswordServiceImpl.class).isWeakPassword(userid,password);
            return result;
        }catch (Exception e){
            e.printStackTrace();
            return "false";
        }
    }

    /**
     * 判断账号的密码是否正确
     */
    @GET
    @Path("isPasswordByLoginid")
    @Produces(MediaType.APPLICATION_JSON)
    public Result isPasswordByLoginid(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        boolean result;

        try {
            String loginid = (String) p.get("loginid");
            String password = (String) p.get("password");

            result = ServiceUtil.getService(PasswordServiceImpl.class).isPasswordByLoginid(loginid,password);
            return MsgResult.ok(result);
        }catch (Exception e){
            e.printStackTrace();
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:更新表单字段数据
     * @author: lijianpan
     * @date: 2023/5/11
     * @param: [request, response, data]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("updateTableField")
    @Produces(MediaType.APPLICATION_JSON)
    public Result updateTableField(@Context HttpServletRequest request, @Context HttpServletResponse response, RequestTableField data){
        try {
            ServiceUtil.getService(TableFieldUpdateServiceImpl.class).updateTableField(data);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
        return MsgResult.ok();
    }

    /**
     * @description:模糊查询投标项目编号
     * @author: lijianpan
     * @date: 2023/10/17
     * @param: name 项目名称
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getAllCompanyProjectCode")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getAllCompanyProjectCode(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        List<Map> mapList = null;

        try {
            String name = (String) p.get("name");
            mapList = ServiceUtil.getService(ProcjectInfoServiceImpl.class).getAllCompanyProjectCode(name);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
        return MsgResult.ok(mapList);
    }
}
