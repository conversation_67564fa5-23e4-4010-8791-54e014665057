package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.TokenService;
import com.api.pubaction.service.impl.TokenServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @ClassName: token
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-13  10:55
 * @Version: 1.0
 */
@Path("/pubaction/token")
public class token extends BaseBean {
    /**
     * @description:appid注册
     * @author: lijianpan
     * @date: 2024/9/13
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("regist")
    @Produces(MediaType.APPLICATION_JSON)
    public Result regist(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        try {
            ReturnData r = ServiceUtil.getService(TokenServiceImpl.class).regist(p);

            if(!r.getSuccess()){
                return MsgResult.error(r.getMsg());
            }
            return MsgResult.ok(r);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:获取appid的公钥
     * @author: lijianpan
     * @date: 2024/9/13
     * @param:
     * @return:
     **/
    @POST
    @Path("publicKey")
    @Produces(MediaType.APPLICATION_JSON)
    public Result publicKey(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        try {
            ReturnData r = ServiceUtil.getService(TokenServiceImpl.class).publicKey(p);
            if(!r.getSuccess()){
                return MsgResult.error(r.getMsg());
            }
            return MsgResult.ok(r);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:对appid的密钥加密
     * @author: lijianpan
     * @date: 2024/9/13
     * @param:
     * @return:
     **/
    @POST
    @Path("encryptSecret")
    @Produces(MediaType.APPLICATION_JSON)
    public Result encryptSecret(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        try {
            ReturnData r = ServiceUtil.getService(TokenServiceImpl.class).encrypt(p,0);
            if(!r.getSuccess()){
                return MsgResult.error(r.getMsg());
            }
            return MsgResult.ok(r);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:使用appid的公钥对userid加密
     * @author: lijianpan
     * @date: 2024/9/13
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("encryptUserid")
    @Produces(MediaType.APPLICATION_JSON)
    public Result encryptUserid(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        try {
            ReturnData r = ServiceUtil.getService(TokenServiceImpl.class).encrypt(p,1);
            if(!r.getSuccess()){
                return MsgResult.error(r.getMsg());
            }
            return MsgResult.ok(r);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }
}