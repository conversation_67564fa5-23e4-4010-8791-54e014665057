package com.api.pubaction.web;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.DeliverProjectStatusServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @ClassName: 交付项目验收和审定
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-15  17:15
 * @Version: 1.0
 */
@Path("deliverProCheckAndAuth")
public class deliverProCheckAndAuth {
    /**
     * @description:查看二级项目目前已备案的所有交付项目是否完成内部验收和审计
     * @author: lijianpan
     * @date: 2024/10/15
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @Path("isCheckAndAuthByAlldeliverPro")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Result isCheckAndAuthByAlldeliverPro(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String secondProjectId = (String) p.get("secondProjectId");

        try {
            ReturnData rd = ServiceUtil.getService(DeliverProjectStatusServiceImpl.class).isAuditBySecondProject(secondProjectId);

            if(!rd.getSuccess()){
                return MsgResult.error(rd.getMsg());
            }
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
        return MsgResult.ok();
    }
}