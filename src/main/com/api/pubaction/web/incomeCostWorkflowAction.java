package com.api.pubaction.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.impl.IncomeCostServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.core.exception.ECException;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import weaver.general.Util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: incomeCostWorkflowAction
 * @Description: 项目收入成本确认流程
 * @Author: lijianpan
 * @CreateTime: 2023-02-21  15:21
 * @Version: 1.0
 */
@Path("/incomeCost")
public class incomeCostWorkflowAction {

    /**
     * @description:二级项目成本调整金额差值
     * @author: lijianpan
     * @date: 2023/2/21
     * @param: [proTwoId]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getCostMath")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getHistoryCost(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String proTwoId = (String)p.get("proTwoId");
        String companyId = (String)p.get("companyId");
        Map map;

        try {
            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getCostMath(proTwoId,companyId);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:二级项目累计确认成本信息
     * @author: lijianpan
     * @date: 2023/2/21
     * @param: [proTwoId]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getPreCost")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPreCost(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> p = ParamUtil.request2Map(request);
        String proTwoId = (String)p.get("proTwoId");
        String companyId = (String)p.get("companyId");
        Map map;

        try {
            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getCostMath(proTwoId,companyId);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    //获取当前二级项目确认状态
    @GET
    @Path("getConfirmStatus")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getTwoProjectConfirmStatus(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String twoProjectId = (String)p.get("twoProjectId");
        String isFinalStatusStr = (String)p.get("isFinalStatus");
        boolean isFinalStatus = false;
        Map map;

        try {
            if(!"".equals(Util.null2String(isFinalStatusStr))){
                if("true".equals(isFinalStatusStr)){
                    isFinalStatus = true;
                }
            }

            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getConfirmStatus(twoProjectId,isFinalStatus);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:U8二级项目5大类成本金额
     * @author: lijianpan
     * @date: 2023/11/22
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getSecondProjectCost")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSecondProjectCost(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String secondProjectCode = (String)p.get("secondProjectCode");
        String companyId = (String)p.get("companyId");
        List<Map> maps;

        try {
            if("".equals(secondProjectCode)||secondProjectCode==null){
                new Exception("二级项目编号不能为空！");
            }
            JSONArray jsonArray = new JSONArray();
            JSONObject object = new JSONObject();
            object.put("secondProjectCode",secondProjectCode);
            object.put("companyId",companyId);
            jsonArray.add(object);

            maps = ServiceUtil.getService(IncomeCostServiceImpl.class).getU8SecondProjectCost(jsonArray);
            return MsgResult.ok(maps);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:U8二级项目5大类成本金额
     * @author: lijianpan
     * @date: 2023/11/22
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("getSecondProjectCost")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSecondProjectCost(@Context HttpServletRequest request, @Context HttpServletResponse response,JSONObject data){
        JSONArray secondProjectCodes = data.getJSONArray("selectParam");
        List<Map> maps;

        try {
            if("".equals(secondProjectCodes)||secondProjectCodes==null){
                new Exception("二级项目编号不能为空！");
            }

            maps = ServiceUtil.getService(IncomeCostServiceImpl.class).getU8SecondProjectCost(secondProjectCodes);
            return MsgResult.ok(maps);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:U8多个二级项目5大类成本金额合计
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("getSecondProjectCostSum")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSecondProjectCostSum(@Context HttpServletRequest request, @Context HttpServletResponse response, JSONObject data){
        JSONArray secondProjectCodes = data.getJSONArray("selectParam");
        Map map;

        try {
            if("".equals(secondProjectCodes)||secondProjectCodes==null){
                new Exception("二级项目编号不能为空！");
            }

            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getU8SecondProjectCostSum(secondProjectCodes);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:U8多个二级项目5大类成本金额合计
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @POST
    @Path("getSecondProjectCostSum2")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSecondProjectCostSum2(@Context HttpServletRequest request, @Context HttpServletResponse response,JSONObject data){
        JSONArray secondProjectCodes = data.getJSONArray("selectParam");
        Map map;

        try {
            if("".equals(secondProjectCodes)||secondProjectCodes==null){
                new Exception("二级项目编号不能为空！");
            }

            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getU8SecondProjectCostSum(secondProjectCodes);
            return MsgResult.ok(map);
        }catch (Exception e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:获取流程信息及人员分部
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: []
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("getWorkFlowInfoAndUserInfo")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getWorkFlowInfoAndUserInfo(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String companyid = (String)p.get("companyid");
        String userid = (String)p.get("userid");
        Map map;

        try {
            map = ServiceUtil.getService(IncomeCostServiceImpl.class).getWorkFlowAttr(companyid,userid);

            return MsgResult.ok(map);
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:二级项目决算校验
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("isSecondProjectReimburse")
    @Produces(MediaType.APPLICATION_JSON)
    public Result isSecondProjectReimburse(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String SecondProjectId = (String)p.get("secondprojectid");
        String companyId = (String)p.get("companyid");

        Map mapList;
        try {
            mapList = ServiceUtil.getService(IncomeCostServiceImpl.class).isSecondProjectReimburse(SecondProjectId,companyId);

            return MsgResult.ok(mapList);
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }

    }

    /**
     * @description:二级项目税率分组的开票金额
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("invoiceTaxGroup")
    @Produces(MediaType.APPLICATION_JSON)
    public Result invoiceTaxGroup(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String ejxmid = (String)p.get("ejxmid");
        String[] taxs = String.valueOf(p.get("taxs")).split(",");

        List<Map> mapList;
        try {
            mapList = ServiceUtil.getService(IncomeCostServiceImpl.class).invoiceTaxGroupMoney(ejxmid,taxs);

            return MsgResult.ok(mapList);
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:二级项目未结算发票信息
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("projectNoSettlementInvoiceSelect")
    @Produces(MediaType.APPLICATION_JSON)
    public Result projectNoSettlementInvoiceSelect(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String ejxmid = (String)p.get("ejxmid");
        String faFlowCode = (String)p.get("faFlowCode");
        String companyId = (String)p.get("companyid");

        List<Map> mapList;
        try {
            mapList = ServiceUtil.getService(IncomeCostServiceImpl.class).projectNoSettlementInvoiceSelect(ejxmid,faFlowCode,companyId);

            return MsgResult.ok(mapList);
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description:二级项目是否存在报销在途流程
     * @author: lijianpan
     * @date: 2024/10/23
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("isExistCostRegisterByScondProject")
    @Produces(MediaType.APPLICATION_JSON)
    public Result isExistCostRegister(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String SecondProjectId = (String)p.get("secondprojectid");
        String companyId = (String)p.get("companyid");

        try {
            ReturnData rd = ServiceUtil.getService(IncomeCostServiceImpl.class).isExamineWorkflowByScondProject(SecondProjectId,companyId,1);

            if(!rd.getSuccess()){
                return MsgResult.error(rd.getMsg());
            }
            return MsgResult.ok(rd.getData());
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }

    }

    /**
     * @description:二级项目是否存在开票、冲红在途流程
     * @author: lijianpan
     * @date: 2024/10/23
     * @param: [request, response]
     * @return: com.engine.edc.biz.action.result.Result
     **/
    @GET
    @Path("isExistIncomeRegisterByScondProject")
    @Produces(MediaType.APPLICATION_JSON)
    public Result isExistIncomeRegister(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String SecondProjectId = (String)p.get("secondprojectid");
        String companyId = (String)p.get("companyid");

        try {
            ReturnData rd = ServiceUtil.getService(IncomeCostServiceImpl.class).isExamineWorkflowByScondProject(SecondProjectId,companyId,0);

            if(!rd.getSuccess()){
                return MsgResult.error(rd.getMsg());
            }
            return MsgResult.ok(rd.getData());
        }catch (ECException e){
            return MsgResult.error(e.getMessage());
        }
    }

    /**
     * @description: 获取二级项目的收入确认金额
     * @author: lijianpan
     **/
    @GET
    @Path("incomeByProAndType")
    @Produces(MediaType.APPLICATION_JSON)
    public Result incomeByProAndType(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String,Object> p = ParamUtil.request2Map(request);
        String proTwoId = (String)p.get("proTwoId");
        String category = (String)p.get("category");
        String taxId = (String)p.get("taxId");
        String mode = (String)p.get("mode");

        taxId = "".equals(taxId)?null:taxId;

        try {
            int type = Integer.parseInt(category);

            Map mapList = ServiceUtil.getService(IncomeCostServiceImpl.class).getIncomeByProAndAdd(proTwoId,type,taxId,mode);

            return MsgResult.ok(mapList);
        } catch (Exception e) {
            return MsgResult.error(e.getMessage());
        }
    }
}