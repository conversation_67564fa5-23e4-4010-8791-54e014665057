package com.api.pubaction.service;

import com.alibaba.fastjson.JSONArray;
import com.api.pubaction.entity.ReturnData;
import com.mysql.cj.xdevapi.JsonArray;

import java.util.List;
import java.util.Map;

public interface IncomeCostService {
    /**
     * @description:收入成本数据
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: [proTwoId]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String,String> getCostMath(String proTwoId,String companyId) throws Exception;

    /**
     * @description:二级项目的确认状态
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: [proTwoId]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String,String> getPreCost(String proTwoId) throws Exception;

    /**
     * @description:二级项目的确认状态
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: [twoProjectId, isFinalStatus]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String,String> getConfirmStatus(String twoProjectId,boolean isFinalStatus);

    /**
     * @description:一个或多个二级项目成本
     * @author: lijianpan
     * @date: 2023/11/24
     * @param:
     * @return:
     **/
    List<Map> getU8SecondProjectCost(JSONArray secodProjectCodes);

    /**
     * @description:一个或多个二级项目成本合计
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: []
     * @return: java.util.Map
     **/
    Map getU8SecondProjectCostSum(String secodProjectCodes,String companyId);

    /**
     * @description:一个或多个二级项目成本合计
     * @author: lijianpan
     * @date: 2023/11/24
     * @param: []
     * @return: java.util.Map
     **/
    Map getU8SecondProjectCostSum(JSONArray secodProjectCodes);

    /**
     * @description:流程属性及人员分部
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: [companyId, userId]
     * @return: java.util.Map
     **/
    Map getWorkFlowAttr(String companyId,String userId);

    /**
     * @description:二级项目是否存在报销
     * @author: lijianpan
     * @date: 2023/12/11
     * @param: [companyId, userId]
     * @return: java.util.Map
     **/
    Map isSecondProjectReimburse(String secondProjectId,String companyId);

    /**
     * @description:二级项目税率分组的开票金额合计
     * @author: lijianpan
     * @date: 2024/1/4
     * @param:
     * @return:
     **/
    List<Map> invoiceTaxGroupMoney(String ejxmid,String[] tax);

    /**
     * @description:项目未结算发票
     * @author: lijianpan
     * @date: 2024/4/7
     * @param:
     * @return:
     **/
    List<Map> projectNoSettlementInvoiceSelect(String ejxmid,String faFlowCode,String companyId);

    /**
     * @description:二级项目是否存在在途流程
     * @author: lijianpan
     * @date: 2024/10/23
     * @param:
     * @return:
     **/
    ReturnData isExamineWorkflowByScondProject(String secondProjectId, String companyId, int type);

    
    /**
     * @description: 获取二级项目的收入确认金额
     * @author: lijianpan
     **/
    Double getIncomeByPro(String proTwoId,int type,String taxId) throws Exception;

    /**
     * @description: 获取二级项目已决算金额
     * @author: lijianpan
     **/
    Double getIncomeByProAndClose(String proTwoId,String taxId) throws Exception;

    /**
     * @description: 获取二级项目新增收入确认金额
     * @author: lijianpan
     **/
    Map getIncomeByProAndAdd(String proTwoId,int type,String taxId,String mode) throws Exception;
}
