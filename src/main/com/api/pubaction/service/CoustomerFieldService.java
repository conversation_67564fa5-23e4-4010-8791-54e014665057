package com.api.pubaction.service;

import com.alibaba.fastjson.JSONArray;

import java.util.List;
import java.util.Map;

public interface CoustomerFieldService {
    //获取字段id
    JSONArray getFieldList(String fieldName, String formid);
    //获取流程信息
    Map<String,String> getWorkflowInfo(Map<String ,Object> params);
    //获取二级项目信息
    Map<String,String> getProTwoInfo(String id) throws Exception;
    //获取项目标签
    Map<String,String> getProTagByPid(String pid) throws Exception;
}
