package com.api.pubaction.service;
/**
 * @Description: 账户密码管理
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/31 9:10
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/9/28      HONOR          v1.0.0               修改原因
 */
public interface PasswordService {

    /**
     * 判断userid的密码是否正确
     * @param userid
     * @param password
     * @return
     */
    String isWeakPassword(int userid,String password);

    /**
     * 判断loginid的密码是否正确
     * @param loginid
     * @param password
     * @return
     */
    Boolean isPasswordByLoginid(String loginid,String password) throws Exception;
}
