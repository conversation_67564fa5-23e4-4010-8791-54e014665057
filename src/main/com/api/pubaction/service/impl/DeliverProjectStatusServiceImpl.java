package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.project.IsAuditBySecondProjectCmd;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.DeliverProjectStatusService;
import com.engine.core.impl.Service;

/**
 * @ClassName: DeliverProjectStatusServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-17  22:04
 * @Version: 1.0
 */
public class DeliverProjectStatusServiceImpl extends Service implements DeliverProjectStatusService {
    @Override
    public ReturnData isAuditBySecondProject(String secondProjectId) {
        return this.commandExecutor.execute(new IsAuditBySecondProjectCmd(secondProjectId));
    }
}