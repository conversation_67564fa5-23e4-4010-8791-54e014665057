package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.GetProjectInfoCmd;
import com.api.pubaction.cmd.project.GetProjectCodeListCmd;
import com.api.pubaction.service.OneProjectModeAuthorityService;
import com.api.pubaction.service.ProjectInfoService;
import com.engine.core.impl.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName: ProcjectInfoServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-05  17:29
 * @Version: 1.0
 */
public class ProcjectInfoServiceImpl extends Service implements ProjectInfoService {

    @Override
    public Map<String, String> getProjectInfo(String id) {
        return this.commandExecutor.execute(new GetProjectInfoCmd(id));
    }

    @Override
    public List<Map> getAllCompanyProjectCode(String projectName) {
        List<Map> allList = new ArrayList<>();

        List<Map> list1 = this.commandExecutor.execute(new GetProjectCodeListCmd(projectName,6));
        List<Map> list2 = this.commandExecutor.execute(new GetProjectCodeListCmd(projectName,11));
        List<Map> list3 = this.commandExecutor.execute(new GetProjectCodeListCmd(projectName,14));
        List<Map> list4 = this.commandExecutor.execute(new GetProjectCodeListCmd(projectName,16));

        int count = list1.size()+list2.size()+list3.size()+list4.size();

        if(count>1000){
            allList = Stream.of(list1,list2,list3,list4)
                    .flatMap(x ->x.stream())
                    .collect(Collectors.toList());
        }else {
            allList.addAll(list1);
            allList.addAll(list2);
            allList.addAll(list3);
            allList.addAll(list4);
        }

        return allList;
    }
}