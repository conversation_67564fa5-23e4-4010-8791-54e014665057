package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.GetUserIdByLoginidCmd;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.UserInfoServer;
import com.api.pubaction.cmd.GetUserMobileCmd;
import com.engine.core.impl.Service;
import weaver.hrm.User;

public class UserInfoServerImpl extends Service implements UserInfoServer {
    @Override
    public String GetMobil(String loginid) {
        return this.commandExecutor.execute(new GetUserMobileCmd(loginid));
    }

    @Override
    public ReturnData GetUserInfo(String loginid) {
        int id = this.commandExecutor.execute(new GetUserIdByLoginidCmd(loginid));
        if(id==-1){
            return ReturnData.error("用户不存在");
        }
        User user = new User(id);

        return ReturnData.ok(user);
    }
}
