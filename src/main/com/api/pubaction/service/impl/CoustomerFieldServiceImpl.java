package com.api.pubaction.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.api.pubaction.cmd.CoustomerFieldCmd;
import com.api.pubaction.cmd.GetProTagInfo;
import com.api.pubaction.cmd.GetProTwoInfoCmd;
import com.api.pubaction.cmd.GetWorkflowNameCmd;
import com.api.pubaction.service.CoustomerFieldService;

import com.engine.core.impl.Service;

import java.util.HashMap;
import java.util.Map;

public class CoustomerFieldServiceImpl extends Service implements CoustomerFieldService {
    @Override
    public JSONArray getFieldList(String fieldName, String formid) {
        return this.commandExecutor.execute(new CoustomerFieldCmd(fieldName,formid));
    }

    @Override
    public Map<String, String> getWorkflowInfo(Map<String ,Object> params) {
        return this.commandExecutor.execute(new GetWorkflowNameCmd(params));
    }

    @Override
    public Map<String, String> getProTwoInfo(String id) throws Exception {
        Map<String,String> map = this.commandExecutor.execute(new GetProTwoInfoCmd(id));

        if(map==null){
            throw new Exception("该二级项目不存在！");
        }

        return map;
    }

    @Override
    public Map<String, String> getProTagByPid(String pid) throws Exception {
        Map<String,String> map = this.commandExecutor.execute(new GetProTagInfo(pid));

        if(map.isEmpty()){
            throw new Exception("该项目标签不存在！");
        }

        return map;
    }
}
