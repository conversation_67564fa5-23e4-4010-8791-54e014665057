package com.api.pubaction.service.impl;


import com.api.pubaction.cmd.GetUserIdByLoginidCmd;
import com.api.pubaction.cmd.OneProjectModeSqlCmd;
import com.api.pubaction.cmd.OneProjectOfResourceIdSqlWhereCmd;
import com.api.pubaction.entity.OneProject;
import com.api.pubaction.service.OneProjectModeAuthorityService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/31 9:20
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/8/31      HONOR          v1.0.0               修改原因
 */
public class OneProjectModeAuthorityServiceImpl extends Service implements OneProjectModeAuthorityService {

    @Override
    public List<Integer> getIdOfList(String loginid) throws Exception {
        String sqlWhere = "";
        int resourceId;
        List<Integer> ProjectList = new ArrayList<>();
        resourceId = this.commandExecutor.execute(new GetUserIdByLoginidCmd(loginid));

        if(resourceId==-1){
            throw new Exception("该用户不存在!");
        }

        User user = new User(resourceId);

        if(user.getUID()!=0){
            sqlWhere = this.commandExecutor.execute(new OneProjectOfResourceIdSqlWhereCmd(user));
            ProjectList = this.commandExecutor.execute(new OneProjectModeSqlCmd(sqlWhere));
        }

        return ProjectList;
    }
}
