package com.api.pubaction.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.cmd.supplier.*;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.entity.Supplier;
import com.api.pubaction.service.SupplierService;
import com.engine.core.exception.ECException;
import com.engine.core.impl.Service;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import weaver.general.Util;
import weaver.interfaces.workflow.action.cyitce.util.FileUtil;

import java.nio.charset.StandardCharsets;
import java.util.Map;


/**
 * @ClassName: SupplierServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-23  09:37
 * @Version: 1.0
 */
public class SupplierServiceImpl extends Service implements SupplierService {

    private static Logger log = Logger.getLogger(SupplierServiceImpl.class);

    @Override
    public Boolean addBankInfo(Supplier supplier) {
        try {
            this.commandExecutor.execute(new AddBankInfoCmd(supplier));
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage()+",供应商银行信息添加失败");
        }

        return true;
    }

    @Override
    public Boolean updataBankInfo(Supplier supplier) {
        try {
            this.commandExecutor.execute(new UpdataBankInfoCmd(supplier));
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage()+",供应商银行信息更新失败");
        }

        return true;
    }

    @Override
    public Boolean addQualificationInfo(Supplier supplier) {
        try {
            this.commandExecutor.execute(new AddQualificationInfoCmd(supplier));
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage()+",供应商资质添加失败");
        }

        return true;
    }

    @Override
    public Boolean updataQualificationInfo(Supplier supplier) {
        try {
            this.commandExecutor.execute(new UpdataQualificationInfoCmd(supplier));
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage()+",供应商资质更新失败");
        }

        return true;
    }

    @Override
    public Boolean updataSupplierInfo(Supplier supplier) {
        try {
            this.commandExecutor.execute(new UpdateSupplierInfo(supplier));
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage()+",供应商信息更新失败");
        }

        return true;
    }

    @Override
    public Boolean addCompanyAuth() {
        return null;
    }

    @Override
    public ReturnData upLoadSupplierSystemFile(String ids) {
        HttpClient client = null;
        HttpPost post = null;
        HttpEntity entity = null;
        MultipartEntityBuilder builder = null;
        HttpResponse response1 = null;
        JSONObject obj = null;
        try {
            if(Util.null2String(ids)==""){
                throw new Exception("没有传入OA附件id");
            }

            String[] idList = ids.split(",");

            for (String id:idList){
                Map<String,String> map = FileUtil.fileInfo(id);

                client = HttpClients.createDefault();
                post = new HttpPost("https://supplier.cyitce.com/api/supplier-system-common/common/file/oaFileUpload");
                post.addHeader("User-Agent", "Mozilla/5.0");

                builder = MultipartEntityBuilder.create();
                builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
                ContentType contentType = ContentType.create(FileUtil.ToContenType(map.get("filename").substring(map.get("filename").lastIndexOf("."))));
                builder.addBinaryBody("file",map.get("bytes").getBytes(StandardCharsets.UTF_8), contentType,map.get("filename"));
                builder.addTextBody("type","2");
                entity = builder.build();
                post.setEntity(entity);
                response1 = client.execute(post);

                String data = EntityUtils.toString(response1.getEntity(), "UTF-8");
                obj = JSONObject.parseObject(data);

                if (response1.getStatusLine().getStatusCode()!=200){
                    log.info(obj);
                    throw new Exception("请求失败，code:"+response1.getStatusLine().getStatusCode()+",msg:");
                }
            }
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }

        return ReturnData.ok(obj.getString("data"));
    }

    @Override
    public ReturnData procureSupplierApplication(Map params) {
        ReturnData rd = this.commandExecutor.execute(new ProcureSupplierApplicationCmd(params));
        return rd;
    }
}