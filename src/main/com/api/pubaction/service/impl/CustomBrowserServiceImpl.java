package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.BrowserFieldIdListCmd;
import com.api.pubaction.cmd.BrowserFieldIdOneCmd;
import com.api.pubaction.cmd.GetTreeFieldIdListCmd;
import com.api.pubaction.cmd.NodeFieldIsShow;
import com.api.pubaction.service.CustomBrowserService;
import com.engine.core.impl.Service;
import weaver.general.Util;

import java.util.List;
import java.util.Map;

public class CustomBrowserServiceImpl extends Service implements CustomBrowserService {

    @Override
    public String getFieldIdList(Map<String ,Object> params) throws Exception {
        String fieldid;
        String showname = Util.null2String(params.get("showname"));//按钮标识
        String billid = Util.null2String(params.get("billid"));//浏览框表单id
        String formid = Util.null2String(params.get("formid"));//当前表单id
        String nodeid = Util.null2String(params.get("nodeid"));//节点id

        if("".equals(showname)){
            fieldid = this.commandExecutor.execute(new BrowserFieldIdListCmd(formid,billid,"0")).get(0);
        }else {
            fieldid = this.commandExecutor.execute(new BrowserFieldIdOneCmd(formid,billid,showname,"0"));
        }

        if("".equals(fieldid)){
            throw new Exception("不存在该字段！");
        }

        if(!"".equals(nodeid)){
            boolean flag = this.commandExecutor.execute(new NodeFieldIsShow(fieldid,nodeid));
            if(!flag){
                fieldid = "";
            }
        }

        return fieldid;
    }

    @Override
    public List<Integer> getTreeFieldList(String formid, String treeId) {
        return this.commandExecutor.execute(new GetTreeFieldIdListCmd(formid,treeId));
    }
}
