package com.api.pubaction.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.api.pubaction.cmd.auth.AppIdPublicKeySaveCmd;
import com.api.pubaction.cmd.auth.AppIdSaveCmd;
import com.api.pubaction.cmd.auth.GetAppIdPublicKeyCmd;
import com.api.pubaction.cmd.auth.GetPrivateKeyCmd;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.TokenService;
import com.engine.core.impl.Service;
import weaver.interfaces.workflow.action.cyitce.config.AddressManagementPool;
import java.util.Map;

/**
 * @ClassName: TokenServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-13  14:15
 * @Version: 1.0
 */
public class TokenServiceImpl extends Service implements TokenService{
    /**
     * @description:注册APPID
     * @author: lijianpan
     * @date: 2024/9/13
     * @param: [param, type]
     * @return: com.api.pubaction.entity.ReturnData
     **/
    @Override
    public ReturnData regist(Map param) {
        try {
            String appid = (String)param.get("appid");
            String name = (String)param.get("name");

            ReturnData r = this.commandExecutor.execute(new AppIdSaveCmd(appid,name));

            if(!r.getSuccess()){
                return r;
            }

            String data = HttpRequest.post(AddressManagementPool.getIpAddress("OA") + "/ec/dev/auth/regist")
                    .header("appid",appid)
                    .header("cpk","123")
                    .timeout(2000)
                    .execute().body();

            Map<String,Object> datas = JSONUtil.parseObj(data);
            ReturnData r2 = this.commandExecutor.execute(new AppIdPublicKeySaveCmd(appid,StrUtil.nullToEmpty((String)datas.get("spk")),StrUtil.nullToEmpty((String)datas.get("secrit"))));

            return r2;
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
    }

    /**
     * @description:获取APPID的公钥
     * @author: lijianpan
     * @date: 2024/9/13
     * @param: []
     * @return: com.api.pubaction.entity.ReturnData
     **/
    @Override
    public ReturnData publicKey(Map param) {
        try {
            String appid = (String) param.get("appid");
            return this.commandExecutor.execute(new GetAppIdPublicKeyCmd(appid));
        }catch (Exception e){
            return ReturnData.error("传入参数错误");
        }
    }

    /**
     * @description:获取加密信息
     * @author: lijianpan
     * @date: 2024/9/13
     * @param: [param]
     * @return: com.api.pubaction.entity.ReturnData
     **/
    @Override
    public ReturnData encrypt(Map param,int type) {
        try {
            String appid = (String)param.get("appid");
            String encryptkey = "";
            if(type==0){
                ReturnData r2 = this.commandExecutor.execute(new GetPrivateKeyCmd(appid));
                if(r2.getSuccess()){
                    encryptkey = (String)r2.getData();
                }
            }else if(type==1){
                encryptkey = (String)param.get("userid");
            }

            ReturnData r = publicKey(param);
            if(!r.getSuccess()){
                return r;
            }

            // 公钥加密,所以RSA对象私钥为null
            RSA rsa = new RSA(null,(String)r.getData());
            //对秘钥进行加密传输，防止篡改数据
            String encryptSecret = rsa.encryptBase64(encryptkey, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);

            return ReturnData.ok(encryptSecret);
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
    }
}