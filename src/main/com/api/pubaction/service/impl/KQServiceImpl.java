package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.GetHolidayListCmd;
import com.engine.core.impl.Service;
import com.api.pubaction.service.KQService;
import weaver.general.Util;

import java.util.List;

/**
 * @ClassName: KQServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-28  14:59
 * @Version: 1.0
 */
public class KQServiceImpl extends Service implements KQService {
    @Override
    public List<String> getHoliday(String year, String type) throws Exception {
        if("".equals(Util.null2String(year))){
            throw new Exception("参数 year 为空");
        }else if("".equals(Util.null2String(type))){
            throw new Exception("参数 type 为空");
        }

        return this.commandExecutor.execute(new GetHolidayListCmd(year,type));
    }
}