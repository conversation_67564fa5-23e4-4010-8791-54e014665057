package com.api.pubaction.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.api.pubaction.cmd.workflow.SelectWorkFlowInfoCmd;
import com.api.pubaction.service.WorkFlowService;
import com.engine.core.exception.ECException;
import com.engine.core.impl.Service;
import weaver.general.BaseBean;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * @ClassName: WrokFlowServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-07-10  15:35
 * @Version: 1.0
 */
public class WorkFlowServiceImpl extends Service implements WorkFlowService {
    private void writeLog(String s){
        BaseBean log = new BaseBean();
        log.writeLog(this.getClass().getName(),s);
    }

    @Override
    public List<Map<String, String>> workflowInfoBySelect(String tableName,String fields,String sqlwhere,String currentnodetype) throws Exception {
        String table = tableName + " t";
        StringBuffer selectSql = new StringBuffer();
        //是否是流程表
        if(!tableName.matches("^formtable_main_.*$")){
            throw new ECException(tableName+"，不是流程表");
        }
        //是否明细表
        if(tableName.matches("^.*_dt\\d+$")){
            table = tableName.substring(0,tableName.lastIndexOf("_")) + " t "+" inner join "+tableName + " d on t.id=d.mainid";
        }else {
            if(sqlwhere.matches("^.*d\\..*=.*$")){
                throw new ECException(sqlwhere+"，条件中不能包含明细字段 d.xx");
            }else if(fields.matches("^.*d\\..*$")){
                throw new ECException(fields+"，查询字段中不能包含明细字段 d.xx");
            }
        }

        String[] fs = fields.split(",");
        StringJoiner sj = new StringJoiner(",");
        for (String field : fs) {
            if (field.trim().matches("^.*\\s+as\\s+.*$")){
                sj.add(field);
            }else if(field.trim().matches("^.*\\s+.*$")){
                sj.add(field);
            }else {
                sj.add(field + " as " + "'"+field+"'");
            }
        }
        fields = sj.toString();

        table = table+" inner join workflow_requestbase w on t.requestid=w.requestid";
        selectSql.append("select "+fields+" from "+table+" where 1=1");

        if(sqlwhere.trim().length()>0){
            sqlwhere = sqlwhere
                    .replace("_a_","and")
                    .replace("_o_","or")
                    .replace("_i_","in");
            selectSql.append(" and ("+sqlwhere+")");
        }

        if(currentnodetype.trim().length()>0){
            selectSql.append(" and w.currentnodetype in ("+currentnodetype+")");
        }

        writeLog("sql:"+selectSql.toString());

        if(fields.trim().length()==0){
            throw new ECException("fields 查询字段为空");
        }

        int fieldCount = fields.split(",").length;

        List<Map<String, String>> list = null;
        Map<String,Object> obj = null;
        try {
            obj = this.commandExecutor.execute(new SelectWorkFlowInfoCmd(selectSql.toString(),fieldCount));
            if (!Boolean.valueOf(String.valueOf(obj.get("status")))){
                throw new ECException(String.valueOf(obj.get("msg")));
            }

            list = (List)obj.get("data");
        }catch (Exception e){
            throw new ECException(e.getMessage());
        }
        return list;
    }

    @Override
    public List<Map<String, String>> modeInfoBySelect(String tableName, String fields, String sqlwhere) throws Exception {
        String table = tableName + " t";
        StringBuffer selectSql = new StringBuffer();

        //是否明细表
        if(tableName.matches("^.*_dt\\d+$")){
            table = tableName.substring(0,tableName.lastIndexOf("_")) + " t "+" inner join "+tableName + " d on t.id=d.mainid";
        }else {
            if(sqlwhere.matches("^.*d\\..*=.*$")){
                throw new ECException(sqlwhere+"，条件中不能包含明细字段 d.xx");
            }else if(fields.matches("^.*d\\..*$")){
                throw new ECException(fields+"，查询字段中不能包含明细字段 d.xx");
            }
        }

        String[] fs = fields.split(",");
        StringJoiner sj = new StringJoiner(",");
        for (String field : fs) {
            if (field.trim().matches("^.*\\s+as\\s+.*$")){
                sj.add(field);
            }else if(field.trim().matches("^.*\\s+.*$")){
                sj.add(field);
            }else {
                sj.add(field + " as " + "'"+field+"'");
            }
        }
        fields = sj.toString();

        selectSql.append("select "+fields+" from "+table+" where 1=1");

        if(sqlwhere.trim().length()>0){
            sqlwhere = sqlwhere.replace("_a_","and")
                    .replace("_o_","or")
                    .replace("_i_","in");
            selectSql.append(" and ("+sqlwhere+")");
        }

        writeLog("sql:"+selectSql.toString());

        if(fields.trim().length()==0){
            throw new ECException("fields 查询字段为空");
        }

        int fieldCount = fields.split(",").length;

        List<Map<String, String>> list = null;
        Map<String,Object> obj = null;
        try {
            obj = this.commandExecutor.execute(new SelectWorkFlowInfoCmd(selectSql.toString(),fieldCount));
            if (!Boolean.valueOf(String.valueOf(obj.get("status")))){
                throw new ECException(String.valueOf(obj.get("msg")));
            }

            list = (List)obj.get("data");
        }catch (Exception e){
            throw new ECException(e.getMessage());
        }
        return list;
    }
}