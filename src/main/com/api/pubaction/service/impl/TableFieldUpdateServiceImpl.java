package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.UpdateMainTableFieldCmd;
import com.api.pubaction.entity.RequestTableField;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.service.TableFieldUpdateService;

import com.engine.core.exception.ECException;
import com.engine.core.impl.Service;

/**
 * @ClassName: TableFieldUpdateServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-05-11  14:07
 * @Version: 1.0
 */
public class TableFieldUpdateServiceImpl extends Service implements TableFieldUpdateService {
    @Override
    public ReturnData updateTableField(RequestTableField requestTableField) {
        try {
            this.commandExecutor.execute(new UpdateMainTableFieldCmd(requestTableField));
        }catch (Exception e){
            throw new ECException("更新表单数据失败");
        }

        return ReturnData.ok();
    }
}