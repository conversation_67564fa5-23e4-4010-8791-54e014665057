package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.GetUserIdByLoginidCmd;
import com.api.pubaction.service.PasswordService;
import com.engine.core.impl.Service;
import weaver.general.Util;
import weaver.hrm.User;

public class PasswordServiceImpl extends Service implements PasswordService {

    @Override
    public String isWeakPassword(int userid,String password) {
        String flag = "false";
        try {
            if(userid<1){
                throw new Exception("用户不存在");
            }

            User user = new User(userid);

            if(user.getPwd().equals(Util.getEncrypt(password))){
                flag = "true";
            }
            return flag;
        }catch (Exception e){
            e.printStackTrace();
            return "false";
        }
    }

    @Override
    public Boolean isPasswordByLoginid(String loginid, String password) throws Exception {
        if("".equals(loginid)){
            throw new Exception("用户不存在");
        }

        int userId = this.commandExecutor.execute(new GetUserIdByLoginidCmd(loginid));

        if(userId==-1){
            throw new Exception("用户不存在");
        }

        User user = new User(userId);

        if(user.getPwd().equals(Util.getEncrypt(password))){
            return true;
        }else {
            return false;
        }
    }
}
