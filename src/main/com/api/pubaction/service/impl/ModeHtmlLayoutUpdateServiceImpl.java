package com.api.pubaction.service.impl;

import com.api.pubaction.cmd.GetModeHtmlLayoutScriptCmd;
import com.api.pubaction.cmd.UpdateModeHtmlLayoutScriptCmd;
import com.api.pubaction.service.ModeHtmlLayoutUpdateService;
import com.engine.core.impl.Service;
import weaver.workflow.exceldesign.ParseExcelLayout;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ModeHtmlLayoutUpdateServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-16  11:02
 * @Version: 1.0
 */
public class ModeHtmlLayoutUpdateServiceImpl extends Service implements ModeHtmlLayoutUpdateService {
    @Override
    public boolean scriptUpdate(String jsSrc,String cssSrc, String ids) throws IOException {
        File file = new File(jsSrc);
        File file2 = new File(cssSrc);
        StringBuffer bf = new StringBuffer();
        StringBuffer bf2 = new StringBuffer();
        BufferedReader reader = null;
        List<Map<String,String>> modeScripts = null;
        boolean boo = true;
        String startTag = "\n"+"//StartCoustomFileUploadFun";
        String endTag = "//EndCoustomFileUploadFun";

        String startTagStr = startTag+"\n"+"//StartCoustomFileUploadFun与EndCoustomFileUploadFun之间的代码请勿手动修改 \n";
        startTagStr = startTagStr + "//请勿删除 StartCoustomFileUploadFun与EndCoustomFileUploadFun这两个注释\n";

        try {
            String jsText = "";
            String cssText = "";
            String scriptstr = "";

            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"));
            while ((jsText = reader.readLine())!=null){
                bf.append(jsText).append(System.getProperty("line.separator"));
            }
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file2), "UTF-8"));
            while ((cssText = reader.readLine())!=null){
                bf2.append(cssText).append(System.getProperty("line.separator"));
            }

            jsText = "\n"+startTagStr+"\n"+"<script>"+"\n"+bf.toString()+"\n"+"</script>"+"\n";
            cssText = "\n"+"<style>"+"\n"+bf2.toString()+"\n"+"</style>"+"\n"+endTag+"\n";
            scriptstr = jsText+cssText;

            String[] idsInt = ids.split(",");

            for (String id:idsInt){
                int i = Integer.parseInt(id);
                modeScripts = this.commandExecutor.execute(new GetModeHtmlLayoutScriptCmd(i));
                for (Map m:modeScripts){
                    String modeScriptStr = decodeStr((String)m.get("scriptstr"));

                    //拼接代码块代码
                    if(modeScriptStr.indexOf(startTag)!=-1){
                        modeScriptStr = modeScriptStr.substring(0,modeScriptStr.indexOf(startTag))+scriptstr+modeScriptStr.substring(modeScriptStr.indexOf(endTag)+endTag.length());
                    }else {
                        if("".equals(modeScriptStr)){
                            scriptstr = "\n" +
                                    "<script type=\"text/javascript\">\n" +
                                    "/*\n" +
                                    "* 请在下面编写JS代码\n" +
                                    "*/\n" +
                                    "\n" +
                                    "</script>\n" +
                                    "\n" +
                                    "<style type=\"text/css\">\n" +
                                    "/*\n" +
                                    "* 请在下方编辑CSS\n" +
                                    "*/\n" +
                                    "\n" +
                                    "</style> \n"+scriptstr;
                            modeScriptStr = scriptstr;
                        }else {
                            modeScriptStr = modeScriptStr+scriptstr;
                        }
                    }

                    modeScriptStr = encodeStr(modeScriptStr);
                    boo = this.commandExecutor.execute(new UpdateModeHtmlLayoutScriptCmd((String)m.get("id"),modeScriptStr));
                    if(!boo){
                        return false;
                    }
                }
            }
            return true;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(reader!=null){
                reader.close();
            }
        }

        return false;
    }

    private String decodeStr(String var1) {
        return var1 != null && !"".equals(var1) ? (new ParseExcelLayout(new HashMap())).decodeStr(var1) : "";
    }

    private String encodeStr(String var1) {
        return var1 != null && !"".equals(var1) ? (new ParseExcelLayout(new HashMap())).encodeStr(var1) : "";
    }
}