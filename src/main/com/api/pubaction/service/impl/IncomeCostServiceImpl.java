package com.api.pubaction.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.cmd.*;
import com.api.pubaction.cmd.U8.U8SecondProjectCostCmd;

import com.api.pubaction.cmd.U8.U8SecondProjectCostSumCmd;
import com.api.pubaction.cmd.project.GetProjectInvoiceCmd;
import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.enums.IncomeStatus;
import com.api.pubaction.service.IncomeCostService;
import com.engine.core.exception.ECException;
import com.engine.core.impl.Service;

import org.apache.log4j.Logger;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * @ClassName: IncomeCostServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-21  15:42
 * @Version: 1.0
 */
public class IncomeCostServiceImpl extends Service implements IncomeCostService {

    private static Logger log = Logger.getLogger(IncomeCostServiceImpl.class);

    @Override
    public Map<String, String> getCostMath(String proTwoId,String companyId) throws Exception {
        if("".equals(Util.null2String(proTwoId))){
            throw new Exception("传入的参数【二级项目id】为空");
        }

        Map<String,String> map = new HashMap<>();

        if("".equals(Util.null2String(companyId))){
            map = this.commandExecutor.execute(new GetHoildayCostCmd(proTwoId));
        }else if("6".equals(Util.null2String(companyId))){
            map = this.commandExecutor.execute(new GetHoildayCostCmd(proTwoId));
        } else if("14".equals(Util.null2String(companyId))){
            map = this.commandExecutor.execute(new GetHoildayCostJLCmd(proTwoId));
        }else if("24".equals(Util.null2String(companyId))){
            map = this.commandExecutor.execute(new GetHoildayCostSTCmd(proTwoId));
        }

        return map;
    }

    @Override
    public Map<String, String> getPreCost(String proTwoId) throws Exception {
        if("".equals(Util.null2String(proTwoId))){
            throw new Exception("传入的参数【二级项目id】为空");
        }

        Map<String,String> map = this.commandExecutor.execute(new GetTwoProPreCostCmd(proTwoId));

        String money = this.commandExecutor.execute(new GetIncAdjustMoneyCmd(proTwoId));
        map.put("dzsrje",money);

        return map;
    }

    @Override
    public Map<String, String> getConfirmStatus(String twoProjectId,boolean isFinalStatus) {
        Map<String,String> map = new HashMap<>();

        try {
            if("".equals(Util.null2String(twoProjectId))){
                throw new Exception("传入的【二级项目】参数为空");
            }

            if("".equals(Util.null2String(isFinalStatus))){
                throw new Exception("传入的【isFinalStatus】参数为空");
            }
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException(e.getMessage());
        }

        try {
            if(isFinalStatus){
                map.put("status","2");
            }else {
                map = this.commandExecutor.execute(new GetConfirmStatusCmd(twoProjectId));
            }
        }catch (Exception e){
            log.info(e.getMessage());
            throw new ECException("查询二级项目确认状态失败");
        }

        return map;
    }

    @Override
    public List<Map> getU8SecondProjectCost(JSONArray secodProjectCodes) {
        List<Map> costList = new ArrayList<>();
        RecordSet rs = new RecordSet();
        String formId = "602";
        try {
            for (int i=0;i<secodProjectCodes.size();++i){
                JSONObject obj = secodProjectCodes.getJSONObject(i);
                String companyId = obj.getString("companyId");
                String code = obj.getString("secondProjectCode");

                rs.executeQuery("select formid from uf_companyWorkflowMapping where type=1 and companyid=?",companyId);
                if(rs.next()){
                    formId = Util.null2String(rs.getString(1));
                }else {
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }

                if("".equals(formId)){
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }

                Map m = new HashMap();
                List<Map> secodProjectCost = this.commandExecutor.execute(new U8SecondProjectCostCmd(code,companyId));
                m.put("secodProjectCode",code);
                m.put("cost",secodProjectCost);
                costList.add(m);
            }
        }catch (Exception e){
            throw new ECException(e.getMessage());
        }
        return costList;
    }

    @Override
    public Map getU8SecondProjectCostSum(String secodProjectCodes,String companyId) {
        RecordSet rs = new RecordSet();
        String formId = "602";
        Map<String,String> map = new HashMap<>();
        try {
            String[] codes = secodProjectCodes.split(",");
            for (int i=0;i<codes.length;++i){
                String code = codes[i];

                rs.executeQuery("select formid from uf_companyWorkflowMapping where type=1 and companyid=?",companyId);
                if(rs.next()){
                    formId = Util.null2String(rs.getString(1));
                }else {
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }

                if("".equals(formId)){
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }
                map.put(code,this.commandExecutor.execute(new U8SecondProjectCostSumCmd(code,companyId)));
            }
        }catch (Exception e){
            throw new ECException(e.getMessage());
        }

        return map;
    }

    @Override
    public Map getU8SecondProjectCostSum(JSONArray secodProjectCodes) {
        RecordSet rs = new RecordSet();
        String formId = "602";
        Map<String,String> map = new HashMap<>();
        try {
            for (int i=0;i<secodProjectCodes.size();++i){
                JSONObject obj = secodProjectCodes.getJSONObject(i);
                String companyId = obj.getString("companyId");
                String code = obj.getString("secondProjectCode");

                rs.executeQuery("select formid from uf_companyWorkflowMapping where type=1 and companyid=?",companyId);
                if(rs.next()){
                    formId = Util.null2String(rs.getString(1));
                }else {
                    throw new ECException("公司id："+obj.getString("companyId")+",请配置该公司的U8账套id");
                }

                if("".equals(formId)){
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }
                map.put(code,this.commandExecutor.execute(new U8SecondProjectCostSumCmd(code,companyId)));
            }
        }catch (Exception e){
            throw new ECException(e.getMessage());
        }

        return map;
    }

    @Override
    public Map getWorkFlowAttr(String companyId, String userId) {
        Map m = new HashMap();

        m = this.commandExecutor.execute(new getWorkFlowAttrCmd(companyId));

        try {
            User user = new User(Integer.parseInt(userId));
            m.put("usercompanyid",user.getUserSubCompany1());
        }catch (Exception e){
            throw new ECException("人员有误！");
        }

        return m;
    }

    @Override
    public Map isSecondProjectReimburse(String secondProjectId,String companyId) {
        return this.commandExecutor.execute(new isSecondProjectReimburseCmd(secondProjectId,companyId));
    }

    @Override
    public List<Map> invoiceTaxGroupMoney(String ejxmid,String[] tax) {
        return this.commandExecutor.execute(new GetInvoiceTaxGroupMoneyCmd(ejxmid,tax));
    }

    @Override
    public List<Map> projectNoSettlementInvoiceSelect(String ejxmid,String faFlowCode,String companyId) {
        if("".equals(faFlowCode)||faFlowCode==null){
            faFlowCode="null";
        }

        return this.commandExecutor.execute(new GetProjectInvoiceCmd(ejxmid,faFlowCode,companyId));
    }

    @Override
    public ReturnData isExamineWorkflowByScondProject(String secondProjectId, String companyId, int type) {
        ReturnData rd = new ReturnData();

        if(type==0){
            rd =  this.commandExecutor.execute(new isSprojectIncomeCmd(secondProjectId,companyId));
        }else if(type==1){
            rd = this.commandExecutor.execute(new isSprojectCostCmd(secondProjectId,companyId));
        }

        return rd;
    }

    @Override
    public Double getIncomeByPro(String proTwoId, int type, String taxId) throws Exception {
        RecordSet rs = new RecordSet();
        double result = 0.0;
        StringBuffer sql = new StringBuffer();
        StringBuilder strList = new StringBuilder("(");

        try {
            if (type == 0) {
                sql.append("select max(b.id) as mid from uf_jfxmysgllcbdjm a inner join uf_jfxmysgllcbdjm_dt1 b on a.id=b.mainid where a.ejxmmc="+proTwoId);
                if(taxId!=null){
                    sql.append(" and a.sl = "+taxId);
                }
                sql.append(" group by b.jfxmmc1,a.sl");


                log.info("sql:"+sql.toString());

                rs.executeQuery(sql.toString());

                boolean first = true;
                while (rs.next()) {
                    if (!first) {
                        strList.append(",");
                    }
                    strList.append(rs.getInt("mid"));
                    first = false;
                }

                // 检查是否存在验收的交付项目
                if (first) {
                    strList.append("0");
                }

                strList.append(")");

                sql.setLength(0);
                sql.append("select ISNULL(sum(b.jfxmysjebhs),0) as ysje from uf_jfxmysgllcbdjm a inner join uf_jfxmysgllcbdjm_dt1 b on a.id=b.mainid");
                sql.append(" where b.id in " + strList.toString());

                log.info("sql:"+sql.toString());

                // 继续执行后续查询
                rs.executeQuery(sql.toString());
                rs.next();

                result = rs.getDouble("ysje");
            } else if (type == 1) {
                sql.setLength(0);
                sql.append("select max(id) as mid from uf_jsshsjsd where ejxmmc="+proTwoId+" and glnrxz=1 and jslx1=1");
                if(taxId!=null){
                    sql.append(" and sl = "+taxId);
                }
                sql.append(" group by jfxmmc1,sl");

                log.info("sql:"+sql.toString());

                rs.executeQuery(sql.toString());

                boolean first = true;
                while (rs.next()) {
                    if (!first) {
                        strList.append(",");
                    }
                    strList.append(rs.getInt("mid"));
                    first = false;
                }

                if (first) {
                    strList.append("0");
                }

                strList.append(")");

                sql.setLength(0);
                sql.append("select ISNULL(sum(bczzjsjebhs),0) as sdje from uf_jsshsjsd where id in " + strList.toString());

                log.info("sql:"+sql.toString());

                rs.execute(sql.toString());
                rs.next();

                Double sdje1 = rs.getDouble("sdje");

                sql.setLength(0);
                sql.append("select ISNULL(sum(bczzjsjebhs),0) as sdje from uf_jsshsjsd where ejxmmc="+proTwoId+" and glnrxz=1 and jslx1=2");
                if(taxId!=null){
                    sql.append(" and sl = "+taxId);
                }

                log.info("sql:"+sql.toString());

                rs.execute(sql.toString());
                rs.next();

                Double sdje2 = rs.getDouble("sdje");

                result = sdje1 + sdje2;
            }
        }catch (Exception e){
            log.info(e);
            throw new Exception("获取项目收入确认金额失败！");
        }

        return result;
    }

    @Override
    public Double getIncomeByProAndClose(String proTwoId, String taxId) throws Exception {
        RecordSet rs = new RecordSet();
        StringBuilder sql = new StringBuilder();
        double result = 0.0;
        StringBuilder strList = new StringBuilder("(");

        try {
            sql.append("select max(id) id from uf_srcbqrtz where ejxmmc="+proTwoId);

            log.info("sql:"+sql.toString());

            rs.executeQuery(sql.toString());

            boolean first = true;
            while (rs.next()) {
                if (!first) {
                    strList.append(",");
                }
                strList.append(rs.getInt("id"));
                first = false;
            }

            if (first) {
                strList.append("0");
            }

            strList.append(")");

            sql.setLength(0);
            sql.append("select ISNULL(SUM(ISNULL(ljyjsjebhs,0)+ISNULL(bcsrqrmxbhs,0)),0) as ljjs from uf_srcbqrtz_dt1 where mainid in "+strList.toString());
            if(taxId!=null){
                sql.append(" and sl="+taxId);
            }

            log.info("sql:"+sql.toString());

            rs.execute(sql.toString());
            rs.next();

            result = rs.getDouble("ljjs");
        }catch (Exception e){
            log.info(e);
            throw new Exception("获取项目已决算金额失败！");
        }

        return result;
    }

    @Override
    public Map getIncomeByProAndAdd(String proTwoId, int type, String taxId,String mode) throws Exception {
        Map result = new HashMap();
        double m1;
        double m2;
        double m = 0.0;

        try {
            m1 = getIncomeByPro(proTwoId,type,taxId);
            m2 = getIncomeByProAndClose(proTwoId,taxId);

            if(IncomeStatus.ALL.getValue().equals(mode)){
                m = m1;
            }else if(IncomeStatus.YES_ADD.getValue().equals(mode)){
                m = m2;
            }else if(IncomeStatus.NO_ADD.getValue().equals(mode)){
                m = m1-m2;
            }

            result.put("total",m);
        }catch (Exception e){
            log.info(e);
            throw new Exception("获取项目收入确认金额失败！",e);
        }

        return result;
    }
}