package com.api.pubaction.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.config.DocSys;
import com.api.pubaction.service.JobService;
import com.api.pubaction.util.DateTimeUtil;
import com.engine.core.impl.Service;
import com.engine.edc.biz.action.result.MsgResult;
import com.engine.edc.biz.action.result.Result;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.cyitce.po.docsManagerSystem.Token;

import java.io.IOException;


/**
 * @ClassName: JobServiceImpl
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-18  09:55
 * @Version: 1.0
 */
public class JobServiceImpl extends Service implements JobService {
    BaseBean log = new BaseBean();
    String token = Token.getToken("ljp1212","李建潘1212");
    long preTimeMillis = System.currentTimeMillis();

    @Override
    public Result projectInsert() throws Exception {
        HttpClient client = null;
        HttpGet get = null;
        HttpPost post = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        RecordSet rs = new RecordSet();
        rs.execute("select xmmc,xmbh,bmcx,xmfzr,sqr,cyry,yjbm,ejbm,sjbm,modedatacreater from uf_yjxmlxjm where\n" +
                "((sfsggxm=0 and zt!=4)\n" +
                "or sfsggxm=1)\n" +
                "and modedatacreater!=1 and xmbh is not null and id>=2388 and bmcx is not null\n");
        while (rs.next()){
            int minute = DateTimeUtil.diffMinute(preTimeMillis,System.currentTimeMillis());
            if(minute>10){
                token = Token.getToken("ljp1212","李建潘1212");
            }
            System.currentTimeMillis();
            client = HttpClients.createDefault();
            get = new HttpGet(DocSys.url+"/file-system/file_center/getProjectList?code="+rs.getString("xmbh"));
            get.addHeader("Tenant-Id","000000");
            get.addHeader("Blade-Auth", token);
            get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            response1 = client.execute(get);
            httpEntity = response1.getEntity();
            obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

            if(response1.getStatusLine().getStatusCode()!=200){
                msg = "获取文件管理系统项目id失败，projectCode："+rs.getString("xmbh")+",httpcode:"+response1.getStatusLine().getStatusCode();
                log.writeLog(msg);
                if("false".equals(obj.getString("success"))){
                    msg = obj.getString("msg");
                    log.writeLog(msg);
                }

                return MsgResult.error(msg);
            }

            obj.getJSONArray("data");
            if(obj.getJSONArray("data").size()==0){
                RecordSet rs2 = new RecordSet();
                log.writeLog("开始同步项目，projectCode："+rs.getString("xmbh"));
                obj = new JSONObject();

                String sqr = Util.null2String(rs.getString("xmfzr"));
                if("".equals(sqr)){
                    sqr = Util.null2String(rs.getString("modedatacreater"));
                }else {
                    sqr = sqr.split(",")[0];
                }

                rs2.execute("SELECT loginid,lastname FROM HrmResource where id="+sqr);
                rs2.next();
                obj.put("createRealname",Util.null2String(rs2.getString("lastname")));
                obj.put("createUsername",Util.null2String(rs2.getString("loginid")));
                obj.put("deptId",Util.null2String(rs.getString("bmcx")));
                String depts = "";
                for (int i=0;i<3;++i){
                    String detp = "";
                    if(i==0){
                        detp = rs.getString("yjbm");
                    }else if(i==1){
                        detp = rs.getString("ejbm");
                    }else {
                        detp = rs.getString("sjbm");
                    }

                    rs2.execute("SELECT departmentname FROM HrmDepartment where id="+detp);
                    rs2.next();
                    depts+=Util.null2String(rs2.getString(1));
                    if(i!=2){
                        depts+=",";
                    }
                }
                obj.put("deptName",depts);
                obj.put("projectCode",Util.null2String(rs.getString("xmbh")));
                obj.put("projectName",Util.null2String(rs.getString("xmmc")));
                obj.put("ids",new String[]{});

                String strings[] = Util.null2String(rs.getString("cyry")).split(",");
                for (int i=0;i<strings.length;++i){
                    rs2.execute("select loginid from hrmresource where id="+strings[i]);
                    if(rs2.next()){
                        strings[i]=rs2.getString(1);
                    }else {
                        strings[i]="";
                    }
                    log.writeLog("usernames="+rs2.getString(1));
                }
                obj.put("usernames",strings);

                log.writeLog("开始创建项目，projectCode："+rs.getString("xmbh"));
                client = HttpClients.createDefault();
                post = new HttpPost(DocSys.url+"/file-system/file_center/createProject");
                post.addHeader("Tenant-Id","000000");
                post.addHeader("Blade-Auth", token);
                post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
                StringEntity entity = new StringEntity(obj.toString(),"UTF-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                post.setEntity(entity);
                response1 = client.execute(post);

                if(response1.getStatusLine().getStatusCode()!=200){
                    msg = "创建项目失败，projectCode："+rs.getString("xmbh")+",httpcode:"+response1.getStatusLine().getStatusCode();
                    log.writeLog(msg);
                    return MsgResult.error(msg);
                }
                log.writeLog("创建项目成功，projectCode："+rs.getString("xmbh"));
            }
        }

        return MsgResult.ok();
    }

    @Override
    public Result updataFileProject() throws Exception {
        RecordSet rs = new RecordSet();
        rs.execute("select yjxmbh,CoustomerFile,CoustomerFile2 from uf_srcbqrtz where modedatacreatedate<'2023-04-14'");
        while (rs.next()){
            int minute = DateTimeUtil.diffMinute(preTimeMillis,System.currentTimeMillis());
            if(minute>10){
                token = Token.getToken("ljp1212","李建潘1212");
            }

            String projectId = getProjectId(Util.null2String(rs.getString("yjxmbh")));
            String ids = "";
            //成本
            if(!"".equals(Util.null2String(rs.getString("CoustomerFile")))){
                ids+=rs.getString("CoustomerFile")+",";
            }

            //收入
            if(!"".equals(Util.null2String(rs.getString("CoustomerFile2")))){
                ids+=rs.getString("CoustomerFile2")+",";
            }

            ids = ids.substring(0,ids.lastIndexOf(","));
            String projectCode = rs.getString("yjxmbh");
            log.writeLog("一级项目，projectCode:"+projectCode);
            if(ids.length()>0){
                JSONArray objs = getFileName(ids);
                if(objs.size()>0){
                    String idsUpdate = "";
                    for(int i=0;i<objs.size();++i){
                        String objectKey = Util.null2String(objs.get(i));
                        if(!"".equals(objectKey)){
                            objectKey = Util.null2String(objs.getJSONObject(i).getString("objectKey"));

                            if("公共文件夹".contains(objectKey)){
                                idsUpdate+=objs.getJSONObject(i).getString("id")+",";
                            }
                        }
                    }
                    if(idsUpdate.length()>0){
                        log.writeLog("公共文件夹文件一级项目更新，projectCode:"+projectCode);
                        log.writeLog("公共文件夹文件id:"+idsUpdate+"，projectCode:"+projectCode);
                        idsUpdate = idsUpdate.substring(0,idsUpdate.lastIndexOf(","));
                        fileAssociate(idsUpdate,projectId);
                        file(idsUpdate);
                    }
                }
            }
        }
        return MsgResult.ok();
    }

    @Override
    public Result updataFileTag() throws Exception {
        RecordSet rs = new RecordSet();
        rs.execute("select yjxmbh,CoustomerFile,CoustomerFile2 from uf_srcbqrtz where modedatacreatedate>'2023-04-10'");
        while (rs.next()){
            int minute = DateTimeUtil.diffMinute(preTimeMillis,System.currentTimeMillis());
            if(minute>10){
                token = Token.getToken("ljp1212","李建潘1212");
            }
            String ids = "";
            JSONObject info;

            String projectCode = rs.getString("yjxmbh");
            log.writeLog("一级项目，projectCode:"+projectCode);

            JSONArray objects = new JSONArray();

            //成本
            if(!"".equals(Util.null2String(rs.getString("CoustomerFile")))){
                ids = Util.null2String(rs.getString("CoustomerFile"));
                JSONArray objs = getFileName(ids);
                for(int i=0;i<objs.size();++i){
                    if(!"".equals(Util.null2String(objs.get(i)))){
                        log.writeLog("成本标签更新，文件id:"+ids+"，projectCode:"+projectCode);
                        JSONObject obj = new JSONObject();
                        obj.put("stageId","1640268453489180673");
                        obj.put("classificationId","1640268476859842561");
                        obj.put("tagId","1640268480366280706");
                        obj.put("flowCode",objs.getJSONObject(i).getString("flowCode"));
                        obj.put("id",objs.getJSONObject(i).getString("id"));

                        objects.add(obj);
                    }
                }
            }
            //收入
            if(!"".equals(Util.null2String(rs.getString("CoustomerFile2")))){
                ids = Util.null2String(rs.getString("CoustomerFile2"));
                JSONArray objs = getFileName(ids);
                for(int i=0;i<objs.size();++i){
                    if(!"".equals(Util.null2String(objs.get(i)))){
                        log.writeLog("成本标签更新，文件id:"+ids+"，projectCode:"+projectCode);
                        JSONObject obj = new JSONObject();
                        obj.put("stageId","1640268453489180673");
                        obj.put("classificationId","1640268476859842561");
                        obj.put("tagId","1640268477572874242");
                        obj.put("flowCode",objs.getJSONObject(i).getString("flowCode"));
                        obj.put("id",objs.getJSONObject(i).getString("id"));

                        objects.add(obj);
                    }
                }
            }

            if(objects.size()>0){
                editFileInfo(objects);
            }
        }

        return MsgResult.ok();
    }

    public boolean fileAssociate(String ids,String projectCode) throws Exception {
        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        client = HttpClients.createDefault();
        post = new HttpPost(DocSys.url+"/file-system/file_center/fileAssociate?fileIds="+ids+"&projectId"+projectCode);
        post.addHeader("Tenant-Id","000000");
        post.addHeader("Blade-Auth", token);
        post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
        response1 = client.execute(post);
        httpEntity = response1.getEntity();
        obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

        if(response1.getStatusLine().getStatusCode()!=200){
            msg = "请求发送失败,httpcode:"+response1.getStatusLine().getStatusCode();
            log.writeLog(msg);
            if("false".equals(obj.getString("success"))){
                msg = "文件所属一级项目更新失败,msg:"+obj.getString("msg");
            }
            throw new Exception(msg);
        }

        return true;
    }

    public boolean editFileInfo(JSONArray o) throws Exception {
        HttpClient client = null;
        HttpPost post = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        client = HttpClients.createDefault();
        post = new HttpPost(DocSys.url+"/file-system/file_center/editFileInfo");
        post.addHeader("Tenant-Id","000000");
        post.addHeader("Blade-Auth", token);
        post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
        StringEntity entity = new StringEntity(o.toString(),"UTF-8");
        post.setEntity(entity);
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        response1 = client.execute(post);
        httpEntity = response1.getEntity();
        obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

        if(response1.getStatusLine().getStatusCode()!=200){
            msg = "请求发送失败,httpcode:"+response1.getStatusLine().getStatusCode();
            log.writeLog(msg);
            if("false".equals(obj.getString("success"))){
                msg = "文件信息更新失败,msg:"+obj.getString("msg");
            }
            throw new Exception(msg);
        }

        return true;
    }

    public String getProjectId(String projectCode) throws Exception {
        HttpClient client = null;
        HttpGet get = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        client = HttpClients.createDefault();
        get = new HttpGet(DocSys.url+"/file-system/file_center/getProjectList?code="+projectCode);
        get.addHeader("Tenant-Id","000000");
        get.addHeader("Blade-Auth", token);
        get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
        response1 = client.execute(get);
        httpEntity = response1.getEntity();
        obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

        if(response1.getStatusLine().getStatusCode()!=200){
            msg = "请求发送失败,httpcode:"+response1.getStatusLine().getStatusCode();
            log.writeLog(msg);
            if("".equals(obj.getString("success"))){
                msg = "文件信息更新失败,msg:"+obj.getString("msg");
            }
            throw new Exception(msg);
        }

        if(obj.getJSONArray("data").size()==0){
            return "";
        }

        return obj.getJSONArray("data").getJSONObject(0).getString("projectId");
    }

    public boolean file(String ids) throws Exception {
        HttpClient client = null;
        HttpGet get = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        client = HttpClients.createDefault();
        get = new HttpGet(DocSys.url+"/file-system/file_center/file?ids="+ids);
        get.addHeader("Tenant-Id","000000");
        get.addHeader("Blade-Auth", token);
        get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
        response1 = client.execute(get);
        httpEntity = response1.getEntity();
        obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

        if(response1.getStatusLine().getStatusCode()!=200){
            msg = "请求发送失败,httpcode:"+response1.getStatusLine().getStatusCode();
            log.writeLog(msg);
            if("false".equals(obj.getString("success"))){
                msg = "文件信息更新失败,msg:"+obj.getString("msg");
            }
            throw new Exception(msg);
        }

        return true;
    }

    public JSONArray getFileName(String ids) throws Exception {
        HttpClient client = null;
        HttpGet get = null;
        HttpResponse response1 = null;
        HttpEntity httpEntity = null;
        JSONObject obj = null;
        String msg = "";

        client = HttpClients.createDefault();
        get = new HttpGet(DocSys.url + "/file-system/file_center/getFileName?ids=" + ids);
        get.addHeader("Tenant-Id", "000000");
        get.addHeader("Blade-Auth", token);
        get.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
        response1 = client.execute(get);
        httpEntity = response1.getEntity();
        obj = JSONObject.parseObject(EntityUtils.toString(httpEntity));

        if (response1.getStatusLine().getStatusCode() != 200) {
            msg = "请求发送失败,httpcode:" + response1.getStatusLine().getStatusCode();
            log.writeLog(msg);
            if ("false".equals(obj.getString("success"))) {
                msg = "文件信息查询失败,msg:" + obj.getString("msg");
            }
            throw new Exception(msg);
        }

        return obj.getJSONArray("data");
    }
}