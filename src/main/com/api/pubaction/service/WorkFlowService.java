package com.api.pubaction.service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: WrokFlowService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-07-10  15:34
 * @Version: 1.0
 */
public interface WorkFlowService {
    //流程表单数据
    List<Map<String,String>> workflowInfoBySelect(String tableName,String fields,String sqlwhere,String currentnodetype) throws Exception;
    //建模表单数据
    List<Map<String,String>> modeInfoBySelect(String tableName,String fields,String sqlwhere) throws Exception;
}