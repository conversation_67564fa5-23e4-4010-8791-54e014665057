package com.api.pubaction.service;

import com.api.pubaction.entity.OneProject;

import java.util.List;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/31 9:10
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/8/31      HONOR          v1.0.0               修改原因
 */
public interface OneProjectModeAuthorityService {

    /**
     * @Description: 获取人员能够查看的项目
     * @param loginid
     * @return java.util.List<java.lang.Integer>
     * @Author:  lijianpan
     * @date: 2022/8/31 9:19
     */
    List<Integer> getIdOfList(String loginid) throws Exception;
}
