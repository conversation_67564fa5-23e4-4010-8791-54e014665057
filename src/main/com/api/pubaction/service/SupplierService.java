package com.api.pubaction.service;

import com.api.pubaction.entity.ReturnData;
import com.api.pubaction.entity.Supplier;

import java.util.Map;

public interface SupplierService {
    //添加供应商银行信息
    Boolean addBankInfo(Supplier supplier) throws Exception;
    //修改供应商银行信息
    Boolean updataBankInfo(Supplier supplier);
    //添加供应商资质
    Boolean addQualificationInfo(Supplier supplier);
    //修改供应商资质
    Boolean updataQualificationInfo(Supplier supplier);
    //供应商基础修改
    Boolean updataSupplierInfo(Supplier supplier);
    //供应商归属添加
    Boolean addCompanyAuth();
    //上传附件到供应商平台
    ReturnData upLoadSupplierSystemFile(String ids);
    /**
     * @description:采购需求供应商报名
     * @author: lijianpan
     * @date: 2024/8/8
     * @param: [procure 采购需求, supplier 供应商]
     * @return: java.lang.Boolean
     **/
    ReturnData procureSupplierApplication(Map params);
}
