package com.api.pubaction.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: Supplier
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-24  09:28
 * @Version: 1.0
 */
public class Supplier {
    //供应商名称
    private String name;
    //供应商编号
    private String code;
    //统一社会信用代码
    private String uniformSocialCreditCode;
    //成立时间
    private String createDate;
    //法人代表
    private String legalPerson;
    //注册资本
    private String registeredCapital;
    //经营范围
    private String businessScope;
    //营业执照照片
    private String businessLicenseImages;
    //供应商注册地址
    private String registeredAddress;
    //银行信息
    private List<Bank> bankInfos;
    //资质信息
    private List<Qualification> qualifications;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUniformSocialCreditCode() {
        return uniformSocialCreditCode;
    }

    public void setUniformSocialCreditCode(String uniformSocialCreditCode) {
        this.uniformSocialCreditCode = uniformSocialCreditCode;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getBusinessLicenseImages() {
        return businessLicenseImages;
    }

    public void setBusinessLicenseImages(String businessLicenseImages) {
        this.businessLicenseImages = businessLicenseImages;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public List<Bank> getBankInfos() {
        return bankInfos;
    }

    public void setBankInfos(List<Bank> bankInfos) {
        this.bankInfos = bankInfos;
    }

    public List<Qualification> getQualifications() {
        return qualifications;
    }

    public void setQualifications(List<Qualification> qualifications) {
        this.qualifications = qualifications;
    }

    @Override
    public String toString() {
        return "Supplier{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", uniformSocialCreditCode='" + uniformSocialCreditCode + '\'' +
                ", createDate='" + createDate + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", registeredCapital='" + registeredCapital + '\'' +
                ", businessScope='" + businessScope + '\'' +
                ", businessLicenseImages='" + businessLicenseImages + '\'' +
                ", registeredAddress='" + registeredAddress + '\'' +
                ", bankInfos=" + bankInfos +
                ", qualifications=" + qualifications +
                '}';
    }
}