package com.api.pubaction.entity;

public class OneProject {
    private int id;
    private String yjxmmc;
    private String yjxmbh;
    private String xmjl;
    private String yjbm;
    private String xmlx;
    private String sf;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getYjxmmc() {
        return yjxmmc;
    }

    public void setYjxmmc(String yjxmmc) {
        this.yjxmmc = yjxmmc;
    }

    public String getYjxmbh() {
        return yjxmbh;
    }

    public void setYjxmbh(String yjxmbh) {
        this.yjxmbh = yjxmbh;
    }

    public String getXmjl() {
        return xmjl;
    }

    public void setXmjl(String xmjl) {
        this.xmjl = xmjl;
    }

    public String getYjbm() {
        return yjbm;
    }

    public void setYjbm(String yjbm) {
        this.yjbm = yjbm;
    }

    public String getXmlx() {
        return xmlx;
    }

    public void setXmlx(String xmlx) {
        this.xmlx = xmlx;
    }

    public String getSf() {
        return sf;
    }

    public void setSf(String sf) {
        this.sf = sf;
    }

    @Override
    public String toString() {
        return "OneProject{" +
                "id=" + id +
                ", yjxmmc='" + yjxmmc + '\'' +
                ", yjxmbh='" + yjxmbh + '\'' +
                ", xmjl='" + xmjl + '\'' +
                ", yjbm='" + yjbm + '\'' +
                ", xmlx='" + xmlx + '\'' +
                ", sf='" + sf + '\'' +
                '}';
    }
}
