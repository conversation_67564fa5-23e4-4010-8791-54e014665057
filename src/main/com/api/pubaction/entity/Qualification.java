package com.api.pubaction.entity;

/**
 * @ClassName: Qualification
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-24  13:59
 * @Version: 1.0
 */
public class Qualification {
    //资质证书名称
    private String name;
    //资质证书编号
    private String code;
    //资质证书等级
    private String level;
    //有效期 开始时间
    private String startDate;
    //有效期 结束时间
    private String endDate;
    //颁发单位
    private String issuUnit;
    //资质照片
    private String images;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getIssuUnit() {
        return issuUnit;
    }

    public void setIssuUnit(String issuUnit) {
        this.issuUnit = issuUnit;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    @Override
    public String toString() {
        return "Qualification{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", level='" + level + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", issuUnit='" + issuUnit + '\'' +
                ", images='" + images + '\'' +
                '}';
    }
}