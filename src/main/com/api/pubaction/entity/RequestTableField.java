package com.api.pubaction.entity;

/**
 * @ClassName: RequestTableField
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-05-11  14:25
 * @Version: 1.0
 */
public class RequestTableField {
    /**
     * id
     */
    private String id;
    /**
     * id or requestid
     */
    private String indexType;
    /**
     * 流程表单id
     */
    private String formid;
    /**
     * 数据库字段名称
     */
    private String fieldName;
    /**
     * 字段值
     */
    private String fieldValue;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    public String getFormid() {
        return formid;
    }

    public void setFormid(String formid) {
        this.formid = formid;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }
}