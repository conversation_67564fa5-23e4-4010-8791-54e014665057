package com.api.pubaction.entity;

/**
 * @ClassName: ReturnData
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-12-22  14:32
 * @Version: 1.0
 */
public class ReturnData {
    // 是否成功（true or false）
    private boolean success;
    // 响应码
    private String code;
    // 返回数据信息
    private String msg;
    // 返回接口信息
    private Object data;

    public ReturnData() {

    }

    public ReturnData(boolean success, String code, String msg){
        this.success = success;
        this.code = code;
        this.msg = msg;
    }

    public ReturnData(boolean success,String code,String msg,Object data){
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Boolean getSuccess(){
        return success;
    }

    public static ReturnData ok(Object data){
        ReturnData rd = new ReturnData();
        rd.setSuccess(true);
        rd.setCode("200");
        rd.setData(data);
        return rd;
    }

    public static ReturnData ok(){
        ReturnData rd = new ReturnData();
        rd.setSuccess(true);
        rd.setCode("200");
        return rd;
    }

    public static ReturnData ok(Object data,String code){
        ReturnData rd = new ReturnData();
        rd.setSuccess(true);
        rd.setCode(code);
        rd.setData(data);
        return rd;
    }

    public static ReturnData error(){
        ReturnData rd = new ReturnData();
        rd.setSuccess(false);
        return rd;
    }

    public static ReturnData error(String msg){
        ReturnData rd = new ReturnData();
        rd.setSuccess(false);
        rd.setMsg(msg);
        return rd;
    }
    public static ReturnData error(String msg,String code){
        ReturnData rd = new ReturnData();
        rd.setSuccess(false);
        rd.setMsg(msg);
        rd.setCode(code);
        return rd;
    }

    @Override
    public String toString() {
        return "ReturnData{" +
                "success=" + success +
                ", code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}