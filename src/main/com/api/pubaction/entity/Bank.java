package com.api.pubaction.entity;

/**
 * @ClassName: SupplierBank
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-23  15:56
 * @Version: 1.0
 */
public class Bank {
    //银行账户名称
    private String bankAccountName;
    //开户行账号
    private String bankAccount;
    //开户银行名称
    private String bankName;
    //开户银行地址
    private String bankAddress;
    //联系电话
    private String phone;

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return "Bank{" +
                "bankAccountName='" + bankAccountName + '\'' +
                ", bankAccount='" + bankAccount + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankAddress='" + bankAddress + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }
}