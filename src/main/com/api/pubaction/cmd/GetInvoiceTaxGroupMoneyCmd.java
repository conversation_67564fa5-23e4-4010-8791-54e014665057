package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.cyitce.util.JoinFieldManage;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: GetInvoiceTaxGroupMoneyCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-01-04  10:09
 * @Version: 1.0
 */
public class GetInvoiceTaxGroupMoneyCmd extends AbstractCommonCommand<List<Map>> {
    private String ejxmid;
    private String[] tax;

    public GetInvoiceTaxGroupMoneyCmd(String ejxmid, String[] tax) {
        this.ejxmid = ejxmid;
        this.tax = tax;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<Map> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();

        List<Map> mapList = new ArrayList<>();
        rs.executeQuery("select kpsl,ISNULL(SUM(fpsywjsje),0) fpsywjsje from uf_fptzdjb where ejxmmc1='"+ejxmid+"' and kpsl is not null group by kpsl");
        while (rs.next()){
            Map<String,String> map = new HashMap<>();
            String ls = JoinFieldManage.getSelectName("kpsl","uf_fptzdjb",rs.getString(1));
            Float lsxs = Float.parseFloat(ls.substring(0,ls.indexOf("%")))/100;
            map.put("taxRate",new BigDecimal(lsxs).setScale(4, BigDecimal.ROUND_HALF_EVEN).toString());
            map.put("thisIncomeInvoiced",rs.getString(2));
            BigDecimal bd = new BigDecimal(Double.parseDouble(rs.getString(2))*lsxs).setScale(2, BigDecimal.ROUND_HALF_EVEN);
            String se = bd.toString();
            map.put("thisIncomeVat",se);
            mapList.add(map);
        }

        List<Map> mapList1 = new ArrayList<>();
        for (String s:tax){
            for (int n=0;n<mapList.size();++n){
                Map m = mapList.get(n);
                if(Double.valueOf(s).equals(Double.valueOf((String)m.get("taxRate")))){
                    mapList1.add(m);
                    break;
                }
            }
        }
        return mapList1;
    }
}