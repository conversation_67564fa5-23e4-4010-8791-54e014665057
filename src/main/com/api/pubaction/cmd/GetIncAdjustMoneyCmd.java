package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

/**
 * @ClassName: GetIncAdjustMoneyCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-22  09:33
 * @Version: 1.0
 */
public class GetIncAdjustMoneyCmd extends AbstractCommonCommand<String> {
    private String proTwoId;

    public GetIncAdjustMoneyCmd(String proTwoId) {
        this.proTwoId = proTwoId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        rs.execute("select isnull(sum(tz),0) from (select sum(srqrjebhs) tz from uf_srcbqrtz where qrzt=0 and ejxmmc="+proTwoId+"\n" +
                "union all\n" +
                "select isnull(sum(dzsrje),0) tz from uf_srcbqrtz where ejxmmc="+proTwoId+") a");
        rs.next();
        return rs.getString(1);
    }
}