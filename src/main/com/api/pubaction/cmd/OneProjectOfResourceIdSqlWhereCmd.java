package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/31 9:24
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/8/31      HONOR          v1.0.0               修改原因
 */
public class OneProjectOfResourceIdSqlWhereCmd extends AbstractCommonCommand<String>{
    private User user;

    @Override
    public BizLogContext getLogContext(){
        return null;
    }

    public OneProjectOfResourceIdSqlWhereCmd(User user){
        this.user=user;
    }

    @Override
    public String execute(CommandContext commandContext){
//        List<Integer> projectList=new ArrayList<>();
        int yjbm,ejbm,sjbm;
        String sqlCondition=" t1.sqr="+user.getUID();
        String projectIds="";

        RecordSet rs=new RecordSet();
        String sql="SELECT yjbm,ejbm,sjbm FROM uf_bmjzbdjm WHERE sqbm ="+user.getUserDepartment();
        rs.execute(sql);
        rs.next();
        yjbm = rs.getInt(1);
        ejbm = rs.getInt(2);
        sjbm = rs.getInt(3);

        try {
            //查询范围无限制
            sql = "select resourceid from HrmRoleMembers WHERE roleid in (2,87,178) and resourceid = "+user.getUID();
            rs.execute(sql);
            if(rs.next()){
                sqlCondition+=" or 1=1";
            }else{
                //项目经理与项目参与人员
                sql = "select id from uf_yjxmlxjm where t1. xmfzr like '"+user.getUID()+"%' or t1. xmfzr like '%,"+user.getUID()+"%' or t1. cyry like '"+user.getUID()+"%' or t1. cyry like '%,"+user.getUID()+"%'";
                rs.execute(sql);
                while (rs.next()) {
                    projectIds+=rs.getInt(1)+",";
                }

                //矩阵2.0
                sql = "select id from uf_yjxmlxjm where "+yjbm+" in (select bm from Matrixtable_9 where yjbmfzr like '"+user.getUID()+"%'or yjbmfzr like '%,"+user.getUID()+"%')";
                rs.execute(sql);
                while (rs.next()) {
                    projectIds+=rs.getInt(1)+",";
                }
                sql = "select id from uf_yjxmlxjm where "+ejbm+" in (select bm from Matrixtable_9 where ejbmfzr like '"+user.getUID()+"%'or ejbmfzr like '%,"+user.getUID()+"%')";
                rs.execute(sql);
                while (rs.next()) {
                    projectIds+=rs.getInt(1)+",";
                }
                sql = "select id from uf_yjxmlxjm where "+sjbm+" in (select bm from Matrixtable_9 where sjbmfzr like '"+user.getUID()+"%'or sjbmfzr like '%,"+user.getUID()+"%')";
                rs.execute(sql);
                while (rs.next()) {
                    projectIds+=rs.getInt(1)+",";
                }

                //角色权限
                sqlCondition += roleAut(2,yjbm,"176");
                sqlCondition += roleAut(3,yjbm,"177");
            }

            if(projectIds.length()>0){
                sqlCondition+=" or t1.id in ("+projectIds.substring(0,projectIds.length()-1)+")";
            }

            return sqlCondition;
        }catch (Exception e){
            return "";
        }
    }

    /**
     * @Description: 根据角色设置权限
     * @param yjbm
     * @param type 权限范围，1：查看所有，2：查看一级部门，3：查看自己所在部门及以下部门
     * @param roleId 角色id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/8/31 11:18
     */
    private String roleAut(int type,int yjbm,String roleId){
        String sqlCondition = "";
        String sqlWhere = "";
        String sql = "select resourceid,resourcetype,seclevelfrom,seclevelto,jobtitlelevel,subdepid from HrmRoleMembers WHERE roleid in ("+roleId+")";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        if(type==1){
            sqlWhere=" or 1=1";
        }else if(type==2){
            sqlWhere=" or t1.yjbm="+yjbm;
        }else if(type==3){
            sqlWhere=" or t1.yjbm="+yjbm+" or t1.ejbm="+yjbm+" or t1.sjbm="+yjbm;
        }

        while (rs.next()){
            if(rs.getInt("resourcetype")==1&&rs.getInt("resourceid")==user.getUID()){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==2&&rs.getInt("resourceid")==user.getUserSubCompany1()&&isSeclevel(user.getSeclevel(),rs)){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==3&&rs.getInt("resourceid")==user.getUserDepartment()&&isSeclevel(user.getSeclevel(),rs)){
                sqlCondition+=sqlWhere;
                break;
            }
            if (rs.getInt("resourcetype")==5){
                if(rs.getInt("subdepid")==user.getUserSubCompany1()&&rs.getString("resourceid").equals(user.getJobtitle())&&rs.getInt("jobtitlelevel")==2){
                    sqlCondition+=sqlWhere;
                    break;
                }else if(rs.getInt("subdepid")==user.getUserSubCompany1()&&rs.getString("resourceid").equals(user.getJobtitle())&&rs.getInt("jobtitlelevel")==3){
                    sqlCondition+=sqlWhere;
                    break;
                }
            }
        }
        return sqlCondition;
    }

    private boolean isSeclevel(String seclevel,RecordSet rs){
        return Integer.valueOf(seclevel)>=rs.getInt("seclevelfrom")&&Integer.valueOf(seclevel)<=rs.getInt("seclevelto");
    }
}

