package com.api.pubaction.cmd.auth;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: AppIdSaveCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-13  14:53
 * @Version: 1.0
 */
public class AppIdSaveCmd extends AbstractCommonCommand<ReturnData> {
    public String appid;
    public String name;

    public AppIdSaveCmd(String appid, String name) {
        this.appid = appid;
        this.name = name;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);

        try {
            rs.executeQuery("select APPID from ECOLOGY_BIZ_EC where APPID=?",new Object[]{appid});
            if(rs.next()){
                rs.executeQuery("select APPID from APPID_PUBLICKEY where APPID=?",new Object[]{appid});
                if(rs.next()){
                    return ReturnData.error("APPID："+appid+",已经被注册");
                }else {
                    return ReturnData.ok();
                }
            }

            rs.executeQuery("select max(ID)+1 from ECOLOGY_BIZ_EC");
            if(!rs.next()){
                return ReturnData.error("字段：id,不存在");
            }
            int id = rs.getInt(1);

            if(!rst.execute("insert into ECOLOGY_BIZ_EC(ID,APPID,NAME) values('"+id+"','"+appid+"','"+name+"')")){
                return ReturnData.error("APPID 注册失败");
            }

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            return ReturnData.error(e.getMessage());
        }

        return ReturnData.ok();
    }
}