package com.api.pubaction.cmd.auth;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: AppIdPublicKeySaveCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-13  15:11
 * @Version: 1.0
 */
public class AppIdPublicKeySaveCmd extends AbstractCommonCommand<ReturnData> {
    public String appid;
    public String spk;
    public String secrit;

    public AppIdPublicKeySaveCmd(String appid, String spk, String secrit) {
        this.appid = appid;
        this.spk = spk;
        this.secrit = secrit;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);

        Map p = new HashMap();
        try {
            rs.executeQuery("select APPID APPID_PUBLICKEY where APPID=?",new Object[]{appid});
            if(rs.next()){
                if(rst.executeUpdate("update APPID_PUBLICKEY set PUBLIC_KEY=? where APPID=?",new Object[]{spk,appid})){
                    return ReturnData.error("公钥信息更新失败");
                }
            }else {
                if(!rst.execute("insert into APPID_PUBLICKEY values('"+spk+"','"+appid+"')")){
                    return ReturnData.error("公钥信息插入失败");
                }
            }

            p.put("PUBLIC_KEY",spk);

            rst.commit();
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
        return ReturnData.ok();
    }
}