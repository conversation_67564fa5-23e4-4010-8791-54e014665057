package com.api.pubaction.cmd.auth;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

/**
 * @ClassName: GetAppIdPublicKeyCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-13  15:48
 * @Version: 1.0
 */
public class GetAppIdPublicKeyCmd extends AbstractCommonCommand<ReturnData> {
    public String appid;

    public GetAppIdPublicKeyCmd(String appid) {
        this.appid = appid;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        try {
            rs.executeQuery("SELECT * FROM ECOLOGY_BIZ_EC where APPID=?",new Object[]{appid});
            if(!rs.next()){
                return ReturnData.error("APPID 不存在");
            }
            rs.executeQuery("select PUBLIC_KEY from APPID_PUBLICKEY where APPID=?",new Object[]{appid});
            if(!rs.next()){
                return ReturnData.error("APPID 注册失败");
            }

            return ReturnData.ok(rs.getString(1));
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
    }
}