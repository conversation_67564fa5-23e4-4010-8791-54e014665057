package com.api.pubaction.cmd.auth;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

/**
 * @ClassName: GetPrivateKeyCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-09-14  13:58
 * @Version: 1.0
 */
public class GetPrivateKeyCmd extends AbstractCommonCommand<ReturnData> {
    public String appid;

    public GetPrivateKeyCmd(String appid) {
        this.appid = appid;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("SELECT secrit FROM ECOLOGY_BIZ_EC where APPID=?",new Object[]{appid});
            if(!rs.next()){
                return ReturnData.error("APPID未注册");
            }
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
        return ReturnData.ok(rs.getString(1));
    }
}