package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: getWorkFlowAttrCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-12-11  15:41
 * @Version: 1.0
 */
public class getWorkFlowAttrCmd extends AbstractCommonCommand<Map> {
    public String companyId;

    public getWorkFlowAttrCmd(String companyId) {
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        Map m = new HashMap();
        String formId = "";

        rs.executeQuery("select formid from uf_companyWorkflowMapping where companyid=? and type=0",companyId);
        if (rs.next()){
            formId = Util.null2String(rs.getString(1));
        }

        if("".equals(formId)){
            throw new ECException("没有找到该公司的“收入成本确认流程”");
        }

        rs.executeQuery("select a.id,a.workflowname,a.workflowtype,b.typename from workflow_base a \n" +
                "inner join workflow_type b on a.workflowtype=b.id\n" +
                "where formid=? and isvalid=1",formId);

        if(rs.next()){
            m.put("workflowid",rs.getString(1));
            m.put("workflowname",rs.getString(2));
            m.put("workflowtype",rs.getString(3));
            m.put("typename",rs.getString(4));
        }else {
            m.put("workflowid","");
            m.put("workflowname","");
            m.put("workflowtype","");
            m.put("typename","");
        }

        return m;
    }
}