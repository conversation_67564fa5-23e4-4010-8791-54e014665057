package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: UpdateModeHtmlLayoutScriptCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-16  14:40
 * @Version: 1.0
 */
public class UpdateModeHtmlLayoutScriptCmd extends AbstractCommonCommand<Boolean> {
    String id;
    String scriptStr;

    public UpdateModeHtmlLayoutScriptCmd(String id, String scriptStr) {
        this.id = id;
        this.scriptStr = scriptStr;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        boolean boo = true;
        if(id!=null&&id!=""){
            boo = rs.execute("update modehtmllayout set scriptstr='"+scriptStr+"' where id="+id);
        }
        return boo;
    }
}