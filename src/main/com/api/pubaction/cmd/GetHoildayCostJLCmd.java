package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetHoildayCostCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-22  09:41
 * @Version: 1.0
 */
public class GetHoildayCostJLCmd extends AbstractCommonCommand<Map<String,String>> {
    private String proTwoId;

    public GetHoildayCostJLCmd(String proTwoId) {
        this.proTwoId = proTwoId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String,String> execute(CommandContext commandContext) {
        Map<String,String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        rs.execute("select sum(ljrg) ljrg,sum(ljcl) ljcl,sum(ljwx) ljwx,sum(ljcb) ljcb,sum(ljfy) ljfy from (select ISNULL(SUM(bcxjzjrg),0) ljrg,ISNULL(SUM(bcxjzjcl),0) ljcl,ISNULL(SUM(bcxjlwwx),0) ljwx,ISNULL(SUM(bcxjqtcb),0) ljcb,ISNULL(SUM(bcxjgcglfzzfy),0) ljfy from uf_jl_xmjssp where ejxmmc="+proTwoId+
                        " union all" +
                " select ISNULL(SUM(lszjrg),0) ljrg,ISNULL(SUM(lszjcl),0) ljcl,ISNULL(SUM(lslwwx),0) ljwx,ISNULL(SUM(lsqtcb),0) ljcb,ISNULL(SUM(lsgcglfzzfy),0) ljfy from uf_jl_xmjssp where qrzt=0 and ejxmmc="+proTwoId+") a");
        if(rs.next()){
            map.put("ls_rg",rs.getString(1));
            map.put("ls_cl",rs.getString(2));
            map.put("ls_wx",rs.getString(3));
            map.put("ls_cb",rs.getString(4));
            map.put("ls_gc",rs.getString(5));
        }else {
            map.put("ls_rg","0");
            map.put("ls_cl","0");
            map.put("ls_wx","0");
            map.put("ls_cb","0");
            map.put("ls_gc","0");
        }

        rs.executeQuery("select b.htlx,ISNULL(b.htbhszj,0) bhsjgzjdwy from uf_jl_ejxmlxsqjmbd a inner join uf_jl_skhtdjbaspbd b on a.htnbbh=b.htnbbh where a.id="+proTwoId);
        if(rs.next()){
            String dthtje = rs.getString(2);
            if("0".equals(Util.null2String(rs.getString(1)))){
                rs.execute("select ISNULL(SUM(ddbhszj),0) from uf_jl_dddjbaspjmbd where ejxmmc="+proTwoId);
                if(rs.next()){
                    map.put("twoProOrderMoneySum",rs.getString(1));
                }else {
                    map.put("twoProOrderMoneySum","0");
                }
            }else if("1".equals(Util.null2String(rs.getString(1)))){
                map.put("twoProOrderMoneySum",dthtje);
            }
        }else {
            map.put("twoProOrderMoneySum","0");
        }

        rs.execute("SELECT id,ejxmnbmc,ejxmnbbh,ISNULL(xmys,0) FROM uf_jl_ejxmlxsqjmbd where yjxmmc=(select yjxmmc from uf_jl_ejxmlxsqjmbd where id="+proTwoId+") and ejxmnbmc like '%公共%'");
        if(rs.next()){
            map.put("conTwoProid",rs.getString(1));
            map.put("conTwoProName",rs.getString(2));
            map.put("conTwoProCode",rs.getString(3));
            map.put("conTwoProResidueShareMoney",rs.getString(4));
        }else {
            map.put("conTwoProid","");
            map.put("conTwoProName","");
            map.put("conTwoProCode","");
            map.put("conTwoProResidueShareMoney","");
        }

        rs.execute("SELECT jszt FROM uf_jl_ejxmlxsqjmbd where id="+proTwoId);
        if(rs.next()){
            map.put("finalAccountsStatus",rs.getString(1));
        }else {
            map.put("finalAccountsStatus","");
        }

        return map;
    }
}