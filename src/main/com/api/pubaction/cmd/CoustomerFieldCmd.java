package com.api.pubaction.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;


public class CoustomerFieldCmd extends AbstractCommonCommand<JSONArray> {
    String fieldName;
    String formid;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public CoustomerFieldCmd(String fieldName,String formid){
        this.fieldName = fieldName;
        this.formid = formid;
    }

    @Override
    public JSONArray execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        JSONArray jsonArray = new JSONArray();
        System.out.println("sss");
        try {

            rs.execute("SELECT id,viewtype,detailtable FROM workflow_billfield where billid="+formid+" and fieldname like '"+fieldName+"%' and fieldhtmltype=1 and type=1");
            while (rs.next()){
                JSONObject obj = new JSONObject();
                obj.put("field",rs.getString(1));
                obj.put("viewtype",rs.getString(2));
                if(rs.getInt(2)==1){
                    obj.put("detail",rs.getString(3).substring(rs.getString(3).indexOf("_dt")+3));
                }
                jsonArray.add(obj);
            }
            return jsonArray;
        }catch (Exception e){
            return jsonArray;
        }
    }
}
