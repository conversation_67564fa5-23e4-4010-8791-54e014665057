package com.api.pubaction.cmd.U8;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.StaticObj;
import weaver.general.Util;
import weaver.interfaces.datasource.DataSource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: U8SecondProjectCostCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-22  16:06
 * @Version: 1.0
 */
public class U8SecondProjectCostCmd extends AbstractCommonCommand<List<Map>> {
    private String SecondProjectCode;
    private String companyId;

    public U8SecondProjectCostCmd(String secondProjectCode, String companyId) {
        SecondProjectCode = secondProjectCode;
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<Map> execute(CommandContext commandContext) {
        List<Map> mapList = new ArrayList<>();
        String ccode=null;
        BigDecimal sum = null;

        DataSource ds = null;
        Connection conn = null;
        PreparedStatement pst = null;
        ResultSet rs = null;

        RecordSet rset = null;
        String formId = "602";
        String U8Table = "dwd_gc_oa_cw_gl_accass_602_2023_df_t";
        try {
            rset = new RecordSet();
            rset.executeQuery("select formid from uf_companyWorkflowMapping where type=1 and companyid=?",companyId);
            if(rset.next()){
                formId = Util.null2String(rset.getString(1));
            }else {
                throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
            }

            if("".equals(formId)){
                formId = "602";
            }

            if("602".equals(formId)){
                U8Table = "dwd_gc_oa_cw_gl_accass_602_2023_df_t";
            }else if("603".equals(formId)){
                U8Table = "dwd_jl_oa_cw_gl_603_2023_df_t";
            }else if("605".equals(formId)){
                U8Table = "dwd_st_oa_cw_gl_605_2023_df_t";
            }
        }catch (Exception e){
            throw new ECException("公司id："+companyId+",该公司的U8账套id为空");
        }

        try {
            //加载ssyt_cw数据源
            ds = (DataSource) StaticObj.getServiceByFullname(("datasource.ssyt_cw"), DataSource.class);
            conn = ds.getConnection();

            for (int i=1;i<=5;++i){
                ccode = "50010"+i;
                sum = new BigDecimal(0.00);
                pst = conn.prepareStatement("select mb from "+U8Table
                        +" where citem_id = '"+SecondProjectCode+"' AND ccode = '"+i+"'");
                rs = pst.executeQuery();

                Map<String,String> map = new HashMap<>();
                while (rs.next()){
                    sum = sum.add(new BigDecimal(rs.getString(1)).setScale(2, RoundingMode.HALF_EVEN));
                }

                map.put("ccode",ccode);
                map.put("cost",sum.toString());
                mapList.add(map);
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ECException("ssyt_cw 数据源错误！",e);
        }finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (pst != null) {
                    pst.close();
                }
                if (conn != null) {
                    conn.close();
                }
            }catch (Exception e){

            }

        }
        return mapList;
    }
}