package com.api.pubaction.cmd.U8;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.StaticObj;
import weaver.general.Util;
import weaver.interfaces.datasource.DataSource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetSecondProjectCostSumCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-11-24  15:29
 * @Version: 1.0
 */
public class U8SecondProjectCostSum2Cmd extends AbstractCommonCommand<Map> {
    private JSONArray secondProjectCodes;

    public U8SecondProjectCostSum2Cmd(JSONArray secondProjectCodes) {
        this.secondProjectCodes = secondProjectCodes;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map execute(CommandContext commandContext) {
        Map<String,String> costSum = new HashMap<>();
        BigDecimal sum;

        DataSource ds = null;
        Connection conn = null;
        PreparedStatement pst = null;
        ResultSet rs = null;
        RecordSet rset = null;
        String formId = "602";
        String U8Table = "dwd_gc_oa_cw_gl_accass_602_2023_df_t";
        try {
            //加载ssyt_cw数据源
            ds = (DataSource) StaticObj.getServiceByFullname(("datasource.ssyt_cw"), DataSource.class);
            conn = ds.getConnection();

            JSONArray codes = secondProjectCodes;
            for (int i=0;i<codes.size();++i){
                JSONObject code = codes.getJSONObject(i);

                String companyId = code.getString("companyId");
                String secondProjectCode = code.getString("companyId");

                rset = new RecordSet();
                rset.executeQuery("select formid from uf_companyWorkflowMapping where type=1 and companyid=?",companyId);
                if(rset.next()){
                    formId = Util.null2String(rset.getString(1));
                }else {
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }

                if("".equals(formId)){
                    throw new ECException("公司id："+companyId+",请配置该公司的U8账套id");
                }

                if("602".equals(formId)){
                    U8Table = "dwd_gc_oa_cw_gl_accass_602_2023_df_t";
                }else if("603".equals(formId)){
                    U8Table = "dwd_jl_oa_cw_gl_603_2023_df_t";
                }

                sum = new BigDecimal("0.00");

                for (int j=1;j<=5;++j){
                    pst = conn.prepareStatement("select mb from " + U8Table +
                            "where citem_id = '"+secondProjectCode+"' AND ccode = '"+j+"'");
                    rs = pst.executeQuery();

                    while (rs.next()){
                        sum = sum.add(new BigDecimal(rs.getString(1)).setScale(2, RoundingMode.HALF_EVEN));
                    }
                }
                costSum.put(secondProjectCode,sum.toString());
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ECException("ssyt_cw 数据源错误！",e);
        }finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (pst != null) {
                    pst.close();
                }
                if (conn != null) {
                    conn.close();
                }
            }catch (Exception e){

            }

        }
        return costSum;
    }
}