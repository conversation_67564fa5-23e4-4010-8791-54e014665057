package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: getHolidayListCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-28  15:00
 * @Version: 1.0
 */
public class GetHolidayListCmd extends AbstractCommonCommand<List<String>> {
    private String year;
    private String type;

    public GetHolidayListCmd(String year, String type) {
        this.year = year;
        this.type = type;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<String> execute(CommandContext commandContext) {
        List<String> list = new ArrayList<>();
        RecordSet rs = new RecordSet();
        rs.execute("select holidayDate FROM kq_HolidaySet \n" +
                "where holidayDate like '"+year+"%' \n" +
                "and changeType in ("+type+") "+
                "and groupId = 1");

        while (rs.next()){
            list.add(rs.getString(1));
        }
        return list;
    }
}