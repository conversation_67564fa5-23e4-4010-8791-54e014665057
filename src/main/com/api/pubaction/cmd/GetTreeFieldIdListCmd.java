package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: GetTreeFieldIdListCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-05  20:23
 * @Version: 1.0
 */
public class GetTreeFieldIdListCmd extends AbstractCommonCommand<List<Integer>> {
    private String formid;
    private String treeId;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetTreeFieldIdListCmd(String formid, String treeId) {
        this.formid = formid;
        this.treeId = treeId;
    }

    @Override
    public List<Integer> execute(CommandContext commandContext) {
        List<Integer> list = new ArrayList<>();
        RecordSet rs;
        try {
            list = new ArrayList<>();
            rs = new RecordSet();

            rs.execute("select id from workflow_billfield \n" +
                    "where type=256 \n" +
                    "and billid=" + formid +" \n" +
                    "and fielddbtype="+treeId);
            while (rs.next()){
                list.add(rs.getInt(1));
            }

            return list;
        }catch (Exception e){
            return list;
        }
    }
}