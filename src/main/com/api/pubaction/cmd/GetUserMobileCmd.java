package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;


/**
 * @ClassName: GetUserMobileCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2022-11-23  18:42
 * @Version: 1.0
 */
public class GetUserMobileCmd extends AbstractCommonCommand<String> {
    public String params;

    public GetUserMobileCmd(String params) {
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        String sql = "SELECT field49 FROM cus_fielddata where id=(SELECT id FROM HrmResource where loginid='"+params+"') and scopeid=1";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()){
            return Util.null2String(rs.getString(1));
        }
        return "";
    }
}