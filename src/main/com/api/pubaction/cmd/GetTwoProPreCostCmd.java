package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetTwoProPreCostCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-11  17:44
 * @Version: 1.0
 */
public class GetTwoProPreCostCmd extends AbstractCommonCommand<Map<String,String>> {
    private String proTwoId;

    public GetTwoProPreCostCmd(String proTwoId) {
        this.proTwoId = proTwoId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, String> execute(CommandContext commandContext) {
        Map<String,String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        rs.execute("SELECT top 1 * FROM uf_srcbqrtz where czlx in (0,2) and ejxmmc="+proTwoId+" order by id DESC");
        if(rs.next()){
            map.put("ls_rg",rs.getString("bcxjzjrg"));
            map.put("ls_cl",rs.getString("bcxjzjcl"));
            map.put("ls_wx",rs.getString("bcxjlwwx"));
            map.put("ls_cb",rs.getString("bcxjqtcb"));
            map.put("ls_gc",rs.getString("bcxjgcglfzzfy"));
        }else {
            map.put("ls_rg","0");
            map.put("ls_cl","0");
            map.put("ls_wx","0");
            map.put("ls_cb","0");
            map.put("ls_gc","0");
        }

        rs.execute("select isnull(sum(gysbcbxje),0) from uf_clkbxbdjm_dt5 where ejxmmc="+proTwoId);
        if(rs.next()){
            map.put("bc_cl",rs.getString(1));
        }else {
            map.put("bc_cl","0");
        }

        rs.execute("select isnull(sum(gysbcbxje),0) from uf_gchzkbxlcbdjm_dt6 where ejxmmc="+proTwoId);
        if(rs.next()){
            map.put("bc_wx",rs.getString(1));
        }else {
            map.put("bc_wx","0");
        }

        return map;
    }
}