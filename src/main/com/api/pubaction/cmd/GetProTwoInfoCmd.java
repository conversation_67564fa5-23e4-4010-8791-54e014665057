package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetProTwoInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-15  15:39
 * @Version: 1.0
 */
public class GetProTwoInfoCmd  extends AbstractCommonCommand<Map<String,String>> {
    private String id;

    public GetProTwoInfoCmd(String id) {
        this.id = id;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, String> execute(CommandContext commandContext) {
        Map<String,String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        rs.execute("select ejxmnbmc,ejxmnbbh from uf_ejxmlx where id="+id);
        if(rs.next()){
            map.put("name", Util.null2String(rs.getString(1)));
            map.put("code", Util.null2String(rs.getString(2)));
            return map;
        }else {
            return null;
        }
    }
}