package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: GetScriptCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-03-16  14:32
 * @Version: 1.0
 */
public class GetModeHtmlLayoutScriptCmd extends AbstractCommonCommand<List<Map<String,String>>> {
    Integer modeId;

    public GetModeHtmlLayoutScriptCmd(Integer modeId) {
        this.modeId = modeId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<Map<String,String>> execute(CommandContext commandContext) {
        List<Map<String,String>> list = new ArrayList<>();
        RecordSet rs = new RecordSet();
        rs.execute("SELECT id,type,scriptstr FROM modehtmllayout where modeid="+modeId);
        while (rs.next()){
            if(rs.getInt(2)!=4){
                Map<String,String> map = new HashMap<>();
                map.put("id", Util.null2String(rs.getString(1)));
                map.put("scriptstr",Util.null2String(rs.getString(3)));
                list.add(map);
            }
        }
        return list;
    }
}