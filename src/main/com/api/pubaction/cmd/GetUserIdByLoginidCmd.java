package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import javax.rmi.CORBA.Util;

public class GetUserIdByLoginidCmd extends AbstractCommonCommand<Integer> {
    String loginid;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetUserIdByLoginidCmd(String loginid){
        this.loginid=loginid;
    }

    @Override
    public Integer execute(CommandContext commandContext) {
        String sql;
        RecordSet rs;
        int userid=-1;

        try {
            rs = new RecordSet();
            sql = "select id from hrmresource where loginid='"+loginid+"'";
            rs.execute(sql);
            if(rs.next()){
                userid = rs.getInt(1);
            }
            return userid;
        }catch (Exception e){
            return -1;
        }

    }
}
