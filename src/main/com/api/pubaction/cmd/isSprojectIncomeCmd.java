package com.api.pubaction.cmd;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: isSprojectIncomeCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-24  09:30
 * @Version: 1.0
 */
public class isSprojectIncomeCmd extends AbstractCommonCommand<ReturnData> {
    private String secondProjectId;
    private String companyId;

    public isSprojectIncomeCmd(String secondProjectId, String companyId) {
        this.secondProjectId = secondProjectId;
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        boolean flag = false;

        String content = "";
        String title = "";

        int type = 0;
        if("6".equals(companyId)){
            rs.executeQuery("SELECT r.requestmark FROM formtable_main_541 a\n" +
                    "inner join formtable_main_541_dt1 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_437 a\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_552 a\n" +
                    "inner join formtable_main_552_dt1 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.fpzt=1 and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }
        }

        if(!flag){
            title = "可以决算。";

            return ReturnData.ok(title);
        }else {
            if(type==1){
                title = "存在在途流程：";

                content=content.lastIndexOf(",")==content.length()-1?content.substring(0,content.lastIndexOf(",")):content;
            }else if(type==2){
                title = "未满足决算条件：";
            }

            return ReturnData.error(title+content);
        }
    }
}