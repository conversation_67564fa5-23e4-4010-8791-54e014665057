package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.List;

/**
 * @ClassName: 该字段在节点是否显示
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-04  11:26
 * @Version: 1.0
 */
public class NodeFieldIsShow extends AbstractCommonCommand<Boolean> {
    private String fieldid;
    private String nodeid;

    public NodeFieldIsShow(String fieldid, String nodeid) {
        this.fieldid = fieldid;
        this.nodeid = nodeid;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        rs.execute("SELECT b.isview FROM workflow_flownode a inner join workflow_nodeform b on a.nodeid=b.nodeid\n" +
                "where b.fieldid="+fieldid+" and b.isview=1 and b.nodeid="+nodeid);
        if (rs.next()){
            return true;
        }
        return false;
    }
}