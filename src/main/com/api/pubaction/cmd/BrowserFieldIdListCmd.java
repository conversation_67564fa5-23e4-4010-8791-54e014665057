package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.List;

public class BrowserFieldIdListCmd extends AbstractCommonCommand<List<String>> {
    private String formid;
    private String billid;
    private String viewtype;

    public BrowserFieldIdListCmd(String formid, String billid,String viewtype) {
        this.formid = formid;
        this.billid = billid;
        this.viewtype = viewtype;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<String> execute(CommandContext commandContext) {
        List<String> list = new ArrayList<>();
        RecordSet rs;
        rs = new RecordSet();
        rs.execute("select id from workflow_billfield \n" +
                "where type=161\n" +
                "and billid="+formid+
                "and viewtype="+viewtype+
                "and fielddbtype in (SELECT 'browser.'+a.showname FROM \n" +
                "MODE_BROWSER a inner join mode_custombrowser b on a.customid=b.id\n" +
                "where b.formid="+billid+")");
        while (rs.next()){
            list.add(Util.null2String(rs.getString(1)));
        }
        return list;
    }
}
