package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetConfirmStatusCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-26  14:11
 * @Version: 1.0
 */
public class GetConfirmStatusCmd extends AbstractCommonCommand<Map<String,String>> {
    String twoProejctId;

    public GetConfirmStatusCmd(String twoProejctId) {
        this.twoProejctId = twoProejctId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, String> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        Map<String,String> map = new HashMap<>();

        try {
            String sql = "SELECT count(*) as num FROM uf_srcbqrtz where qrzt=0 and ejxmmc=?";
            rs.executeQuery(sql,twoProejctId);
            if(rs.next()){
                if ("0".equals(Util.null2String(rs.getString("num")))){
                    map.put("status","0");
                }else {
                    map.put("status","1");
                }
            }
        }catch (Exception e){
            writeLog(e.getMessage());
            throw new ECException(e.getMessage());
        }

        return map;
    }
}