package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetProTagInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-03  10:52
 * @Version: 1.0
 */
public class GetProTagInfo extends AbstractCommonCommand<Map<String,String>> {
    private String pid;

    public GetProTagInfo(String pid) {
        this.pid = pid;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, String> execute(CommandContext commandContext) {
        Map<String,String > map = new HashMap<>();

        RecordSet rs = new RecordSet();
        rs.execute("select * from uf_xmwjbq where pid="+pid);

        if (rs.next()){
            map.put("pid",rs.getString("pid"));
            map.put("bqmz",rs.getString("bqmz"));
            map.put("parent",rs.getString("parent"));
            map.put("level1",rs.getString("level1"));
        }

        return map;
    }
}