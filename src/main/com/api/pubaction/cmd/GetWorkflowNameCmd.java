package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: 获取流程名称
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-15  15:21
 * @Version: 1.0
 */
public class GetWorkflowNameCmd extends AbstractCommonCommand<Map<String,String>> {
    private Map<String ,Object> params;

    public GetWorkflowNameCmd(Map<String ,Object> params) {
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String,String> execute(CommandContext commandContext) {
        Map<String,String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        String worlflowid = (String) params.get("workflowid");
        String requestid = (String) params.get("requestid");

        if(!"".equals(Util.null2String(worlflowid))){
            rs.execute("select workflowname from workflow_base where isvalid=1 and id="+worlflowid);
            if(rs.next()){
                map.put("workFlowName",Util.null2String(rs.getString(1)));
            }
        }

        if(!"".equals(Util.null2String(requestid))){
            rs.execute("select requestmark from workflow_requestbase where requestId="+requestid);
            if(rs.next()){
                map.put("requestCode",Util.null2String(rs.getString(1)));
            }
        }

        return map;
    }
}