package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

/**
 * @ClassName: BrowserFieldIdOneCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-02-17  14:02
 * @Version: 1.0
 */
public class BrowserFieldIdOneCmd extends AbstractCommonCommand<String> {
    private String formid;
    private String billid;
    private String showname;
    private String viewtype;

    public BrowserFieldIdOneCmd(String formid, String billid, String showname,String viewtype) {
        this.formid = formid;
        this.billid = billid;
        this.showname = showname;
        this.viewtype = viewtype;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public String execute(CommandContext commandContext) {
        RecordSet rs;
        rs = new RecordSet();
        rs.execute("select id from workflow_billfield \n" +
                "where type=161\n" +
                "and billid="+formid+
                "and viewtype="+viewtype+
                "and fielddbtype in (SELECT 'browser.'+a.showname FROM \n" +
                "MODE_BROWSER a inner join mode_custombrowser b on a.customid=b.id\n" +
                "where b.formid="+billid+" and a.showname='"+showname+"')");
        if (rs.next()){
            return Util.null2String(rs.getString(1));
        }
        return "";
    }
}