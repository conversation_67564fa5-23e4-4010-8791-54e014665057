package com.api.pubaction.cmd;

import com.alibaba.fastjson.JSONArray;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import org.docx4j.wml.R;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GetProjectInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-01-05  17:30
 * @Version: 1.0
 */
public class GetProjectInfoCmd extends AbstractCommonCommand<Map<String,String>> {
    private String id;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetProjectInfoCmd(String id) {
        this.id = id;
    }

    @Override
    public Map<String, String> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        Map<String,String> map = new HashMap<>();

        rs.execute("select * from uf_yjxmlxjm where id="+id);
        if(rs.next()){
            map.put("code", Util.null2String(rs.getString("xmbh")));
        }

        return map;
    }
}