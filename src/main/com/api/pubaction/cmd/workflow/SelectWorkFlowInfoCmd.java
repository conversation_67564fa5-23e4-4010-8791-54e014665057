package com.api.pubaction.cmd.workflow;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: workflowInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-07-10  15:54
 * @Version: 1.0
 */
public class SelectWorkFlowInfoCmd extends AbstractCommonCommand<Map<String, Object>> {
    private String sql;
    private int fieldCount;

    public SelectWorkFlowInfoCmd(String sql, int fieldCount) {
        this.sql = sql;
        this.fieldCount = fieldCount;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        List<Map<String, String>> list=null;
        Map<String,Object> obj = new HashMap<>();
        obj.put("status",true);
        try {
            list = new ArrayList<>();
            if(!rs.execute(sql)){
                throw new ECException(rs.getExceptionMsg());
            }
            while (rs.next()){
                Map<String,String> map = new HashMap<>();
                for(int i=1;i<=fieldCount;++i){
                    map.put(rs.getColumnName(i),rs.getString(i));
                }
                list.add(map);
            }

            obj.put("data",list);
        }catch (Exception e){
            writeLog("SelectWorkFlowInfoCmd-----:"+e.getMessage());
            writeLog(e);
            obj.put("status",false);
            obj.put("msg",e.getMessage());
        }

        return obj;
    }
}