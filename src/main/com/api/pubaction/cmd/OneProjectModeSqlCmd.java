package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 该类的功能描述
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/8/31 10:13
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/8/31      HONOR          v1.0.0               修改原因
 */
public class OneProjectModeSqlCmd extends AbstractCommonCommand<List<Integer>> {
    private String sqlWhere;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public OneProjectModeSqlCmd(String sqlWhere){
        this.sqlWhere = sqlWhere;
    }

    @Override
    public List<Integer> execute(CommandContext commandContext) {
        List<Integer> list = new ArrayList<>();
        String sql="select id from uf_yjxmlxjm t1 where "+sqlWhere;
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {

            list.add(rs.getInt(1));

        }
        return list;
    }
}
