package com.api.pubaction.cmd.supplier;

import com.api.pubaction.entity.Qualification;
import com.api.pubaction.entity.Supplier;
import com.api.pubaction.util.MathUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;
import weaver.general.Util;

/**
 * @ClassName: updateSupplierInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-25  14:33
 * @Version: 1.0
 */
public class UpdateSupplierInfo extends AbstractCommonCommand<Boolean> {
    Supplier s;

    public UpdateSupplierInfo(Supplier supplier) {
        this.s = supplier;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        boolean ret = true;
        String sql = "";
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);

        try {
            String rgisteredCapital = "";
            if(!"".equals(Util.null2String(s.getRegisteredCapital()))){
                rgisteredCapital = MathUtil.round(s.getRegisteredCapital(),2);
            }

            sql = "update uf_gysdjbdjm set gysmc=?,tyshxydm=?,yyfw=?,frdb=?,zczbwy=?,gyszcdz=?,yyzzfb=?,clsj=? where gysbh=?";
            ret = rs.executeQuery(sql,s.getName(),s.getUniformSocialCreditCode(),s.getBusinessScope(),s.getLegalPerson(),rgisteredCapital,s.getRegisteredAddress(),s.getBusinessLicenseImages(),s.getCreateDate(),s.getCode());

            if(!ret){
                writeLog("sql执行失败："+sql);
                throw new Exception("sql执行失败："+sql);
            }

            rs.commit();
            return true;
        }catch (Exception e){
            rs.rollback();
            writeLog(e);
            throw new ECException(e.getMessage());
        }
    }
}