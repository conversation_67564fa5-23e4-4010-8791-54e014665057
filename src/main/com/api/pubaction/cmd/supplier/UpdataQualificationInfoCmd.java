package com.api.pubaction.cmd.supplier;

import com.api.pubaction.entity.Bank;
import com.api.pubaction.entity.Qualification;
import com.api.pubaction.entity.Supplier;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: UpdataQualificationInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-25  11:27
 * @Version: 1.0
 */
public class UpdataQualificationInfoCmd extends AbstractCommonCommand<Boolean> {
    Supplier supplier;

    public UpdataQualificationInfoCmd(Supplier supplier) {
        this.supplier = supplier;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        boolean ret = true;
        String sql = "";
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);

        try {
            sql = "select id from uf_gysdjbdjm where gysbh=?";
            rs.executeQuery(sql,supplier.getCode());
            if(rs.next()){
                String mainid = rs.getString(1);

                if(supplier.getBankInfos()!=null){
                    for (Qualification qua:supplier.getQualifications()){
                        sql = "update uf_gysdjbdjm_dt3 set xgxyzzmc=?,zsbh=?,zzdj=?,yxqjssj=?,yxqkssj=?,bfdw=?,zzzp=? where mainid=? and zsbh=?";
                        ret = rs.executeUpdate(sql,qua.getName(),qua.getCode(),qua.getLevel(),qua.getStartDate(),qua.getEndDate(),qua.getIssuUnit(),qua.getImages(),mainid,qua.getCode());
                        if (!ret){
                            writeLog("sql执行失败："+sql);
                            throw new Exception("sql执行失败："+sql);
                        }
                    }
                }
            }

            rs.commit();
            return true;
        }catch (Exception e){
            rs.rollback();
            writeLog(e.getMessage());
            throw new ECException(e.getMessage());
        }
    }
}