package com.api.pubaction.cmd.supplier;

import com.api.pubaction.entity.Bank;
import com.api.pubaction.entity.Supplier;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: UpdataBankInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-24  14:55
 * @Version: 1.0
 */
public class UpdataBankInfoCmd extends AbstractCommonCommand<Boolean>{
    Supplier supplier;

    public UpdataBankInfoCmd(Supplier supplier) {
        this.supplier = supplier;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        boolean ret = true;
        String sql = "";
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);

        try {
            sql = "select id from uf_gysdjbdjm where gysbh=?";
            rs.executeQuery(sql,supplier.getCode());
            if(rs.next()){
                String mainid = rs.getString(1);

                if(supplier.getBankInfos()!=null){
                    for (Bank b:supplier.getBankInfos()){
                        sql = "update uf_gysdjbdjm_dt1 set gysmc=?,khxmc=?,khxdz=?,lxdh=? where mainid=? and khxzh=?";
                        ret = rs.executeUpdate(sql,b.getBankAccountName(),b.getBankName(),b.getBankAddress(),b.getPhone(),mainid,b.getBankAccount());
                        if (!ret){
                            writeLog("sql执行失败："+sql);
                            throw new Exception("sql执行失败："+sql);
                        }
                    }
                }
            }

            rs.commit();
            return true;
        }catch (Exception e){
            rs.rollback();
            writeLog(e.getMessage());
            throw new ECException(e.getMessage());
        }
    }
}