package com.api.pubaction.cmd.supplier;

import com.api.pubaction.entity.Bank;
import com.api.pubaction.entity.Qualification;
import com.api.pubaction.entity.Supplier;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: AddQualificationInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-25  11:20
 * @Version: 1.0
 */
public class AddQualificationInfoCmd extends AbstractCommonCommand<Boolean> {
    Supplier supplier;

    public AddQualificationInfoCmd(Supplier supplier) {
        this.supplier = supplier;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        boolean ret = true;
        String sql = "";
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);

        try {
            sql = "select id from uf_gysdjbdjm where gysbh=?";
            rs.executeQuery(sql,supplier.getCode());
            if(rs.next()){
                String mainid = rs.getString(1);

                if(supplier.getQualifications()!=null){
                    for (Qualification qua:supplier.getQualifications()){
                        sql = "select zsbh from uf_gysdjbdjm_dt3 where mainid=? and zsbh=?";
                        rs.executeQuery(sql,mainid,qua.getCode());
                        if (rs.next()){
                            writeLog("该供应商下已经存在该证书，证书编号："+qua.getCode());
                            throw new Exception("该供应商下已经存在该证书，证书编号："+qua.getCode());
                        }

                        sql = "insert into uf_gysdjbdjm_dt3(mainid,xgxyzzmc,zsbh,zzdj,yxqjssj,yxqkssj,bfdw,zzzp) " +
                                "values("+mainid+",'"+qua.getName()+"','"+qua.getCode()+"','"+qua.getLevel()+"','"+qua.getStartDate()+"','"+qua.getEndDate()+"','"+qua.getIssuUnit()+"','"+qua.getImages()+"')";
                        ret = rs.execute(sql);
                        if (!ret){
                            writeLog("sql执行失败："+sql);
                            throw new Exception("sql执行失败："+sql);
                        }
                    }
                }
            }

            rs.commit();
            return true;
        }catch (Exception e){
            rs.rollback();
            writeLog(e);
            throw new ECException(e.getMessage());
        }
    }
}