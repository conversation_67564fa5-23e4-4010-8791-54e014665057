package com.api.pubaction.cmd.supplier;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: ProcureSupplierApplicationCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-08-08  16:11
 * @Version: 1.0
 */
public class ProcureSupplierApplicationCmd extends AbstractCommonCommand<ReturnData> {
    private Map params;

    public ProcureSupplierApplicationCmd(Map params) {
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        int cID;//采购需求ID
        int sID;//供应商ID
        String pStatus;//采购状态
        String sStatus;//供应商状态
        String procure = String.valueOf(params.get("procure"));
        String supplier = String.valueOf(params.get("supplier"));
        User user = (User)params.get("user");

        try {
            if(procure.isEmpty()){
                throw new Exception("procure 参数为空");
            }
            if(supplier.isEmpty()){
                throw new Exception("supplier 参数为空");
            }

            rs.executeQuery("select id,cgzt from uf_styjs_cgxqspbd where cgxqbh=?",procure);
            if(rs.next()){
                cID = rs.getInt(1);
                pStatus = rs.getString(2);
                if(!pStatus.equals("0")){
                    throw new Exception("已停止采购");
                }
            }else {
                throw new Exception("采购需求不存在");
            }

            rs.executeQuery("select id,status from uf_st_CustomerInfo where gysbh=?",supplier);
            if(rs.next()){
                sID = rs.getInt(1);
                sStatus = rs.getString(2);
                if(!sStatus.equals("2")){
                    throw new Exception("无效供应商");
                }
            }else {
                throw new Exception("供应商不存在");
            }

            rs.executeQuery("select id from uf_styjs_cgxqgysbmd where gysbh=? and cgxqbh=?",new Object[]{supplier,procure});
            if(rs.next()){
                throw new Exception("供应商已报名");
            }

            // 获取年、月、日
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            String date = dateFormat.format(calendar.getTime());
            String time = timeFormat.format(calendar.getTime());
            writeLog("报名日期："+date);
            writeLog("报名时间："+time);

            String values = "values("+sID+",'"+supplier+"',"+cID+",'"+procure+"','"+date+"',987,"+user.getUID()+",0,'"+date+"','"+time+"','"+UUID.randomUUID()+"')";
            rs.execute("insert into uf_styjs_cgxqgysbmd(gys,gysbh,cgxq,cgxqbh,bmrq,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,MODEUUID) "+values);
            rs.executeQuery("select id from uf_styjs_cgxqgysbmd where gysbh=? and cgxqbh=?",new Object[]{supplier,procure});
            if(!rs.next()){
                throw new Exception("供应商报名失败");
            }

            return ReturnData.ok();
        }catch (Exception e){
            return ReturnData.error(e.getMessage());
        }
    }
}