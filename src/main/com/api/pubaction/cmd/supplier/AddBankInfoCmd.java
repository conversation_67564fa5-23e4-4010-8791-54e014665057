package com.api.pubaction.cmd.supplier;


import com.api.pubaction.entity.Bank;
import com.api.pubaction.entity.Supplier;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: AddBankInfoCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-04-23  15:54
 * @Version: 1.0
 */
public class AddBankInfoCmd extends AbstractCommonCommand<Boolean> {
    Supplier supplier;

    public AddBankInfoCmd(Supplier supplier) {
        this.supplier = supplier;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        boolean ret = true;
        String sql = "";
        RecordSetTrans rs = new RecordSetTrans();
        rs.setAutoCommit(false);

        try {
            sql = "select id from uf_gysdjbdjm where gysbh=?";
            rs.executeQuery(sql,supplier.getCode());
            if(rs.next()){
                String mainid = rs.getString(1);

                if(supplier.getBankInfos()!=null){
                    for (Bank b:supplier.getBankInfos()){
                        sql = "select khxzh from uf_gysdjbdjm_dt1 where mainid=? and khxzh=?";
                        rs.executeQuery(sql,mainid,b.getBankAccount());
                        if (rs.next()){
                            writeLog("该供应商下已经存在该银行账号："+b.getBankAccount());
                            throw new Exception("该供应商下已经存在该银行账号："+b.getBankAccount());
                        }

                        sql = "insert into uf_gysdjbdjm_dt1(mainid,gysmc,khxmc,khxzh,khxdz,lxdh) " +
                                "values("+mainid+",'"+b.getBankAccountName()+"','"+b.getBankName()+"','"+b.getBankAccount()+"','"+b.getBankAddress()+"','"+b.getPhone()+"')";
                        ret = rs.execute(sql);
                        if (!ret){
                            writeLog("sql执行失败："+sql);
                            throw new Exception("sql执行失败："+sql);
                        }

                        sql = "insert into uf_VendorAccountInfo(zhmc,yxmc,yxzx,khxxxjzh,gysmc,gysbh,nsrsbh,khdz,formmodeid) " +
                                "values('"+b.getBankAccountName()+"','"+b.getBankName()+"','"+b.getBankAccount()+"','"+b.getBankAddress()+"','"+supplier.getName()+ "','" +
                                supplier.getCode()+"','"+supplier.getUniformSocialCreditCode()+"','"+supplier.getRegisteredAddress()+"',238)";
                        ret = rs.execute(sql);
                        if (!ret){
                            writeLog("sql执行失败："+sql);
                            throw new Exception("sql执行失败："+sql);
                        }
                    }
                }
            }

            rs.commit();
            return true;
        }catch (Exception e){
            rs.rollback();
            writeLog(e);
            throw new ECException(e.getMessage());
        }
    }
}