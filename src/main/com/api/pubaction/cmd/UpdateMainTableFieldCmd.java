package com.api.pubaction.cmd;

import com.api.pubaction.entity.RequestTableField;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;

/**
 * @ClassName: UpdateMainTableFieldCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-05-11  14:11
 * @Version: 1.0
 */
public class UpdateMainTableFieldCmd extends AbstractCommonCommand<Boolean> {
    private RequestTableField rtf;

    public UpdateMainTableFieldCmd(RequestTableField rtf) {
        this.rtf = rtf;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        RecordSetTrans rst = null;
        String sql = null;
        try {
            rst = new RecordSetTrans();
            rst.setAutoCommit(false);

            sql = "SELECT tablename FROM workflow_bill where id=?";
            rst.executeQuery(sql,rtf.getFormid());
            if (rst.next()){
                if("id".equals(rtf.getIndexType())){
                    sql = "update "+rst.getString(1)+" set "+rtf.getFieldName()+"=? where id=?";
                }else{
                    sql = "update "+rst.getString(1)+" set "+rtf.getFieldName()+"=? where requestid=?";
                }

                rst.executeUpdate(sql,rtf.getFieldValue(),rtf.getId());
            }else {
                throw new Exception("formid:"+rtf.getFormid()+"，表单不存在");
            }

            rst.commit();
        }catch (Exception e){
            rst.rollback();
            throw new ECException(e.getMessage());
        }
        return true;
    }
}