package com.api.pubaction.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.exception.ECException;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: isSecondProjectReimburseCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-12-11  15:42
 * @Version: 1.0
 */
public class isSecondProjectReimburseCmd extends AbstractCommonCommand<Map> {
    private String secondProjectId;
    private String companyId;

    public isSecondProjectReimburseCmd(String secondProjectId, String companyId) {
        this.secondProjectId = secondProjectId;
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        boolean flag = false;

        Map map = new HashMap();
        map.put("code",flag);
        map.put("data","");

        String content = "";
        String title = "";

        int type = 0;
        if("6".equals(companyId)){
            rs.executeQuery("SELECT r.requestmark FROM formtable_main_518 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_417 a\n" +
                    "inner join formtable_main_417_dt4 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_330 a\n" +
                    "inner join formtable_main_330_dt6 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_336 a\n" +
                    "inner join formtable_main_336_dt5 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_359 a\n" +
                    "inner join formtable_main_359_dt7 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_541 a\n" +
                    "inner join formtable_main_541_dt1 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_437 a\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            if(!flag){
                rs.executeQuery("SELECT sfxsj,sjzt,yszt FROM uf_ejxmlx where ejxmmc=?",secondProjectId);
                if(rs.next()){
                    String sfxsj = Util.null2String(rs.getString("sfxsj"));
                    String sjzt = Util.null2String(rs.getString("sjzt"));
                    String yszt = Util.null2String(rs.getString("sjzt"));

                    if(((!"".equals(sfxsj)&&!"0".equals(sfxsj))||!"2".equals(sjzt))||(!"1".equals(sfxsj)||(!"1".equals(yszt)&&!"2".equals(yszt)))){
                        flag = true;
                        type = 2;
                        content="【是否需审计】！=是或空值，【二级项目审计状态】！=“已审计”或者【是否需审计】！=否，【二级项目验收状态】！=“已初验”或“已终验”";
                    }
                }
            }
        }else if("14".equals(companyId)){
            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1179 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1));
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1203 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1434 a\n" +
                    "inner join formtable_main_1434_dt1 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1163 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1208 a\n" +
                    "inner join formtable_main_1208_dt1 b on a.id=b.mainid\n" +
                    "inner join workflow_nownode c on a.requestid=c.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where c.nownodetype in (1,2) and b.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1211 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1329 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1197 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1201 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1193 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1198 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }
        }else if("24".equals(companyId)){
            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1758 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1681 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1587 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1591 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }

            rs.executeQuery("SELECT r.requestmark FROM formtable_main_1594 a \n" +
                    "inner join workflow_nownode b on a.requestid=b.requestid\n" +
                    "inner join workflow_requestbase r on a.requestid=r.requestid\n" +
                    "where b.nownodetype in (1,2) and a.ejxmmc=?",secondProjectId);
            if(rs.next()){
                flag = true;
                type = 1;
                content+=Util.null2String(rs.getString(1))+",";
            }
        }

        if(!flag){
            title = "可以决算。";
        }else if(type==1){
            title = "存在在途流程：";

            content=content.lastIndexOf(",")==content.length()-1?content.substring(0,content.lastIndexOf(",")):content;
        }else if(type==2){
            title = "未满足决算条件：";
        }

        map.put("code",flag);
        map.put("data",title+content);

        return map;
    }
}