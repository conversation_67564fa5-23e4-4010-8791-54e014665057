package com.api.pubaction.cmd.project;

import com.api.pubaction.util.JoinFieldManage;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: GetProjectInvoiceCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-04-07  14:50
 * @Version: 1.0
 */
public class GetProjectInvoiceCmd extends AbstractCommonCommand<List<Map>> {
    private String ejxmid;
    private String faFlowCode;
    private String companyId;

    public GetProjectInvoiceCmd(String ejxmid, String faFlowCode, String companyId) {
        this.ejxmid = ejxmid;
        this.faFlowCode = faFlowCode;
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<Map> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        List<Map> list = new ArrayList<>();

        if("6".equals(companyId)){
            rs.execute("select * from uf_fptzdjb where ejxmmc1="+ejxmid+" and fpsywjsje!=0 and fpjszt=0 and\n" +
                    "kpsl in (select sl from uf_srcbqrtz_dt1 where mainid="+faFlowCode+" GROUP BY sl)");

            while (rs.next()){
                Map<String,String> map = new HashMap<>();

                map.put("fph",rs.getString("id"));
                map.put("fphm",rs.getString("fphm"));
                map.put("kpsj",rs.getString("kpsj"));
                map.put("kphsje",rs.getString("kphsje"));
                map.put("kpbhsje",rs.getString("kpbhsje"));
                String slStr = Util.null2String(JoinFieldManage.getSelectName("kpsl","uf_fptzdjb",rs.getString("kpsl")));
                if("".equals(slStr)){
                    slStr = "0%";
                }
                try {
                    slStr = String.valueOf(Double.parseDouble(slStr.split("%")[0])/100);
                }catch (Exception e){
                    slStr = "0.00";
                }

                map.put("kpsl",slStr);
                map.put("kpslid",rs.getString("kpsl"));
                map.put("fpsywjsje",rs.getString("fpsywjsje"));
                list.add(map);
            }
        }else if("14".equals(companyId)){
            rs.execute("select * from uf_jl_fptzdjb where ejxmmc="+ejxmid+" and fpsywjsje!=0 and fpjszt=0 and\n" +
                    "kpsl in (select sl from uf_jl_xmjssp_dt1 where mainid="+faFlowCode+" GROUP BY sl)");

            while (rs.next()){
                Map<String,String> map = new HashMap<>();

                map.put("fph",rs.getString("id"));
                map.put("fphm",rs.getString("fphm"));
                map.put("kpsj",rs.getString("kpsj"));
                map.put("kphsje",rs.getString("kphsje"));
                map.put("kpbhsje",rs.getString("kpbhsje"));
                String slStr = Util.null2String(JoinFieldManage.getSelectName("kpsl","uf_jl_fptzdjb",rs.getString("kpsl")));
                if("".equals(slStr)){
                    slStr = "0%";
                }
                try {
                    slStr = String.valueOf(Double.parseDouble(slStr.split("%")[0])/100);
                }catch (Exception e){
                    slStr = "0.00";
                }

                map.put("kpsl",slStr);
                map.put("kpslid",rs.getString("kpsl"));
                map.put("fpsywjsje",rs.getString("fpsywjsje"));
                list.add(map);
            }
        }else if("24".equals(companyId)){
            rs.execute("select * from uf_stfptzdjb where ejxmmc="+ejxmid+" and fpsywjsje!=0 and fpjszt=0 and\n" +
                    "kpsl in (select sl from uf_st_xmjstz_dt1 where mainid="+faFlowCode+" GROUP BY sl)");

            while (rs.next()) {
                Map<String, String> map = new HashMap<>();

                map.put("fph", rs.getString("id"));
                map.put("fphm", rs.getString("fphm"));
                map.put("kpsj", rs.getString("kpsj"));
                map.put("kphsje", rs.getString("kphsje"));
                map.put("kpbhsje", rs.getString("kpbhsje"));
                String slStr = Util.null2String(JoinFieldManage.getSelectName("kpsl", "uf_stfptzdjb", rs.getString("kpsl")));
                if ("".equals(slStr)) {
                    slStr = "0%";
                }
                try {
                    slStr = String.valueOf(Double.parseDouble(slStr.split("%")[0]) / 100);
                } catch (Exception e) {
                    slStr = "0.00";
                }

                map.put("kpsl", slStr);
                map.put("kpslid", rs.getString("kpsl"));
                map.put("fpsywjsje", rs.getString("fpsywjsje"));
                list.add(map);
            }
        }
        return list;
    }
}