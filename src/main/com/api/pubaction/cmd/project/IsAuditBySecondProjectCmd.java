package com.api.pubaction.cmd.project;

import com.api.pubaction.entity.ReturnData;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;

/**
 * @ClassName: IsAuditBySecondProjectCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2024-10-17  22:15
 * @Version: 1.0
 */
public class IsAuditBySecondProjectCmd extends AbstractCommonCommand<ReturnData> {
    private String secondProjectId;

    public IsAuditBySecondProjectCmd(String secondProjectId) {
        this.secondProjectId = secondProjectId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public ReturnData execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();

        try {
            rs.executeQuery("select b.id from uf_xkjfxmbalcbdjm a \n" +
                    "inner join uf_xkjfxmbalcbdjm_dt1 b on a.id=b.mainid \n" +
                    "where a.ejxmmc="+secondProjectId+"  and b.id not in " +
                    "(select y.jfxmxz from uf_jfxmnbyssjgltz x " +
                    "inner join uf_jfxmnbyssjgltz_dt1 y on x.id=y.mainid " +
                    "where x.ejxmmc="+secondProjectId+")");

            if (rs.next()){
                return ReturnData.error("存在未验收审定的交付项目");
            }
        }catch (Exception e){
            ReturnData.error(e.getMessage());
        }
        return ReturnData.ok();
    }
}