package com.api.pubaction.cmd.project;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import org.eclipse.swt.internal.C;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.*;

/**
 * @ClassName: GetProjectCodeListCmd
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2023-10-17  15:23
 * @Version: 1.0
 */
public class GetProjectCodeListCmd extends AbstractCommonCommand<List<Map>> {
    private String name;
    private int companyId;

    public GetProjectCodeListCmd(String name, int companyId) {
        this.name = name;
        this.companyId = companyId;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public List<Map> execute(CommandContext commandContext) {
        RecordSet rs = new RecordSet();
        String sql = "";
        List<Map> mapList = new ArrayList<>();
        String currDate = "";

        Calendar c = Calendar.getInstance();
        String year = String.valueOf(c.get(Calendar.YEAR));
        String month = String.valueOf(c.get(Calendar.MONTH)+1);
        if(month.length()==1){
            month = "0"+month;
        }
        String day = String.valueOf(c.get(Calendar.DATE));
        if(day.length()==1){
            day = "0"+day;
        }

        currDate = year+"-"+month+"-"+ day;

        try {
            if(companyId==6){
                if("".equals(name)){
                    sql = "select zbxmmc,tbbh,tbjzsj,tbjzsj1 from formtable_main_698 where tbjzsj>='"+currDate+"'";
                }else {
                    sql = "select zbxmmc,tbbh,tbjzsj,tbjzsj1 from formtable_main_698 where zbxmmc like '%"+name+"%' and tbjzsj>='"+currDate+"'";
                }
            }else if(companyId==11){

            }else if(companyId==14){
                if("".equals(name)){
                    sql = "select zbxmmc,tbnbbh,bsjztdsj,bsjztdsz from formtable_main_1132 where bsjztdsj>='"+currDate+"'";
                }else {
                    sql = "select zbxmmc,tbnbbh,bsjztdsj,bsjztdsz from formtable_main_1132 where zbxmmc like '%"+name+"%' and bsjztdsj>='"+currDate+"'";
                }
            }else if(companyId==16){

            }

            rs.execute(sql);

            while (rs.next()){
                Map<String,String> m = new HashMap<>();
                m.put("name", Util.null2String(rs.getString(1)));
                m.put("code",Util.null2String(rs.getString(2)));
                m.put("endDate",Util.null2String(rs.getString(3))+" "+Util.null2String(rs.getString(4)));
                mapList.add(m);
            }

            return mapList;
        }catch (Exception e){
            writeLog(e.getMessage());
        }
        return new ArrayList<>();
    }
}