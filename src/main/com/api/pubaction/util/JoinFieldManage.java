package com.api.pubaction.util;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

/**
 * @Description: 表单关联类型字段管理
 * @version: v1.0.0
 * @Auther: HONOR
 * @Date: 2022/6/24 16:41
 * <p>
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2022/6/24      HONOR          v1.0.0               修改原因
 */
public class JoinFieldManage extends BaseBean {

    /**
     * @Description: 获取分部信息
     * @param id 分部id
     * @param fieldName 字段名
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/24 15:19
     */
    public static String getCompanyName(String id,String fieldName){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select "+fieldName+" from hrmsubcompany where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取下拉框选项内容
     * @param tableName 字段名
     * @param tableName 表单名
     * @param fieldValue 字段值
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/24 10:31
     */
    public static String getSelectName(String fieldName,String tableName,String fieldValue) {
        if(fieldName==null||fieldValue==null||tableName==null){
            return "";
        }
        if("".equals(fieldName)||"".equals(fieldValue)||"".equals(tableName)){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("SELECT c.selectname FROM workflow_billfield a,workflow_bill b,workflow_SelectItem c "+
                " where a.billid = b.id and  a.id=c.fieldid "+
                " and b.tablename ='"+tableName+"' and  a.fieldname='"+fieldName+"'  and c.selectvalue ="+fieldValue);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取供应商账户名称
     * @param id 供应商账户id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/24 16:17
     */
    public static String getVendorAccountName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select gysmc from uf_VendorAccountInfo where id = "+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: String类型日期转换,
     * @param date  格式：2022-06-28
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/6/28 13:28
     */
    public static String dateStringToString(String date){
        if(date==null||"".equals(date)){
            return "";
        }

        String[] str = date.split("-");
        StringBuffer sb = new StringBuffer();
        sb.append(str[0]+"年"+str[1]+"月"+str[2]+"日");
        return sb.toString();
    }

    /**
     * @Description:  获取人员姓名
     * @param id 人员id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/12 14:27
     */
    public static String getPersonName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select lastname from hrmresource where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1)).replaceAll("\\d+","");
    }

    /**
     * @Description: 获取一级项目名称
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/12 14:58
     */
    public static String getProjectName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select xmmc from uf_yjxmlxjm where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取省份名称
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/12 15:02
     */
    public static String getProvinceName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select provincename from HrmProvince where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取客户名称
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/12 15:54
     */
    public static String getCustomerName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select name from uf_crm_customer where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取收款合同名称
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/12 16:05
     */
    public static String getContractName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select htmc from uf_kgdthtbd where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取人员职务
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/13 11:15
     */
    public static String getJobactivity(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select jobactivityname from hrmjobactivities where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }

    /**
     * @Description: 获取部门名称
     * @param id
     * @return java.lang.String
     * @Author:  lijianpan
     * @date: 2022/7/13 11:15
     */
    public static String getDepartmentName(String id){
        if("".equals(id)||id==null){
            return "";
        }

        RecordSet rs = new RecordSet();
        rs.execute("select departmentname from HrmDepartment where id="+id);
        rs.next();
        return Util.null2String(rs.getString(1));
    }
}
