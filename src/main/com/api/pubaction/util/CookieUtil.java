package com.api.pubaction.util;

import com.alibaba.fastjson.JSONObject;
import com.api.pubaction.config.DocSys;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import weaver.interfaces.workflow.action.cyitce.invoice.RSAUtils;

import java.security.PublicKey;


public class CookieUtil {

    private static String url = "https://oa.cyitce.com/api/hrm/login/checkLogin";//正式服

    private static String Burl = DocSys.url+"/blade-auth/oauth/token";

    private static String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnC72BNzcnNkSFPVDHzmzLP/5F2ffygKzS7owIfBBjdikvH3ddcfikpOq0o+DqtE5eMOTpOog6c699xFwKu3gHjxAXDUc0N+rYdteuBBZsDa2KTLGXInqgQn9+ah+lpPqwVnaR9eUiVbeSsPbqB59pbie/eTOSuswAATmeEQHKv0oFhORxczlhw21e0EwMyTcBLKguuTp/O/upawBj0Qxln693IK3MKUtonM98qnCH78YwixvN96NNiMqc3dOr2PBGSyUL7tuVltL9gGmlbVXF6eiunss2lka7/5n4MoXNoF1z4eKqnsG+wfXqdeRae9qBjM43e8V0qYBmn5ylfevuwIDAQAB";

    /**
     *
     * @description 获取cookie
     * @param username 登录名
     * @param password 登录密码
     * @return
     */
    public static String getCookies(String username, String password) {
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            client = HttpClients.createDefault();
            post = new HttpPost();
            post.addHeader("Content-Type", "application/json");
            builder = new URIBuilder(url);
            builder.addParameter("loginid",username);
            builder.addParameter("userpassword",password);
            post = new HttpPost(builder.build());
            response = client.execute(post);

            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                return "";
            }
            headers = response.getHeaders("Set-Cookie");
            for (int i = 0;i<headers.length;++i){
                if("ecology_JSessionId".equals(headers[i].getElements()[0].getName())){
                    cookieStr = headers[i].getElements()[0].getValue();
                }
            }
            return cookieStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     *
     * @description 获取文件管理系统 token
     * @param loginid loginid
     * @return
     */
    public static String getToken(String loginid,String name){
        HttpClient client = null;
        HttpPost post = null;
        URIBuilder builder = null;
        HttpResponse response = null;
        HttpEntity httpEntity = null;
        Header[] headers = null;
        String cookieStr = null;
        try {
            client = HttpClients.createDefault();
            builder = new URIBuilder(Burl);
            //获取公钥
            PublicKey publicKey = RSAUtils.getPublicKey(PUBLIC_KEY);
            System.out.println("--------------------user.name-----------------------"+name);
            String user = "{\"loginId\":\""+loginid+"\",\"timestamp\":\""+System.currentTimeMillis()+"\",\"name\":\""+name+"\"}";
            System.out.println("--------------------token-----------------------"+user);
            //RSA加密
            String secret = RSAUtils.encrypt(user,publicKey);
            builder.addParameter("tenantId","000000");
            builder.addParameter("grant_type","oa_secret");
            builder.addParameter("secret",secret);
            post = new HttpPost(builder.build());
            post.addHeader("Content-Type", "application/json;charset=UTF-8");
            post.addHeader("Authorization", "Basic c3dvcmQ6c3dvcmRfc2VjcmV0");
            System.out.println("开始发送请求");
            response = client.execute(post);

            System.out.println("httpcode:"+response.getStatusLine().getStatusCode());
            if(response.getStatusLine().getStatusCode() != HttpStatus.SC_OK){
                throw new Exception("获取token失败");
            }

            httpEntity = response.getEntity();
            return JSONObject.parseObject(EntityUtils.toString(httpEntity)).getString("access_token");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("报错: "+e.getMessage());
            return "";
        }
    }

//    public static void main(String[] args) {
//        long start = System.currentTimeMillis();
//        System.out.println(getToken("zhh0527","曾鸿浩0527"));
//        long end = (System.currentTimeMillis() - start)/1000;
//        System.out.println(end);
//    }
}
