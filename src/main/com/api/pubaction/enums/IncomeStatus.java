package com.api.pubaction.enums;

/**
 * @ClassName: IncomeStatus
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-03-09  20:24
 * @Version: 1.0
 */
public enum IncomeStatus {
    ALL(0,"all","总收入"),
    YES_ADD(1,"yesAdd","已决算收入"),
    NO_ADD(2,"noAdd","未决算收入");

    private int code;
    private String value;
    private String desc;

    private IncomeStatus(int code,String value,String desc){
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}