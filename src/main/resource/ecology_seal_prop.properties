### \u6D4B\u8BD5\u73AF\u5883
### \u5929\u5A01   # 测试天威云 相关认证
itruscloud.appId=a5cc224cbe1f4b
itruscloud.secretKey=c471e66f68a642e5984e0d0c2d1c7e1d
## \u5B9E\u540D\u8BA4\u8BC1\u670D\u52A1
#itruscloud.realAuth.api.url=http://139.159.242.69:8070/apigate/authapi
itruscloud.realAuth.api.url=https://open.itruscloud.com/apigate/authapi/
## \u7535\u5B50\u5370\u7AE0\u670D\u52A1
itruscloud.eSeal.api.url=http://139.159.242.69:8070/apigate/contractapi
itruscloud.eSeal.api.serviceCode=contract003
itruscloud.eSeal.api.companyUUID=M011GYSR00YS8WP
# \u6587\u4EF6\u5B58\u50A8\u8DEF\u5F84
itruscloud.eSeal.api.savePath=/temp 
## \u77ED\u4FE1\u670D\u52A1\u670D\u52A1
itruscloud.sms.api.url=http://open.itruscloud.com/apigate
itruscloud.sms.api.autograph=\u3010\u5929\u5A01\u8BDA\u4FE1\u3011
## \u7269\u7406\u5370\u7AE0  实体章对应请求接口
nbhuilang.physical.api.url=http://139.159.242.69:9080/SealCustomerApi
## oa  oa接口
#oa.cyitce.url=http://218.245.99.207:7001/api
oa.cyitce.url=https://oa.cyitce.com/api


third.table=uf_seal_thrid_mapp
third.yz.table=uf_seal_yz
# 目前支持的公司
support.company=6,16
#MD5 请求校验
check.MD5=!&@CyitcE@2025..
#### \u751F\u6210\u73AF\u5883
### \u5B9E\u540D\u8BA4\u8BC1\u670D\u52A1
#itruscloud.realAuth.api.url=https://open.itruscloud.com/apigate/authapi/
#itruscloud.realAuth.api.appId=233bbf6b44454b
#itruscloud.realAuth.api.secretKey=e88903cfbed6465bac333b263a78fa04
#
### \u7535\u5B50\u5370\u7AE0
#itruscloud.eSeal.api.url=http://183.230.9.103:11500/apigate/contractapi
#itruscloud.eSeal.api.appId=233bbf6b44454b
#itruscloud.eSeal.api.secretKey=e88903cfbed6465bac333b263a78fa04
#itruscloud.eSeal.api.serviceCode=contract003
#itruscloud.eSeal.api.companyUUID=M011JRGICPKTWF7
##\u6587\u4EF6\u5B58\u50A8\u8DEF\u5F84
#itruscloud.eSeal.api.savePath=/temp
#
### \u7269\u7406\u5370\u7AE0
#nbhuilang.physical.api.url=http://139.159.242.69:9080