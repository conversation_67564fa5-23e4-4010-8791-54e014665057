<script>
    //自定义按钮
    jQuery(document).ready(function () {
        //明细表2 数组
        var rows = WfForm.getDetailAllRowIndexStr("detail_2");
        rows = rows === "" ? [] : rows.split(",");
        console.log("明细表数组" + rows)
        //用印明细2数组
        $.each(rows, function (key, value) {
            var userId = WfForm.getFieldValue("field75799");
            var docId = WfForm.getFieldValue("field75118_" + value);
            // 定义按钮的HTML
            var buttonHtml =

                '<a onclick="btnAction(' + userId + "," + docId + ')" title="用印执行请点击">' +
                '<div  style="color:blue">用印执行请点击</div>' +
                '</a>'
            ;

            var anid = "field75814_" + value
            // 将按钮插入到指定的单元格中，btnLink 为 表单单元格 自定义数据的id值
            //jQuery(anid).html(buttonHtml);
            WfForm.proxyFieldComp(anid, buttonHtml,"1");
            console.log("js代码块已触发")
            // jQuery("#btdiv"+value).html(buttonHtml);
            // 为按钮添加点击事件处理程序
        });
    });
    let canClick = true;
    function btnAction(userId, docId) {
        console.log("点了" + userId + docId)
        var companyId =   WfForm.getFieldValue("field74506");
        var userId2 = WfForm.getFieldValue("field75799");
        if (canClick) {
            // 执行你的代码
            var apiUrl = "http://218.245.99.207:7001/api/Seal/web/send/GZ" + "?userId=" + userId2 + "&docId=" + docId +"&companyId="+companyId
            console.log("接口链接:" + apiUrl)
            try {
                +
                    jQuery.ajax({
                        url: apiUrl,
                        dataType: "json",
                        type: "GET",
                        async: false,
                        success: function (data) {
                            //let _date = data.data.date;

                            console.log("返回值", data);
                            if (data.code == 200) {
                                window.open(data.data, "_blank");
                            }

                        },
                        error: function (e) {
                            console.log(e);
                            alert(e.responseJSON.message)

                        }
                    })
            } catch (e) {
                console.log("报错信息：", e)

            }
            // 设置标志为false，禁止再次点击
            canClick = false;

            // 可以在这里设置一个定时器，在一段时间后重置canClick为true
            setTimeout(() => {
                canClick = true;
            }, 5000); // 例如，2秒后允许再次点击
        }


    }
</script>


