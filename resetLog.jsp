<%@ page import="java.util.*" %>
<%@ page import="weaver.hrm.*" %>
<%@ page import="weaver.systeminfo.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="ln.LN"%>
<%@ page import="weaver.hrm.settings.RemindSettings" %>
<%@ page import="org.apache.commons.logging.Log"%>
<%@ page import="org.apache.commons.logging.LogFactory"%>
<%@ page import="weaver.workflow.workflow.TestWorkflowCheck" %>
<%@ page import="weaver.systeminfo.template.UserTemplate"%>
<%@ page import="weaver.systeminfo.setting.*" %>
<%@	page import="org.apache.log4j.PropertyConfigurator"%>
<%@ page import="java.io.*" %>

<%
//重设日志输出目录
		ServletContext context = config.getServletContext();
      	System.out.println("Log init start ....") ;
        String rootPath = context.getRealPath("/");
        if(!rootPath.endsWith(""+File.separatorChar))
        rootPath+=File.separatorChar;
		try{

			PropertyConfigurator.configure(rootPath+"log4j.properties");
			System.out.println("Log init end ....") ;
			out.println("Log4j:"+rootPath+"log4j.properties");
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Log init end with error....") ;
			out.println("Log init end with error....") ;
		}finally{
			
		}

%>
