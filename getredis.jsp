<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ page import="com.cloudstore.api.util.Util_Redis" %>
<%@ page import="com.cloudstore.dev.api.util.Util_TableMap" %>
<%@ page import="java.util.Set" %>
<%@ page import="java.io.*" %>
<%@ page import="java.net.*" %>
<%@ page import="java.util.*" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>获取</title>
</head>
<body>
redis:
<%
	String dataKey=request.getParameter("dataKey");
	out.write("<br>dataKey:"+dataKey);

	String Val=Util_TableMap.getVal(dataKey);
	String ValWithEh=Util_TableMap.getValWithEh(dataKey);
	String ValWithRedis=Util_TableMap.getValWithRedis(dataKey);
	Object ObjValWithRedis=Util_TableMap.getObjValWithRedis(dataKey);

	out.write("<br>数据All<textarea>:"+Val+"</textarea>");
	out.write("<br>数据Eh:<textarea>"+ValWithEh+"</textarea>");
	out.write("<br>数据Redis:<textarea>"+ValWithRedis+"</textarea>");
	out.write("<br>数据RedisObj:<textarea>"+ObjValWithRedis+"</textarea>");



%>
</br>
</br>
</br>
IP地址清单:
</br>
<%

String[] ips = getAllLocalHostIP();  
    for(String ip : ips){
%>
<%=ip %><br>
<%
    }
%>
本机IP:<br>
<%
InetAddress ia = InetAddress.getLocalHost();
String hostaddr = ia.getHostAddress();
%>
<%=hostaddr%>
<%!

    private static String[] getAllLocalHostIP() {  
        List<String> res = new ArrayList<String>();  
        Enumeration netInterfaces;  
        try {  
            netInterfaces = NetworkInterface.getNetworkInterfaces();  
            InetAddress ip = null;  
            while (netInterfaces.hasMoreElements()) {  
                NetworkInterface ni = (NetworkInterface) netInterfaces  
                        .nextElement();  
                Enumeration nii = ni.getInetAddresses();  
                while (nii.hasMoreElements()) {  
                    ip = (InetAddress) nii.nextElement();  
                    if (ip.getHostAddress().indexOf(":") == -1) {  
                        res.add(ip.getHostAddress());  
                    }  
                }  
            }  
        } catch (SocketException e) {  
            e.printStackTrace();  
        }  
        return (String[]) res.toArray(new String[0]);  
    }  
  
    /** 
     * 获取本机所有物理地址 
     *  
     * @return 
     */  
    public static String getMacAddress() {  
        String mac = "";  
        String line = "";  
  
        String os = System.getProperty("os.name");  
  
        if (os != null && os.startsWith("Windows")) {  
            try {  
                String command = "cmd.exe /c ipconfig /all";  
                Process p = Runtime.getRuntime().exec(command);  
  
                BufferedReader br = new BufferedReader(new InputStreamReader(p  
                        .getInputStream()));  
  
                while ((line = br.readLine()) != null) {  
                    if (line.indexOf("Physical Address") > 0) {  
                        int index = line.indexOf(":") + 2;  
  
                        mac = line.substring(index);  
  
                        break;  
                    }  
                }  
  
                br.close();  
  
            } catch (IOException e) {  
            }  
        }  
  
        return mac;  
    }  
  
    public String getMacAddress(String host) {  
        String mac = "";  
        StringBuffer sb = new StringBuffer();  
  
        try {  
            NetworkInterface ni = NetworkInterface.getByInetAddress(InetAddress  
                    .getByName(host));  
  
            byte[] macs = ni.getHardwareAddress();  
  
            for (int i = 0; i < macs.length; i++) {  
                mac = Integer.toHexString(macs[i] & 0xFF);  
  
                if (mac.length() == 1) {  
                    mac = '0' + mac;  
                }  
  
                sb.append(mac + "-");  
            }  
  
        } catch (SocketException e) {  
            e.printStackTrace();  
        } catch (UnknownHostException e) {  
            e.printStackTrace();  
        }  
  
        mac = sb.toString();  
        mac = mac.substring(0, mac.length() - 1);  
  
        return mac;  
    }  
%>


</body>
</html>