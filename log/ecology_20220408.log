2022-04-08 09:29:04,733 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-08 09:29:04,740 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-08 09:29:04,740 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-08 09:29:05,366 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:29:05,366 INFO  weaver.general.InitServer  - init ioc container...
2022-04-08 09:29:06,104 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:29:06,872 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:29:07,200 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-08 09:29:07,306 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-08 09:29:07,309 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-08 09:29:10,263 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-08 09:29:10,367 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-08 09:29:10,736 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4347160671352593449.jar
2022-04-08 09:29:10,736 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4347160671352593449.jar
2022-04-08 09:29:10,746 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-08 09:29:12,393 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-08 09:29:12,394 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-08 09:29:12,396 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-08 09:29:12,396 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-08 09:29:12,546 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-08 09:29:14,155 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-08 09:29:14,163 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-08 09:29:14,163 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-08 09:29:14,166 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-08 09:29:14,168 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-08 09:29:14,169 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-08 09:29:16,170 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-08 09:29:16,171 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-08 09:29:16,180 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-08 09:29:16,180 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-08 09:29:16,180 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-08 09:29:16,183 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-08 09:29:16,183 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-08 09:29:16,185 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-08 09:29:16,185 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-08 09:29:16,187 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-08 09:29:16,784 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-08 09:29:17,026 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-08 09:29:17,044 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-08 09:29:17,057 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-08 09:29:17,274 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-08 09:29:17,352 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-08 09:29:17,357 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-08 09:29:17,360 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-08 09:29:17,360 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-08 09:29:17,363 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-08 09:29:17,363 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-08 09:29:17,368 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-08 09:29:17,411 INFO  weaver.general.InitServer  - end.....
2022-04-08 09:29:17,430 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-08 09:29:17,459 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-08 09:29:17,531 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-08 09:29:17,622 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-08 09:29:17,650 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-08 09:29:17,740 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-08 09:29:17,874 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-08 09:29:17,922 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:29:17,922 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-08 09:29:17,922 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-08 09:29:17,926 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-08 09:29:17,950 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-08 09:29:17,950 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-08 09:29:17,983 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-08 09:29:18,005 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-08 09:29:18,008 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-08 09:29:18,144 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-08 09:29:18,144 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-08 09:29:18,144 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-08 09:29:18,144 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-08 09:29:18,145 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-08 09:29:18,161 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-08 09:29:18,170 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-08 09:29:18,299 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-08 09:29:18,360 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:29:18,365 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-08 09:29:18,449 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:29:18,456 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-08 09:29:18,467 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=0ea6cc96-f1c9-4790-9119-98818f52afd7,��ʼ�ʼ��ڲ��ռ�������
2022-04-08 09:29:18,467 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=0ea6cc96-f1c9-4790-9119-98818f52afd7,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-08 09:29:18,556 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-08 09:29:18,556 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-08 09:29:18,562 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:29:18,636 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=0ea6cc96-f1c9-4790-9119-98818f52afd7,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-08 09:29:18,636 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:29:19,155 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-08 09:29:19,155 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-08 09:29:19,213 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-08 09:29:19,217 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-08 09:29:20,392 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-08 09:29:20,446 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-08 09:29:21,307 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-10' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-10') or  lastLoginDate<'2021-10-10')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-08 09:29:21,308 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-08 09:29:21,308 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-08 09:29:21,518 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-08 09:29:30,961 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-08 09:29:30,965 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-08 09:29:30,965 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-08 09:29:31,637 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:29:31,637 INFO  weaver.general.InitServer  - init ioc container...
2022-04-08 09:29:32,411 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:29:33,267 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:29:33,653 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-08 09:29:33,898 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-08 09:29:33,900 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-08 09:29:36,869 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-08 09:29:36,885 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-08 09:29:39,918 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-08 09:29:39,918 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-08 09:29:39,920 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-08 09:29:39,920 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-08 09:29:40,155 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-08 09:29:42,144 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-08 09:29:42,151 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-08 09:29:42,151 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-08 09:29:42,153 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-08 09:29:42,154 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-08 09:29:42,155 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-08 09:29:44,156 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-08 09:29:44,157 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-08 09:29:44,164 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-08 09:29:44,165 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-08 09:29:44,165 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-08 09:29:44,168 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-08 09:29:44,168 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-08 09:29:44,170 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-08 09:29:44,171 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-08 09:29:44,174 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-08 09:29:44,684 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-08 09:29:44,926 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-08 09:29:44,939 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-08 09:29:44,952 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-08 09:29:44,952 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2022-04-08 09:29:45,122 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-08 09:29:45,213 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-08 09:29:45,217 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-08 09:29:45,219 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-08 09:29:45,219 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-08 09:29:45,221 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-08 09:29:45,221 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-08 09:29:45,223 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-08 09:29:45,265 INFO  weaver.general.InitServer  - end.....
2022-04-08 09:29:45,277 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-08 09:29:45,345 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-08 09:29:45,457 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-08 09:29:45,525 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-08 09:29:45,569 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-08 09:29:45,672 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-08 09:29:45,701 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-08 09:29:45,702 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:29:45,702 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-08 09:29:45,702 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-08 09:29:45,740 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-08 09:29:45,740 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-08 09:29:45,767 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-08 09:29:45,888 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-08 09:29:45,889 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-08 09:29:45,889 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-08 09:29:45,889 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-08 09:29:45,892 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-08 09:29:45,903 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-08 09:29:45,979 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-08 09:29:46,038 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-08 09:29:46,040 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-08 09:29:46,207 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-08 09:29:46,219 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:29:46,221 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-08 09:29:46,223 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:29:46,228 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-08 09:29:46,233 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8027e4e5-28ca-4979-85c5-56dc9e58385f,��ʼ�ʼ��ڲ��ռ�������
2022-04-08 09:29:46,233 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8027e4e5-28ca-4979-85c5-56dc9e58385f,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-08 09:29:46,249 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-08 09:29:46,258 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-08 09:29:46,262 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:29:46,314 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-08 09:29:46,346 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8027e4e5-28ca-4979-85c5-56dc9e58385f,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-08 09:29:46,346 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:29:46,373 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,374 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,375 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,376 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,377 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,378 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,380 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:29:46,658 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-08 09:29:46,658 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-08 09:29:46,679 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-08 09:29:46,680 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-08 09:29:47,213 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-08 09:29:48,245 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-08 09:29:48,469 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-10' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-10') or  lastLoginDate<'2021-10-10')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-08 09:29:48,469 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-08 09:29:48,469 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-08 09:29:48,765 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-08 09:29:51,308 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2022-04-08 09:30:03,854 ERROR weaver.general.BaseBean  - qrcode_config>>>
2022-04-08 09:30:24,229 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,333 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,385 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,388 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,388 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,389 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,391 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,407 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,409 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,478 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,606 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,611 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,715 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:24,734 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:30:24,734 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:30:25,221 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,273 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,461 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,503 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,514 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,559 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,569 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,603 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:30:25,624 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,676 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,723 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,771 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,774 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,780 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:30:25,817 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,926 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:25,940 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,064 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,170 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,170 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,234 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,248 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,306 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,307 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,326 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,348 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,370 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,409 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,740 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:26,828 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:28,660 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:28,867 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:28,888 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:29,036 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:29,663 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:29,745 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:29,959 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:29,991 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:30,156 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:32,008 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,185 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,226 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,266 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,340 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,825 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:30:32,998 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:34,075 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:36,244 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:30:37,321 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:32:12,265 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/pubAction/getAttendanceCardCustom,��ǰsessionId��abclNnsfF2tAJhJjoZgay
2022-04-08 09:32:48,069 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:32:48,091 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - �����쳣
java.time.format.DateTimeParseException: Text '' could not be parsed at index 0
	at java.time.format.DateTimeFormatter.parseResolved0(DateTimeFormatter.java:1949)
	at java.time.format.DateTimeFormatter.parse(DateTimeFormatter.java:1851)
	at java.time.LocalDate.parse(LocalDate.java:400)
	at java.time.LocalDate.parse(LocalDate.java:385)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:50)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:26)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.api.pubaction.service.impl.GetAttendanceCardCustomServerImpl.GetAttendanceCar(GetAttendanceCardCustomServerImpl.java:14)
	at com.api.pubaction.web.Action.getAttendanceCard(Action.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.server.impl.uri.rules.SubLocatorRule.dispatch(SubLocatorRule.java:197)
	at com.sun.jersey.server.impl.uri.rules.SubLocatorRule.invokeSubLocator(SubLocatorRule.java:183)
	at com.sun.jersey.server.impl.uri.rules.SubLocatorRule.accept(SubLocatorRule.java:110)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor392.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor348.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:33:26,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:33:26,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:33:26,850 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:35:33,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:36:09,005 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-08 09:36:09,013 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-08 09:36:09,013 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-08 09:36:09,735 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:36:09,735 INFO  weaver.general.InitServer  - init ioc container...
2022-04-08 09:36:10,372 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:36:11,081 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:36:11,377 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-08 09:36:11,558 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-08 09:36:11,560 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-08 09:36:14,288 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-08 09:36:14,322 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-08 09:36:14,364 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4006729310130246579.jar
2022-04-08 09:36:14,364 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4006729310130246579.jar
2022-04-08 09:36:14,373 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-08 09:36:16,300 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-08 09:36:16,300 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-08 09:36:16,305 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-08 09:36:16,306 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-08 09:36:16,462 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-08 09:36:18,382 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-08 09:36:18,390 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-08 09:36:18,390 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-08 09:36:18,393 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-08 09:36:18,395 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-08 09:36:18,395 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-08 09:36:20,396 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-08 09:36:20,397 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-08 09:36:20,407 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-08 09:36:20,408 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-08 09:36:20,408 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-08 09:36:20,410 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-08 09:36:20,410 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-08 09:36:20,413 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-08 09:36:20,413 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-08 09:36:20,415 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-08 09:36:20,918 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-08 09:36:21,137 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-08 09:36:21,200 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-08 09:36:21,213 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-08 09:36:21,390 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-08 09:36:21,473 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-08 09:36:21,478 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-08 09:36:21,481 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-08 09:36:21,482 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-08 09:36:21,484 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-08 09:36:21,484 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-08 09:36:21,487 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-08 09:36:21,524 INFO  weaver.general.InitServer  - end.....
2022-04-08 09:36:21,544 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-08 09:36:21,602 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-08 09:36:21,697 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-08 09:36:21,747 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-08 09:36:21,769 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-08 09:36:21,853 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-08 09:36:21,978 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-08 09:36:22,005 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:36:22,005 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-08 09:36:22,005 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-08 09:36:22,035 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-08 09:36:22,035 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-08 09:36:22,051 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-08 09:36:22,062 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-08 09:36:22,148 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-08 09:36:22,150 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-08 09:36:22,188 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-08 09:36:22,189 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-08 09:36:22,189 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-08 09:36:22,189 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-08 09:36:22,189 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-08 09:36:22,202 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-08 09:36:22,297 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-08 09:36:22,482 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:36:22,484 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-08 09:36:22,488 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:36:22,494 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-08 09:36:22,499 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=9f6562b5-7ace-4151-87f6-6856286a2363,��ʼ�ʼ��ڲ��ռ�������
2022-04-08 09:36:22,499 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=9f6562b5-7ace-4151-87f6-6856286a2363,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-08 09:36:22,636 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-08 09:36:22,672 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-08 09:36:22,676 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:36:22,678 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-08 09:36:22,732 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=9f6562b5-7ace-4151-87f6-6856286a2363,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-08 09:36:22,733 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:36:22,912 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:22,914 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:22,917 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:22,919 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:22,922 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:23,011 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-08 09:36:23,012 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-08 09:36:23,047 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-08 09:36:23,049 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-08 09:36:23,754 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-08 09:36:24,511 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-08 09:36:24,724 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-10' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-10') or  lastLoginDate<'2021-10-10')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-08 09:36:24,724 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-08 09:36:24,724 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-08 09:36:24,932 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-08 09:36:35,220 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-08 09:36:35,223 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-08 09:36:35,223 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-08 09:36:35,963 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:36:35,963 INFO  weaver.general.InitServer  - init ioc container...
2022-04-08 09:36:36,687 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:36:37,515 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-08 09:36:38,480 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-08 09:36:38,569 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-08 09:36:38,571 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-08 09:36:40,418 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-08 09:36:40,431 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-08 09:36:41,850 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-08 09:36:41,850 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-08 09:36:41,852 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-08 09:36:41,852 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-08 09:36:42,083 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-08 09:36:43,560 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-08 09:36:43,567 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-08 09:36:43,567 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-08 09:36:43,569 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-08 09:36:43,572 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-08 09:36:43,572 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-08 09:36:45,578 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-08 09:36:45,579 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-08 09:36:45,589 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-08 09:36:45,589 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-08 09:36:45,589 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-08 09:36:45,592 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-08 09:36:45,592 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-08 09:36:45,594 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-08 09:36:45,595 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-08 09:36:45,596 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-08 09:36:46,107 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-08 09:36:46,282 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-08 09:36:46,375 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-08 09:36:46,389 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-08 09:36:46,389 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2022-04-08 09:36:46,585 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-08 09:36:46,676 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-08 09:36:46,679 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-08 09:36:46,681 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-08 09:36:46,681 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-08 09:36:46,683 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-08 09:36:46,684 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-08 09:36:46,687 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-08 09:36:46,726 INFO  weaver.general.InitServer  - end.....
2022-04-08 09:36:46,736 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-08 09:36:46,789 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-08 09:36:46,872 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-08 09:36:46,913 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-08 09:36:46,943 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-08 09:36:47,091 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-08 09:36:47,234 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-08 09:36:47,234 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-08 09:36:47,234 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-08 09:36:47,246 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-08 09:36:47,261 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-08 09:36:47,261 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-08 09:36:47,279 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-08 09:36:47,289 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-08 09:36:47,352 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-08 09:36:47,353 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-08 09:36:47,378 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-08 09:36:47,378 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-08 09:36:47,378 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-08 09:36:47,378 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-08 09:36:47,378 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-08 09:36:47,389 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-08 09:36:47,532 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-08 09:36:47,681 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:36:47,684 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-08 09:36:47,687 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-08 09:36:47,693 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-08 09:36:47,703 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=52fb01b7-3afc-4fa7-aeb7-25f5738820be,��ʼ�ʼ��ڲ��ռ�������
2022-04-08 09:36:47,703 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=52fb01b7-3afc-4fa7-aeb7-25f5738820be,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-08 09:36:47,711 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-08 09:36:47,725 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-08 09:36:47,726 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:36:47,742 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-08 09:36:48,034 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=52fb01b7-3afc-4fa7-aeb7-25f5738820be,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-08 09:36:48,035 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-08 09:36:48,161 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-08 09:36:48,162 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-08 09:36:48,191 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-08 09:36:48,193 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-08 09:36:48,252 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,255 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,256 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,258 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,259 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,260 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:48,262 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:36:49,007 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-08 09:36:49,708 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-08 09:36:49,934 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-10' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-10') or  lastLoginDate<'2021-10-10')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-08 09:36:49,936 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-08 09:36:49,936 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-08 09:36:50,157 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-08 09:36:51,743 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:36:51,749 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:36:51,831 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:36:51,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:36:52,609 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2022-04-08 09:36:52,905 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:03,865 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:03,868 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:03,948 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - �����쳣
java.time.format.DateTimeParseException: Text '' could not be parsed at index 0
	at java.time.format.DateTimeFormatter.parseResolved0(DateTimeFormatter.java:1949)
	at java.time.format.DateTimeFormatter.parse(DateTimeFormatter.java:1851)
	at java.time.LocalDate.parse(LocalDate.java:400)
	at java.time.LocalDate.parse(LocalDate.java:385)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:50)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:26)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.api.pubaction.service.impl.GetAttendanceCardCustomServerImpl.GetAttendanceCar(GetAttendanceCardCustomServerImpl.java:14)
	at com.api.pubaction.web.Action.getAttendanceCard(Action.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-04-08 09:37:03,999 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:05,413 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:05,471 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:05,476 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:05,484 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:37:05,602 ERROR weaver.general.BaseBean  - qrcode_config>>>
2022-04-08 09:37:08,236 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:09,229 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:09,955 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:09,989 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,040 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,041 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,047 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,054 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,054 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,059 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,061 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,138 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,236 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,236 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,411 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:10,457 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:38:10,460 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:38:10,838 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,066 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,109 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,144 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,163 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,209 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:38:11,215 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,314 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,369 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,374 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,409 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,411 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,458 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-08 09:38:11,469 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,596 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,615 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,622 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,794 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,799 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,811 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,818 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,856 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,857 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,878 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,895 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,896 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,942 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:11,966 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:12,114 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:12,130 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:12,292 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:13,999 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:14,063 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:14,227 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:14,227 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:14,228 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:15,164 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:15,297 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:15,297 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:15,297 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:15,843 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:15,851 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:15,892 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:15,940 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:16,303 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:16,803 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:17,272 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:17,983 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:17,983 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,049 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,050 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,066 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,221 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,222 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,227 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,350 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:18,474 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,559 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:18,574 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:19,412 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:19,412 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:19,414 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:19,993 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:20,133 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:21,209 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-08 09:38:21,906 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:21,908 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:21,909 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:21,913 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:21,937 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:21,996 ERROR weaver.conn.RecordSet  - sql: select field001 FROM kq_att_proc_set where field005=1 
2022-04-08 09:38:22,881 ERROR weaver.conn.RecordSet  - wfids:1376,1377,1378,1379,1380,1381,1382:lsWfType:[]
2022-04-08 09:38:24,486 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:25,804 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:26,430 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:29,458 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:29,885 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:30,412 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:30,849 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:31,176 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:31,606 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:32,481 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:32,676 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:32,860 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:33,182 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:33,557 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:34,552 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:38:37,170 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:39:26,292 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:39:26,294 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-08 09:39:26,304 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
