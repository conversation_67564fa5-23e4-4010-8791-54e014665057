2021-05-13 10:53:29,027 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-05-13 10:53:31,188 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 10:53:31,194 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 10:53:31,194 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 10:53:31,940 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 10:53:31,940 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 10:53:32,622 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 10:53:33,481 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 10:53:33,817 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 10:53:34,062 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 10:53:34,064 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 10:53:36,505 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 10:53:36,537 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 10:53:36,580 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3013373558470614654.jar
2021-05-13 10:53:36,580 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3013373558470614654.jar
2021-05-13 10:53:36,589 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 10:53:38,126 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 10:53:38,126 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 10:53:38,129 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 10:53:38,129 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 10:53:38,222 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 10:53:39,147 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 10:53:39,157 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 10:53:39,157 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 10:53:39,160 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 10:53:39,162 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 10:53:39,163 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 10:53:41,168 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 10:53:41,169 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 10:53:41,177 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 10:53:41,177 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 10:53:41,177 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 10:53:41,179 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 10:53:41,180 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 10:53:41,182 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 10:53:41,182 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 10:53:41,185 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 10:53:41,511 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 10:53:41,753 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 10:53:41,765 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 10:53:41,779 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 10:53:41,921 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 10:53:41,967 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 10:53:41,972 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 10:53:41,974 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 10:53:41,974 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 10:53:41,977 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 10:53:41,977 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 10:53:41,979 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 10:53:42,015 INFO  weaver.general.InitServer  - end.....
2021-05-13 10:53:42,041 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 10:53:42,067 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 10:53:42,122 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 10:53:42,155 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 10:53:42,168 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 10:53:42,211 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 10:53:42,474 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 10:53:42,530 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 10:53:42,535 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 10:53:42,537 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 10:53:42,645 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 10:53:42,666 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 10:53:42,667 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 10:53:42,667 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 10:53:42,687 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 10:53:42,688 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 10:53:42,729 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 10:53:42,974 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 10:53:42,977 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 10:53:42,979 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 10:53:42,987 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 10:53:42,989 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3d69bb2e-e6ea-4784-982e-97c79a1d061f,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 10:53:42,989 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3d69bb2e-e6ea-4784-982e-97c79a1d061f,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 10:53:43,004 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 10:53:43,009 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 10:53:43,013 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 10:53:43,017 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 10:53:43,017 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 10:53:43,017 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 10:53:43,017 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 10:53:43,017 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 10:53:43,037 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 10:53:43,047 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3d69bb2e-e6ea-4784-982e-97c79a1d061f,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 10:53:43,048 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 10:53:44,304 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 10:53:44,591 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 10:53:44,593 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 10:53:44,729 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 10:53:44,737 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 10:53:45,004 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 10:53:45,166 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 10:53:45,167 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 10:53:45,167 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 10:53:45,350 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 10:53:45,980 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 10:53:56,014 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 10:53:56,018 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 10:53:56,018 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 10:53:57,285 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 10:53:57,285 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 10:53:58,121 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 10:53:58,997 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 10:53:59,343 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 10:53:59,437 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 10:53:59,438 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 10:54:01,243 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 10:54:01,257 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 10:54:01,675 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 10:54:01,675 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 10:54:01,677 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 10:54:01,677 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 10:54:01,760 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 10:54:02,794 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 10:54:02,802 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 10:54:02,802 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 10:54:02,805 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 10:54:02,807 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 10:54:02,808 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 10:54:04,812 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 10:54:04,812 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 10:54:04,821 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 10:54:04,821 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 10:54:04,821 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 10:54:04,823 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 10:54:04,823 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 10:54:04,824 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 10:54:04,824 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 10:54:04,826 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 10:54:05,134 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 10:54:05,295 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 10:54:05,307 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 10:54:05,317 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 10:54:05,317 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 10:54:05,489 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 10:54:05,549 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 10:54:05,554 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 10:54:05,556 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 10:54:05,556 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 10:54:05,558 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 10:54:05,558 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 10:54:05,563 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 10:54:05,595 INFO  weaver.general.InitServer  - end.....
2021-05-13 10:54:05,630 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 10:54:05,655 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 10:54:05,706 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 10:54:05,758 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 10:54:05,774 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 10:54:05,817 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 10:54:06,003 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 10:54:06,064 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 10:54:06,065 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 10:54:06,197 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 10:54:06,213 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 10:54:06,225 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 10:54:06,227 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 10:54:06,228 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 10:54:06,255 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 10:54:06,255 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 10:54:06,273 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 10:54:06,496 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 10:54:06,496 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 10:54:06,496 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 10:54:06,496 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 10:54:06,496 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 10:54:06,516 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 10:54:06,556 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 10:54:06,559 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 10:54:06,564 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 10:54:06,568 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 10:54:06,575 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e3a345f6-1084-46e3-a33a-094368a03f48,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 10:54:06,575 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e3a345f6-1084-46e3-a33a-094368a03f48,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 10:54:06,602 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 10:54:06,602 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 10:54:06,604 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 10:54:06,637 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e3a345f6-1084-46e3-a33a-094368a03f48,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 10:54:06,637 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 10:54:06,783 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 10:54:06,784 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 10:54:06,842 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 10:54:06,844 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 10:54:06,929 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 10:54:07,499 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 10:54:08,593 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 10:54:08,768 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 10:54:08,768 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 10:54:08,768 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 10:54:08,823 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 10:54:15,467 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 10:54:32,345 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 10:55:27,841 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:27,916 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,007 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,007 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,014 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,015 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,015 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,015 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,031 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,086 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,140 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,146 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,181 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,244 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 10:55:28,278 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 10:55:28,408 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,414 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,504 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,552 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,579 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,597 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,638 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,663 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,676 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,747 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,768 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,779 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 10:55:28,821 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,844 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,889 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,898 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,947 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:28,954 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,051 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,062 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,081 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,086 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,161 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,180 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,230 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,230 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,236 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,245 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,295 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,619 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,619 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,641 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:29,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:30,099 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor445.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 10:55:34,146 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:55:34,224 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:58:28,849 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:58:29,091 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 10:58:29,488 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:00:29,797 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:00:35,432 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/api/crm/saveSj,��ǰsessionId��abc9as41MFia7-NcYVxHx
2021-05-13 11:01:28,850 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:01:29,087 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:01:29,680 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:01:35,248 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/api/crm/saveSj,��ǰsessionId��abc9as41MFia7-NcYVxHx
2021-05-13 11:02:56,474 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/api/crm/saveSj,��ǰsessionId��abc9as41MFia7-NcYVxHx
2021-05-13 11:03:41,138 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/api/crm/saveSj,��ǰsessionId��abc9as41MFia7-NcYVxHx
2021-05-13 11:03:58,188 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-05-13 11:04:15,212 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/api/crm/saveSj,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-05-13 11:04:22,728 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:22,830 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:22,902 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:22,906 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,067 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,129 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,417 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,513 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,554 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,557 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,563 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:23,696 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,454 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,869 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,932 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,934 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,934 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,935 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,937 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,942 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,946 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,985 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,986 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:24,998 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,031 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:04:25,033 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,095 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:04:25,215 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,229 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,263 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,273 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,297 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,325 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,326 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,326 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,352 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,380 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,387 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,413 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,415 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,426 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:04:25,451 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,452 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,478 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,549 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,563 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,566 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,590 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,639 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,640 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,650 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,704 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,705 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,705 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,711 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,747 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:25,757 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:26,049 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor445.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:04:26,303 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:26,354 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:41,555 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:04:41,702 ERROR weaver.conn.RecordSet  - insert into uf_sqxmdjb(sjmc,sjje,sjszbmid,fzrid,cjrid,sjxylx,ar) values(�̻�����,12341,268,2080,2080,1,2080)
2021-05-13 11:04:41,702 ERROR weaver.conn.RecordSet  - weaver.conn.RecordSet
com.microsoft.sqlserver.jdbc.SQLServerException: ���� '�̻�����' ��Ч��
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:196)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1454)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.doExecuteStatement(SQLServerStatement.java:786)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$StmtExecCmd.doExecute(SQLServerStatement.java:685)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:185)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:160)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.execute(SQLServerStatement.java:658)
	at sun.reflect.GeneratedMethodAccessor102.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.logicalcobwebs.proxool.ProxyStatement.invoke(ProxyStatement.java:100)
	at org.logicalcobwebs.proxool.ProxyStatement.intercept(ProxyStatement.java:57)
	at $java.sql.Wrapper$$EnhancerByProxool$$68ba86b2.execute(<generated>)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1464)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:795)
	at weaver.conn.RecordSet.execute(RecordSet.java:1643)
	at com.api.presettle.webServiceTest.CRMActionSaveSJ.saveSJ(CRMActionSaveSJ.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$VoidOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:167)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor445.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:05:36,082 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2021-05-13 11:05:36,125 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2021-05-13 11:05:36,207 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2021-05-13 11:05:36,237 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2021-05-13 11:05:36,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2021-05-13 11:05:36,292 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2021-05-13 11:05:36,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2021-05-13 11:05:36,347 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2021-05-13 11:05:36,394 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2021-05-13 11:05:36,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2021-05-13 11:05:36,446 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2021-05-13 11:05:36,475 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2021-05-13 11:05:36,500 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2021-05-13 11:05:36,526 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2021-05-13 11:05:36,553 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2021-05-13 11:05:36,590 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2021-05-13 11:05:36,615 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2021-05-13 11:05:36,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2021-05-13 11:05:36,671 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2021-05-13 11:05:36,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2021-05-13 11:05:36,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2021-05-13 11:05:36,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2021-05-13 11:05:36,791 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2021-05-13 11:05:36,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2021-05-13 11:05:36,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2021-05-13 11:05:36,868 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2021-05-13 11:05:36,894 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2021-05-13 11:05:36,920 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2021-05-13 11:05:36,950 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2021-05-13 11:05:36,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2021-05-13 11:05:37,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2021-05-13 11:05:37,068 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2021-05-13 11:05:37,110 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2021-05-13 11:05:37,154 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2021-05-13 11:05:37,187 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2021-05-13 11:05:37,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2021-05-13 11:05:37,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2021-05-13 11:05:37,285 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2021-05-13 11:05:37,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2021-05-13 11:05:37,340 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2021-05-13 11:05:37,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2021-05-13 11:05:37,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2021-05-13 11:05:37,439 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2021-05-13 11:05:37,468 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2021-05-13 11:05:37,500 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2021-05-13 11:05:37,529 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2021-05-13 11:05:37,555 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2021-05-13 11:05:37,583 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2021-05-13 11:05:37,610 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2021-05-13 11:05:37,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2021-05-13 11:05:37,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2021-05-13 11:05:37,685 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2021-05-13 11:05:37,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2021-05-13 11:05:37,741 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2021-05-13 11:05:37,779 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2021-05-13 11:05:37,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2021-05-13 11:05:37,844 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2021-05-13 11:05:37,874 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2021-05-13 11:05:37,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2021-05-13 11:05:37,934 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2021-05-13 11:05:37,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2021-05-13 11:05:37,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2021-05-13 11:05:38,015 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2021-05-13 11:05:38,047 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2021-05-13 11:05:38,068 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2021-05-13 11:05:38,095 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2021-05-13 11:05:38,122 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2021-05-13 11:05:38,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2021-05-13 11:05:38,172 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2021-05-13 11:05:38,207 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2021-05-13 11:05:38,230 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2021-05-13 11:05:38,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2021-05-13 11:05:38,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2021-05-13 11:05:38,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2021-05-13 11:05:38,354 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2021-05-13 11:05:38,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2021-05-13 11:05:38,411 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2021-05-13 11:05:38,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2021-05-13 11:05:38,481 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2021-05-13 11:05:38,506 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2021-05-13 11:05:38,532 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2021-05-13 11:05:38,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2021-05-13 11:05:38,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2021-05-13 11:05:38,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2021-05-13 11:05:38,643 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2021-05-13 11:05:38,672 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2021-05-13 11:05:38,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2021-05-13 11:05:38,730 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2021-05-13 11:05:38,806 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2021-05-13 11:05:38,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2021-05-13 11:05:38,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2021-05-13 11:05:38,892 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2021-05-13 11:05:38,922 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2021-05-13 11:05:38,945 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2021-05-13 11:05:38,968 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2021-05-13 11:05:38,999 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2021-05-13 11:05:39,025 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2021-05-13 11:05:39,049 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2021-05-13 11:05:39,072 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2021-05-13 11:05:39,095 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2021-05-13 11:05:39,119 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2021-05-13 11:05:39,151 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2021-05-13 11:05:39,175 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2021-05-13 11:05:39,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2021-05-13 11:05:39,230 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2021-05-13 11:05:39,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2021-05-13 11:05:39,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2021-05-13 11:05:39,333 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2021-05-13 11:05:39,368 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2021-05-13 11:05:39,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2021-05-13 11:05:39,422 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2021-05-13 11:05:39,453 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2021-05-13 11:05:39,482 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2021-05-13 11:05:39,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2021-05-13 11:05:39,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2021-05-13 11:05:39,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2021-05-13 11:05:39,588 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2021-05-13 11:05:39,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2021-05-13 11:05:39,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2021-05-13 11:05:39,665 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2021-05-13 11:05:39,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2021-05-13 11:05:39,727 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2021-05-13 11:05:39,755 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2021-05-13 11:05:39,784 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2021-05-13 11:05:39,812 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2021-05-13 11:05:39,837 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2021-05-13 11:05:39,862 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2021-05-13 11:05:39,889 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2021-05-13 11:05:39,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2021-05-13 11:05:39,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2021-05-13 11:05:39,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2021-05-13 11:05:40,000 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2021-05-13 11:05:40,022 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2021-05-13 11:05:40,048 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2021-05-13 11:05:40,072 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2021-05-13 11:05:40,097 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2021-05-13 11:05:40,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2021-05-13 11:05:40,156 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2021-05-13 11:05:40,194 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2021-05-13 11:05:40,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2021-05-13 11:05:40,278 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2021-05-13 11:05:40,322 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2021-05-13 11:05:40,362 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2021-05-13 11:05:40,402 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2021-05-13 11:05:40,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2021-05-13 11:05:40,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2021-05-13 11:05:40,502 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2021-05-13 11:05:40,546 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2021-05-13 11:05:40,573 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2021-05-13 11:05:40,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2021-05-13 11:05:40,622 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2021-05-13 11:05:40,650 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2021-05-13 11:05:40,676 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2021-05-13 11:05:40,700 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2021-05-13 11:05:40,724 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2021-05-13 11:05:40,751 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2021-05-13 11:05:40,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2021-05-13 11:05:40,812 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2021-05-13 11:05:40,842 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2021-05-13 11:05:40,869 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2021-05-13 11:05:40,896 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2021-05-13 11:05:40,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2021-05-13 11:05:40,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2021-05-13 11:05:40,981 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2021-05-13 11:05:41,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2021-05-13 11:05:41,028 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2021-05-13 11:05:41,054 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2021-05-13 11:05:41,097 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2021-05-13 11:05:41,152 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2021-05-13 11:05:41,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2021-05-13 11:05:41,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2021-05-13 11:05:41,263 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2021-05-13 11:05:41,291 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2021-05-13 11:05:41,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2021-05-13 11:05:41,340 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2021-05-13 11:05:41,364 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2021-05-13 11:05:41,395 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2021-05-13 11:05:41,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2021-05-13 11:05:41,451 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2021-05-13 11:05:41,478 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2021-05-13 11:05:41,504 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2021-05-13 11:05:41,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2021-05-13 11:05:41,592 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2021-05-13 11:05:41,615 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2021-05-13 11:05:41,641 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2021-05-13 11:05:41,672 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2021-05-13 11:05:41,708 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2021-05-13 11:05:41,735 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2021-05-13 11:05:41,764 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2021-05-13 11:05:41,798 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2021-05-13 11:05:41,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2021-05-13 11:05:41,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2021-05-13 11:05:41,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2021-05-13 11:05:41,900 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2021-05-13 11:05:41,927 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2021-05-13 11:05:41,950 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2021-05-13 11:05:41,973 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2021-05-13 11:05:42,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2021-05-13 11:05:42,022 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2021-05-13 11:05:42,046 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2021-05-13 11:05:42,071 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2021-05-13 11:05:42,097 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2021-05-13 11:05:42,122 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2021-05-13 11:05:42,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2021-05-13 11:05:42,173 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2021-05-13 11:05:42,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2021-05-13 11:05:42,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2021-05-13 11:05:42,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2021-05-13 11:05:42,284 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2021-05-13 11:05:42,319 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2021-05-13 11:05:42,345 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2021-05-13 11:05:42,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2021-05-13 11:05:42,414 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2021-05-13 11:05:42,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2021-05-13 11:05:42,468 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2021-05-13 11:05:42,493 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2021-05-13 11:05:42,525 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2021-05-13 11:05:42,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2021-05-13 11:05:42,573 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2021-05-13 11:05:42,600 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2021-05-13 11:05:42,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2021-05-13 11:05:42,650 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2021-05-13 11:05:42,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2021-05-13 11:05:42,721 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2021-05-13 11:05:42,744 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2021-05-13 11:05:42,769 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2021-05-13 11:05:42,796 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2021-05-13 11:05:42,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2021-05-13 11:05:42,858 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2021-05-13 11:05:42,885 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2021-05-13 11:05:42,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2021-05-13 11:05:42,932 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2021-05-13 11:05:42,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2021-05-13 11:05:43,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2021-05-13 11:05:43,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2021-05-13 11:05:43,066 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2021-05-13 11:05:43,097 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2021-05-13 11:05:43,127 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2021-05-13 11:05:43,153 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2021-05-13 11:05:43,192 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2021-05-13 11:05:43,224 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2021-05-13 11:05:43,254 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2021-05-13 11:05:43,288 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2021-05-13 11:05:43,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2021-05-13 11:05:43,360 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2021-05-13 11:05:43,397 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2021-05-13 11:05:43,428 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2021-05-13 11:05:43,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2021-05-13 11:05:43,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2021-05-13 11:05:43,516 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2021-05-13 11:05:43,546 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2021-05-13 11:05:43,574 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2021-05-13 11:05:43,604 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2021-05-13 11:05:43,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2021-05-13 11:05:43,661 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2021-05-13 11:05:43,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2021-05-13 11:05:43,714 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2021-05-13 11:05:43,744 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2021-05-13 11:05:43,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2021-05-13 11:05:43,796 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2021-05-13 11:05:43,823 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2021-05-13 11:05:43,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2021-05-13 11:05:43,892 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2021-05-13 11:05:43,915 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2021-05-13 11:05:43,942 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2021-05-13 11:05:43,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2021-05-13 11:05:44,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2021-05-13 11:05:44,043 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2021-05-13 11:05:44,077 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2021-05-13 11:05:44,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2021-05-13 11:05:44,127 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2021-05-13 11:05:44,161 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2021-05-13 11:05:44,209 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2021-05-13 11:05:44,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2021-05-13 11:05:44,266 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2021-05-13 11:05:44,304 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2021-05-13 11:05:44,338 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2021-05-13 11:05:44,372 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2021-05-13 11:05:44,406 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2021-05-13 11:05:44,435 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2021-05-13 11:05:44,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2021-05-13 11:05:44,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2021-05-13 11:05:44,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2021-05-13 11:05:44,569 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2021-05-13 11:05:44,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2021-05-13 11:05:44,623 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2021-05-13 11:05:44,649 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2021-05-13 11:05:44,684 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2021-05-13 11:05:44,714 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2021-05-13 11:05:44,760 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2021-05-13 11:05:44,785 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2021-05-13 11:05:44,809 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2021-05-13 11:05:44,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2021-05-13 11:05:44,869 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2021-05-13 11:05:44,896 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2021-05-13 11:05:44,937 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2021-05-13 11:05:44,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2021-05-13 11:05:44,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2021-05-13 11:05:45,021 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2021-05-13 11:05:45,055 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2021-05-13 11:05:45,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2021-05-13 11:05:45,110 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2021-05-13 11:05:45,138 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2021-05-13 11:05:45,162 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2021-05-13 11:05:45,190 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2021-05-13 11:05:45,217 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2021-05-13 11:05:45,241 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2021-05-13 11:05:45,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2021-05-13 11:05:45,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2021-05-13 11:05:45,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2021-05-13 11:05:45,358 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2021-05-13 11:05:45,393 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2021-05-13 11:05:45,420 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2021-05-13 11:05:45,450 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2021-05-13 11:05:45,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2021-05-13 11:05:45,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2021-05-13 11:05:45,557 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2021-05-13 11:05:45,599 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2021-05-13 11:05:45,628 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2021-05-13 11:05:45,655 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2021-05-13 11:05:45,680 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2021-05-13 11:05:45,706 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2021-05-13 11:05:45,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2021-05-13 11:05:45,768 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2021-05-13 11:05:45,798 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2021-05-13 11:05:45,825 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2021-05-13 11:05:45,854 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2021-05-13 11:05:45,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2021-05-13 11:05:45,907 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2021-05-13 11:05:45,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2021-05-13 11:05:45,953 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2021-05-13 11:05:45,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2021-05-13 11:05:46,009 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2021-05-13 11:05:46,035 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2021-05-13 11:05:46,076 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2021-05-13 11:05:46,117 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2021-05-13 11:05:46,140 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2021-05-13 11:05:46,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2021-05-13 11:05:46,204 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2021-05-13 11:05:46,231 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2021-05-13 11:05:46,258 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2021-05-13 11:05:46,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2021-05-13 11:05:46,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2021-05-13 11:05:46,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2021-05-13 11:05:46,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2021-05-13 11:05:46,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2021-05-13 11:05:46,426 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2021-05-13 11:05:46,454 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2021-05-13 11:05:46,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2021-05-13 11:05:46,530 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2021-05-13 11:05:46,588 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2021-05-13 11:05:46,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2021-05-13 11:05:46,667 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2021-05-13 11:05:46,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2021-05-13 11:05:46,736 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2021-05-13 11:05:46,777 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2021-05-13 11:05:46,808 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2021-05-13 11:05:46,835 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2021-05-13 11:05:46,867 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2021-05-13 11:05:46,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2021-05-13 11:05:46,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2021-05-13 11:05:46,953 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2021-05-13 11:05:46,980 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2021-05-13 11:05:47,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2021-05-13 11:05:47,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2021-05-13 11:05:47,079 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2021-05-13 11:05:47,109 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2021-05-13 11:05:47,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2021-05-13 11:05:47,179 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2021-05-13 11:05:47,204 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2021-05-13 11:05:47,278 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2021-05-13 11:05:47,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2021-05-13 11:05:47,339 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2021-05-13 11:05:47,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2021-05-13 11:05:47,423 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2021-05-13 11:05:47,453 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2021-05-13 11:05:47,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2021-05-13 11:05:47,525 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2021-05-13 11:05:47,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2021-05-13 11:05:47,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2021-05-13 11:05:47,624 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2021-05-13 11:05:47,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2021-05-13 11:05:47,704 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2021-05-13 11:05:47,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2021-05-13 11:05:47,771 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2021-05-13 11:05:47,795 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2021-05-13 11:05:47,821 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2021-05-13 11:05:47,848 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2021-05-13 11:05:47,879 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2021-05-13 11:05:47,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2021-05-13 11:05:47,954 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2021-05-13 11:05:47,997 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2021-05-13 11:05:48,045 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2021-05-13 11:05:48,090 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2021-05-13 11:05:48,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2021-05-13 11:05:48,141 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2021-05-13 11:05:48,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2021-05-13 11:05:48,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2021-05-13 11:05:48,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2021-05-13 11:05:48,233 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2021-05-13 11:05:48,260 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2021-05-13 11:05:48,284 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2021-05-13 11:05:48,318 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2021-05-13 11:05:48,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2021-05-13 11:05:48,372 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2021-05-13 11:05:48,414 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2021-05-13 11:05:48,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2021-05-13 11:05:48,463 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2021-05-13 11:05:48,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2021-05-13 11:05:48,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2021-05-13 11:05:48,534 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2021-05-13 11:05:48,565 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2021-05-13 11:05:48,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2021-05-13 11:05:48,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2021-05-13 11:05:48,658 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2021-05-13 11:05:48,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2021-05-13 11:05:48,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2021-05-13 11:05:48,738 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2021-05-13 11:05:48,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2021-05-13 11:05:48,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2021-05-13 11:05:48,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2021-05-13 11:05:48,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2021-05-13 11:05:48,868 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2021-05-13 11:05:48,894 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2021-05-13 11:05:48,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2021-05-13 11:05:48,944 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2021-05-13 11:05:48,975 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2021-05-13 11:05:49,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2021-05-13 11:05:49,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2021-05-13 11:05:49,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2021-05-13 11:05:49,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2021-05-13 11:05:49,111 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2021-05-13 11:05:49,135 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2021-05-13 11:05:49,163 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2021-05-13 11:05:49,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2021-05-13 11:05:49,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2021-05-13 11:05:49,251 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2021-05-13 11:05:49,298 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2021-05-13 11:05:49,332 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2021-05-13 11:05:49,367 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2021-05-13 11:05:49,393 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2021-05-13 11:05:49,419 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2021-05-13 11:05:49,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2021-05-13 11:05:49,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2021-05-13 11:05:49,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2021-05-13 11:05:49,536 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2021-05-13 11:05:49,560 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2021-05-13 11:05:49,605 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2021-05-13 11:05:49,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2021-05-13 11:05:49,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2021-05-13 11:05:49,709 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2021-05-13 11:05:49,754 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2021-05-13 11:05:49,788 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2021-05-13 11:05:49,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2021-05-13 11:05:49,848 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2021-05-13 11:05:49,888 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2021-05-13 11:05:49,919 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2021-05-13 11:05:49,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2021-05-13 11:05:49,984 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2021-05-13 11:05:50,013 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2021-05-13 11:05:50,038 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2021-05-13 11:05:50,064 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2021-05-13 11:05:50,092 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2021-05-13 11:05:50,117 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2021-05-13 11:05:50,143 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2021-05-13 11:05:50,167 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2021-05-13 11:05:50,194 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2021-05-13 11:05:50,217 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2021-05-13 11:05:50,241 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2021-05-13 11:05:50,267 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2021-05-13 11:05:50,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2021-05-13 11:05:50,338 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2021-05-13 11:05:50,370 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2021-05-13 11:05:50,405 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2021-05-13 11:05:50,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2021-05-13 11:05:50,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2021-05-13 11:05:50,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2021-05-13 11:05:50,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2021-05-13 11:05:50,553 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2021-05-13 11:05:50,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2021-05-13 11:05:50,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2021-05-13 11:05:50,707 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2021-05-13 11:05:50,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2021-05-13 11:05:50,799 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2021-05-13 11:05:50,829 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2021-05-13 11:05:50,860 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2021-05-13 11:05:50,887 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2021-05-13 11:05:50,923 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2021-05-13 11:05:50,955 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2021-05-13 11:05:50,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2021-05-13 11:05:51,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2021-05-13 11:05:51,043 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2021-05-13 11:05:51,068 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2021-05-13 11:05:51,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2021-05-13 11:05:51,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2021-05-13 11:05:51,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2021-05-13 11:05:51,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2021-05-13 11:05:51,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2021-05-13 11:05:51,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2021-05-13 11:05:51,611 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2021-05-13 11:05:51,640 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2021-05-13 11:05:51,667 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2021-05-13 11:05:51,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2021-05-13 11:05:51,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2021-05-13 11:05:51,757 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2021-05-13 11:05:51,798 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2021-05-13 11:05:51,829 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2021-05-13 11:05:51,855 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2021-05-13 11:05:51,883 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2021-05-13 11:05:51,930 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2021-05-13 11:05:51,954 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2021-05-13 11:05:51,977 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2021-05-13 11:05:52,004 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2021-05-13 11:05:52,034 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2021-05-13 11:05:52,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2021-05-13 11:05:52,081 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2021-05-13 11:05:52,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2021-05-13 11:05:52,142 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2021-05-13 11:05:52,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2021-05-13 11:05:52,195 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2021-05-13 11:05:52,226 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2021-05-13 11:05:52,258 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2021-05-13 11:05:52,283 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2021-05-13 11:05:52,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2021-05-13 11:05:52,330 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:05:52,330 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:05:52,330 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2021-05-13 11:06:47,956 ERROR weaver.monitor.BaseMonitorWithFile  - �ر��ļ�������
2021-05-13 11:06:47,958 ERROR weaver.monitor.BaseMonitorWithFile  - weaver.monitor.BaseMonitorWithFile
java.io.FileNotFoundException: D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\log\thread\2021-05-13\thread_110647.log (ϵͳ�Ҳ���ָ�����ļ���)
	at java.io.FileInputStream.open0(Native Method)
	at java.io.FileInputStream.open(FileInputStream.java:195)
	at java.io.FileInputStream.<init>(FileInputStream.java:138)
	at weaver.monitor.BaseMonitorWithFile.zipFile(BaseMonitorWithFile.java:298)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:184)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:42)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:30)
	at weaver.monitor.BaseMonitor.writeLog(BaseMonitor.java:33)
	at weaver.monitor.monitor.SysThreadMonitor.writeMonitorInfo(SysThreadMonitor.java:84)
	at weaver.monitor.monitor.SysThreadMonitor.getMonitorInfo(SysThreadMonitor.java:54)
	at weaver.monitor.monitor.MemMonitor.writeMonitorInfo(MemMonitor.java:149)
	at weaver.monitor.monitor.MemMonitor.getMonitorInfo(MemMonitor.java:120)
	at weaver.monitor.threads.MenThread.doThreadWork(MenThread.java:16)
	at weaver.system.ThreadWorkTimer.run(ThreadWorkTimer.java:67)
2021-05-13 11:07:25,445 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:07:25,579 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:07:26,123 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:07:29,719 ERROR weaver.monitor.BaseMonitorWithFile  - �ر��ļ�������
2021-05-13 11:07:29,725 ERROR weaver.monitor.BaseMonitorWithFile  - weaver.monitor.BaseMonitorWithFile
java.io.FileNotFoundException: D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\log\thread\2021-05-13\thread_110729.log (ϵͳ�Ҳ���ָ�����ļ���)
	at java.io.FileInputStream.open0(Native Method)
	at java.io.FileInputStream.open(FileInputStream.java:195)
	at java.io.FileInputStream.<init>(FileInputStream.java:138)
	at weaver.monitor.BaseMonitorWithFile.zipFile(BaseMonitorWithFile.java:298)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:184)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:42)
	at weaver.monitor.BaseMonitorWithFile.writeLog(BaseMonitorWithFile.java:30)
	at weaver.monitor.BaseMonitor.writeLog(BaseMonitor.java:33)
	at weaver.monitor.monitor.SysThreadMonitor.writeMonitorInfo(SysThreadMonitor.java:84)
	at weaver.monitor.monitor.SysThreadMonitor.getMonitorInfo(SysThreadMonitor.java:54)
	at weaver.monitor.monitor.MemMonitor.writeMonitorInfo(MemMonitor.java:149)
	at weaver.monitor.monitor.MemMonitor.getMonitorInfo(MemMonitor.java:120)
	at weaver.monitor.threads.MenThread.doThreadWork(MenThread.java:16)
	at weaver.system.ThreadWorkTimer.run(ThreadWorkTimer.java:67)
2021-05-13 11:08:15,860 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:08:15,941 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:08:15,941 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:08:16,770 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:08:16,770 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:08:17,637 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:08:18,582 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:08:19,092 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:08:19,214 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:08:19,216 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:08:21,249 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:08:21,286 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 11:08:21,333 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3092498536552560099.jar
2021-05-13 11:08:21,333 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3092498536552560099.jar
2021-05-13 11:08:21,343 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:08:22,449 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:08:22,449 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:08:22,451 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:08:22,452 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:08:22,600 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:08:23,631 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:08:23,640 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:08:23,640 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:08:23,644 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:08:23,648 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:08:23,649 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:08:25,651 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:08:25,652 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:08:25,661 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:08:25,662 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:08:25,662 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:08:25,664 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:08:25,664 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:08:25,666 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:08:25,666 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:08:25,669 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:08:25,911 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:08:26,141 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:08:26,154 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:08:26,167 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:08:26,471 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:08:26,532 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:08:26,537 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:08:26,540 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:08:26,540 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:08:26,542 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:08:26,542 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:08:26,545 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:08:26,586 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:08:26,598 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:08:26,633 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:08:26,697 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:08:26,723 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:08:26,734 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:08:26,775 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:08:26,942 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:08:26,976 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:08:27,035 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:08:27,038 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:08:27,074 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:08:27,074 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:08:27,074 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:08:27,091 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:08:27,091 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:08:27,139 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:08:27,140 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:08:27,287 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:08:27,287 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:08:27,287 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:08:27,287 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:08:27,287 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:08:27,310 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:08:27,632 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:08:27,633 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:08:27,633 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:08:27,649 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:08:27,658 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:08:27,666 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:08:27,670 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=650e0f39-d28c-42a8-ad09-80a2d8b837e4,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:08:27,671 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:08:27,671 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=650e0f39-d28c-42a8-ad09-80a2d8b837e4,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:08:27,735 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=650e0f39-d28c-42a8-ad09-80a2d8b837e4,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:08:27,735 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:08:27,786 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:08:28,322 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:08:28,323 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:08:28,463 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:08:28,467 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:08:29,674 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:08:29,836 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:08:29,836 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:08:29,836 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:08:30,040 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:08:30,178 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:08:39,609 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:08:39,613 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:08:39,613 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:08:41,037 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:08:41,037 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:08:41,919 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:08:42,866 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:08:43,203 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:08:43,344 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:08:43,346 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:08:44,854 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:08:44,876 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:08:45,828 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:08:45,829 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:08:45,832 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:08:45,833 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:08:45,955 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:08:47,223 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:08:47,231 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:08:47,231 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:08:47,235 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:08:47,238 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:08:47,239 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:08:49,251 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:08:49,251 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:08:49,260 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:08:49,260 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:08:49,260 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:08:49,263 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:08:49,263 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:08:49,266 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:08:49,266 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:08:49,269 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:08:49,620 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:08:49,758 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:08:49,771 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:08:49,785 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:08:49,785 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 11:08:49,916 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:08:49,974 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:08:49,978 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:08:49,981 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:08:49,981 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:08:49,984 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:08:49,984 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:08:49,987 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:08:50,016 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:08:50,053 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:08:50,074 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:08:50,114 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:08:50,153 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:08:50,181 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:08:50,224 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:08:50,553 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:08:50,622 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:08:50,623 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:08:50,719 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:08:50,831 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:08:50,870 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:08:50,872 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:08:50,872 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:08:50,891 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:08:50,891 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:08:50,923 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:08:50,987 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:08:50,987 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:08:50,997 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:08:50,999 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:08:51,000 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a15eaee5-1afa-4593-bde9-8c15abf54301,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:08:51,000 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a15eaee5-1afa-4593-bde9-8c15abf54301,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:08:51,033 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:08:51,040 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:08:51,042 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:08:51,043 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:08:51,043 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:08:51,043 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:08:51,043 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:08:51,043 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:08:51,059 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:08:51,100 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a15eaee5-1afa-4593-bde9-8c15abf54301,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:08:51,101 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:08:51,255 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:08:51,257 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:08:51,295 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:08:51,298 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:08:51,393 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:08:51,976 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:08:53,013 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:08:53,324 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:08:53,324 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:08:53,324 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:08:53,348 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:08:57,411 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:08:57,411 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:08:57,413 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:08:57,413 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:08:57,578 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 11:08:58,329 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:14,565 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:14,693 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:15,968 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:16,040 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:16,053 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:16,056 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:16,139 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 11:09:17,921 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,031 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,474 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,507 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,587 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,587 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,587 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,596 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,605 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,607 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,616 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,666 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,737 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,755 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,876 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:33,900 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:09:33,900 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:09:33,993 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,081 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,139 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,173 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,234 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,290 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,296 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,420 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:09:34,459 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,486 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,516 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,528 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,563 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,602 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,606 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,711 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,711 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,793 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,797 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,798 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,876 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,884 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,890 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,935 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,935 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,970 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:34,998 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:35,069 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:35,078 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:35,093 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:35,299 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor415.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor359.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:09:35,405 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:35,613 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:09:39,719 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:07,762 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:07,859 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:07,859 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:07,908 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:07,945 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,290 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,298 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,298 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,299 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,300 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,306 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,328 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,340 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,342 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,353 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,375 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,454 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:08,500 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 11:10:08,612 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,319 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,322 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,370 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,373 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,374 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:10:09,469 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:17:17,713 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:18:29,052 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:19:45,590 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:19:45,598 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:19:45,598 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:19:46,327 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:19:46,328 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:19:47,172 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:19:48,118 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:19:48,621 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:19:48,706 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:19:48,707 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:19:50,806 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:19:50,842 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 11:19:50,882 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent2371065885156358459.jar
2021-05-13 11:19:50,882 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent2371065885156358459.jar
2021-05-13 11:19:50,889 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:19:52,350 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:19:52,350 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:19:52,352 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:19:52,353 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:19:52,452 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:19:53,324 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:19:53,380 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:19:53,380 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:19:53,383 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:19:53,386 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:19:53,386 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:19:55,387 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:19:55,388 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:19:55,405 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:19:55,405 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:19:55,406 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:19:55,408 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:19:55,408 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:19:55,410 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:19:55,411 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:19:55,413 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:19:55,765 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:19:55,991 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:19:56,003 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:19:56,015 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:19:56,149 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:19:56,191 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:19:56,196 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:19:56,199 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:19:56,199 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:19:56,201 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:19:56,202 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:19:56,204 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:19:56,237 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:19:56,259 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:19:56,282 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:19:56,323 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:19:56,352 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:19:56,370 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:19:56,428 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:19:56,689 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:19:56,718 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:19:56,920 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:19:56,924 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:19:57,021 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:19:57,105 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:19:57,106 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:19:57,106 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:19:57,125 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:19:57,126 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:19:57,170 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:19:57,199 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:19:57,202 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:19:57,205 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:19:57,222 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:19:57,225 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=381a9bba-5355-4f0b-94ed-b4d8fa13524e,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:19:57,226 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=381a9bba-5355-4f0b-94ed-b4d8fa13524e,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:19:57,325 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:19:57,325 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:19:57,329 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:19:57,458 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=381a9bba-5355-4f0b-94ed-b4d8fa13524e,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:19:57,459 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:19:57,486 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:19:57,486 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:19:57,486 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:19:57,487 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:19:57,489 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:19:57,533 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:19:57,533 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:19:58,153 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:19:58,162 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:19:58,397 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:19:58,402 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:19:59,228 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:19:59,391 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:19:59,392 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:19:59,392 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:19:59,574 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:20:01,062 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:20:12,741 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:20:12,746 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:20:12,746 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:20:13,580 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:20:13,581 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:20:14,994 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:20:15,891 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:20:16,217 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:20:16,311 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:20:16,312 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:20:17,984 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:20:18,003 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:20:18,667 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:20:18,667 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:20:18,668 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:20:18,669 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:20:18,756 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:20:20,031 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:20:20,038 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:20:20,038 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:20:20,040 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:20:20,041 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:20:20,042 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:20:22,048 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:20:22,048 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:20:22,059 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:20:22,059 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:20:22,059 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:20:22,063 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:20:22,063 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:20:22,065 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:20:22,066 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:20:22,071 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:20:22,550 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:20:22,721 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:20:22,746 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:20:22,747 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 11:20:22,755 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:20:22,935 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:20:22,997 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:20:23,002 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:20:23,004 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:20:23,005 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:20:23,008 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:20:23,008 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:20:23,010 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:20:23,046 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:20:23,157 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:20:23,186 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:20:23,274 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:20:23,322 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:20:23,355 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:20:23,487 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:20:23,678 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:20:23,738 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:20:23,740 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:20:23,860 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:20:23,938 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:20:23,948 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:20:23,949 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:20:23,949 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:20:23,986 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:20:23,986 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:20:24,003 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:20:24,016 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:20:24,020 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:20:24,022 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:20:24,022 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:20:24,036 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=bfd73c25-8cb6-42b0-8656-5a524866fac2,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:20:24,036 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=bfd73c25-8cb6-42b0-8656-5a524866fac2,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:20:24,052 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:20:24,055 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:20:24,067 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:20:24,142 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=bfd73c25-8cb6-42b0-8656-5a524866fac2,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:20:24,144 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:20:24,247 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:20:24,280 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:20:24,281 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:20:24,281 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:20:24,281 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:20:24,281 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:20:24,298 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:20:24,686 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:20:24,687 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:20:24,722 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:20:24,723 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:20:25,799 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:20:26,067 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:20:26,244 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:20:26,244 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:20:26,244 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:20:26,276 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:20:30,396 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:30,489 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:30,516 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 11:20:31,465 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:49,937 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:49,942 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:49,940 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:49,937 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:50,113 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:50,999 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:51,290 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:51,300 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:51,315 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:51,395 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 11:20:53,453 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:20:58,505 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:21:08,213 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:21:53,490 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:22:00,623 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:22:04,915 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:24:00,334 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-05-13 11:24:02,493 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:24:02,499 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:24:02,499 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:24:03,334 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:24:03,334 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:24:04,077 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:24:05,006 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:24:05,629 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:24:05,734 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:24:05,735 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:24:07,802 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:24:07,836 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 11:24:07,877 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3395316183276962796.jar
2021-05-13 11:24:07,877 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3395316183276962796.jar
2021-05-13 11:24:07,887 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:24:09,681 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:24:09,681 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:24:09,684 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:24:09,684 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:24:09,809 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:24:10,820 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:24:10,828 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:24:10,828 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:24:10,830 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:24:10,833 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:24:10,833 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:24:12,837 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:24:12,838 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:24:12,849 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:24:12,849 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:24:12,850 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:24:12,854 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:24:12,854 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:24:12,859 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:24:12,860 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:24:12,865 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:24:13,468 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:24:13,723 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:24:13,737 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:24:13,747 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:24:13,882 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:24:14,003 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:24:14,007 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:24:14,011 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:24:14,011 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:24:14,015 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:24:14,015 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:24:14,018 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:24:14,054 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:24:14,073 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:24:14,107 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:24:14,158 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:24:14,201 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:24:14,222 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:24:14,309 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:24:14,507 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:24:14,514 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:24:14,575 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:24:14,577 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:24:14,711 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:24:14,711 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:24:14,711 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:24:14,740 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:24:14,740 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:24:14,767 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:24:14,769 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:24:14,906 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:24:14,906 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:24:14,909 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:24:14,909 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:24:14,909 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:24:14,932 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:24:15,011 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:24:15,016 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:24:15,019 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:24:15,024 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:24:15,034 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=41d88686-7e59-4122-89f3-6c180967c2ed,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:24:15,035 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=41d88686-7e59-4122-89f3-6c180967c2ed,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:24:15,058 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:24:15,191 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:24:15,195 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:24:15,202 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:24:15,271 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=41d88686-7e59-4122-89f3-6c180967c2ed,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:24:15,272 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:24:15,758 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:24:15,758 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:24:15,798 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:24:15,800 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:24:17,678 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:24:17,761 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:24:17,874 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:24:17,875 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:24:17,875 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:24:18,098 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:24:24,965 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-05-13 11:24:27,153 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:24:27,156 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:24:27,156 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:24:27,952 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:24:27,952 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:24:28,920 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:24:31,043 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:24:31,483 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:24:31,689 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:24:31,691 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:24:32,537 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:24:32,712 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:24:34,321 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:24:34,321 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:24:34,323 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:24:34,324 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:24:34,492 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:24:35,694 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:24:35,700 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:24:35,701 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:24:35,704 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:24:35,707 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:24:35,707 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:24:37,720 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:24:37,720 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:24:37,728 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:24:37,728 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:24:37,729 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:24:37,731 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:24:37,731 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:24:37,734 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:24:37,735 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:24:37,737 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:24:38,097 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:24:38,246 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:24:38,264 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:24:38,276 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:24:38,277 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 11:24:38,398 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:24:38,461 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:24:38,465 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:24:38,467 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:24:38,467 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:24:38,469 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:24:38,469 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:24:38,474 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:24:38,506 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:24:38,591 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:24:38,625 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:24:38,744 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:24:38,796 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:24:38,820 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:24:38,867 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:24:39,287 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:24:39,332 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:24:39,333 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:24:39,461 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:24:39,540 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:24:39,573 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:24:39,574 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:24:39,574 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:24:39,592 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:24:39,593 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:24:39,627 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:24:39,837 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:24:39,837 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:24:39,838 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:24:39,838 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:24:39,838 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:24:39,856 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:24:39,872 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:24:40,195 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:24:40,197 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:24:40,236 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:24:40,238 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:24:40,470 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:24:40,470 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:24:40,515 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:24:40,537 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:24:40,550 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:24:40,554 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:24:40,559 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:24:40,563 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3aa00282-130d-4318-aad2-b0bf9e6e6bf6,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:24:40,563 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3aa00282-130d-4318-aad2-b0bf9e6e6bf6,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:24:40,621 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=3aa00282-130d-4318-aad2-b0bf9e6e6bf6,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:24:40,621 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:24:41,539 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:24:41,555 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:24:41,724 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:24:41,724 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:24:41,725 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:24:41,772 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:24:44,978 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:24:45,065 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 11:24:45,543 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:24:46,025 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:01,402 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:01,404 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:01,410 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:01,549 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:02,076 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:02,192 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:02,193 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:02,193 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:02,332 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 11:25:04,687 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:09,440 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:25:40,349 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-05-13 11:25:42,520 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:25:42,526 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:25:42,526 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:25:43,216 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:25:43,217 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:25:43,831 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:25:44,569 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:25:44,841 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:25:45,002 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:25:45,004 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:25:47,745 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:25:47,845 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 11:25:47,895 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3282903317801749429.jar
2021-05-13 11:25:47,896 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3282903317801749429.jar
2021-05-13 11:25:47,908 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:25:48,394 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:25:48,394 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:25:48,396 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:25:48,396 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:25:48,490 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:25:49,441 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:25:49,448 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:25:49,448 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:25:49,450 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:25:49,453 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:25:49,454 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:25:51,468 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:25:51,469 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:25:51,480 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:25:51,481 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:25:51,481 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:25:51,483 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:25:51,483 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:25:51,485 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:25:51,486 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:25:51,488 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:25:51,756 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:25:52,100 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:25:52,111 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:25:52,122 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:25:52,242 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:25:52,290 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:25:52,295 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:25:52,298 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:25:52,298 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:25:52,302 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:25:52,302 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:25:52,305 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:25:52,333 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:25:52,361 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:25:52,383 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:25:52,423 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:25:52,453 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:25:52,468 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:25:52,514 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:25:52,696 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:25:52,725 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:25:52,774 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:25:52,775 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:25:52,870 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:25:52,871 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:25:52,871 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:25:52,871 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:25:52,896 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:25:52,896 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:25:52,922 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:25:53,198 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:25:53,199 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:25:53,199 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:25:53,199 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:25:53,199 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:25:53,219 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:25:53,307 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:25:53,310 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:25:53,308 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:25:53,349 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:25:53,378 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:25:53,378 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:25:53,384 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=13a8df0f-bbc3-4917-b448-15fbcc404b8b,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:25:53,385 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:25:53,385 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=13a8df0f-bbc3-4917-b448-15fbcc404b8b,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:25:53,532 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=13a8df0f-bbc3-4917-b448-15fbcc404b8b,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:25:53,533 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:25:53,635 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:25:53,749 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:25:53,752 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:25:53,754 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 11:25:53,903 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:25:53,905 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:25:54,034 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:25:54,036 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:25:55,330 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:25:55,497 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:25:55,498 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:25:55,498 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:25:55,528 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:25:55,723 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:26:07,826 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 11:26:07,829 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 11:26:07,829 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 11:26:08,915 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:26:08,916 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 11:26:09,834 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:26:11,089 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 11:26:11,505 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 11:26:11,660 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 11:26:11,661 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 11:26:13,182 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 11:26:13,199 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 11:26:13,889 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 11:26:13,889 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 11:26:13,891 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 11:26:13,891 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 11:26:13,970 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 11:26:14,849 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 11:26:14,855 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 11:26:14,855 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 11:26:14,857 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 11:26:14,859 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 11:26:14,860 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 11:26:16,862 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 11:26:16,863 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 11:26:16,876 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 11:26:16,876 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 11:26:16,876 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 11:26:16,880 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 11:26:16,880 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 11:26:16,882 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 11:26:16,882 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 11:26:16,883 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 11:26:17,387 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 11:26:17,592 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 11:26:17,604 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 11:26:17,621 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 11:26:17,622 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 11:26:17,790 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 11:26:17,866 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 11:26:17,873 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 11:26:17,875 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 11:26:17,875 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 11:26:17,878 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 11:26:17,878 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 11:26:17,881 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 11:26:17,936 INFO  weaver.general.InitServer  - end.....
2021-05-13 11:26:17,959 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 11:26:18,026 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 11:26:18,084 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 11:26:18,128 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 11:26:18,147 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 11:26:18,242 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 11:26:18,513 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 11:26:18,630 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 11:26:18,633 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 11:26:18,770 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 11:26:18,782 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 11:26:18,880 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:26:18,883 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 11:26:18,883 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 11:26:18,892 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 11:26:18,937 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 11:26:18,940 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=09cf6780-60c0-422e-92cc-2f3a5764ba79,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 11:26:18,940 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=09cf6780-60c0-422e-92cc-2f3a5764ba79,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 11:26:18,947 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 11:26:18,956 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 11:26:18,958 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 11:26:18,958 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:26:18,958 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 11:26:19,067 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 11:26:19,067 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 11:26:19,089 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 11:26:19,104 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 11:26:19,104 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=09cf6780-60c0-422e-92cc-2f3a5764ba79,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 11:26:19,104 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 11:26:19,305 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 11:26:19,305 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 11:26:19,305 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 11:26:19,305 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 11:26:19,305 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 11:26:19,322 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 11:26:19,825 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 11:26:19,827 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 11:26:19,865 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 11:26:19,866 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 11:26:20,919 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 11:26:21,149 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 11:26:21,150 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 11:26:21,150 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 11:26:21,192 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 11:26:22,249 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 11:26:26,415 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:26,499 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:26,528 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 11:26:27,243 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:41,362 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:41,363 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:41,362 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:41,491 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:42,047 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:42,188 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:42,200 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:42,201 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:42,292 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 11:26:44,317 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 11:26:50,383 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:47:03,381 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 14:47:03,387 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 14:47:03,387 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 14:47:04,362 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 14:47:04,362 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 14:47:05,265 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 14:47:06,119 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 14:47:06,576 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 14:47:06,669 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 14:47:06,671 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 14:47:09,532 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 14:47:09,725 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-05-13 14:47:11,106 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent8430770981437629380.jar
2021-05-13 14:47:11,107 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent8430770981437629380.jar
2021-05-13 14:47:11,123 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 14:47:12,314 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 14:47:12,314 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 14:47:12,318 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 14:47:12,318 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 14:47:12,537 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 14:47:14,116 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 14:47:14,128 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 14:47:14,128 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 14:47:14,132 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 14:47:14,135 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 14:47:14,136 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 14:47:16,319 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 14:47:16,320 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 14:47:16,329 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 14:47:16,329 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 14:47:16,329 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 14:47:16,331 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 14:47:16,331 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 14:47:16,333 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 14:47:16,334 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 14:47:16,336 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 14:47:16,759 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 14:47:16,921 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 14:47:16,933 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 14:47:16,946 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 14:47:17,218 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 14:47:17,298 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 14:47:17,303 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 14:47:17,308 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 14:47:17,308 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 14:47:17,312 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 14:47:17,312 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 14:47:17,315 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 14:47:17,355 INFO  weaver.general.InitServer  - end.....
2021-05-13 14:47:17,367 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 14:47:17,404 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 14:47:17,468 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 14:47:17,509 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 14:47:17,529 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 14:47:17,611 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 14:47:17,769 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 14:47:17,770 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 14:47:17,770 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 14:47:17,771 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 14:47:17,800 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 14:47:17,803 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 14:47:17,803 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 14:47:17,827 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 14:47:17,880 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 14:47:17,881 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 14:47:17,935 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 14:47:17,935 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 14:47:17,935 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 14:47:17,935 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 14:47:17,936 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 14:47:17,946 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 14:47:18,012 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 14:47:18,309 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 14:47:18,312 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 14:47:18,315 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 14:47:18,419 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 14:47:18,459 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 14:47:18,462 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 14:47:18,465 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=d003711e-9eeb-4467-bf4e-e15b08d8bb48,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 14:47:18,465 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=d003711e-9eeb-4467-bf4e-e15b08d8bb48,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 14:47:18,552 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 14:47:18,558 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=d003711e-9eeb-4467-bf4e-e15b08d8bb48,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 14:47:18,559 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 14:47:18,651 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 14:47:18,664 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,666 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,670 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,672 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,675 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,676 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,678 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:47:18,873 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 14:47:18,873 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 14:47:18,917 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 14:47:18,921 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 14:47:20,170 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 14:47:20,349 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 14:47:21,531 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 14:47:21,531 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 14:47:21,532 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 14:47:21,589 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 14:47:29,087 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-05-13 14:47:31,215 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-05-13 14:47:31,218 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-05-13 14:47:31,218 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-05-13 14:47:31,905 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 14:47:31,906 INFO  weaver.general.InitServer  - init ioc container...
2021-05-13 14:47:32,585 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 14:47:33,359 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-05-13 14:47:33,706 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-05-13 14:47:33,794 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-05-13 14:47:33,795 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-05-13 14:47:36,413 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-05-13 14:47:36,429 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-05-13 14:47:37,068 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-05-13 14:47:37,068 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-05-13 14:47:37,070 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-05-13 14:47:37,071 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-05-13 14:47:37,186 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-05-13 14:47:38,677 INFO  weaver.general.InitServer  - end ioc container init...
2021-05-13 14:47:38,684 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-05-13 14:47:38,684 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-05-13 14:47:38,686 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-05-13 14:47:38,689 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-05-13 14:47:38,689 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-05-13 14:47:40,692 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-05-13 14:47:40,692 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-05-13 14:47:40,699 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-05-13 14:47:40,700 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-05-13 14:47:40,700 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-05-13 14:47:40,703 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-05-13 14:47:40,703 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-05-13 14:47:40,705 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-05-13 14:47:40,706 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-05-13 14:47:40,708 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-05-13 14:47:41,143 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-05-13 14:47:41,354 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-05-13 14:47:41,367 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-05-13 14:47:41,380 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-05-13 14:47:41,380 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-05-13 14:47:41,522 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-05-13 14:47:41,577 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-05-13 14:47:41,582 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-05-13 14:47:41,584 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-05-13 14:47:41,584 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-05-13 14:47:41,587 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-05-13 14:47:41,587 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-05-13 14:47:41,590 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-05-13 14:47:41,619 INFO  weaver.general.InitServer  - end.....
2021-05-13 14:47:41,641 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-05-13 14:47:41,663 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-05-13 14:47:41,719 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-05-13 14:47:41,777 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-05-13 14:47:41,808 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-05-13 14:47:41,896 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-05-13 14:47:42,045 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-05-13 14:47:42,101 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-05-13 14:47:42,102 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-05-13 14:47:42,273 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-05-13 14:47:42,274 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-05-13 14:47:42,274 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-05-13 14:47:42,287 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-05-13 14:47:42,293 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-05-13 14:47:42,297 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-05-13 14:47:42,297 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-05-13 14:47:42,312 ERROR weaver.general.BaseBean  - ������ʱ����
2021-05-13 14:47:42,390 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-05-13 14:47:42,390 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-05-13 14:47:42,390 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-05-13 14:47:42,390 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-05-13 14:47:42,390 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-05-13 14:47:42,405 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-05-13 14:47:42,586 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 14:47:42,588 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-05-13 14:47:42,591 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-05-13 14:47:42,594 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-05-13 14:47:42,594 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c2b97c5e-7826-4638-a16f-32cd573d703f,��ʼ�ʼ��ڲ��ռ�������
2021-05-13 14:47:42,594 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c2b97c5e-7826-4638-a16f-32cd573d703f,-> ########## ִ�м�ʱ��ʼ ##########
2021-05-13 14:47:42,619 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-05-13 14:47:42,620 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-05-13 14:47:42,621 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 14:47:42,667 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c2b97c5e-7826-4638-a16f-32cd573d703f,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-05-13 14:47:42,667 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-05-13 14:47:42,878 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-05-13 14:47:42,878 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-05-13 14:47:42,907 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-05-13 14:47:42,920 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-05-13 14:47:42,922 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-05-13 14:47:43,513 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-05-13 14:47:44,610 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-05-13 14:47:44,778 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-11-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-11-14') or  lastLoginDate<'2020-11-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-05-13 14:47:44,778 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-05-13 14:47:44,779 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-05-13 14:47:44,982 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-05-13 14:47:49,037 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-05-13 14:48:01,786 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-05-13 14:48:09,750 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:09,821 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,210 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,213 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,215 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,227 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,230 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,227 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,303 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,350 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,409 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,468 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,468 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,610 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 14:48:10,610 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 14:48:10,819 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:10,884 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,079 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,126 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,171 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,207 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,236 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,259 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,302 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,344 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,386 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,419 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,421 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-05-13 14:48:11,443 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,488 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,490 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,569 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,637 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,692 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,750 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,842 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,914 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,976 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:11,984 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,008 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,011 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,098 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,108 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,112 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,123 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,282 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,636 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:48:12,650 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor374.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor359.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-05-13 14:49:38,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:49:50,972 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:51:11,510 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:51:12,006 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:51:12,494 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:53:13,143 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:54:11,507 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:54:12,052 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-05-13 14:54:12,495 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
