2021-03-19 17:21:50,557 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-19 17:21:52,668 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-19 17:21:52,675 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-19 17:21:52,676 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-19 17:21:53,323 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, **************]
2021-03-19 17:21:53,324 INFO  weaver.general.InitServer  - init ioc container...
2021-03-19 17:21:53,941 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-19 17:21:54,768 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-19 17:21:57,886 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-19 17:21:57,916 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-19 17:21:57,955 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5121934826815563305.jar
2021-03-19 17:21:57,955 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5121934826815563305.jar
2021-03-19 17:21:57,963 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-19 17:22:24,220 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:22:24,223 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:22:53,637 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:22:54,226 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:23:23,048 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:23:24,228 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:23:52,458 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:23:54,234 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:24:21,873 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:24:24,234 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:24:51,280 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2021-03-19 17:24:54,236 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2021-03-19 17:25:13,436 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-19 17:25:13,696 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-19 17:25:13,698 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-19 17:25:16,427 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-19 17:25:16,428 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-19 17:25:16,430 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-19 17:25:16,431 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-19 17:25:16,613 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-19 17:25:17,713 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-19 17:25:17,722 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-19 17:25:17,722 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-19 17:25:17,724 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-19 17:25:17,726 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-19 17:25:17,727 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-19 17:25:19,732 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-19 17:25:19,733 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-19 17:25:19,744 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-19 17:25:19,745 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-19 17:25:19,745 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-19 17:25:19,748 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-19 17:25:19,748 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-19 17:25:19,750 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-19 17:25:19,751 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-19 17:25:19,752 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-19 17:25:20,072 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-19 17:25:20,376 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-19 17:25:20,404 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-19 17:25:20,416 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-19 17:25:20,563 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-19 17:25:20,618 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-19 17:25:20,627 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-19 17:25:20,630 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-19 17:25:20,630 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-19 17:25:20,634 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-19 17:25:20,635 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-19 17:25:20,641 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-19 17:25:20,673 INFO  weaver.general.InitServer  - end.....
2021-03-19 17:25:20,710 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-19 17:25:20,740 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-19 17:25:20,785 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-19 17:25:20,818 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-19 17:25:20,830 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-19 17:25:20,876 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-19 17:25:21,162 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-19 17:25:21,223 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-19 17:25:21,227 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-19 17:25:21,326 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-19 17:25:21,515 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-19 17:25:21,593 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-19 17:25:21,595 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-19 17:25:21,595 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-19 17:25:21,631 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-19 17:25:21,635 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-19 17:25:21,635 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-19 17:25:21,636 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-19 17:25:21,643 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-19 17:25:21,651 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-19 17:25:21,670 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-19 17:25:21,672 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8274707b-ef0c-47b2-bed1-421de6bc90a8,��ʼ�ʼ��ڲ��ռ�������
2021-03-19 17:25:21,672 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8274707b-ef0c-47b2-bed1-421de6bc90a8,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-19 17:25:21,687 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-19 17:25:21,696 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-19 17:25:21,715 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-19 17:25:21,742 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=8274707b-ef0c-47b2-bed1-421de6bc90a8,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-19 17:25:21,742 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-19 17:25:21,877 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-19 17:25:21,886 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-19 17:25:21,887 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-19 17:25:21,887 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-19 17:25:21,887 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-19 17:25:21,887 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-19 17:25:21,904 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-19 17:25:22,522 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-19 17:25:22,524 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-19 17:25:22,683 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-19 17:25:22,707 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-19 17:25:23,673 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-19 17:25:23,852 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-20' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-20') or  lastLoginDate<'2020-09-20')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-19 17:25:23,852 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-19 17:25:23,852 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-19 17:25:23,882 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-19 17:25:23,885 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-19 17:25:33,520 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-19 17:25:33,526 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-19 17:25:33,526 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-19 17:25:34,302 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-19 17:25:34,302 INFO  weaver.general.InitServer  - init ioc container...
2021-03-19 17:25:35,113 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-19 17:25:36,028 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-19 17:25:36,377 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-19 17:25:36,470 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-19 17:25:36,471 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-19 17:25:38,568 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-19 17:25:38,568 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-19 17:25:38,569 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-19 17:25:38,569 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-19 17:25:38,713 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-19 17:25:38,787 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-19 17:25:38,805 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-19 17:25:39,456 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-19 17:25:39,465 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-19 17:25:39,465 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-19 17:25:39,467 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-19 17:25:39,469 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-19 17:25:39,469 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-19 17:25:41,473 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-19 17:25:41,474 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-19 17:25:41,492 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-19 17:25:41,492 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-19 17:25:41,492 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-19 17:25:41,497 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-19 17:25:41,497 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-19 17:25:41,500 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-19 17:25:41,500 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-19 17:25:41,508 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-19 17:25:41,850 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-19 17:25:42,094 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-19 17:25:42,109 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-19 17:25:42,122 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-19 17:25:42,123 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-19 17:25:42,234 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-19 17:25:42,273 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-19 17:25:42,277 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-19 17:25:42,279 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-19 17:25:42,279 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-19 17:25:42,281 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-19 17:25:42,282 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-19 17:25:42,287 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-19 17:25:42,313 INFO  weaver.general.InitServer  - end.....
2021-03-19 17:25:42,347 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-19 17:25:42,375 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-19 17:25:42,416 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-19 17:25:42,451 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-19 17:25:42,462 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-19 17:25:42,500 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-19 17:25:42,816 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-19 17:25:42,866 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-19 17:25:42,867 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-19 17:25:42,944 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-19 17:25:43,136 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-19 17:25:43,272 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-19 17:25:43,272 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-19 17:25:43,273 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-19 17:25:43,281 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-19 17:25:43,282 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-19 17:25:43,288 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-19 17:25:43,292 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=85fbc389-3279-41d2-b3ad-a48b1f085f52,��ʼ�ʼ��ڲ��ռ�������
2021-03-19 17:25:43,292 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-19 17:25:43,292 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-19 17:25:43,292 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-19 17:25:43,292 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=85fbc389-3279-41d2-b3ad-a48b1f085f52,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-19 17:25:43,298 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-19 17:25:43,307 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-19 17:25:43,310 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-19 17:25:43,317 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-19 17:25:43,338 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=85fbc389-3279-41d2-b3ad-a48b1f085f52,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-19 17:25:43,339 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-19 17:25:43,429 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-19 17:25:43,429 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-19 17:25:43,429 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-19 17:25:43,432 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-19 17:25:43,432 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-19 17:25:43,451 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-19 17:25:43,674 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-19 17:25:43,718 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-19 17:25:43,720 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-19 17:25:43,772 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-19 17:25:43,789 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-19 17:25:43,793 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-19 17:25:44,968 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-19 17:25:45,314 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-19 17:25:45,485 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-20' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-20') or  lastLoginDate<'2020-09-20')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-19 17:25:45,485 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-19 17:25:45,485 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-19 17:25:45,682 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-19 17:25:53,265 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-19 17:26:50,178 ERROR weaver.general.BaseBean  - qrcode_config>>>
