2022-04-25 09:47:17,509 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-25 09:47:17,519 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-25 09:47:17,519 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-25 09:47:18,346 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-25 09:47:18,347 INFO  weaver.general.InitServer  - init ioc container...
2022-04-25 09:47:19,505 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-25 09:47:21,672 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-25 09:47:22,455 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-25 09:47:22,635 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-25 09:47:22,638 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-25 09:47:22,887 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-25 09:47:22,956 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-25 09:47:23,018 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3626441116255832721.jar
2022-04-25 09:47:23,018 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3626441116255832721.jar
2022-04-25 09:47:23,035 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-25 09:47:31,148 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-25 09:47:31,148 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-25 09:47:31,152 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-25 09:47:31,152 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-25 09:47:31,428 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-25 09:47:34,887 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-25 09:47:34,894 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-25 09:47:34,894 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-25 09:47:34,897 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-25 09:47:34,922 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-25 09:47:34,924 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-25 09:47:36,928 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-25 09:47:36,928 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-25 09:47:36,939 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-25 09:47:36,939 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-25 09:47:36,939 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-25 09:47:36,941 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-25 09:47:36,941 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-25 09:47:36,944 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-25 09:47:36,944 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-25 09:47:36,946 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-25 09:47:37,577 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-25 09:47:37,710 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-25 09:47:37,726 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-25 09:47:37,741 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-25 09:47:37,962 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-25 09:47:38,152 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-25 09:47:38,157 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-25 09:47:38,161 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-25 09:47:38,162 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-25 09:47:38,166 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-25 09:47:38,166 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-25 09:47:38,170 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-25 09:47:38,216 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-25 09:47:38,217 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-25 09:47:38,218 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-25 09:47:38,226 INFO  weaver.general.InitServer  - end.....
2022-04-25 09:47:38,240 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-25 09:47:38,256 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-25 09:47:38,256 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-25 09:47:38,268 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-25 09:47:38,300 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-25 09:47:38,429 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-25 09:47:38,460 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-25 09:47:38,490 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-25 09:47:38,502 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-25 09:47:38,502 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-25 09:47:38,502 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-25 09:47:38,502 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-25 09:47:38,503 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-25 09:47:38,513 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-25 09:47:38,519 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-25 09:47:38,726 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-25 09:47:38,797 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-25 09:47:38,890 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-25 09:47:38,892 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-25 09:47:39,048 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-25 09:47:39,068 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-25 09:47:39,161 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-25 09:47:39,167 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-25 09:47:39,170 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-25 09:47:39,172 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-25 09:47:39,172 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fa3a991a-8ba4-4bb5-92f4-c9bb7207d46c,��ʼ�ʼ��ڲ��ռ�������
2022-04-25 09:47:39,172 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fa3a991a-8ba4-4bb5-92f4-c9bb7207d46c,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-25 09:47:39,187 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-25 09:47:39,190 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-25 09:47:39,194 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-25 09:47:39,206 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,209 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,211 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,214 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,216 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,218 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,220 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:47:39,345 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fa3a991a-8ba4-4bb5-92f4-c9bb7207d46c,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-25 09:47:39,346 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-25 09:47:39,787 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-25 09:47:39,788 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-25 09:47:39,820 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-25 09:47:39,825 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-25 09:47:41,109 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-25 09:47:41,260 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-25 09:47:41,560 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-27' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-27') or  lastLoginDate<'2021-10-27')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-25 09:47:41,561 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-25 09:47:41,561 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-25 09:47:41,780 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-25 09:47:53,091 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-25 09:47:53,095 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-25 09:47:53,095 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-25 09:47:53,628 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-25 09:47:53,629 INFO  weaver.general.InitServer  - init ioc container...
2022-04-25 09:47:54,151 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-25 09:47:55,358 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-25 09:47:55,674 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-25 09:47:55,769 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-25 09:47:55,771 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-25 09:47:58,298 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-25 09:47:58,311 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-25 09:48:00,088 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-25 09:48:00,088 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-25 09:48:00,090 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-25 09:48:00,090 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-25 09:48:00,263 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-25 09:48:02,589 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-25 09:48:02,595 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-25 09:48:02,595 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-25 09:48:02,597 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-25 09:48:02,600 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-25 09:48:02,600 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-25 09:48:04,603 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-25 09:48:04,604 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-25 09:48:04,615 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-25 09:48:04,616 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-25 09:48:04,616 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-25 09:48:04,620 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-25 09:48:04,620 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-25 09:48:04,622 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-25 09:48:04,622 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-25 09:48:04,625 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-25 09:48:05,280 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-25 09:48:05,358 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-25 09:48:05,382 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-25 09:48:05,396 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-25 09:48:05,396 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2022-04-25 09:48:05,649 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-25 09:48:05,741 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-25 09:48:05,746 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-25 09:48:05,748 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-25 09:48:05,748 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-25 09:48:05,750 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-25 09:48:05,750 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-25 09:48:05,752 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-25 09:48:05,912 INFO  weaver.general.InitServer  - end.....
2022-04-25 09:48:05,951 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-25 09:48:05,989 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-25 09:48:06,033 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-25 09:48:06,033 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-25 09:48:06,033 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-25 09:48:06,050 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-25 09:48:06,050 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-25 09:48:06,053 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-25 09:48:06,074 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-25 09:48:06,101 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-25 09:48:06,132 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-25 09:48:06,148 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-25 09:48:06,192 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-25 09:48:06,192 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-25 09:48:06,192 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-25 09:48:06,192 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-25 09:48:06,192 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-25 09:48:06,206 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-25 09:48:06,358 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-25 09:48:06,420 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-25 09:48:06,577 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-25 09:48:06,578 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-25 09:48:06,767 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-25 09:48:06,770 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-25 09:48:06,770 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-25 09:48:06,771 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-25 09:48:06,779 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-25 09:48:06,783 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=ee0a822b-e575-48c4-874f-4865d058f73f,��ʼ�ʼ��ڲ��ռ�������
2022-04-25 09:48:06,783 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=ee0a822b-e575-48c4-874f-4865d058f73f,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-25 09:48:06,793 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-25 09:48:06,810 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-25 09:48:06,810 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-25 09:48:06,814 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-25 09:48:06,873 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,877 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,879 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,881 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,885 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,887 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,891 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 09:48:06,954 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=ee0a822b-e575-48c4-874f-4865d058f73f,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-25 09:48:06,955 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-25 09:48:07,408 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-25 09:48:07,409 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-25 09:48:07,433 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-25 09:48:07,434 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-25 09:48:08,139 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-25 09:48:08,899 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-25 09:48:09,200 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-27' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-27') or  lastLoginDate<'2021-10-27')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-25 09:48:09,200 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-25 09:48:09,200 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-25 09:48:09,410 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-25 09:48:10,909 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/pubAction/getAttendanceCardCustom,��ǰsessionId��abciSfD5MDwkZ6g35zEby
2022-04-25 09:48:12,454 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2022-04-25 09:48:25,999 ERROR weaver.general.BaseBean  - qrcode_config>>>
2022-04-25 09:48:37,184 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/pubAction/getAttendanceCardCustom,��ǰsessionId��abc9z2Hz17nMVbbh6zEby
2022-04-25 09:48:44,489 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,667 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,719 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,721 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,722 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,722 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,723 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,731 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,746 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:44,923 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:45,013 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:45,024 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:45,186 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:45,207 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-25 09:48:45,210 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-25 09:48:45,817 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,011 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,047 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,055 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,066 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,113 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-25 09:48:46,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,156 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,207 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,260 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,298 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,322 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,337 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,418 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,459 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-25 09:48:46,490 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,507 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,563 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,744 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,764 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,783 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,827 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,874 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,894 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,896 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:46,903 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,054 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,055 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,117 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,140 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,153 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,154 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,243 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,348 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:47,366 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:49,228 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:49,460 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:49,710 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:49,712 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:49,737 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:50,630 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:50,760 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:50,766 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:50,799 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:50,801 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:51,375 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:48:52,686 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:53,799 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:55,791 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:56,852 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-25 09:48:58,792 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:49:17,076 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:49:27,181 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-25 09:49:27,188 ERROR weaver.general.BaseBean  - ����������ʶ��daily
2022-04-25 10:02:30,482 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-04-25 10:02:31,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2022-04-25 10:02:31,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2022-04-25 10:02:31,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2022-04-25 10:02:31,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2022-04-25 10:02:31,729 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2022-04-25 10:02:31,910 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2022-04-25 10:02:32,054 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2022-04-25 10:02:32,206 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2022-04-25 10:02:32,332 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2022-04-25 10:02:32,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2022-04-25 10:02:32,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2022-04-25 10:02:33,042 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2022-04-25 10:02:33,239 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2022-04-25 10:02:33,417 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2022-04-25 10:02:33,610 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2022-04-25 10:02:34,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2022-04-25 10:02:34,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2022-04-25 10:02:34,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2022-04-25 10:02:34,476 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2022-04-25 10:02:34,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2022-04-25 10:02:34,811 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2022-04-25 10:02:35,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2022-04-25 10:02:35,189 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2022-04-25 10:02:35,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2022-04-25 10:02:35,607 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2022-04-25 10:02:35,853 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2022-04-25 10:02:36,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2022-04-25 10:02:36,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2022-04-25 10:02:36,404 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2022-04-25 10:02:36,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2022-04-25 10:02:36,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2022-04-25 10:02:37,057 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2022-04-25 10:02:37,382 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2022-04-25 10:02:37,616 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2022-04-25 10:02:37,795 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2022-04-25 10:02:37,959 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2022-04-25 10:02:38,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2022-04-25 10:02:38,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2022-04-25 10:02:38,649 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2022-04-25 10:02:38,892 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2022-04-25 10:02:39,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2022-04-25 10:02:39,175 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2022-04-25 10:02:39,334 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2022-04-25 10:02:39,487 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2022-04-25 10:02:39,620 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2022-04-25 10:02:39,807 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2022-04-25 10:02:39,899 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2022-04-25 10:02:40,057 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2022-04-25 10:02:40,210 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2022-04-25 10:02:40,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2022-04-25 10:02:40,501 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2022-04-25 10:02:40,622 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2022-04-25 10:02:40,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2022-04-25 10:02:40,831 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2022-04-25 10:02:40,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2022-04-25 10:02:41,134 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2022-04-25 10:02:41,295 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2022-04-25 10:02:41,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2022-04-25 10:02:41,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2022-04-25 10:02:41,723 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2022-04-25 10:02:41,883 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2022-04-25 10:02:42,043 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2022-04-25 10:02:42,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2022-04-25 10:02:42,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2022-04-25 10:02:42,637 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2022-04-25 10:02:42,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2022-04-25 10:02:43,134 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2022-04-25 10:02:43,361 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2022-04-25 10:02:43,580 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2022-04-25 10:02:43,751 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2022-04-25 10:02:43,887 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2022-04-25 10:02:44,106 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2022-04-25 10:02:44,288 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2022-04-25 10:02:44,492 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2022-04-25 10:02:44,692 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2022-04-25 10:02:44,978 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2022-04-25 10:02:45,178 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2022-04-25 10:02:45,435 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2022-04-25 10:02:45,701 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2022-04-25 10:02:45,879 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2022-04-25 10:02:46,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2022-04-25 10:02:46,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2022-04-25 10:02:46,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2022-04-25 10:02:46,778 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2022-04-25 10:02:47,011 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2022-04-25 10:02:47,189 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2022-04-25 10:02:47,392 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2022-04-25 10:02:47,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2022-04-25 10:02:47,978 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2022-04-25 10:02:48,199 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2022-04-25 10:02:48,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2022-04-25 10:02:48,586 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2022-04-25 10:02:48,788 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2022-04-25 10:02:48,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2022-04-25 10:02:49,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2022-04-25 10:02:49,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2022-04-25 10:02:49,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2022-04-25 10:02:49,667 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2022-04-25 10:02:49,841 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2022-04-25 10:02:50,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2022-04-25 10:02:50,123 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2022-04-25 10:02:50,278 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2022-04-25 10:02:50,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2022-04-25 10:02:50,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2022-04-25 10:02:50,948 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2022-04-25 10:02:51,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2022-04-25 10:02:51,283 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2022-04-25 10:02:51,507 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2022-04-25 10:02:51,721 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2022-04-25 10:02:51,993 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2022-04-25 10:02:52,198 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2022-04-25 10:02:52,444 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2022-04-25 10:02:52,607 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2022-04-25 10:02:52,777 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2022-04-25 10:02:52,902 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2022-04-25 10:02:53,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2022-04-25 10:02:53,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2022-04-25 10:02:53,334 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2022-04-25 10:02:53,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2022-04-25 10:02:53,548 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2022-04-25 10:02:53,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2022-04-25 10:02:53,831 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2022-04-25 10:02:54,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2022-04-25 10:02:54,187 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2022-04-25 10:02:54,336 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2022-04-25 10:02:54,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2022-04-25 10:02:54,574 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2022-04-25 10:02:54,736 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2022-04-25 10:02:54,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2022-04-25 10:02:54,976 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2022-04-25 10:02:55,066 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2022-04-25 10:02:55,205 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2022-04-25 10:02:55,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2022-04-25 10:02:55,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2022-04-25 10:02:55,614 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2022-04-25 10:02:55,778 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2022-04-25 10:02:55,927 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2022-04-25 10:02:56,133 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2022-04-25 10:02:56,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2022-04-25 10:02:56,444 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2022-04-25 10:02:56,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2022-04-25 10:02:56,804 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2022-04-25 10:02:56,988 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2022-04-25 10:02:57,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2022-04-25 10:02:57,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2022-04-25 10:02:57,609 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2022-04-25 10:02:57,828 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2022-04-25 10:02:57,935 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2022-04-25 10:02:58,103 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2022-04-25 10:02:58,236 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2022-04-25 10:02:58,352 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2022-04-25 10:02:58,487 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2022-04-25 10:02:58,635 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2022-04-25 10:02:58,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2022-04-25 10:02:58,935 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2022-04-25 10:02:59,098 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2022-04-25 10:02:59,382 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2022-04-25 10:02:59,609 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2022-04-25 10:02:59,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2022-04-25 10:02:59,927 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2022-04-25 10:03:00,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2022-04-25 10:03:00,224 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2022-04-25 10:03:00,405 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2022-04-25 10:03:00,565 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2022-04-25 10:03:00,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2022-04-25 10:03:00,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2022-04-25 10:03:01,039 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2022-04-25 10:03:01,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2022-04-25 10:03:01,332 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2022-04-25 10:03:01,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2022-04-25 10:03:01,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2022-04-25 10:03:01,784 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2022-04-25 10:03:02,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2022-04-25 10:03:02,209 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2022-04-25 10:03:02,365 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2022-04-25 10:03:02,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2022-04-25 10:03:02,624 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2022-04-25 10:03:02,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2022-04-25 10:03:02,918 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2022-04-25 10:03:03,035 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2022-04-25 10:03:03,177 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2022-04-25 10:03:03,390 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2022-04-25 10:03:03,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2022-04-25 10:03:03,665 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2022-04-25 10:03:03,815 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2022-04-25 10:03:04,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2022-04-25 10:03:04,277 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2022-04-25 10:03:04,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2022-04-25 10:03:04,615 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2022-04-25 10:03:04,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2022-04-25 10:03:04,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2022-04-25 10:03:05,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2022-04-25 10:03:05,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2022-04-25 10:03:05,578 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2022-04-25 10:03:05,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2022-04-25 10:03:05,863 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2022-04-25 10:03:05,968 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2022-04-25 10:03:06,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2022-04-25 10:03:06,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2022-04-25 10:03:06,336 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2022-04-25 10:03:06,446 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2022-04-25 10:03:06,585 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2022-04-25 10:03:06,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2022-04-25 10:03:06,949 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2022-04-25 10:03:07,157 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2022-04-25 10:03:07,321 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2022-04-25 10:03:07,473 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2022-04-25 10:03:07,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2022-04-25 10:03:07,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2022-04-25 10:03:07,870 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2022-04-25 10:03:07,977 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2022-04-25 10:03:08,177 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2022-04-25 10:03:08,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2022-04-25 10:03:08,467 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2022-04-25 10:03:08,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2022-04-25 10:03:08,763 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2022-04-25 10:03:08,933 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2022-04-25 10:03:09,126 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2022-04-25 10:03:09,382 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2022-04-25 10:03:09,566 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2022-04-25 10:03:09,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2022-04-25 10:03:09,970 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2022-04-25 10:03:10,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2022-04-25 10:03:10,354 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2022-04-25 10:03:10,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2022-04-25 10:03:10,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2022-04-25 10:03:10,893 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2022-04-25 10:03:11,170 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2022-04-25 10:03:11,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2022-04-25 10:03:11,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2022-04-25 10:03:11,681 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2022-04-25 10:03:11,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2022-04-25 10:03:12,080 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2022-04-25 10:03:12,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2022-04-25 10:03:12,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2022-04-25 10:03:12,550 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2022-04-25 10:03:12,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2022-04-25 10:03:12,955 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2022-04-25 10:03:13,151 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2022-04-25 10:03:13,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2022-04-25 10:03:13,481 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2022-04-25 10:03:13,675 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2022-04-25 10:03:13,900 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2022-04-25 10:03:14,095 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2022-04-25 10:03:14,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2022-04-25 10:03:14,407 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2022-04-25 10:03:14,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2022-04-25 10:03:14,709 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2022-04-25 10:03:14,878 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2022-04-25 10:03:15,028 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2022-04-25 10:03:15,197 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2022-04-25 10:03:15,356 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2022-04-25 10:03:15,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2022-04-25 10:03:15,649 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2022-04-25 10:03:15,823 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2022-04-25 10:03:16,007 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2022-04-25 10:03:16,176 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2022-04-25 10:03:16,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2022-04-25 10:03:16,359 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2022-04-25 10:03:16,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2022-04-25 10:03:16,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2022-04-25 10:03:16,760 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2022-04-25 10:03:16,918 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2022-04-25 10:03:17,071 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2022-04-25 10:03:17,143 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2022-04-25 10:03:17,256 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2022-04-25 10:03:17,441 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2022-04-25 10:03:17,646 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2022-04-25 10:03:17,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2022-04-25 10:03:18,037 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2022-04-25 10:03:18,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2022-04-25 10:03:18,613 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2022-04-25 10:03:18,878 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2022-04-25 10:03:19,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2022-04-25 10:03:19,286 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2022-04-25 10:03:19,431 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2022-04-25 10:03:19,568 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2022-04-25 10:03:19,680 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2022-04-25 10:03:19,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2022-04-25 10:03:20,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2022-04-25 10:03:20,193 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2022-04-25 10:03:20,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2022-04-25 10:03:20,375 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2022-04-25 10:03:20,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2022-04-25 10:03:20,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2022-04-25 10:03:20,659 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2022-04-25 10:03:20,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2022-04-25 10:03:20,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2022-04-25 10:03:21,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2022-04-25 10:03:21,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2022-04-25 10:03:21,386 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2022-04-25 10:03:21,580 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2022-04-25 10:03:21,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2022-04-25 10:03:21,860 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2022-04-25 10:03:21,986 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2022-04-25 10:03:22,127 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2022-04-25 10:03:22,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2022-04-25 10:03:22,463 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2022-04-25 10:03:22,682 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2022-04-25 10:03:22,836 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2022-04-25 10:03:22,997 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2022-04-25 10:03:23,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2022-04-25 10:03:23,479 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2022-04-25 10:03:23,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2022-04-25 10:03:24,046 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2022-04-25 10:03:24,274 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2022-04-25 10:03:24,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2022-04-25 10:03:24,714 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2022-04-25 10:03:24,947 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2022-04-25 10:03:25,160 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2022-04-25 10:03:25,473 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2022-04-25 10:03:25,775 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2022-04-25 10:03:25,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2022-04-25 10:03:26,318 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2022-04-25 10:03:26,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2022-04-25 10:03:26,820 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2022-04-25 10:03:27,093 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2022-04-25 10:03:27,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2022-04-25 10:03:27,520 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2022-04-25 10:03:27,764 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2022-04-25 10:03:28,004 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2022-04-25 10:03:28,330 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2022-04-25 10:03:28,565 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2022-04-25 10:03:28,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2022-04-25 10:03:28,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2022-04-25 10:03:29,091 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2022-04-25 10:03:29,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2022-04-25 10:03:29,386 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2022-04-25 10:03:29,573 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2022-04-25 10:03:29,753 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2022-04-25 10:03:29,974 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2022-04-25 10:03:30,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2022-04-25 10:03:30,447 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2022-04-25 10:03:30,668 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2022-04-25 10:03:30,839 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2022-04-25 10:03:31,030 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2022-04-25 10:03:31,239 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2022-04-25 10:03:31,498 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2022-04-25 10:03:31,705 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2022-04-25 10:03:31,857 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2022-04-25 10:03:32,049 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2022-04-25 10:03:32,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2022-04-25 10:03:32,481 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2022-04-25 10:03:32,665 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2022-04-25 10:03:32,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2022-04-25 10:03:33,126 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2022-04-25 10:03:33,355 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2022-04-25 10:03:33,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2022-04-25 10:03:33,745 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2022-04-25 10:03:33,967 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2022-04-25 10:03:34,170 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2022-04-25 10:03:34,330 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2022-04-25 10:03:34,573 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2022-04-25 10:03:34,779 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2022-04-25 10:03:34,954 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2022-04-25 10:03:35,119 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2022-04-25 10:03:35,325 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2022-04-25 10:03:35,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2022-04-25 10:03:35,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2022-04-25 10:03:35,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2022-04-25 10:03:36,119 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2022-04-25 10:03:36,278 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2022-04-25 10:03:36,440 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2022-04-25 10:03:36,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2022-04-25 10:03:36,785 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2022-04-25 10:03:36,982 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2022-04-25 10:03:37,168 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2022-04-25 10:03:37,368 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2022-04-25 10:03:37,538 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2022-04-25 10:03:37,747 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2022-04-25 10:03:37,908 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2022-04-25 10:03:38,082 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2022-04-25 10:03:38,437 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2022-04-25 10:03:38,730 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2022-04-25 10:03:38,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2022-04-25 10:03:39,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2022-04-25 10:03:39,221 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2022-04-25 10:03:39,415 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2022-04-25 10:03:39,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2022-04-25 10:03:39,804 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2022-04-25 10:03:40,037 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2022-04-25 10:03:40,230 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2022-04-25 10:03:40,365 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2022-04-25 10:03:40,534 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2022-04-25 10:03:40,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2022-04-25 10:03:40,857 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2022-04-25 10:03:41,044 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2022-04-25 10:03:41,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2022-04-25 10:03:41,319 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2022-04-25 10:03:41,454 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2022-04-25 10:03:41,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2022-04-25 10:03:41,898 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2022-04-25 10:03:42,072 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2022-04-25 10:03:42,206 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2022-04-25 10:03:42,367 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2022-04-25 10:03:42,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2022-04-25 10:03:42,731 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2022-04-25 10:03:42,913 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2022-04-25 10:03:43,152 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2022-04-25 10:03:43,350 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2022-04-25 10:03:43,495 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2022-04-25 10:03:43,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2022-04-25 10:03:43,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2022-04-25 10:03:44,129 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2022-04-25 10:03:44,289 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2022-04-25 10:03:44,447 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2022-04-25 10:03:44,560 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2022-04-25 10:03:44,698 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2022-04-25 10:03:44,841 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2022-04-25 10:03:44,967 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2022-04-25 10:03:45,149 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2022-04-25 10:03:45,352 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2022-04-25 10:03:45,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2022-04-25 10:03:45,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2022-04-25 10:03:45,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2022-04-25 10:03:46,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2022-04-25 10:03:46,425 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2022-04-25 10:03:46,623 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2022-04-25 10:03:46,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2022-04-25 10:03:46,909 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2022-04-25 10:03:47,076 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2022-04-25 10:03:47,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2022-04-25 10:03:47,319 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2022-04-25 10:03:47,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2022-04-25 10:03:47,526 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2022-04-25 10:03:47,665 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2022-04-25 10:03:47,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2022-04-25 10:03:48,000 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2022-04-25 10:03:48,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2022-04-25 10:03:48,199 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2022-04-25 10:03:48,404 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2022-04-25 10:03:48,541 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2022-04-25 10:03:48,685 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2022-04-25 10:03:48,817 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2022-04-25 10:03:48,996 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2022-04-25 10:03:49,152 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2022-04-25 10:03:49,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2022-04-25 10:03:49,490 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2022-04-25 10:03:49,590 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2022-04-25 10:03:49,694 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2022-04-25 10:03:49,815 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2022-04-25 10:03:49,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2022-04-25 10:03:50,083 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2022-04-25 10:03:50,256 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2022-04-25 10:03:50,443 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2022-04-25 10:03:50,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2022-04-25 10:03:50,777 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2022-04-25 10:03:50,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2022-04-25 10:03:51,160 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2022-04-25 10:03:51,356 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2022-04-25 10:03:51,598 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2022-04-25 10:03:51,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2022-04-25 10:03:52,012 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2022-04-25 10:03:52,236 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2022-04-25 10:03:52,396 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2022-04-25 10:03:52,553 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2022-04-25 10:03:52,747 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2022-04-25 10:03:52,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2022-04-25 10:03:53,093 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2022-04-25 10:03:53,250 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2022-04-25 10:03:53,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2022-04-25 10:03:53,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2022-04-25 10:03:53,845 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2022-04-25 10:03:54,075 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2022-04-25 10:03:54,309 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2022-04-25 10:03:54,535 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2022-04-25 10:03:54,781 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2022-04-25 10:03:54,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2022-04-25 10:03:55,151 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2022-04-25 10:03:55,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2022-04-25 10:03:55,585 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2022-04-25 10:03:55,693 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2022-04-25 10:03:55,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2022-04-25 10:03:55,937 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2022-04-25 10:03:56,071 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2022-04-25 10:03:56,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2022-04-25 10:03:56,328 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2022-04-25 10:03:56,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2022-04-25 10:03:56,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2022-04-25 10:03:56,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2022-04-25 10:03:56,827 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2022-04-25 10:03:56,966 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2022-04-25 10:03:57,087 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2022-04-25 10:03:57,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2022-04-25 10:03:57,307 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2022-04-25 10:03:57,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2022-04-25 10:03:57,598 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2022-04-25 10:03:57,748 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2022-04-25 10:03:57,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2022-04-25 10:03:58,044 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2022-04-25 10:03:58,230 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2022-04-25 10:03:58,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2022-04-25 10:03:58,603 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2022-04-25 10:03:58,743 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2022-04-25 10:03:58,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2022-04-25 10:03:59,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2022-04-25 10:03:59,175 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2022-04-25 10:03:59,376 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2022-04-25 10:03:59,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2022-04-25 10:03:59,667 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2022-04-25 10:03:59,786 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2022-04-25 10:03:59,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2022-04-25 10:04:00,067 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2022-04-25 10:04:00,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2022-04-25 10:04:00,330 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2022-04-25 10:04:00,526 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2022-04-25 10:04:00,655 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2022-04-25 10:04:00,796 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2022-04-25 10:04:00,971 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2022-04-25 10:04:01,136 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2022-04-25 10:04:01,281 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2022-04-25 10:04:01,476 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2022-04-25 10:04:01,656 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2022-04-25 10:04:01,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2022-04-25 10:04:01,959 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2022-04-25 10:04:02,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2022-04-25 10:04:02,237 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2022-04-25 10:04:02,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2022-04-25 10:04:02,535 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2022-04-25 10:04:02,665 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2022-04-25 10:04:02,824 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2022-04-25 10:04:02,958 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2022-04-25 10:04:03,072 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2022-04-25 10:04:03,147 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-25 10:04:03,147 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-25 10:04:03,148 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2022-04-25 10:05:39,732 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-04-25 10:05:39,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2022-04-25 10:05:40,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2022-04-25 10:05:40,132 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2022-04-25 10:05:40,263 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2022-04-25 10:05:40,395 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2022-04-25 10:05:40,522 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2022-04-25 10:05:40,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2022-04-25 10:05:40,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2022-04-25 10:05:40,934 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2022-04-25 10:05:41,092 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2022-04-25 10:05:41,236 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2022-04-25 10:05:41,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2022-04-25 10:05:41,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2022-04-25 10:05:41,674 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2022-04-25 10:05:41,777 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2022-04-25 10:05:41,923 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2022-04-25 10:05:42,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2022-04-25 10:05:42,255 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2022-04-25 10:05:42,372 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2022-04-25 10:05:42,501 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2022-04-25 10:05:42,645 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2022-04-25 10:05:42,771 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2022-04-25 10:05:42,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2022-04-25 10:05:43,024 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2022-04-25 10:05:43,150 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2022-04-25 10:05:43,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2022-04-25 10:05:43,383 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2022-04-25 10:05:43,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2022-04-25 10:05:43,653 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2022-04-25 10:05:43,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2022-04-25 10:05:43,894 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2022-04-25 10:05:44,019 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2022-04-25 10:05:44,163 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2022-04-25 10:05:44,285 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2022-04-25 10:05:44,404 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2022-04-25 10:05:44,525 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2022-04-25 10:05:44,657 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2022-04-25 10:05:44,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2022-04-25 10:05:44,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2022-04-25 10:05:45,022 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2022-04-25 10:05:45,133 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2022-04-25 10:05:45,262 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2022-04-25 10:05:45,395 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2022-04-25 10:05:45,504 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2022-04-25 10:05:45,584 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2022-04-25 10:05:45,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2022-04-25 10:05:45,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2022-04-25 10:05:45,887 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2022-04-25 10:05:46,032 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2022-04-25 10:05:46,152 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2022-04-25 10:05:46,247 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2022-04-25 10:05:46,333 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2022-04-25 10:05:46,467 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2022-04-25 10:05:46,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2022-04-25 10:05:46,646 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2022-04-25 10:05:46,729 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2022-04-25 10:05:46,835 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2022-04-25 10:05:46,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2022-04-25 10:05:47,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2022-04-25 10:05:47,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2022-04-25 10:05:47,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2022-04-25 10:05:47,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2022-04-25 10:05:47,454 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2022-04-25 10:05:47,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2022-04-25 10:05:47,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2022-04-25 10:05:47,800 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2022-04-25 10:05:47,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2022-04-25 10:05:47,993 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2022-04-25 10:05:48,099 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2022-04-25 10:05:48,197 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2022-04-25 10:05:48,292 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2022-04-25 10:05:48,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2022-04-25 10:05:48,490 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2022-04-25 10:05:48,623 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2022-04-25 10:05:48,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2022-04-25 10:05:48,884 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2022-04-25 10:05:48,983 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2022-04-25 10:05:49,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2022-04-25 10:05:49,177 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2022-04-25 10:05:49,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2022-04-25 10:05:49,426 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2022-04-25 10:05:49,504 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2022-04-25 10:05:49,597 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2022-04-25 10:05:49,716 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2022-04-25 10:05:49,836 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2022-04-25 10:05:49,942 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2022-04-25 10:05:50,064 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2022-04-25 10:05:50,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2022-04-25 10:05:50,393 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2022-04-25 10:05:50,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2022-04-25 10:05:50,627 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2022-04-25 10:05:50,750 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2022-04-25 10:05:50,863 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2022-04-25 10:05:50,981 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2022-04-25 10:05:51,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2022-04-25 10:05:51,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2022-04-25 10:05:51,370 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2022-04-25 10:05:51,522 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2022-04-25 10:05:51,658 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2022-04-25 10:05:51,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2022-04-25 10:05:51,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2022-04-25 10:05:52,106 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2022-04-25 10:05:52,245 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2022-04-25 10:05:52,332 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2022-04-25 10:05:52,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2022-04-25 10:05:52,422 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2022-04-25 10:05:52,457 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2022-04-25 10:05:52,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2022-04-25 10:05:52,533 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2022-04-25 10:05:52,559 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2022-04-25 10:05:52,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2022-04-25 10:05:52,802 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2022-04-25 10:05:52,907 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2022-04-25 10:05:53,042 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2022-04-25 10:05:53,172 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2022-04-25 10:05:53,287 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2022-04-25 10:05:53,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2022-04-25 10:05:53,579 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2022-04-25 10:05:53,762 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2022-04-25 10:05:53,888 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2022-04-25 10:05:54,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2022-04-25 10:05:54,142 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2022-04-25 10:05:54,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2022-04-25 10:05:54,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2022-04-25 10:05:54,502 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2022-04-25 10:05:54,623 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2022-04-25 10:05:54,747 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2022-04-25 10:05:54,871 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2022-04-25 10:05:55,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2022-04-25 10:05:55,204 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2022-04-25 10:05:55,334 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2022-04-25 10:05:55,462 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2022-04-25 10:05:55,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2022-04-25 10:05:55,726 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2022-04-25 10:05:55,845 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2022-04-25 10:05:55,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2022-04-25 10:05:56,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2022-04-25 10:05:56,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2022-04-25 10:05:56,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2022-04-25 10:05:56,446 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2022-04-25 10:05:56,551 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2022-04-25 10:05:56,693 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2022-04-25 10:05:56,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2022-04-25 10:05:56,976 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2022-04-25 10:05:57,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2022-04-25 10:05:57,258 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2022-04-25 10:05:57,368 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2022-04-25 10:05:57,495 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2022-04-25 10:05:57,635 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2022-04-25 10:05:57,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2022-04-25 10:05:57,893 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2022-04-25 10:05:58,023 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2022-04-25 10:05:58,162 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2022-04-25 10:05:58,281 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2022-04-25 10:05:58,401 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2022-04-25 10:05:58,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2022-04-25 10:05:58,653 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2022-04-25 10:05:58,773 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2022-04-25 10:05:58,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2022-04-25 10:05:59,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2022-04-25 10:05:59,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2022-04-25 10:05:59,395 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2022-04-25 10:05:59,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2022-04-25 10:05:59,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2022-04-25 10:05:59,807 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2022-04-25 10:05:59,922 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2022-04-25 10:06:00,136 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2022-04-25 10:06:00,376 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2022-04-25 10:06:00,601 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2022-04-25 10:06:00,815 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2022-04-25 10:06:00,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2022-04-25 10:06:01,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2022-04-25 10:06:01,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2022-04-25 10:06:01,312 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2022-04-25 10:06:01,450 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2022-04-25 10:06:01,582 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2022-04-25 10:06:01,712 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2022-04-25 10:06:01,844 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2022-04-25 10:06:02,021 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2022-04-25 10:06:02,153 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2022-04-25 10:06:02,304 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2022-04-25 10:06:02,432 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2022-04-25 10:06:02,557 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2022-04-25 10:06:02,703 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2022-04-25 10:06:02,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2022-04-25 10:06:02,952 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2022-04-25 10:06:03,082 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2022-04-25 10:06:03,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2022-04-25 10:06:03,402 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2022-04-25 10:06:03,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2022-04-25 10:06:03,645 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2022-04-25 10:06:03,795 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2022-04-25 10:06:03,931 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2022-04-25 10:06:04,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2022-04-25 10:06:04,212 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2022-04-25 10:06:04,347 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2022-04-25 10:06:04,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2022-04-25 10:06:04,614 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2022-04-25 10:06:04,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2022-04-25 10:06:04,844 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2022-04-25 10:06:04,977 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2022-04-25 10:06:05,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2022-04-25 10:06:05,299 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2022-04-25 10:06:05,425 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2022-04-25 10:06:05,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2022-04-25 10:06:05,716 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2022-04-25 10:06:05,862 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2022-04-25 10:06:05,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2022-04-25 10:06:06,181 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2022-04-25 10:06:06,355 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2022-04-25 10:06:06,485 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2022-04-25 10:06:06,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2022-04-25 10:06:06,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2022-04-25 10:06:06,868 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2022-04-25 10:06:07,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2022-04-25 10:06:07,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2022-04-25 10:06:07,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2022-04-25 10:06:07,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2022-04-25 10:06:07,534 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2022-04-25 10:06:07,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2022-04-25 10:06:07,753 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2022-04-25 10:06:07,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2022-04-25 10:06:07,984 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2022-04-25 10:06:08,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2022-04-25 10:06:08,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2022-04-25 10:06:08,410 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2022-04-25 10:06:08,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2022-04-25 10:06:08,672 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2022-04-25 10:06:08,786 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2022-04-25 10:06:08,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2022-04-25 10:06:09,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2022-04-25 10:06:09,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2022-04-25 10:06:09,393 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2022-04-25 10:06:09,538 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2022-04-25 10:06:09,700 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2022-04-25 10:06:09,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2022-04-25 10:06:09,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2022-04-25 10:06:10,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2022-04-25 10:06:10,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2022-04-25 10:06:10,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2022-04-25 10:06:10,419 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2022-04-25 10:06:10,552 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2022-04-25 10:06:10,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2022-04-25 10:06:10,794 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2022-04-25 10:06:10,883 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2022-04-25 10:06:11,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2022-04-25 10:06:11,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2022-04-25 10:06:11,304 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2022-04-25 10:06:11,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2022-04-25 10:06:11,563 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2022-04-25 10:06:11,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2022-04-25 10:06:11,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2022-04-25 10:06:11,982 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2022-04-25 10:06:12,149 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2022-04-25 10:06:12,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2022-04-25 10:06:12,422 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2022-04-25 10:06:12,566 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2022-04-25 10:06:12,687 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2022-04-25 10:06:12,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2022-04-25 10:06:12,959 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2022-04-25 10:06:13,106 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2022-04-25 10:06:13,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2022-04-25 10:06:13,343 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2022-04-25 10:06:13,462 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2022-04-25 10:06:13,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2022-04-25 10:06:13,726 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2022-04-25 10:06:13,885 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2022-04-25 10:06:14,064 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2022-04-25 10:06:14,204 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2022-04-25 10:06:14,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2022-04-25 10:06:14,466 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2022-04-25 10:06:14,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2022-04-25 10:06:14,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2022-04-25 10:06:14,834 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2022-04-25 10:06:14,955 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2022-04-25 10:06:15,093 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2022-04-25 10:06:15,233 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2022-04-25 10:06:15,373 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2022-04-25 10:06:15,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2022-04-25 10:06:15,639 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2022-04-25 10:06:15,773 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2022-04-25 10:06:15,911 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2022-04-25 10:06:16,033 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2022-04-25 10:06:16,172 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2022-04-25 10:06:16,312 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2022-04-25 10:06:16,479 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2022-04-25 10:06:16,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2022-04-25 10:06:16,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2022-04-25 10:06:16,858 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2022-04-25 10:06:16,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2022-04-25 10:06:17,132 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2022-04-25 10:06:17,264 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2022-04-25 10:06:17,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2022-04-25 10:06:17,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2022-04-25 10:06:17,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2022-04-25 10:06:17,823 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2022-04-25 10:06:17,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2022-04-25 10:06:18,099 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2022-04-25 10:06:18,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2022-04-25 10:06:18,334 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2022-04-25 10:06:18,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2022-04-25 10:06:18,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2022-04-25 10:06:18,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2022-04-25 10:06:18,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2022-04-25 10:06:19,025 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2022-04-25 10:06:19,177 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2022-04-25 10:06:19,337 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2022-04-25 10:06:19,456 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2022-04-25 10:06:19,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2022-04-25 10:06:19,762 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2022-04-25 10:06:19,892 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2022-04-25 10:06:20,066 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2022-04-25 10:06:20,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2022-04-25 10:06:20,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2022-04-25 10:06:20,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2022-04-25 10:06:20,575 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2022-04-25 10:06:20,748 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2022-04-25 10:06:20,923 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2022-04-25 10:06:21,066 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2022-04-25 10:06:21,221 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2022-04-25 10:06:21,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2022-04-25 10:06:21,553 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2022-04-25 10:06:21,706 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2022-04-25 10:06:21,834 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2022-04-25 10:06:21,962 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2022-04-25 10:06:22,129 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2022-04-25 10:06:22,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2022-04-25 10:06:22,414 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2022-04-25 10:06:22,511 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2022-04-25 10:06:22,560 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2022-04-25 10:06:22,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2022-04-25 10:06:22,685 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2022-04-25 10:06:22,757 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2022-04-25 10:06:22,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2022-04-25 10:06:22,910 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2022-04-25 10:06:23,072 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2022-04-25 10:06:23,213 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2022-04-25 10:06:23,343 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2022-04-25 10:06:23,503 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2022-04-25 10:06:23,632 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2022-04-25 10:06:23,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2022-04-25 10:06:23,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2022-04-25 10:06:24,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2022-04-25 10:06:24,221 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2022-04-25 10:06:24,385 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2022-04-25 10:06:24,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2022-04-25 10:06:24,654 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2022-04-25 10:06:24,805 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2022-04-25 10:06:24,966 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2022-04-25 10:06:25,083 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2022-04-25 10:06:25,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2022-04-25 10:06:25,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2022-04-25 10:06:25,494 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2022-04-25 10:06:25,640 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2022-04-25 10:06:25,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2022-04-25 10:06:25,901 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2022-04-25 10:06:26,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2022-04-25 10:06:26,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2022-04-25 10:06:26,400 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2022-04-25 10:06:26,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2022-04-25 10:06:26,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2022-04-25 10:06:26,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2022-04-25 10:06:27,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2022-04-25 10:06:27,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2022-04-25 10:06:27,309 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2022-04-25 10:06:27,475 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2022-04-25 10:06:27,646 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2022-04-25 10:06:27,844 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2022-04-25 10:06:28,100 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2022-04-25 10:06:28,222 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2022-04-25 10:06:28,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2022-04-25 10:06:28,494 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2022-04-25 10:06:28,655 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2022-04-25 10:06:28,803 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2022-04-25 10:06:28,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2022-04-25 10:06:29,065 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2022-04-25 10:06:29,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2022-04-25 10:06:29,376 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2022-04-25 10:06:29,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2022-04-25 10:06:29,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2022-04-25 10:06:29,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2022-04-25 10:06:30,031 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2022-04-25 10:06:30,201 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2022-04-25 10:06:30,360 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2022-04-25 10:06:30,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2022-04-25 10:06:30,672 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2022-04-25 10:06:30,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2022-04-25 10:06:30,933 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2022-04-25 10:06:31,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2022-04-25 10:06:31,255 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2022-04-25 10:06:31,410 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2022-04-25 10:06:31,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2022-04-25 10:06:31,738 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2022-04-25 10:06:31,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2022-04-25 10:06:32,107 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2022-04-25 10:06:32,322 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2022-04-25 10:06:32,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2022-04-25 10:06:32,661 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2022-04-25 10:06:32,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2022-04-25 10:06:32,986 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2022-04-25 10:06:33,146 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2022-04-25 10:06:33,303 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2022-04-25 10:06:33,449 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2022-04-25 10:06:33,597 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2022-04-25 10:06:33,736 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2022-04-25 10:06:33,911 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2022-04-25 10:06:34,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2022-04-25 10:06:34,246 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2022-04-25 10:06:34,383 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2022-04-25 10:06:34,605 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2022-04-25 10:06:34,742 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2022-04-25 10:06:34,887 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2022-04-25 10:06:34,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2022-04-25 10:06:35,124 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2022-04-25 10:06:35,267 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2022-04-25 10:06:35,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2022-04-25 10:06:35,579 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2022-04-25 10:06:35,716 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2022-04-25 10:06:35,833 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2022-04-25 10:06:35,972 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2022-04-25 10:06:36,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2022-04-25 10:06:36,287 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2022-04-25 10:06:36,447 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2022-04-25 10:06:36,555 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2022-04-25 10:06:36,675 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2022-04-25 10:06:36,802 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2022-04-25 10:06:36,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2022-04-25 10:06:37,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2022-04-25 10:06:37,135 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2022-04-25 10:06:37,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2022-04-25 10:06:37,382 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2022-04-25 10:06:37,515 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2022-04-25 10:06:37,666 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2022-04-25 10:06:37,780 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2022-04-25 10:06:37,922 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2022-04-25 10:06:38,066 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2022-04-25 10:06:38,181 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2022-04-25 10:06:38,348 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2022-04-25 10:06:38,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2022-04-25 10:06:38,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2022-04-25 10:06:38,758 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2022-04-25 10:06:38,954 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2022-04-25 10:06:39,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2022-04-25 10:06:39,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2022-04-25 10:06:39,387 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2022-04-25 10:06:39,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2022-04-25 10:06:39,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2022-04-25 10:06:39,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2022-04-25 10:06:39,957 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2022-04-25 10:06:40,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2022-04-25 10:06:40,226 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2022-04-25 10:06:40,356 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2022-04-25 10:06:40,481 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2022-04-25 10:06:40,619 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2022-04-25 10:06:40,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2022-04-25 10:06:40,893 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2022-04-25 10:06:41,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2022-04-25 10:06:41,134 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2022-04-25 10:06:41,233 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2022-04-25 10:06:41,378 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2022-04-25 10:06:41,515 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2022-04-25 10:06:41,658 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2022-04-25 10:06:41,803 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2022-04-25 10:06:41,919 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2022-04-25 10:06:42,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2022-04-25 10:06:42,192 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2022-04-25 10:06:42,323 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2022-04-25 10:06:42,458 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2022-04-25 10:06:42,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2022-04-25 10:06:42,727 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2022-04-25 10:06:42,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2022-04-25 10:06:42,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2022-04-25 10:06:43,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2022-04-25 10:06:43,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2022-04-25 10:06:43,397 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2022-04-25 10:06:43,542 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2022-04-25 10:06:43,699 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2022-04-25 10:06:43,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2022-04-25 10:06:43,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2022-04-25 10:06:44,105 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2022-04-25 10:06:44,265 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2022-04-25 10:06:44,403 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2022-04-25 10:06:44,537 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2022-04-25 10:06:44,669 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2022-04-25 10:06:44,809 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2022-04-25 10:06:44,932 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2022-04-25 10:06:45,073 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2022-04-25 10:06:45,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2022-04-25 10:06:45,403 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2022-04-25 10:06:45,566 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2022-04-25 10:06:45,729 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2022-04-25 10:06:45,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2022-04-25 10:06:46,031 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2022-04-25 10:06:46,167 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2022-04-25 10:06:46,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2022-04-25 10:06:46,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2022-04-25 10:06:46,583 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2022-04-25 10:06:46,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2022-04-25 10:06:46,854 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2022-04-25 10:06:46,972 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2022-04-25 10:06:47,092 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2022-04-25 10:06:47,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2022-04-25 10:06:47,353 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2022-04-25 10:06:47,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2022-04-25 10:06:47,637 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2022-04-25 10:06:47,732 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2022-04-25 10:06:47,878 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2022-04-25 10:06:47,998 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2022-04-25 10:06:48,132 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2022-04-25 10:06:48,263 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2022-04-25 10:06:48,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2022-04-25 10:06:48,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2022-04-25 10:06:48,644 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2022-04-25 10:06:48,721 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2022-04-25 10:06:48,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2022-04-25 10:06:48,993 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2022-04-25 10:06:49,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2022-04-25 10:06:49,308 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2022-04-25 10:06:49,438 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2022-04-25 10:06:49,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2022-04-25 10:06:49,689 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2022-04-25 10:06:49,843 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2022-04-25 10:06:49,980 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2022-04-25 10:06:50,044 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-25 10:06:50,044 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-25 10:06:50,044 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2022-04-25 10:07:39,130 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-04-25 10:07:39,132 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-04-25 10:07:39,132 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-04-25 10:08:06,891 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-04-25 10:08:06,893 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-04-25 10:08:06,893 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
