2022-04-23 00:34:14,837 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2022-04-23 00:34:16,956 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-23 00:34:16,970 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-23 00:34:16,970 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-23 00:34:17,400 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, *************]
2022-04-23 00:34:17,400 INFO  weaver.general.InitServer  - init ioc container...
2022-04-23 00:34:17,804 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 00:34:19,038 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 00:34:22,227 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-23 00:34:22,272 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-23 00:34:22,338 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent8495319470177973618.jar
2022-04-23 00:34:22,338 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent8495319470177973618.jar
2022-04-23 00:34:22,351 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-23 00:34:48,567 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:34:48,567 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:35:18,027 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:35:18,577 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:35:47,482 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:35:48,595 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:36:58,227 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2022-04-23 00:37:00,355 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-23 00:37:00,355 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-23 00:37:00,355 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-23 00:37:00,773 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, *************]
2022-04-23 00:37:00,773 INFO  weaver.general.InitServer  - init ioc container...
2022-04-23 00:37:01,141 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 00:37:01,550 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 00:37:05,560 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-23 00:37:05,586 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-23 00:37:05,628 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3034916921591369807.jar
2022-04-23 00:37:05,628 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent3034916921591369807.jar
2022-04-23 00:37:05,637 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-23 00:37:31,027 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:37:31,037 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:38:00,482 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:38:01,070 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:38:29,956 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:38:31,110 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:38:59,401 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:39:01,120 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:39:28,818 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:39:31,128 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:39:58,228 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:40:01,132 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:40:27,694 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at ln.LnTimer.<init>(LnTimer.java:15)
	at ln.LN.ReadFromFile(LN.java:195)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:40:31,176 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:40:57,179 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at ln.LnTimer.<init>(LnTimer.java:15)
	at ln.LN.ReadFromFile(LN.java:195)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:41:01,194 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:41:26,622 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:41:31,216 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:41:41,617 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LnTimer.doThreadWork(LnTimer.java:20)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-23 00:41:56,052 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:42:01,216 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:42:11,047 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LnTimer.doThreadWork(LnTimer.java:20)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-23 00:42:25,481 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.putRecordToObj(StaticObj.java:160)
	at ln.LN.ReadFromFile(LN.java:211)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:42:31,202 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:42:40,465 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.reloadLicenseInfo(LN.java:372)
	at ln.LnTimer.doThreadWork(LnTimer.java:21)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-23 00:42:54,918 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.putRecordToObj(StaticObj.java:160)
	at ln.LN.ReadFromFile(LN.java:211)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:42:54,927 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-23 00:43:01,232 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 00:43:09,914 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.reloadLicenseInfo(LN.java:372)
	at ln.LnTimer.doThreadWork(LnTimer.java:21)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-23 00:43:24,366 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1435)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:795)
	at weaver.conn.RecordSet.execute(RecordSet.java:1643)
	at ln.LN.reloadLicenseInfo(LN.java:396)
	at ln.LN.ReadFromFile(LN.java:233)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 00:43:31,265 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 10:37:55,427 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-23 10:37:55,441 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-23 10:37:55,441 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-23 10:37:55,831 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, *************]
2022-04-23 10:37:55,841 INFO  weaver.general.InitServer  - init ioc container...
2022-04-23 10:37:56,241 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 10:37:56,742 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-23 10:38:00,632 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-23 10:38:00,662 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-23 10:38:00,712 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4369536610740692206.jar
2022-04-23 10:38:00,712 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4369536610740692206.jar
2022-04-23 10:38:00,722 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-23 10:38:26,224 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 10:38:26,224 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 10:38:55,678 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 10:38:56,258 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-23 10:39:25,131 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-23 10:39:26,302 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
