2025-06-12 10:42:33,726 INFO  [Thread:http-app1-7001-8$1744733816] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/security/isEnableRandomCode  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:33,885 INFO  [Thread:http-app1-7001-6$1645960530] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/system/componentmanage/queryComponentByComtype  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:33,919 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/security/isEnableRandomCode  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:34,068 INFO  [Thread:http-app1-7001-10$1961691031] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/ec/dev/app/checkJoin  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:34,245 INFO  [Thread:http-app1-7001-11$146083964] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/security/isEnableRandomCode  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:40,882 INFO  [Thread:http-app1-7001-1$1714971780] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/ec/dev/locale/getLabelByModule  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:40,922 INFO  [Thread:http-app1-7001-11$146083964] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/integration/common1/checkCasIsDeployed  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:40,926 INFO  [Thread:http-app1-7001-8$1744733816] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/ec/dev/locale/getLabelByModule  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:40,930 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/login/UpgradeMessage.jsp  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:41,698 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/ec/dev/locale/getLabelByModule  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:41,716 INFO  [Thread:http-app1-7001-11$146083964] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/integration/common1/checkCasIsDeployed  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:41,730 INFO  [Thread:http-app1-7001-11$146083964] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/portal/login/logininfo  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:41,948 INFO  [Thread:http-app1-7001-6$1645960530] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/system/language/base/getActiveLanguage  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:51,467 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/hrm/login/getLoginForm  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:51,610 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/ec/dev/locale/getLabelByModule  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
2025-06-12 10:42:52,226 INFO  [Thread:http-app1-7001-5$1471110855] weaver.filter.MultiLangFilter.doFilter() - multiLang::url==/api/hrm/login/getLoginForm  是否禁止了多语言单行文本功能：false 是否禁止了当前页面的多语言单行文本：true;
