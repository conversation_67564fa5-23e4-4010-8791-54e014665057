2022-04-11 10:02:47,888 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-11 10:02:47,898 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-11 10:02:47,899 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-11 10:02:48,614 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-11 10:02:48,614 INFO  weaver.general.InitServer  - init ioc container...
2022-04-11 10:02:49,224 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:02:49,902 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:02:53,100 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-11 10:02:53,129 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-11 10:02:53,172 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent2890861907019591454.jar
2022-04-11 10:02:53,172 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent2890861907019591454.jar
2022-04-11 10:02:53,182 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-11 10:03:19,361 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:03:19,366 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:03:48,772 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at weaver.system.License.<init>(License.java:60)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:03:49,367 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:04:18,196 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:04:19,388 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:04:47,620 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.system.License.OutLicensecode(License.java:177)
	at weaver.system.License.<init>(License.java:61)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:34)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:04:49,375 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:05:17,036 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:05:19,429 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:05:46,442 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.getCid(LN.java:433)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:05:49,432 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:06:15,863 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at ln.LnTimer.<init>(LnTimer.java:15)
	at ln.LN.ReadFromFile(LN.java:195)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:06:19,436 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:06:45,272 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LN.<init>(LN.java:70)
	at ln.LnTimer.<init>(LnTimer.java:15)
	at ln.LN.ReadFromFile(LN.java:195)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:06:49,438 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:07:14,686 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:07:19,440 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:07:29,682 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LnTimer.doThreadWork(LnTimer.java:20)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-11 10:07:44,099 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:07:49,446 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:07:59,094 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at ln.LN.MakeLicensecode(LN.java:293)
	at ln.LN.OutLicensecode(LN.java:286)
	at ln.LnTimer.doThreadWork(LnTimer.java:20)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-11 10:08:13,822 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.putRecordToObj(StaticObj.java:160)
	at ln.LN.ReadFromFile(LN.java:211)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:08:20,042 ERROR org.logicalcobwebs.proxool.ecology  - Prototype
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.Prototyper.sweep(Prototyper.java:102)
	at org.logicalcobwebs.proxool.PrototyperThread.run(PrototyperThread.java:44)
2022-04-11 10:08:30,943 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: ͨ���˿� 8003 ���ӵ����� ************** �� TCP/IP ����ʧ�ܡ�����:��connect timed out������֤�������ԣ������ SQL Server ��ʵ���������������У����ڴ˶˿ڽ��� TCP/IP ���ӣ���Ҫȷ������ǽû����ֹ���˶˿ڵ� TCP ���ӡ�����
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:170)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1049)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.getRecordFromObj(StaticObj.java:142)
	at ln.LN.ReadFromFile(LN.java:204)
	at ln.LN.reloadLicenseInfo(LN.java:372)
	at ln.LnTimer.doThreadWork(LnTimer.java:21)
	at ln.ThreadWorkTimer.run(ThreadWorkTimer.java:36)
2022-04-11 10:08:44,556 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: �޷��򿪵�¼����������ݿ� "ecology"����¼ʧ�ܡ�
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:196)
	at com.microsoft.sqlserver.jdbc.TDSTokenHandler.onEOF(tdsparser.java:246)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:83)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.sendLogon(SQLServerConnection.java:2532)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.logon(SQLServerConnection.java:1929)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.access$000(SQLServerConnection.java:41)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection$LogonCommand.doExecute(SQLServerConnection.java:1917)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:1061)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:833)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:716)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:841)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.logicalcobwebs.proxool.DefaultConnectionBuilder.buildConnection(DefaultConnectionBuilder.java:39)
	at org.logicalcobwebs.proxool.Prototyper.buildConnection(Prototyper.java:159)
	at org.logicalcobwebs.proxool.ConnectionPool.getConnection(ConnectionPool.java:211)
	at org.logicalcobwebs.proxool.ProxoolDataSource.getConnection(ProxoolDataSource.java:97)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:306)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.CacheManager.initCacheKeys(CacheManager.java:28)
	at weaver.interfaces.cache.CacheManager.isCacheKey(CacheManager.java:72)
	at weaver.general.StaticObj.getObject(StaticObj.java:95)
	at weaver.general.StaticObj.putRecordToObj(StaticObj.java:160)
	at ln.LN.ReadFromFile(LN.java:211)
	at ln.LN.getCid(LN.java:435)
	at weaver.system.License.getCId(License.java:320)
	at com.weaver.upgrade.FunctionUpgrade.doUpgrade(FunctionUpgrade.java:35)
	at weaver.general.InitServer.init(InitServer.java:216)
	at com.caucho.server.dispatch.ServletConfigImpl.createServletImpl(ServletConfigImpl.java:830)
	at com.caucho.server.dispatch.ServletConfigImpl.createServlet(ServletConfigImpl.java:732)
	at com.caucho.server.dispatch.ServletManager.init(ServletManager.java:159)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1874)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
2022-04-11 10:08:44,571 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-11 10:09:04,376 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-11 10:09:04,381 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-11 10:09:04,382 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-11 10:09:04,940 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-11 10:09:04,940 INFO  weaver.general.InitServer  - init ioc container...
2022-04-11 10:09:05,715 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:09:06,453 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:09:06,782 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-11 10:09:07,050 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-11 10:09:07,051 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-11 10:09:09,574 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-11 10:09:09,606 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-04-11 10:09:09,641 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4407759858126747229.jar
2022-04-11 10:09:09,641 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4407759858126747229.jar
2022-04-11 10:09:09,649 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-11 10:09:10,558 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-11 10:09:10,558 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-11 10:09:10,563 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-11 10:09:10,563 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-11 10:09:10,876 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-11 10:09:11,648 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-11 10:09:11,655 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-11 10:09:11,655 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-11 10:09:11,658 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-11 10:09:11,660 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-11 10:09:11,661 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-11 10:09:13,664 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-11 10:09:13,664 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-11 10:09:13,674 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-11 10:09:13,674 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-11 10:09:13,675 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-11 10:09:13,677 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-11 10:09:13,677 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-11 10:09:13,680 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-11 10:09:13,681 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-11 10:09:13,683 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-11 10:09:14,516 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-11 10:09:15,151 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-11 10:09:15,164 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-11 10:09:15,176 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-11 10:09:15,332 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-11 10:09:15,436 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-11 10:09:15,440 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-11 10:09:15,445 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-11 10:09:15,446 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-11 10:09:15,449 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-11 10:09:15,449 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-11 10:09:15,453 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-11 10:09:15,554 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-11 10:09:15,561 INFO  weaver.general.InitServer  - end.....
2022-04-11 10:09:15,643 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-11 10:09:15,670 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-11 10:09:15,864 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-11 10:09:15,865 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-11 10:09:15,865 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-11 10:09:15,878 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-11 10:09:15,878 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-11 10:09:15,909 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-11 10:09:16,031 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-11 10:09:16,032 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-11 10:09:16,032 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-11 10:09:16,033 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-11 10:09:16,033 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-11 10:09:16,141 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-11 10:09:16,446 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-11 10:09:16,450 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-11 10:09:16,453 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-11 10:09:16,456 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-11 10:09:16,456 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=08d00138-10b2-4a74-86c7-6598302ec580,��ʼ�ʼ��ڲ��ռ�������
2022-04-11 10:09:16,457 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=08d00138-10b2-4a74-86c7-6598302ec580,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-11 10:09:17,016 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-11 10:09:17,022 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-11 10:09:17,026 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-11 10:09:17,028 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-11 10:09:17,057 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=08d00138-10b2-4a74-86c7-6598302ec580,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-11 10:09:17,057 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-11 10:09:17,138 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-11 10:09:17,275 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-11 10:09:17,285 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-11 10:09:18,166 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-11 10:09:18,169 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-11 10:09:18,267 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-11 10:09:18,319 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-11 10:09:18,340 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-11 10:09:18,472 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-11 10:09:18,919 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-11 10:09:18,920 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-11 10:09:18,963 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-11 10:09:18,969 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-11 10:09:19,390 ERROR com.engine.kq.biz.KQReportBiz  - 2022-04-11ִ�п��ڱ������ݸ�ʽ���ɹ���
2022-04-11 10:09:19,515 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-13' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-13') or  lastLoginDate<'2021-10-13')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-11 10:09:19,516 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-11 10:09:19,516 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-11 10:09:19,706 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-11 10:09:19,964 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-11 10:09:21,805 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,200 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,280 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,348 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,406 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,612 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,697 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,782 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:22,851 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:23,654 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2022-04-11 10:09:32,528 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-04-11 10:09:32,534 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-04-11 10:09:32,534 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-04-11 10:09:34,303 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-11 10:09:34,304 INFO  weaver.general.InitServer  - init ioc container...
2022-04-11 10:09:35,737 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:09:38,020 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-04-11 10:09:38,361 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-04-11 10:09:38,400 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-04-11 10:09:38,868 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-04-11 10:09:39,092 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-04-11 10:09:39,094 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-04-11 10:09:42,333 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-04-11 10:09:42,333 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-04-11 10:09:42,334 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-04-11 10:09:42,335 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-04-11 10:09:42,484 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-04-11 10:09:43,586 INFO  weaver.general.InitServer  - end ioc container init...
2022-04-11 10:09:43,595 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-04-11 10:09:43,596 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-04-11 10:09:43,598 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-04-11 10:09:43,601 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-04-11 10:09:43,601 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-04-11 10:09:45,789 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-04-11 10:09:45,790 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-04-11 10:09:45,801 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-04-11 10:09:45,802 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-04-11 10:09:45,802 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-04-11 10:09:45,806 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-04-11 10:09:45,806 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-04-11 10:09:45,810 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-04-11 10:09:45,810 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-04-11 10:09:45,815 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-04-11 10:09:46,175 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-04-11 10:09:46,546 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-04-11 10:09:46,570 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-04-11 10:09:46,585 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-04-11 10:09:46,586 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2022-04-11 10:09:46,753 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-04-11 10:09:46,829 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-04-11 10:09:46,835 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-04-11 10:09:46,837 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-04-11 10:09:46,837 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-04-11 10:09:46,839 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-04-11 10:09:46,839 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-04-11 10:09:46,840 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-04-11 10:09:46,873 INFO  weaver.general.InitServer  - end.....
2022-04-11 10:09:46,937 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-04-11 10:09:46,967 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-04-11 10:09:47,014 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-04-11 10:09:47,048 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-04-11 10:09:47,063 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-04-11 10:09:47,113 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-04-11 10:09:47,660 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-04-11 10:09:47,757 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-04-11 10:09:47,759 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-04-11 10:09:47,807 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-04-11 10:09:47,840 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-04-11 10:09:47,841 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-11 10:09:47,845 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-04-11 10:09:47,848 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-04-11 10:09:47,848 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=2be88409-0314-4f34-9f85-677857835d84,��ʼ�ʼ��ڲ��ռ�������
2022-04-11 10:09:47,849 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=2be88409-0314-4f34-9f85-677857835d84,-> ########## ִ�м�ʱ��ʼ ##########
2022-04-11 10:09:47,873 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-04-11 10:09:47,881 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-04-11 10:09:47,884 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-11 10:09:47,922 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-04-11 10:09:47,941 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-04-11 10:09:47,957 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=2be88409-0314-4f34-9f85-677857835d84,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-04-11 10:09:47,958 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-04-11 10:09:48,017 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-04-11 10:09:48,018 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-04-11 10:09:48,018 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-04-11 10:09:48,111 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-04-11 10:09:48,111 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-04-11 10:09:48,138 ERROR weaver.general.BaseBean  - ������ʱ����
2022-04-11 10:09:48,432 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-04-11 10:09:48,437 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-04-11 10:09:48,437 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-04-11 10:09:48,438 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-04-11 10:09:48,438 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-04-11 10:09:48,495 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-04-11 10:09:49,345 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-04-11 10:09:49,354 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-04-11 10:09:49,448 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-04-11 10:09:49,452 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-04-11 10:09:49,884 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-04-11 10:09:50,127 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-10-13' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-10-13') or  lastLoginDate<'2021-10-13')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-04-11 10:09:50,127 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-04-11 10:09:50,128 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-04-11 10:09:50,344 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-04-11 10:09:50,962 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-04-11 10:09:56,514 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2022-04-11 10:10:19,418 ERROR weaver.general.BaseBean  - qrcode_config>>>
2022-04-11 10:10:21,020 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/pubAction/getAttendanceCardCustom,��ǰsessionId��abcJnSKUp47qHcVeUyway
2022-04-11 10:10:31,369 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,468 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,523 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,525 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,526 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,526 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,526 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,548 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,558 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,802 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,802 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:31,999 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-11 10:10:32,142 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,378 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,390 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,648 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,707 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,723 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,733 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,737 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,744 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,833 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,863 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,891 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,910 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-11 10:10:32,914 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,964 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:32,971 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-11 10:10:33,002 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,016 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,051 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,077 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,115 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,331 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,343 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,407 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,419 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:33,712 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268,-8,268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6,-9,6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-8,%'  or  '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%'  or  '%,'+jobtitlesharevalue+',%' like '%,-9,%'  or  '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6,-9,6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6,-9,6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6,-9,6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-04-11 10:10:34,237 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:34,414 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:34,594 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:35,959 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:35,959 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:35,972 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:36,285 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:36,540 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:37,024 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:37,035 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:37,050 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:37,092 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,108 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,591 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:37,636 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,653 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,900 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:37,981 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:38,033 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:38,687 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:38,936 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:10:43,756 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:44,780 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:45,980 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/pubAction/getAttendanceCardCustom,��ǰsessionId��abcJnSKUp47qHcVeUyway
2022-04-11 10:10:46,526 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:10:47,578 ERROR weaver.general.BaseBean  - ΢�ѻ�ȡRMI�쳣:connect timed out
2022-04-11 10:11:06,847 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-04-11 10:11:06,857 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - �����쳣
java.time.format.DateTimeParseException: Text '' could not be parsed at index 0
	at java.time.format.DateTimeFormatter.parseResolved0(DateTimeFormatter.java:1949)
	at java.time.format.DateTimeFormatter.parse(DateTimeFormatter.java:1851)
	at java.time.LocalDate.parse(LocalDate.java:400)
	at java.time.LocalDate.parse(LocalDate.java:385)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:50)
	at com.api.pubaction.cmd.GetAttendanceCardCustomCmd.execute(GetAttendanceCardCustomCmd.java:26)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.api.pubaction.service.impl.GetAttendanceCardCustomServerImpl.GetAttendanceCar(GetAttendanceCardCustomServerImpl.java:14)
	at com.api.pubaction.web.Action.getAttendanceCard(Action.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor422.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor347.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
