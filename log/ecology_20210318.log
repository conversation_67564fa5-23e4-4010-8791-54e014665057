2021-03-18 16:48:37,912 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-18 16:48:40,041 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 16:48:40,046 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 16:48:40,047 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 16:48:40,791 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:48:40,791 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 16:48:41,478 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:48:42,267 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:48:42,607 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 16:48:42,706 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 16:48:42,708 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 16:48:45,297 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 16:48:45,332 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-18 16:48:45,377 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5485671036312985749.jar
2021-03-18 16:48:45,378 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5485671036312985749.jar
2021-03-18 16:48:45,389 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 16:48:49,141 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 16:48:49,142 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 16:48:49,150 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 16:48:49,151 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 16:48:49,462 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 16:48:51,594 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 16:48:51,602 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 16:48:51,602 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 16:48:51,605 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 16:48:51,608 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 16:48:51,609 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 16:48:53,689 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 16:48:53,689 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 16:48:53,700 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 16:48:53,700 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 16:48:53,700 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 16:48:53,703 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 16:48:53,703 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 16:48:53,705 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 16:48:53,705 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 16:48:53,707 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 16:48:54,137 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 16:48:54,512 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 16:48:54,529 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 16:48:54,548 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 16:48:54,819 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 16:48:54,899 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 16:48:54,903 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 16:48:54,906 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 16:48:54,906 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 16:48:54,910 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 16:48:54,910 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 16:48:54,913 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 16:48:54,958 INFO  weaver.general.InitServer  - end.....
2021-03-18 16:48:54,980 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 16:48:55,016 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 16:48:55,074 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 16:48:55,115 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 16:48:55,158 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 16:48:55,430 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 16:48:55,465 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 16:48:55,526 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 16:48:55,528 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:48:55,529 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 16:48:55,529 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 16:48:55,657 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 16:48:55,657 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 16:48:55,684 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 16:48:55,702 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 16:48:55,705 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 16:48:55,841 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 16:48:55,881 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 16:48:55,881 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 16:48:55,881 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 16:48:55,881 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 16:48:55,881 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 16:48:55,882 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 16:48:55,895 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 16:48:55,906 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:48:55,910 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 16:48:55,913 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:48:55,915 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 16:48:55,920 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e9d57ca0-f297-4ec2-9703-aeeb51eb0f02,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 16:48:55,921 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e9d57ca0-f297-4ec2-9703-aeeb51eb0f02,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 16:48:55,965 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 16:48:55,965 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 16:48:55,971 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:48:56,072 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e9d57ca0-f297-4ec2-9703-aeeb51eb0f02,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 16:48:56,072 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:48:56,526 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 16:48:56,527 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 16:48:56,567 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 16:48:56,571 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 16:48:57,718 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 16:48:57,934 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 16:48:58,141 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 16:48:58,141 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 16:48:58,142 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 16:48:58,398 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 16:49:06,902 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 16:49:06,906 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 16:49:06,906 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 16:49:07,634 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:49:07,635 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 16:49:09,129 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:49:09,922 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:49:10,308 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 16:49:10,421 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 16:49:10,422 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 16:49:12,125 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 16:49:12,143 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 16:49:15,603 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 16:49:15,603 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 16:49:15,605 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 16:49:15,605 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 16:49:15,724 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 16:49:17,778 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 16:49:17,785 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 16:49:17,785 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 16:49:17,788 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 16:49:17,790 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 16:49:17,790 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 16:49:19,792 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 16:49:19,793 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 16:49:19,803 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 16:49:19,803 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 16:49:19,803 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 16:49:19,806 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 16:49:19,806 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 16:49:19,809 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 16:49:19,813 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 16:49:19,815 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 16:49:20,461 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 16:49:20,765 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 16:49:20,779 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 16:49:20,792 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 16:49:20,792 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-18 16:49:21,029 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 16:49:21,117 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 16:49:21,120 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 16:49:21,122 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 16:49:21,122 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 16:49:21,125 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 16:49:21,125 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 16:49:21,127 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 16:49:21,163 INFO  weaver.general.InitServer  - end.....
2021-03-18 16:49:21,176 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 16:49:21,254 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 16:49:21,392 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 16:49:21,443 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 16:49:21,462 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 16:49:21,543 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 16:49:21,693 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 16:49:21,722 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:49:21,723 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 16:49:21,723 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 16:49:21,725 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 16:49:21,816 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 16:49:21,816 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 16:49:21,843 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 16:49:21,878 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 16:49:21,879 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 16:49:21,954 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 16:49:21,973 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 16:49:21,973 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 16:49:21,973 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 16:49:21,973 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 16:49:21,973 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 16:49:21,992 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 16:49:22,084 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 16:49:22,123 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:49:22,127 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 16:49:22,129 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:49:22,133 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=5114809c-5120-471a-a11d-c7a262a683f8,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 16:49:22,133 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=5114809c-5120-471a-a11d-c7a262a683f8,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 16:49:22,133 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 16:49:22,183 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 16:49:22,183 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 16:49:22,187 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:49:22,422 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=5114809c-5120-471a-a11d-c7a262a683f8,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 16:49:22,422 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:49:22,723 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 16:49:22,724 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 16:49:22,747 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 16:49:22,749 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 16:49:23,338 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 16:49:24,148 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 16:49:24,424 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 16:49:24,424 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 16:49:24,424 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 16:49:24,469 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 16:49:27,439 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-18 16:49:43,339 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:49:48,032 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-18 16:49:51,279 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:49:53,469 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,634 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,722 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,722 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,724 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,724 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,724 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,739 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,745 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,878 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,940 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:53,940 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:54,194 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 16:49:54,200 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 16:49:54,659 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:54,685 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:54,962 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,039 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,130 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,153 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,184 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,195 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,281 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,365 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,407 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,420 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,497 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,502 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,503 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,524 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 16:49:55,560 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,564 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,569 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,705 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,760 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,827 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:55,911 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:56,116 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:56,223 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:56,242 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:56,244 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:56,327 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:57,638 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:57,639 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:57,638 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:57,679 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:49:58,273 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:49:58,528 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor408.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor347.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:49:58,560 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:50:01,276 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:50:09,168 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:50:13,902 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:50:39,293 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:50:55,460 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:51:07,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:51:12,402 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:51:16,731 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:51:19,815 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:51:46,489 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcNN6vbujJcPeB543XFx
2021-03-18 16:51:57,966 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:52:09,659 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:52:44,992 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 16:52:45,001 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 16:52:45,001 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 16:52:47,024 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:52:47,025 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 16:52:48,372 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:52:49,155 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:52:49,651 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 16:52:49,752 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 16:52:49,756 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 16:52:50,375 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 16:52:50,414 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-18 16:52:50,466 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent6530052562738167190.jar
2021-03-18 16:52:50,466 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent6530052562738167190.jar
2021-03-18 16:52:50,475 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 16:52:55,873 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 16:52:55,874 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 16:52:55,962 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 16:52:55,963 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 16:52:56,112 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 16:52:58,127 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 16:52:58,134 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 16:52:58,134 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 16:52:58,137 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 16:52:58,140 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 16:52:58,141 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 16:53:00,765 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 16:53:00,765 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 16:53:00,778 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 16:53:00,779 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 16:53:00,780 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 16:53:00,783 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 16:53:00,783 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 16:53:00,786 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 16:53:00,787 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 16:53:00,790 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 16:53:01,398 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 16:53:01,771 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 16:53:01,819 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 16:53:01,832 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 16:53:03,193 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 16:53:03,421 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 16:53:03,428 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 16:53:03,431 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 16:53:03,431 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 16:53:03,436 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 16:53:03,436 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 16:53:03,444 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 16:53:03,542 INFO  weaver.general.InitServer  - end.....
2021-03-18 16:53:03,704 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 16:53:03,752 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 16:53:03,850 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 16:53:03,921 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 16:53:03,964 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 16:53:04,109 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 16:53:04,470 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:53:04,471 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 16:53:04,471 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 16:53:04,712 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 16:53:04,714 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 16:53:04,714 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 16:53:04,770 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 16:53:04,899 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 16:53:05,066 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 16:53:05,068 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 16:53:05,120 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 16:53:05,120 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 16:53:05,120 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 16:53:05,122 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 16:53:05,122 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 16:53:05,134 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 16:53:05,157 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 16:53:05,429 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 16:53:05,483 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:53:05,555 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:53:05,523 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 16:53:05,580 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 16:53:05,630 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=627a0f86-c9c1-43a9-839e-734f13eadb95,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 16:53:05,631 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=627a0f86-c9c1-43a9-839e-734f13eadb95,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 16:53:05,780 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 16:53:06,120 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 16:53:06,124 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:53:06,279 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=627a0f86-c9c1-43a9-839e-734f13eadb95,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 16:53:06,279 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:53:06,548 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 16:53:06,851 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 16:53:06,852 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 16:53:06,852 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 16:53:07,060 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 16:53:07,080 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 16:53:07,082 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 16:53:07,138 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 16:53:07,142 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 16:53:08,094 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 16:53:21,227 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 16:53:21,232 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 16:53:21,232 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 16:53:22,221 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:53:22,221 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 16:53:23,239 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:53:24,342 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:53:24,798 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 16:53:25,023 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 16:53:25,025 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 16:53:26,900 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 16:53:26,956 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 16:53:33,402 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 16:53:33,402 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 16:53:33,406 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 16:53:33,407 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 16:53:33,570 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 16:53:36,675 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 16:53:36,689 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 16:53:36,689 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 16:53:36,692 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 16:53:36,694 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 16:53:36,695 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 16:53:38,699 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 16:53:38,699 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 16:53:38,716 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 16:53:38,716 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 16:53:38,716 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 16:53:38,720 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 16:53:38,720 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 16:53:38,723 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 16:53:38,723 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 16:53:38,727 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 16:53:39,419 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 16:53:39,550 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 16:53:39,566 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 16:53:39,579 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 16:53:39,580 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-18 16:53:39,770 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 16:53:39,876 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 16:53:39,883 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 16:53:39,885 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 16:53:39,885 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 16:53:39,892 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 16:53:39,892 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 16:53:39,898 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 16:53:39,952 INFO  weaver.general.InitServer  - end.....
2021-03-18 16:53:39,972 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 16:53:40,007 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 16:53:40,149 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 16:53:40,286 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 16:53:40,373 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 16:53:40,532 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 16:53:40,552 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 16:53:40,650 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 16:53:40,651 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 16:53:40,869 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:53:40,869 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 16:53:40,869 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 16:53:40,905 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 16:53:40,905 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 16:53:40,906 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 16:53:40,929 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 16:53:40,942 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 16:53:41,037 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 16:53:41,038 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 16:53:41,038 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 16:53:41,038 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 16:53:41,038 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 16:53:41,053 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 16:53:41,891 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:53:41,899 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 16:53:41,901 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 16:53:41,909 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 16:53:41,911 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=261ff1b1-6c0b-4643-8c73-58957d3d8e4b,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 16:53:41,911 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=261ff1b1-6c0b-4643-8c73-58957d3d8e4b,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 16:53:41,971 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 16:53:41,990 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 16:53:42,043 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 16:53:42,045 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:53:42,135 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 16:53:42,135 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 16:53:42,165 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 16:53:42,167 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 16:53:42,649 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=261ff1b1-6c0b-4643-8c73-58957d3d8e4b,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 16:53:42,649 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 16:53:42,908 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,911 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,913 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,916 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,918 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,922 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:42,925 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 16:53:42,927 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 16:53:43,583 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 16:53:43,608 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 16:53:43,609 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 16:53:43,609 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 16:53:43,846 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 16:53:45,370 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:53:45,939 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-18 16:54:02,462 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:54:02,465 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:54:02,475 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:54:02,476 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:54:02,472 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:54:02,484 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:56:14,702 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/listOfItems,��ǰsessionId��abcEma6c8KbDC8924IeHx
2021-03-18 16:56:17,377 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:56:18,149 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:56:18,294 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:56:18,302 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 16:56:18,555 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-18 16:59:45,453 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-18 16:59:47,690 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 16:59:47,695 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 16:59:47,695 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 16:59:48,583 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 16:59:48,583 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 16:59:49,407 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:59:50,249 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 16:59:50,690 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 16:59:50,882 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 16:59:50,884 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 16:59:53,151 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 16:59:53,191 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-18 16:59:53,234 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5042011916291302802.jar
2021-03-18 16:59:53,234 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5042011916291302802.jar
2021-03-18 16:59:53,245 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 16:59:56,493 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 16:59:56,493 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 16:59:56,497 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 16:59:56,497 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 16:59:56,714 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 16:59:58,704 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 16:59:58,720 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 16:59:58,720 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 16:59:58,723 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 16:59:58,726 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 16:59:58,727 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 17:00:00,737 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 17:00:00,737 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 17:00:00,747 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 17:00:00,747 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 17:00:00,747 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 17:00:00,751 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 17:00:00,752 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 17:00:00,754 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 17:00:00,754 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 17:00:00,757 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 17:00:01,445 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 17:00:01,707 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 17:00:01,721 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 17:00:01,737 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 17:00:01,953 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 17:00:02,060 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 17:00:02,067 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 17:00:02,071 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 17:00:02,071 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 17:00:02,075 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 17:00:02,075 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 17:00:02,078 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 17:00:02,172 INFO  weaver.general.InitServer  - end.....
2021-03-18 17:00:02,208 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 17:00:02,275 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 17:00:02,354 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 17:00:02,430 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 17:00:02,458 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 17:00:02,541 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 17:00:02,541 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 17:00:02,541 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 17:00:02,624 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 17:00:02,681 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 17:00:02,681 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 17:00:02,722 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 17:00:02,730 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 17:00:02,789 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 17:00:02,894 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 17:00:02,896 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 17:00:02,912 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 17:00:02,951 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 17:00:02,951 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 17:00:02,951 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 17:00:02,951 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 17:00:02,951 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 17:00:02,964 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 17:00:03,071 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 17:00:03,075 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 17:00:03,078 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 17:00:03,084 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 17:00:03,084 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=78cc91ee-cdb8-4942-9383-4640204e318a,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 17:00:03,084 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=78cc91ee-cdb8-4942-9383-4640204e318a,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 17:00:03,114 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 17:00:03,137 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 17:00:03,137 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 17:00:03,142 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 17:00:03,307 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=78cc91ee-cdb8-4942-9383-4640204e318a,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 17:00:03,307 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 17:00:03,914 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 17:00:03,915 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 17:00:03,969 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 17:00:03,971 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 17:00:04,794 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 17:00:05,169 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 17:00:05,435 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 17:00:05,436 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 17:00:05,436 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 17:00:05,718 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 17:00:12,891 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-18 17:00:15,060 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-18 17:00:15,062 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-18 17:00:15,062 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-18 17:00:15,964 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 17:00:15,965 INFO  weaver.general.InitServer  - init ioc container...
2021-03-18 17:00:16,830 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 17:00:17,768 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-18 17:00:18,270 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-18 17:00:18,380 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-18 17:00:18,382 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-18 17:00:21,443 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-18 17:00:21,470 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-18 17:00:24,580 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-18 17:00:24,581 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-18 17:00:24,582 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-18 17:00:24,583 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-18 17:00:24,782 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-18 17:00:27,127 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-18 17:00:27,139 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-18 17:00:27,139 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-18 17:00:27,142 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-18 17:00:27,146 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-18 17:00:27,147 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-18 17:00:29,197 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-18 17:00:29,197 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-18 17:00:29,214 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-18 17:00:29,214 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-18 17:00:29,214 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-18 17:00:29,221 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-18 17:00:29,221 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-18 17:00:29,224 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-18 17:00:29,225 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-18 17:00:29,229 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-18 17:00:29,829 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-18 17:00:29,951 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-18 17:00:29,976 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-18 17:00:29,991 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-18 17:00:29,991 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-18 17:00:30,181 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-18 17:00:30,252 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-18 17:00:30,256 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-18 17:00:30,258 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-18 17:00:30,258 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-18 17:00:30,261 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-18 17:00:30,263 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-18 17:00:30,266 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-18 17:00:30,303 INFO  weaver.general.InitServer  - end.....
2021-03-18 17:00:30,338 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-18 17:00:30,444 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-18 17:00:30,528 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-18 17:00:30,600 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-18 17:00:30,635 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-18 17:00:30,849 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-18 17:00:31,084 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-18 17:00:31,237 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-18 17:00:31,244 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-18 17:00:31,255 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-18 17:00:31,256 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-18 17:00:31,256 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-18 17:00:31,288 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-18 17:00:31,288 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-18 17:00:31,311 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-18 17:00:31,467 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-18 17:00:31,467 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-18 17:00:31,467 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-18 17:00:31,467 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-18 17:00:31,467 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-18 17:00:31,481 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-18 17:00:31,505 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-18 17:00:31,550 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-18 17:00:32,261 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 17:00:32,264 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-18 17:00:32,269 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c4661f11-0145-4853-a072-36cfe1807578,��ʼ�ʼ��ڲ��ռ�������
2021-03-18 17:00:32,269 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c4661f11-0145-4853-a072-36cfe1807578,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-18 17:00:32,279 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-18 17:00:32,282 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-18 17:00:32,295 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-18 17:00:32,295 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-18 17:00:32,305 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-18 17:00:32,309 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-18 17:00:32,314 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 17:00:32,345 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-18 17:00:32,347 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-18 17:00:32,394 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-18 17:00:32,413 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=c4661f11-0145-4853-a072-36cfe1807578,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-18 17:00:32,413 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-18 17:00:33,291 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-18 17:00:33,345 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-18 17:00:33,577 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-19' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-19') or  lastLoginDate<'2020-09-19')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-18 17:00:33,577 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-18 17:00:33,577 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-18 17:00:33,726 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-18 17:00:37,847 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:37,954 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:37,999 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:38,001 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:38,001 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-18 17:00:38,650 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:54,402 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:54,575 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:55,320 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:55,414 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:55,424 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:55,447 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:00:55,573 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-18 17:00:59,242 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:01,470 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,684 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,733 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,793 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,793 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,793 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,793 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,798 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:02,813 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,027 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,027 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,417 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 17:01:03,417 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 17:01:03,686 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,714 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,755 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:03,922 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,019 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,022 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,083 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,129 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,147 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,206 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,230 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,270 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,271 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,285 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,449 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,452 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,453 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,552 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,564 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,566 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-18 17:01:04,574 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,624 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,660 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,731 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,822 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,910 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,951 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,969 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:04,971 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,043 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,046 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,068 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,070 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,095 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,665 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:01:05,950 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor414.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor358.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-18 17:01:38,087 ERROR com.cloudstore.dev.api.service.SessionFilter  - ��ʱ����:/api/preSet/ping,��ǰsessionId��abcEma6c8KbDC8924IeHx
2021-03-18 17:02:11,844 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:02:22,281 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:05,962 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:05,975 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:05,983 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:32,397 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:39,101 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:04:48,837 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:05:01,820 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:05:22,923 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:05:42,225 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:06:06,758 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:06:56,055 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:06:56,196 ERROR com.api.presettle.util.Prop  - not found file <D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\WEB-INF/prop/presettle.properties>
2021-03-18 17:07:04,950 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:07:04,950 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:07:05,561 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:10:04,943 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:10:04,949 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:10:05,523 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:10:57,880 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:11:05,561 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-18 17:12:43,732 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
