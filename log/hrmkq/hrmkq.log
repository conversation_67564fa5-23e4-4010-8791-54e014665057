2025-06-12 10:41:21,106 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:629:flowendtimeIndex:1020:flowMins:391
2025-06-12 10:41:21,117 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,118 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,119 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,120 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,122 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,123 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,124 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,124 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,125 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,127 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,128 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,130 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,132 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,148 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,149 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,150 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,151 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,153 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,153 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:21,154 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:629:flowMins:88
2025-06-12 10:41:22,103 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:41:22,104 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,104 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,106 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,106 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,107 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,107 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,108 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,109 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,110 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,110 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,111 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,111 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,112 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,113 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,113 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,114 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,115 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,115 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,116 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,116 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,117 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:22,118 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:41:24,704 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:689:flowendtimeIndex:1020:flowMins:331
2025-06-12 10:41:24,706 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,707 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,707 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,708 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,709 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,710 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,710 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,711 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,715 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,717 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,718 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,719 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,720 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,721 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:41:24,721 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,827 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:915:flowendtimeIndex:1020:flowMins:105
2025-06-12 10:42:31,828 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,828 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,829 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,829 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,830 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,831 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,831 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,832 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,833 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,835 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,835 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,836 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,837 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,837 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,839 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,839 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,841 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,841 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,842 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,843 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,844 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:31,844 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:915:flowMins:374
2025-06-12 10:42:32,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:42:32,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,569 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,572 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,574 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,576 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,580 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:32,583 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:42:35,249 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:854:flowendtimeIndex:1020:flowMins:166
2025-06-12 10:42:35,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,251 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,251 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,251 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,252 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,252 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,252 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,253 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,253 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,254 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,254 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,254 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,255 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,255 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,255 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,256 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:35,256 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,409 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:854:flowendtimeIndex:1020:flowMins:166
2025-06-12 10:42:36,410 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,411 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,411 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,411 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,412 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:36,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,439 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:550:flowendtimeIndex:1020:flowMins:470
2025-06-12 10:42:38,439 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,440 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,441 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,441 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,441 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,442 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,443 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,446 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,446 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,447 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,447 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:38,447 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:550:flowMins:9
2025-06-12 10:42:39,500 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:550:flowendtimeIndex:1020:flowMins:470
2025-06-12 10:42:39,501 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,501 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,502 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,503 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,503 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,504 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,505 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,506 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,506 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,508 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,509 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,509 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,510 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,510 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,511 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:39,511 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:550:flowMins:9
2025-06-12 10:42:41,701 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:884:flowendtimeIndex:1020:flowMins:136
2025-06-12 10:42:41,702 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,703 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,704 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,704 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,705 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,706 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,706 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,707 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,708 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,709 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,709 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,710 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,715 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,715 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:41,717 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:884:flowMins:343
2025-06-12 10:42:43,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:966:flowendtimeIndex:1020:flowMins:54
2025-06-12 10:42:43,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,712 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,713 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,714 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,715 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,715 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,716 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,717 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,717 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,717 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,718 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,719 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,719 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,719 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,720 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:43,720 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:966:flowMins:425
2025-06-12 10:42:44,410 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:966:flowendtimeIndex:1020:flowMins:54
2025-06-12 10:42:44,410 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,411 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,412 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,423 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,423 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:44,426 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:966:flowMins:425
2025-06-12 10:42:46,764 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:812:flowendtimeIndex:1020:flowMins:208
2025-06-12 10:42:46,764 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,764 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,765 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,765 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,766 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,767 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,767 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:46,768 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:540:flowMins:0
2025-06-12 10:42:46,768 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::11::flowbegintimeIndex:541:flowendtimeIndex:540:flowMins:0
2025-06-12 10:42:48,854 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:670:flowendtimeIndex:1020:flowMins:350
2025-06-12 10:42:48,855 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,855 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,856 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,856 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,857 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,857 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,857 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,858 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,858 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,859 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,859 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,859 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,859 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,860 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,860 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,861 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,861 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,861 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,862 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,863 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,863 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:48,864 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:670:flowMins:129
2025-06-12 10:42:49,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:42:49,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,625 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,625 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,626 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,626 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,627 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,627 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,627 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,628 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:49,628 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:42:51,918 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:879:flowendtimeIndex:1020:flowMins:141
2025-06-12 10:42:51,919 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,919 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,920 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,920 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,921 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,921 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,921 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,921 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,922 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,922 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,922 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,923 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,923 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,923 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:51,926 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:879:flowMins:338
2025-06-12 10:42:52,618 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:879:flowendtimeIndex:1020:flowMins:141
2025-06-12 10:42:52,619 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,619 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,625 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,625 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,626 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,626 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,626 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,627 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,627 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,628 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,628 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,628 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,629 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,629 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:52,629 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:879:flowMins:338
2025-06-12 10:42:54,750 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:561:flowendtimeIndex:1020:flowMins:459
2025-06-12 10:42:54,750 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,750 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,750 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,751 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,751 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,752 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,752 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,752 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,753 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,753 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,753 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,754 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,754 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,755 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,755 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,757 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,757 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,757 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:54,758 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:561:flowMins:20
2025-06-12 10:42:55,466 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:42:55,467 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,467 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,468 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,468 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,468 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,468 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,468 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,469 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,469 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,469 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,470 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,470 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,470 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,471 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,471 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,471 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,472 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,472 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,473 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,473 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,473 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:55,473 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:42:57,682 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:665:flowendtimeIndex:1020:flowMins:355
2025-06-12 10:42:57,683 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,683 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,684 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,684 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,685 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,685 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,685 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,686 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,686 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,687 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,687 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,687 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,688 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,688 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,688 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,689 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,689 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,689 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,690 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,690 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:57,690 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:665:flowMins:124
2025-06-12 10:42:58,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:42:58,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,426 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,426 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,428 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,428 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,429 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,429 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,429 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,430 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,430 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,431 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,431 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,432 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,432 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,433 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,433 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,433 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:42:58,434 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:00,697 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:639:flowendtimeIndex:1020:flowMins:381
2025-06-12 10:43:00,698 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,698 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,699 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,699 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,699 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,700 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,700 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,701 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,701 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,701 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,702 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,702 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,703 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,703 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,703 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,704 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,704 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,705 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,705 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,705 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:00,706 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:02,996 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:42:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:02,997 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,285 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:709:flowendtimeIndex:1020:flowMins:311
2025-06-12 10:43:04,285 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,286 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,286 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,286 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,287 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,287 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,289 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,289 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:04,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:709:flowMins:168
2025-06-12 10:43:06,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:988:flowendtimeIndex:1020:flowMins:32
2025-06-12 10:43:06,574 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,574 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,576 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,583 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:06,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,361 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:07,361 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,362 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,362 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,363 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,364 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,364 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,365 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,366 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,366 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,367 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,367 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,368 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,370 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,373 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:07,373 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:09,478 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:629:flowendtimeIndex:1020:flowMins:391
2025-06-12 10:43:09,479 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,479 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,480 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,480 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:09,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:629:flowMins:88
2025-06-12 10:43:10,196 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:10,197 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,197 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,198 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,198 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,199 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,199 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,199 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,200 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,200 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,201 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,201 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,201 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,202 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,202 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,202 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,203 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,203 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:10,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:12,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:801:flowendtimeIndex:1020:flowMins:219
2025-06-12 10:43:12,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,569 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,569 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,572 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,572 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:12,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:801:flowMins:260
2025-06-12 10:43:13,326 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:13,326 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,327 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,327 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,328 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,329 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,329 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,329 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,330 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,331 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,331 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,331 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,332 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,333 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,334 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,334 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,335 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,335 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,336 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,337 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,337 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,338 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:13,338 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:15,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:617:flowendtimeIndex:1020:flowMins:403
2025-06-12 10:43:15,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,568 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,569 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,569 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,570 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:15,571 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:17,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:17,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,775 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,775 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,776 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,776 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,776 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,777 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,777 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,777 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,777 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,778 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,778 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,778 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,779 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,779 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:17,779 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:18,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,611 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,611 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,611 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,612 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,612 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,612 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,613 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,613 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,613 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,614 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,615 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,615 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,615 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:18,615 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:20,755 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:823:flowendtimeIndex:1020:flowMins:197
2025-06-12 10:43:20,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,756 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,757 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,757 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,758 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,758 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,758 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,759 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,759 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,760 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,760 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,760 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,761 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,761 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,762 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,762 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,762 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,763 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:20,763 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,497 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:21,498 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,499 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,500 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,500 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,501 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,501 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,502 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,503 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,503 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,504 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,504 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,504 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,505 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,505 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,505 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,506 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,506 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,506 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:21,507 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:23,524 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1005:flowendtimeIndex:1020:flowMins:15
2025-06-12 10:43:23,524 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,525 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,525 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,525 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,526 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,526 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,528 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,528 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,529 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,529 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,529 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,530 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,530 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,531 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,531 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,531 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,532 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,532 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:23,533 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:1005:flowMins:464
2025-06-12 10:43:24,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1005:flowendtimeIndex:1020:flowMins:15
2025-06-12 10:43:24,298 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,298 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,298 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,303 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,304 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,304 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,305 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,305 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,305 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,306 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,306 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:24,307 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:1005:flowMins:464
2025-06-12 10:43:26,351 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1215:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:26,351 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1215:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:26,351 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,352 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,352 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,352 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,353 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,353 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,353 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,353 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,353 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,354 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:26,354 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:570:flowMins:29
2025-06-12 10:43:28,287 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:644:flowendtimeIndex:1020:flowMins:376
2025-06-12 10:43:28,287 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,288 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,289 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,289 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:644:flowMins:103
2025-06-12 10:43:28,985 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:28,985 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,988 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,988 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,989 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,989 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,989 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,990 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,990 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,991 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,991 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,991 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,992 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,992 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:28,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:31,004 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:813:flowendtimeIndex:1020:flowMins:207
2025-06-12 10:43:31,005 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,005 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:813:flowMins:272
2025-06-12 10:43:31,992 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:623:flowendtimeIndex:1020:flowMins:397
2025-06-12 10:43:31,992 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,995 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,995 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,995 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,996 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,996 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,996 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,996 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,997 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,997 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,997 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,997 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,998 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,998 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,998 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:31,998 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:623:flowMins:82
2025-06-12 10:43:32,655 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:32,655 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,655 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,656 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,656 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,657 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,657 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,657 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,658 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,659 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,659 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,660 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,660 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,661 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,661 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,661 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,662 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,662 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,665 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:32,665 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:34,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:947:flowendtimeIndex:1020:flowMins:73
2025-06-12 10:43:34,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:34,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:540:flowMins:0
2025-06-12 10:43:34,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::31::flowbegintimeIndex:541:flowendtimeIndex:540:flowMins:0
2025-06-12 10:43:36,630 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:685:flowendtimeIndex:1020:flowMins:335
2025-06-12 10:43:36,632 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,632 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,633 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,634 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,634 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,635 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,636 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,636 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,636 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,637 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,638 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,638 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,638 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,639 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,639 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,640 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,640 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,641 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,641 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:36,641 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,381 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:37,381 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,382 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,383 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,383 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,383 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,384 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,384 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,388 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,388 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,390 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,390 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,390 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:37,391 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:39,441 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:635:flowendtimeIndex:1020:flowMins:385
2025-06-12 10:43:39,441 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,442 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,442 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,442 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,442 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,443 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,443 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,443 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,444 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,445 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,446 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:39,446 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,154 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:40,155 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,155 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,156 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,156 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,156 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,157 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,157 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,158 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,158 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,158 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,159 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,159 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,160 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,160 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,160 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,161 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,161 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,162 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,162 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,163 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,163 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:40,163 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:42,537 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1072:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:42,538 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1072:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:43:42,538 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:42,538 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1072:flowMins:479
2025-06-12 10:43:43,572 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:641:flowendtimeIndex:1020:flowMins:379
2025-06-12 10:43:43,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,573 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,574 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,574 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,575 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,576 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,576 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,577 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,580 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,580 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:43,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:641:flowMins:100
2025-06-12 10:43:44,266 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:44,266 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,266 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,267 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,267 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,271 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,271 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,272 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,272 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:44,274 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:46,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:937:flowendtimeIndex:1020:flowMins:83
2025-06-12 10:43:46,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,413 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:46,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:937:flowMins:396
2025-06-12 10:43:48,524 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:976:flowendtimeIndex:1020:flowMins:44
2025-06-12 10:43:48,525 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,526 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,526 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,527 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,528 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,528 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,528 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,529 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,529 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,530 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,530 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,530 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,531 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,531 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,532 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,532 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,532 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,533 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:48,533 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,727 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:984:flowendtimeIndex:1020:flowMins:36
2025-06-12 10:43:50,727 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,727 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,728 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,728 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,729 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,729 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,729 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,730 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,730 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,731 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,731 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,731 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,732 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,732 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,733 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,733 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,734 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,734 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,735 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,735 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,736 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:50,736 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:984:flowMins:443
2025-06-12 10:43:51,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:984:flowendtimeIndex:1020:flowMins:36
2025-06-12 10:43:51,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:51,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:984:flowMins:443
2025-06-12 10:43:53,543 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:933:flowendtimeIndex:1020:flowMins:87
2025-06-12 10:43:53,543 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,543 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,544 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,544 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,544 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,545 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,545 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,545 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,546 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,546 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,546 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,546 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,547 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,547 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,547 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,548 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,548 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,548 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:53,550 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:933:flowMins:392
2025-06-12 10:43:54,511 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:54,511 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,511 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,512 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,512 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,512 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,512 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,515 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,515 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,515 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:54,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:56,769 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:915:flowendtimeIndex:1020:flowMins:105
2025-06-12 10:43:56,769 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,769 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,770 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,770 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,770 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,770 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,771 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,771 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,771 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,771 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,772 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,773 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:56,774 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:915:flowMins:374
2025-06-12 10:43:57,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:43:57,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:57,567 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:43:59,666 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:679:flowendtimeIndex:1020:flowMins:341
2025-06-12 10:43:59,666 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,669 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,669 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,669 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,669 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,670 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,670 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,670 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,670 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,671 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,671 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,671 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,671 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:43:59,672 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:679:flowMins:138
2025-06-12 10:44:00,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:00,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:00,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:44:02,781 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:560:flowendtimeIndex:1020:flowMins:460
2025-06-12 10:44:02,782 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,782 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,782 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,782 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,783 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,783 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,783 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,784 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,785 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,786 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::32::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::33::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,787 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::34::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::35::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::36::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::39::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,788 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::40::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::41::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::42::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::43::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,789 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::46::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::47::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::48::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::49::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,790 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::50::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::53::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::54::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::55::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::56::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,791 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::57::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,792 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::60::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,792 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::61::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,793 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::62::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,793 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::63::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,793 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::64::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,793 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::67::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,794 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::68::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,794 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::69::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,794 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::70::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,795 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::71::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,795 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::74::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,796 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::75::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,796 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::76::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,796 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::77::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,796 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::78::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,797 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::81::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,797 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::82::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,797 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::83::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,798 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::84::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,798 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::85::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,798 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::88::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,799 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::89::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,799 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::90::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:02,799 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::91::flowbegintimeIndex:541:flowendtimeIndex:560:flowMins:19
2025-06-12 10:44:03,619 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:881:flowendtimeIndex:1020:flowMins:139
2025-06-12 10:44:03,619 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,620 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,621 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,622 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,623 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:03,624 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:881:flowMins:340
2025-06-12 10:44:06,384 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:709:flowendtimeIndex:1020:flowMins:311
2025-06-12 10:44:06,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:06,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:06,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:600:flowMins:59
2025-06-12 10:44:08,289 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:904:flowendtimeIndex:1020:flowMins:116
2025-06-12 10:44:08,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,290 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,291 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:08,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:904:flowMins:363
2025-06-12 10:44:10,358 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1050:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:10,358 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1050:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:10,358 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:10,359 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1050:flowMins:479
2025-06-12 10:44:12,578 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1015:flowendtimeIndex:1020:flowMins:5
2025-06-12 10:44:12,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,579 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,580 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,580 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,581 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,582 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,584 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,584 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,584 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,585 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,585 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,586 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,586 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,586 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,587 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,587 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:12,588 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:1015:flowMins:474
2025-06-12 10:44:14,985 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:908:flowendtimeIndex:1020:flowMins:112
2025-06-12 10:44:14,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:14,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:908:flowMins:367
2025-06-12 10:44:16,512 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:905:flowendtimeIndex:1020:flowMins:115
2025-06-12 10:44:16,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,513 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,514 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,515 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,515 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,516 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,517 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,517 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,518 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,518 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,519 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,519 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,519 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,520 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:16,520 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:905:flowMins:364
2025-06-12 10:44:18,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:929:flowendtimeIndex:1020:flowMins:91
2025-06-12 10:44:18,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,271 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,271 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,271 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,272 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,272 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,272 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,273 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,274 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,274 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,275 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,275 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,275 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,276 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:18,276 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:929:flowMins:388
2025-06-12 10:44:19,015 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:929:flowendtimeIndex:1020:flowMins:91
2025-06-12 10:44:19,016 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,016 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,016 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,017 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,017 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,017 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,017 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,018 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,018 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,019 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,019 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,019 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,020 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,020 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,020 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,021 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,021 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,021 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,022 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,022 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,022 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:19,023 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:929:flowMins:388
2025-06-12 10:44:21,244 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:910:flowendtimeIndex:1020:flowMins:110
2025-06-12 10:44:21,244 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,245 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,245 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,245 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,246 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,246 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,246 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,246 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,247 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,247 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,247 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,247 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,247 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,248 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,248 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,248 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,249 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,249 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,250 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:21,251 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:910:flowMins:369
2025-06-12 10:44:22,061 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:22,062 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,063 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,063 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,064 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,064 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,065 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,065 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,066 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,066 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,066 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,066 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,067 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,067 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,067 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,067 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,068 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,068 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,068 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,068 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,069 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,069 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:22,069 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:44:24,558 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:24,559 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,559 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,559 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,560 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,560 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,560 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,561 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,562 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,563 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,564 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,565 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:24,566 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,292 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:25,293 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,294 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,295 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,296 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,297 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,298 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:25,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,475 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:588:flowendtimeIndex:1020:flowMins:432
2025-06-12 10:44:27,475 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,476 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,476 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,476 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,477 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,477 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,478 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,478 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,478 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,479 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,480 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,480 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,480 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:27,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,448 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:28,448 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,449 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,450 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,450 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,451 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,451 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,452 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,452 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,453 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,453 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,454 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,454 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,455 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,455 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,455 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:28,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:44:31,034 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1054:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:31,034 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1054:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:31,035 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,035 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,036 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,036 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,036 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,037 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,037 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,038 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,038 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:31,038 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:600:flowMins:59
2025-06-12 10:44:33,406 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:589:flowendtimeIndex:1020:flowMins:431
2025-06-12 10:44:33,406 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:33,407 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:589:flowMins:48
2025-06-12 10:44:34,448 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:973:flowendtimeIndex:1020:flowMins:47
2025-06-12 10:44:34,448 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,449 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,449 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,450 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,450 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,450 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,451 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,451 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,452 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,452 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,452 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,453 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,453 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,453 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,454 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,454 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,454 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,455 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,455 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:34,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:973:flowMins:432
2025-06-12 10:44:35,202 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:973:flowendtimeIndex:1020:flowMins:47
2025-06-12 10:44:35,203 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,203 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,204 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,205 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,207 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,207 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,207 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:35,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:973:flowMins:432
2025-06-12 10:44:37,595 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:37,595 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:37,596 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,596 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,597 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,597 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,597 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,598 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,598 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,598 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,598 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,598 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,599 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,599 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,600 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,600 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,600 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,601 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,601 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,602 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,602 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:37,602 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,367 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:38,367 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1059:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:38,367 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,368 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,368 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,368 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,368 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,369 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,370 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,370 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,370 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,370 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,371 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:38,372 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:671:flowendtimeIndex:1020:flowMins:349
2025-06-12 10:44:40,481 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,482 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,483 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,484 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,485 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,486 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,487 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,488 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,488 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,489 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,489 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:40,489 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:671:flowMins:130
2025-06-12 10:44:41,262 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:671:flowendtimeIndex:1020:flowMins:349
2025-06-12 10:44:41,262 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,263 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,263 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,263 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,264 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,264 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,265 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,265 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,265 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,266 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,266 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,267 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,267 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,267 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,268 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,269 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:41,270 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:671:flowMins:130
2025-06-12 10:44:43,606 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1005:flowendtimeIndex:1020:flowMins:15
2025-06-12 10:44:43,606 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,606 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,607 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,607 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,607 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,607 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,608 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,609 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,610 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:43,611 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,661 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:700:flowendtimeIndex:1020:flowMins:320
2025-06-12 10:44:45,661 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,662 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,662 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,662 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,663 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,664 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,664 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,665 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,665 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,666 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,666 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,667 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,668 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,669 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:45,670 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:700:flowMins:159
2025-06-12 10:44:46,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:700:flowendtimeIndex:1020:flowMins:320
2025-06-12 10:44:46,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,462 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,462 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,462 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,463 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,463 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,463 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,464 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,464 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,465 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:46,465 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:700:flowMins:159
2025-06-12 10:44:48,547 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:643:flowendtimeIndex:1020:flowMins:377
2025-06-12 10:44:48,548 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,549 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,550 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,550 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,551 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,551 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,551 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,552 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,553 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,553 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,553 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,554 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,554 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,555 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,555 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,555 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,556 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:48,556 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,414 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:551:flowendtimeIndex:1020:flowMins:469
2025-06-12 10:44:50,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,415 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,416 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,417 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,418 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,419 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,276 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:551:flowendtimeIndex:1020:flowMins:469
2025-06-12 10:44:51,276 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,277 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,278 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,278 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,278 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,279 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,279 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,280 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,280 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,280 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,280 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,281 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,281 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,281 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,281 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,282 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,282 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,282 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,282 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,283 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:51,283 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,456 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1027:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:53,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1189 : 不记录中间表 i::1::flowbegintimeIndex:1027:flowendtimeIndex:1020:flowMins:0
2025-06-12 10:44:53,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,457 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,458 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,459 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,460 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,461 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,462 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:53,462 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,299 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:44:54,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,300 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,301 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,302 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,303 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,303 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,303 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,304 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,305 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,307 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,307 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,307 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,308 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,309 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,309 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,309 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,310 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,311 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,311 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,311 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,312 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:54,312 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:44:57,040 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,040 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,041 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,041 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,041 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,042 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,042 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,043 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,043 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,044 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,044 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,045 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,045 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,045 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,046 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,046 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,050 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,051 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,051 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,051 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,052 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,052 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:957:flowMins:416
2025-06-12 10:44:57,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,924 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,925 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,926 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,926 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,926 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,926 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,927 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,927 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,927 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,927 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,927 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,928 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,928 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,928 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,928 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,928 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,929 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:44:57,929 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:957:flowMins:416
2025-06-12 10:45:00,384 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:923:flowendtimeIndex:1020:flowMins:97
2025-06-12 10:45:00,384 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,385 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,386 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,387 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,388 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,388 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,388 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:00,389 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,207 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:923:flowendtimeIndex:1020:flowMins:97
2025-06-12 10:45:01,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,208 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,209 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,210 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,210 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,211 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,211 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,211 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,212 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,213 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,213 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,213 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,214 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,214 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,215 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,215 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,215 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:01,216 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,141 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:714:flowendtimeIndex:1020:flowMins:306
2025-06-12 10:45:39,141 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,142 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,142 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,142 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,143 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,143 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,143 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,143 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,143 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,144 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,144 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,144 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,144 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,145 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,145 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,145 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,145 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,146 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,147 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,147 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,147 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:39,148 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:714:flowMins:173
2025-06-12 10:45:40,230 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:714:flowendtimeIndex:1020:flowMins:306
2025-06-12 10:45:40,230 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,230 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,231 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,231 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,232 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,232 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,232 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,233 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,233 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,234 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,234 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,234 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,235 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,235 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,236 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,236 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,236 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,237 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,237 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,237 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,238 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:40,238 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:714:flowMins:173
2025-06-12 10:45:43,128 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:1004:flowendtimeIndex:1020:flowMins:16
2025-06-12 10:45:43,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:43,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1080:flowMins:479
2025-06-12 10:45:46,190 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:551:flowendtimeIndex:1020:flowMins:469
2025-06-12 10:45:46,190 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,190 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,191 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::4::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,191 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,192 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,192 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,192 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,192 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::11::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,193 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,194 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,194 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,194 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,195 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::18::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,195 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,196 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,196 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,197 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,197 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::25::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,198 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,198 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,198 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,199 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:551:flowMins:10
2025-06-12 10:45:46,982 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:45:46,982 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,983 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,985 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,985 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,986 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,987 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,988 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,988 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,988 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,989 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,990 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,990 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,990 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,991 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,991 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,993 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,994 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:46,995 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
2025-06-12 10:45:50,420 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:654:flowendtimeIndex:1020:flowMins:366
2025-06-12 10:45:50,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::5::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,421 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,422 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,423 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::12::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,423 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,423 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,424 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::19::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,425 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,426 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,426 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::26::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:50,427 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,121 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::1::flowbegintimeIndex:920:flowendtimeIndex:1020:flowMins:100
2025-06-12 10:45:51,122 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::2::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,122 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::3::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,123 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::6::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,123 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::7::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,123 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::8::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,124 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::9::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,124 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::10::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,125 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::13::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,125 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::14::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,125 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::15::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,126 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::16::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,126 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::17::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,127 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::20::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,128 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::21::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,128 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::22::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::23::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,129 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::24::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,130 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::27::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,130 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::28::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,130 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::29::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,131 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::30::flowbegintimeIndex:541:flowendtimeIndex:1020:flowMins:479
2025-06-12 10:45:51,131 INFO  [Thread:Thread-49] com.engine.kq.wfset.util.SplitActionUtil.commonShiftSplit() - line:1186 : i::31::flowbegintimeIndex:541:flowendtimeIndex:920:flowMins:379
