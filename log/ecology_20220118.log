2022-01-18 15:46:28,368 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-01-18 15:46:28,377 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-01-18 15:46:28,378 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-01-18 15:46:29,254 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-01-18 15:46:29,255 INFO  weaver.general.InitServer  - init ioc container...
2022-01-18 15:46:30,222 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-01-18 15:46:31,799 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-01-18 15:46:33,139 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-01-18 15:46:33,253 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-01-18 15:46:33,258 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-01-18 15:46:33,854 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-01-18 15:46:33,923 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2022-01-18 15:46:34,504 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent9003156348678626535.jar
2022-01-18 15:46:34,504 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent9003156348678626535.jar
2022-01-18 15:46:34,523 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-01-18 15:46:36,971 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-01-18 15:46:36,972 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-01-18 15:46:36,974 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-01-18 15:46:36,974 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-01-18 15:46:37,120 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-01-18 15:46:38,671 INFO  weaver.general.InitServer  - end ioc container init...
2022-01-18 15:46:38,678 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-01-18 15:46:38,678 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-01-18 15:46:38,681 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-01-18 15:46:38,685 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-01-18 15:46:38,686 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-01-18 15:46:40,690 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-01-18 15:46:40,691 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-01-18 15:46:40,700 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-01-18 15:46:40,700 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-01-18 15:46:40,700 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-01-18 15:46:40,702 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-01-18 15:46:40,703 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-01-18 15:46:40,705 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-01-18 15:46:40,705 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-01-18 15:46:40,707 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-01-18 15:46:41,286 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-01-18 15:46:41,529 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-01-18 15:46:41,543 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-01-18 15:46:41,560 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-01-18 15:46:41,795 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-01-18 15:46:42,000 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-01-18 15:46:42,005 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-01-18 15:46:42,008 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-01-18 15:46:42,009 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-01-18 15:46:42,013 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-01-18 15:46:42,013 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-01-18 15:46:42,017 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-01-18 15:46:42,057 INFO  weaver.general.InitServer  - end.....
2022-01-18 15:46:42,092 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-01-18 15:46:42,142 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-01-18 15:46:42,258 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-01-18 15:46:42,311 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-01-18 15:46:42,332 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-01-18 15:46:42,433 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-01-18 15:46:42,787 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-01-18 15:46:43,035 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-01-18 15:46:43,040 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-01-18 15:46:43,042 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-01-18 15:46:43,131 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-01-18 15:46:43,131 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-01-18 15:46:43,131 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-01-18 15:46:43,173 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-01-18 15:46:43,173 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-01-18 15:46:43,183 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-01-18 15:46:43,207 ERROR weaver.general.BaseBean  - ������ʱ����
2022-01-18 15:46:43,422 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-01-18 15:46:43,423 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-01-18 15:46:43,423 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-01-18 15:46:43,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-01-18 15:46:43,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-01-18 15:46:43,451 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-01-18 15:46:44,009 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-01-18 15:46:44,017 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-01-18 15:46:44,049 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-01-18 15:46:44,053 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-01-18 15:46:44,143 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-01-18 15:46:44,227 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-01-18 15:46:44,288 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-01-18 15:46:44,297 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-01-18 15:46:44,512 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=512cf442-3293-4aff-b1d3-933c80b32815,��ʼ�ʼ��ڲ��ռ�������
2022-01-18 15:46:44,518 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=512cf442-3293-4aff-b1d3-933c80b32815,-> ########## ִ�м�ʱ��ʼ ##########
2022-01-18 15:46:44,700 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=512cf442-3293-4aff-b1d3-933c80b32815,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-01-18 15:46:44,701 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-01-18 15:46:45,070 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-01-18 15:46:45,254 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-07-22' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-07-22') or  lastLoginDate<'2021-07-22')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-01-18 15:46:45,255 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-01-18 15:46:45,255 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-01-18 15:46:45,498 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-01-18 15:46:47,821 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-01-18 15:46:47,828 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-01-18 15:46:48,039 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-01-18 15:46:48,049 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-01-18 15:46:51,704 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-01-18 15:47:05,497 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2022-01-18 15:47:05,503 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2022-01-18 15:47:05,503 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2022-01-18 15:47:06,716 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-01-18 15:47:06,717 INFO  weaver.general.InitServer  - init ioc container...
2022-01-18 15:47:08,918 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-01-18 15:47:10,433 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2022-01-18 15:47:10,977 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2022-01-18 15:47:11,004 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2022-01-18 15:47:11,194 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2022-01-18 15:47:11,360 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2022-01-18 15:47:11,365 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2022-01-18 15:47:17,460 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2022-01-18 15:47:17,461 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2022-01-18 15:47:17,464 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2022-01-18 15:47:17,464 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2022-01-18 15:47:17,580 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2022-01-18 15:47:19,027 INFO  weaver.general.InitServer  - end ioc container init...
2022-01-18 15:47:19,034 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2022-01-18 15:47:19,035 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2022-01-18 15:47:19,037 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2022-01-18 15:47:19,040 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2022-01-18 15:47:19,041 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2022-01-18 15:47:21,056 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2022-01-18 15:47:21,056 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2022-01-18 15:47:21,065 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2022-01-18 15:47:21,065 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2022-01-18 15:47:21,065 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2022-01-18 15:47:21,069 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2022-01-18 15:47:21,069 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2022-01-18 15:47:21,072 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2022-01-18 15:47:21,073 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2022-01-18 15:47:21,075 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2022-01-18 15:47:21,566 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2022-01-18 15:47:21,821 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2022-01-18 15:47:21,835 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2022-01-18 15:47:21,849 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2022-01-18 15:47:21,849 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2022-01-18 15:47:22,031 INFO  weaver.general.InitServer  - ESB INIT Start.....
2022-01-18 15:47:22,109 INFO  weaver.general.InitServer  - ESB INIT End.....
2022-01-18 15:47:22,114 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2022-01-18 15:47:22,117 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2022-01-18 15:47:22,117 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2022-01-18 15:47:22,121 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2022-01-18 15:47:22,121 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2022-01-18 15:47:22,125 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2022-01-18 15:47:22,161 INFO  weaver.general.InitServer  - end.....
2022-01-18 15:47:22,187 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2022-01-18 15:47:22,218 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2022-01-18 15:47:22,286 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2022-01-18 15:47:22,334 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2022-01-18 15:47:22,354 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2022-01-18 15:47:22,443 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2022-01-18 15:47:22,782 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2022-01-18 15:47:22,860 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2022-01-18 15:47:22,863 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2022-01-18 15:47:23,012 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2022-01-18 15:47:23,168 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2022-01-18 15:47:23,185 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2022-01-18 15:47:23,185 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2022-01-18 15:47:23,185 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2022-01-18 15:47:23,215 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-01-18 15:47:23,215 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-01-18 15:47:23,244 ERROR weaver.general.BaseBean  - ������ʱ����
2022-01-18 15:47:23,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2022-01-18 15:47:23,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2022-01-18 15:47:23,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2022-01-18 15:47:23,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2022-01-18 15:47:23,425 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2022-01-18 15:47:23,444 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-01-18 15:47:23,745 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2022-01-18 15:47:23,747 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2022-01-18 15:47:23,780 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2022-01-18 15:47:23,783 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2022-01-18 15:47:24,134 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2022-01-18 15:47:24,136 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-01-18 15:47:24,136 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2022-01-18 15:47:24,149 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2022-01-18 15:47:24,171 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a6f995fe-5d58-4016-b47a-dcf48b50b946,��ʼ�ʼ��ڲ��ռ�������
2022-01-18 15:47:24,171 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a6f995fe-5d58-4016-b47a-dcf48b50b946,-> ########## ִ�м�ʱ��ʼ ##########
2022-01-18 15:47:24,186 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2022-01-18 15:47:24,186 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2022-01-18 15:47:24,192 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-01-18 15:47:24,354 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=a6f995fe-5d58-4016-b47a-dcf48b50b946,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2022-01-18 15:47:24,354 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2022-01-18 15:47:24,375 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2022-01-18 15:47:25,283 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2022-01-18 15:47:25,318 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2022-01-18 15:47:25,397 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2021-07-22' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2021-07-22') or  lastLoginDate<'2021-07-22')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2022-01-18 15:47:25,397 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2022-01-18 15:47:25,397 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2022-01-18 15:47:25,646 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2022-01-18 15:47:34,666 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2022-01-18 15:47:52,111 ERROR weaver.general.BaseBean  - qrcode_config>>>
2022-01-18 15:52:28,194 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,313 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,376 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,380 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,382 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,388 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,381 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,408 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,433 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,506 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,624 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,624 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,861 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:28,888 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-01-18 15:52:28,888 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-01-18 15:52:29,380 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,627 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,688 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,768 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,779 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,843 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,879 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,932 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:29,986 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,045 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,059 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-01-18 15:52:30,132 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,148 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-01-18 15:52:30,201 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,243 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,285 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,349 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,374 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,378 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,418 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,505 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,506 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,555 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,586 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,604 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,628 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,674 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,674 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,698 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,739 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,740 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:30,747 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:31,075 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:31,130 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:31,275 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:31,513 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor409.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 15:52:33,480 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,182 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,183 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,199 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,548 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,553 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:34,716 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor409.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 15:52:35,102 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor409.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 15:52:35,156 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor409.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor346.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 15:52:37,022 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,062 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,097 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,098 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,237 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,246 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,704 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,705 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,710 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,721 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,750 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,773 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,782 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,787 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,800 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,811 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,848 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,889 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:37,971 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2022-01-18 15:52:39,213 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:39,229 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:39,339 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:39,349 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:39,363 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:39,462 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:42,640 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:42,732 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:42,737 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:42,745 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:43,178 ERROR com.engine.workflow.cmd.workflowIndex.GetIndexInfoCmd  - 
2022-01-18 15:52:43,453 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:43,909 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,237 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,313 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,431 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,515 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,535 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,537 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,561 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:44,668 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:45,127 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:45,186 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:45,187 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:45,773 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:46,536 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:46,646 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:59,441 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:59,831 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:59,920 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:52:59,921 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:00,106 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:00,392 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:01,120 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:01,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:01,883 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:01,890 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:01,891 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:02,458 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:06,261 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:06,261 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:07,187 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:07,426 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:07,427 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:08,042 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:08,045 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:08,049 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:08,598 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:08,599 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:09,311 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:11,575 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:11,591 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:11,592 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:11,593 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:11,639 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:12,561 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:16,591 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:16,591 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:16,592 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,061 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,061 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,104 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,109 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,156 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,829 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:17,854 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:21,580 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:21,632 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:21,633 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,758 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,758 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,759 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,926 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,926 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:22,937 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,448 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,448 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,504 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,515 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,515 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,515 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,582 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,614 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,754 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:23,766 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:24,685 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:24,691 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:24,691 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:24,692 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:24,718 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,314 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,844 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,845 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,888 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,896 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,896 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,898 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:25,988 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:26,133 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:53:26,144 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:55:30,724 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:55:30,725 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:55:31,586 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:57:32,313 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:58:30,730 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:58:30,733 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 15:58:31,579 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:01:30,725 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:01:30,729 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:01:31,309 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:02:25,795 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-01-18 16:02:25,851 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2022-01-18 16:02:25,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2022-01-18 16:02:25,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2022-01-18 16:02:26,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2022-01-18 16:02:26,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2022-01-18 16:02:26,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2022-01-18 16:02:26,209 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2022-01-18 16:02:26,251 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2022-01-18 16:02:26,313 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2022-01-18 16:02:26,361 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2022-01-18 16:02:26,424 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2022-01-18 16:02:26,474 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2022-01-18 16:02:26,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2022-01-18 16:02:26,575 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2022-01-18 16:02:26,632 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2022-01-18 16:02:26,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2022-01-18 16:02:26,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2022-01-18 16:02:26,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2022-01-18 16:02:26,921 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2022-01-18 16:02:26,972 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2022-01-18 16:02:27,031 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2022-01-18 16:02:27,080 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2022-01-18 16:02:27,167 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2022-01-18 16:02:27,231 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2022-01-18 16:02:27,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2022-01-18 16:02:27,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2022-01-18 16:02:27,385 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2022-01-18 16:02:27,435 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2022-01-18 16:02:27,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2022-01-18 16:02:27,533 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2022-01-18 16:02:27,595 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2022-01-18 16:02:27,672 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2022-01-18 16:02:27,754 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2022-01-18 16:02:27,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2022-01-18 16:02:27,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2022-01-18 16:02:27,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2022-01-18 16:02:28,023 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2022-01-18 16:02:28,081 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2022-01-18 16:02:28,132 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2022-01-18 16:02:28,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2022-01-18 16:02:28,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2022-01-18 16:02:28,322 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2022-01-18 16:02:28,393 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2022-01-18 16:02:28,463 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2022-01-18 16:02:28,521 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2022-01-18 16:02:28,582 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2022-01-18 16:02:28,633 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2022-01-18 16:02:28,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2022-01-18 16:02:28,793 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2022-01-18 16:02:28,847 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2022-01-18 16:02:28,902 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2022-01-18 16:02:28,973 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2022-01-18 16:02:29,031 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2022-01-18 16:02:29,081 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2022-01-18 16:02:29,125 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2022-01-18 16:02:29,154 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2022-01-18 16:02:29,190 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2022-01-18 16:02:29,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2022-01-18 16:02:29,242 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2022-01-18 16:02:29,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2022-01-18 16:02:29,303 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2022-01-18 16:02:29,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2022-01-18 16:02:29,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2022-01-18 16:02:29,457 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2022-01-18 16:02:29,548 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2022-01-18 16:02:29,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2022-01-18 16:02:29,691 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2022-01-18 16:02:29,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2022-01-18 16:02:29,818 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2022-01-18 16:02:29,873 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2022-01-18 16:02:29,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2022-01-18 16:02:29,993 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2022-01-18 16:02:30,062 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2022-01-18 16:02:30,111 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2022-01-18 16:02:30,156 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2022-01-18 16:02:30,210 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2022-01-18 16:02:30,277 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2022-01-18 16:02:30,327 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2022-01-18 16:02:30,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2022-01-18 16:02:30,440 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2022-01-18 16:02:30,496 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2022-01-18 16:02:30,548 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2022-01-18 16:02:30,606 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2022-01-18 16:02:30,687 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2022-01-18 16:02:30,781 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2022-01-18 16:02:30,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2022-01-18 16:02:30,890 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2022-01-18 16:02:30,949 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2022-01-18 16:02:31,005 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2022-01-18 16:02:31,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2022-01-18 16:02:31,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2022-01-18 16:02:31,167 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2022-01-18 16:02:31,247 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2022-01-18 16:02:31,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2022-01-18 16:02:31,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2022-01-18 16:02:31,416 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2022-01-18 16:02:31,485 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2022-01-18 16:02:31,536 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2022-01-18 16:02:31,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2022-01-18 16:02:31,640 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2022-01-18 16:02:31,771 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2022-01-18 16:02:31,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2022-01-18 16:02:31,874 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2022-01-18 16:02:31,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2022-01-18 16:02:31,984 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2022-01-18 16:02:32,031 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2022-01-18 16:02:32,093 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2022-01-18 16:02:32,125 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2022-01-18 16:02:32,152 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2022-01-18 16:02:32,193 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:02:32,207 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2022-01-18 16:02:32,256 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2022-01-18 16:02:32,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2022-01-18 16:02:32,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2022-01-18 16:02:32,428 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2022-01-18 16:02:32,491 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2022-01-18 16:02:32,549 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2022-01-18 16:02:32,625 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2022-01-18 16:02:32,741 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2022-01-18 16:02:32,841 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2022-01-18 16:02:32,902 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2022-01-18 16:02:32,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2022-01-18 16:02:33,034 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2022-01-18 16:02:33,091 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2022-01-18 16:02:33,140 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2022-01-18 16:02:33,185 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2022-01-18 16:02:33,229 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2022-01-18 16:02:33,280 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2022-01-18 16:02:33,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2022-01-18 16:02:33,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2022-01-18 16:02:33,443 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2022-01-18 16:02:33,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2022-01-18 16:02:33,578 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2022-01-18 16:02:33,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2022-01-18 16:02:33,694 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2022-01-18 16:02:33,783 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2022-01-18 16:02:33,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2022-01-18 16:02:33,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2022-01-18 16:02:33,955 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2022-01-18 16:02:34,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2022-01-18 16:02:34,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2022-01-18 16:02:34,130 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2022-01-18 16:02:34,179 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2022-01-18 16:02:34,226 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2022-01-18 16:02:34,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2022-01-18 16:02:34,308 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2022-01-18 16:02:34,359 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2022-01-18 16:02:34,402 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2022-01-18 16:02:34,458 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2022-01-18 16:02:34,521 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2022-01-18 16:02:34,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2022-01-18 16:02:34,685 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2022-01-18 16:02:34,750 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2022-01-18 16:02:34,807 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2022-01-18 16:02:34,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2022-01-18 16:02:34,942 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2022-01-18 16:02:34,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2022-01-18 16:02:35,045 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2022-01-18 16:02:35,110 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2022-01-18 16:02:35,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2022-01-18 16:02:35,172 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2022-01-18 16:02:35,204 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2022-01-18 16:02:35,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2022-01-18 16:02:35,285 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2022-01-18 16:02:39,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2022-01-18 16:02:39,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2022-01-18 16:02:39,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2022-01-18 16:02:39,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2022-01-18 16:02:39,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2022-01-18 16:02:39,256 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2022-01-18 16:02:39,311 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2022-01-18 16:02:39,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2022-01-18 16:02:39,411 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2022-01-18 16:02:39,471 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2022-01-18 16:02:39,518 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2022-01-18 16:02:39,559 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2022-01-18 16:02:39,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2022-01-18 16:02:39,727 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2022-01-18 16:02:39,775 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2022-01-18 16:02:39,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2022-01-18 16:02:39,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2022-01-18 16:02:39,957 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2022-01-18 16:02:40,009 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2022-01-18 16:02:40,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2022-01-18 16:02:40,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2022-01-18 16:02:40,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2022-01-18 16:02:40,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2022-01-18 16:02:40,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2022-01-18 16:02:40,331 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2022-01-18 16:02:40,418 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2022-01-18 16:02:40,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2022-01-18 16:02:40,536 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2022-01-18 16:02:40,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2022-01-18 16:02:40,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2022-01-18 16:02:40,736 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2022-01-18 16:02:40,780 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2022-01-18 16:02:40,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2022-01-18 16:02:40,887 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2022-01-18 16:02:40,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2022-01-18 16:02:41,026 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2022-01-18 16:02:41,090 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2022-01-18 16:02:41,160 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2022-01-18 16:02:41,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2022-01-18 16:02:41,261 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2022-01-18 16:02:41,308 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2022-01-18 16:02:41,367 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2022-01-18 16:02:41,411 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2022-01-18 16:02:41,467 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2022-01-18 16:02:41,516 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2022-01-18 16:02:41,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2022-01-18 16:02:41,606 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2022-01-18 16:02:41,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2022-01-18 16:02:41,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2022-01-18 16:02:41,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2022-01-18 16:02:41,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2022-01-18 16:02:41,925 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2022-01-18 16:02:41,978 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2022-01-18 16:02:42,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2022-01-18 16:02:42,099 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2022-01-18 16:02:42,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2022-01-18 16:02:42,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2022-01-18 16:02:42,288 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2022-01-18 16:02:42,338 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2022-01-18 16:02:42,383 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2022-01-18 16:02:42,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2022-01-18 16:02:42,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2022-01-18 16:02:42,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2022-01-18 16:02:42,589 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2022-01-18 16:02:42,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2022-01-18 16:02:42,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2022-01-18 16:02:42,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2022-01-18 16:02:42,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2022-01-18 16:02:42,888 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2022-01-18 16:02:42,935 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2022-01-18 16:02:43,005 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2022-01-18 16:02:43,057 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2022-01-18 16:02:43,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2022-01-18 16:02:43,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2022-01-18 16:02:43,214 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2022-01-18 16:02:43,280 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2022-01-18 16:02:43,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2022-01-18 16:02:43,382 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2022-01-18 16:02:43,422 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2022-01-18 16:02:43,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2022-01-18 16:02:43,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2022-01-18 16:02:43,566 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2022-01-18 16:02:43,629 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2022-01-18 16:02:43,686 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2022-01-18 16:02:43,775 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2022-01-18 16:02:43,824 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2022-01-18 16:02:43,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2022-01-18 16:02:43,926 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2022-01-18 16:02:43,974 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2022-01-18 16:02:44,026 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2022-01-18 16:02:44,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2022-01-18 16:02:44,173 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2022-01-18 16:02:44,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2022-01-18 16:02:44,278 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2022-01-18 16:02:44,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2022-01-18 16:02:44,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2022-01-18 16:02:44,405 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2022-01-18 16:02:44,453 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2022-01-18 16:02:44,506 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2022-01-18 16:02:44,546 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2022-01-18 16:02:44,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2022-01-18 16:02:44,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2022-01-18 16:02:44,712 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2022-01-18 16:02:44,753 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2022-01-18 16:02:44,799 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2022-01-18 16:02:44,869 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2022-01-18 16:02:44,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2022-01-18 16:02:44,972 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2022-01-18 16:02:45,020 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2022-01-18 16:02:45,064 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2022-01-18 16:02:45,112 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2022-01-18 16:02:45,163 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2022-01-18 16:02:45,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2022-01-18 16:02:45,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2022-01-18 16:02:45,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2022-01-18 16:02:45,352 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2022-01-18 16:02:45,402 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2022-01-18 16:02:45,456 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2022-01-18 16:02:45,513 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2022-01-18 16:02:45,558 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2022-01-18 16:02:45,598 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2022-01-18 16:02:45,661 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2022-01-18 16:02:45,727 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2022-01-18 16:02:45,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2022-01-18 16:02:45,851 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2022-01-18 16:02:45,903 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2022-01-18 16:02:45,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2022-01-18 16:02:46,007 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2022-01-18 16:02:46,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2022-01-18 16:02:46,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2022-01-18 16:02:46,146 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2022-01-18 16:02:46,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2022-01-18 16:02:46,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2022-01-18 16:02:46,344 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2022-01-18 16:02:46,404 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2022-01-18 16:02:46,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2022-01-18 16:02:46,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2022-01-18 16:02:46,610 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2022-01-18 16:02:46,681 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2022-01-18 16:02:46,745 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2022-01-18 16:02:46,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2022-01-18 16:02:46,838 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2022-01-18 16:02:46,890 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2022-01-18 16:02:46,933 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2022-01-18 16:02:46,986 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2022-01-18 16:02:47,042 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2022-01-18 16:02:47,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2022-01-18 16:02:47,121 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2022-01-18 16:02:47,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2022-01-18 16:02:47,178 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2022-01-18 16:02:47,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2022-01-18 16:02:47,231 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2022-01-18 16:02:47,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2022-01-18 16:02:47,299 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2022-01-18 16:02:47,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2022-01-18 16:02:47,369 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2022-01-18 16:02:47,407 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2022-01-18 16:02:47,435 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2022-01-18 16:02:47,474 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2022-01-18 16:02:47,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2022-01-18 16:02:47,550 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2022-01-18 16:02:47,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2022-01-18 16:02:47,967 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2022-01-18 16:02:48,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2022-01-18 16:02:48,065 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2022-01-18 16:02:48,122 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2022-01-18 16:02:48,171 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2022-01-18 16:02:48,224 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2022-01-18 16:02:48,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2022-01-18 16:02:48,325 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2022-01-18 16:02:48,372 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2022-01-18 16:02:48,426 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2022-01-18 16:02:48,475 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2022-01-18 16:02:48,517 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2022-01-18 16:02:48,565 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2022-01-18 16:02:48,613 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2022-01-18 16:02:48,675 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2022-01-18 16:02:48,744 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2022-01-18 16:02:48,847 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2022-01-18 16:02:48,901 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2022-01-18 16:02:48,952 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2022-01-18 16:02:48,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2022-01-18 16:02:49,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2022-01-18 16:02:49,098 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2022-01-18 16:02:49,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2022-01-18 16:02:49,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2022-01-18 16:02:49,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2022-01-18 16:02:49,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2022-01-18 16:02:49,383 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2022-01-18 16:02:49,444 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2022-01-18 16:02:49,503 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2022-01-18 16:02:49,550 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2022-01-18 16:02:49,599 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2022-01-18 16:02:49,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2022-01-18 16:02:49,723 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2022-01-18 16:02:49,781 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2022-01-18 16:02:49,833 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2022-01-18 16:02:49,886 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2022-01-18 16:02:49,936 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2022-01-18 16:02:49,990 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2022-01-18 16:02:50,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2022-01-18 16:02:50,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2022-01-18 16:02:50,126 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2022-01-18 16:02:50,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2022-01-18 16:02:50,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2022-01-18 16:02:50,225 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2022-01-18 16:02:50,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2022-01-18 16:02:50,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2022-01-18 16:02:50,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2022-01-18 16:02:50,388 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2022-01-18 16:02:50,445 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2022-01-18 16:02:50,508 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2022-01-18 16:02:50,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2022-01-18 16:02:50,605 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2022-01-18 16:02:50,669 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2022-01-18 16:02:50,724 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2022-01-18 16:02:50,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2022-01-18 16:02:50,823 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2022-01-18 16:02:50,869 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2022-01-18 16:02:50,922 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2022-01-18 16:02:50,961 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2022-01-18 16:02:51,012 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2022-01-18 16:02:51,060 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2022-01-18 16:02:51,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2022-01-18 16:02:51,167 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2022-01-18 16:02:51,213 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2022-01-18 16:02:51,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2022-01-18 16:02:51,361 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2022-01-18 16:02:51,410 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2022-01-18 16:02:51,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2022-01-18 16:02:51,511 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2022-01-18 16:02:51,557 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2022-01-18 16:02:51,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2022-01-18 16:02:51,681 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2022-01-18 16:02:51,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2022-01-18 16:02:51,792 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2022-01-18 16:02:51,842 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2022-01-18 16:02:51,885 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2022-01-18 16:02:51,953 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2022-01-18 16:02:51,997 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2022-01-18 16:02:52,041 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2022-01-18 16:02:52,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2022-01-18 16:02:52,133 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2022-01-18 16:02:52,185 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2022-01-18 16:02:52,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2022-01-18 16:02:52,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2022-01-18 16:02:52,358 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2022-01-18 16:02:52,419 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2022-01-18 16:02:52,463 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2022-01-18 16:02:52,522 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2022-01-18 16:02:52,573 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2022-01-18 16:02:52,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2022-01-18 16:02:52,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2022-01-18 16:02:52,755 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2022-01-18 16:02:52,804 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2022-01-18 16:02:52,864 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2022-01-18 16:02:52,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2022-01-18 16:02:52,948 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2022-01-18 16:02:52,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2022-01-18 16:02:53,044 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2022-01-18 16:02:53,096 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2022-01-18 16:02:53,141 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2022-01-18 16:02:53,197 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2022-01-18 16:02:53,253 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2022-01-18 16:02:53,298 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2022-01-18 16:02:53,345 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2022-01-18 16:02:53,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2022-01-18 16:02:53,444 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2022-01-18 16:02:53,499 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2022-01-18 16:02:53,544 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2022-01-18 16:02:53,586 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2022-01-18 16:02:53,634 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2022-01-18 16:02:53,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2022-01-18 16:02:53,803 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2022-01-18 16:02:53,857 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2022-01-18 16:02:53,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2022-01-18 16:02:53,978 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2022-01-18 16:02:54,028 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2022-01-18 16:02:54,076 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2022-01-18 16:02:54,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2022-01-18 16:02:54,173 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2022-01-18 16:02:54,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2022-01-18 16:02:54,275 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2022-01-18 16:02:54,331 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2022-01-18 16:02:54,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2022-01-18 16:02:54,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2022-01-18 16:02:54,502 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2022-01-18 16:02:54,552 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2022-01-18 16:02:54,602 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2022-01-18 16:02:54,658 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2022-01-18 16:02:54,730 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2022-01-18 16:02:54,780 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2022-01-18 16:02:54,829 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2022-01-18 16:02:54,884 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2022-01-18 16:02:54,936 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2022-01-18 16:02:55,007 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2022-01-18 16:02:55,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2022-01-18 16:02:55,109 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2022-01-18 16:02:55,153 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2022-01-18 16:02:55,203 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2022-01-18 16:02:55,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2022-01-18 16:02:55,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2022-01-18 16:02:55,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2022-01-18 16:02:55,428 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2022-01-18 16:02:55,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2022-01-18 16:02:55,543 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2022-01-18 16:02:55,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2022-01-18 16:02:55,711 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2022-01-18 16:02:55,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2022-01-18 16:02:55,845 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2022-01-18 16:02:55,893 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2022-01-18 16:02:55,965 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2022-01-18 16:02:56,024 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2022-01-18 16:02:56,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2022-01-18 16:02:56,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2022-01-18 16:02:56,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2022-01-18 16:02:56,318 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2022-01-18 16:02:56,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2022-01-18 16:02:56,434 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2022-01-18 16:02:56,493 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2022-01-18 16:02:56,542 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2022-01-18 16:02:56,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2022-01-18 16:02:56,743 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2022-01-18 16:02:56,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2022-01-18 16:02:56,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2022-01-18 16:02:56,965 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2022-01-18 16:02:57,049 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2022-01-18 16:02:57,113 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2022-01-18 16:02:57,179 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2022-01-18 16:02:57,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2022-01-18 16:02:57,307 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2022-01-18 16:02:57,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2022-01-18 16:02:57,444 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2022-01-18 16:02:57,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2022-01-18 16:02:57,568 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2022-01-18 16:02:57,635 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2022-01-18 16:02:57,718 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2022-01-18 16:02:57,786 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2022-01-18 16:02:57,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2022-01-18 16:02:57,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2022-01-18 16:02:57,994 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2022-01-18 16:02:58,073 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2022-01-18 16:02:58,136 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2022-01-18 16:02:58,201 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2022-01-18 16:02:58,264 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2022-01-18 16:02:58,327 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2022-01-18 16:02:58,405 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2022-01-18 16:02:58,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2022-01-18 16:02:58,566 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2022-01-18 16:02:58,617 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2022-01-18 16:02:58,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2022-01-18 16:02:58,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2022-01-18 16:02:58,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2022-01-18 16:02:58,923 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2022-01-18 16:02:58,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2022-01-18 16:02:59,039 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2022-01-18 16:02:59,118 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2022-01-18 16:02:59,155 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2022-01-18 16:02:59,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2022-01-18 16:02:59,241 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2022-01-18 16:02:59,285 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-01-18 16:02:59,285 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-01-18 16:02:59,290 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2022-01-18 16:04:30,761 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:04:30,761 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:04:31,368 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:05:54,005 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-01-18 16:05:54,082 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2022-01-18 16:05:54,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2022-01-18 16:05:54,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2022-01-18 16:05:54,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2022-01-18 16:05:54,283 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2022-01-18 16:05:54,339 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2022-01-18 16:05:54,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2022-01-18 16:05:54,435 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2022-01-18 16:05:54,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2022-01-18 16:05:54,538 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2022-01-18 16:05:54,590 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2022-01-18 16:05:54,642 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2022-01-18 16:05:54,740 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2022-01-18 16:05:54,791 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2022-01-18 16:05:54,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2022-01-18 16:05:54,874 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2022-01-18 16:05:54,919 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2022-01-18 16:05:54,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2022-01-18 16:05:55,043 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2022-01-18 16:05:55,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2022-01-18 16:05:55,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2022-01-18 16:05:55,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2022-01-18 16:05:55,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2022-01-18 16:05:55,298 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2022-01-18 16:05:55,365 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2022-01-18 16:05:55,416 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2022-01-18 16:05:55,479 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2022-01-18 16:05:55,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2022-01-18 16:05:55,600 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2022-01-18 16:05:55,680 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2022-01-18 16:05:55,771 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2022-01-18 16:05:55,818 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2022-01-18 16:05:55,864 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2022-01-18 16:05:55,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2022-01-18 16:05:55,976 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2022-01-18 16:05:56,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2022-01-18 16:05:56,109 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2022-01-18 16:05:56,159 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2022-01-18 16:05:56,186 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2022-01-18 16:05:56,213 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2022-01-18 16:05:56,245 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2022-01-18 16:05:56,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2022-01-18 16:05:56,313 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2022-01-18 16:05:56,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2022-01-18 16:05:56,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2022-01-18 16:05:56,533 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2022-01-18 16:05:56,575 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2022-01-18 16:05:56,624 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2022-01-18 16:05:56,709 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2022-01-18 16:05:56,788 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2022-01-18 16:05:56,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2022-01-18 16:05:56,973 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2022-01-18 16:05:57,023 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2022-01-18 16:05:57,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2022-01-18 16:05:57,109 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2022-01-18 16:05:57,185 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2022-01-18 16:05:57,257 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2022-01-18 16:05:57,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2022-01-18 16:05:57,359 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2022-01-18 16:05:57,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2022-01-18 16:05:57,496 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2022-01-18 16:05:57,571 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2022-01-18 16:05:57,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2022-01-18 16:05:57,682 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2022-01-18 16:05:57,770 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2022-01-18 16:05:57,825 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2022-01-18 16:05:57,871 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2022-01-18 16:05:57,932 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2022-01-18 16:05:57,976 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2022-01-18 16:05:58,027 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2022-01-18 16:05:58,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2022-01-18 16:05:58,122 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2022-01-18 16:05:58,169 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2022-01-18 16:05:58,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2022-01-18 16:05:58,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2022-01-18 16:05:58,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2022-01-18 16:05:58,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2022-01-18 16:05:58,472 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2022-01-18 16:05:58,548 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2022-01-18 16:05:58,589 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2022-01-18 16:05:58,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2022-01-18 16:05:58,733 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2022-01-18 16:05:58,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2022-01-18 16:05:59,000 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2022-01-18 16:05:59,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2022-01-18 16:05:59,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2022-01-18 16:05:59,262 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2022-01-18 16:05:59,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2022-01-18 16:05:59,379 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2022-01-18 16:05:59,426 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2022-01-18 16:05:59,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2022-01-18 16:05:59,558 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2022-01-18 16:05:59,609 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2022-01-18 16:05:59,676 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2022-01-18 16:05:59,733 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2022-01-18 16:05:59,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2022-01-18 16:05:59,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2022-01-18 16:05:59,867 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2022-01-18 16:05:59,915 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2022-01-18 16:05:59,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2022-01-18 16:06:00,030 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2022-01-18 16:06:00,088 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2022-01-18 16:06:00,160 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2022-01-18 16:06:00,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2022-01-18 16:06:00,264 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2022-01-18 16:06:00,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2022-01-18 16:06:00,378 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2022-01-18 16:06:00,439 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2022-01-18 16:06:00,481 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2022-01-18 16:06:00,533 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2022-01-18 16:06:00,600 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2022-01-18 16:06:00,652 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2022-01-18 16:06:00,743 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2022-01-18 16:06:00,795 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2022-01-18 16:06:00,852 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2022-01-18 16:06:00,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2022-01-18 16:06:00,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2022-01-18 16:06:01,017 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2022-01-18 16:06:01,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2022-01-18 16:06:01,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2022-01-18 16:06:01,176 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2022-01-18 16:06:01,262 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2022-01-18 16:06:01,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2022-01-18 16:06:01,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2022-01-18 16:06:01,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2022-01-18 16:06:01,475 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2022-01-18 16:06:01,522 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2022-01-18 16:06:01,575 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2022-01-18 16:06:01,627 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2022-01-18 16:06:01,697 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2022-01-18 16:06:01,774 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2022-01-18 16:06:01,811 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2022-01-18 16:06:01,899 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2022-01-18 16:06:01,987 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2022-01-18 16:06:02,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2022-01-18 16:06:02,146 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2022-01-18 16:06:02,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2022-01-18 16:06:02,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2022-01-18 16:06:02,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2022-01-18 16:06:02,360 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2022-01-18 16:06:02,419 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2022-01-18 16:06:02,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2022-01-18 16:06:02,535 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2022-01-18 16:06:02,600 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2022-01-18 16:06:02,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2022-01-18 16:06:02,774 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2022-01-18 16:06:02,815 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2022-01-18 16:06:02,859 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2022-01-18 16:06:02,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2022-01-18 16:06:02,949 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2022-01-18 16:06:03,009 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2022-01-18 16:06:03,057 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2022-01-18 16:06:03,130 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2022-01-18 16:06:03,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2022-01-18 16:06:03,263 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2022-01-18 16:06:03,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2022-01-18 16:06:03,358 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2022-01-18 16:06:03,420 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2022-01-18 16:06:03,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2022-01-18 16:06:03,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2022-01-18 16:06:03,609 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2022-01-18 16:06:03,744 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2022-01-18 16:06:03,795 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2022-01-18 16:06:03,854 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2022-01-18 16:06:03,908 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2022-01-18 16:06:03,968 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2022-01-18 16:06:04,020 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2022-01-18 16:06:04,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2022-01-18 16:06:04,123 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2022-01-18 16:06:04,173 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2022-01-18 16:06:04,249 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2022-01-18 16:06:04,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2022-01-18 16:06:04,362 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2022-01-18 16:06:04,423 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2022-01-18 16:06:04,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2022-01-18 16:06:04,523 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2022-01-18 16:06:04,563 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2022-01-18 16:06:04,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2022-01-18 16:06:04,706 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2022-01-18 16:06:04,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2022-01-18 16:06:04,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2022-01-18 16:06:04,863 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2022-01-18 16:06:04,973 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2022-01-18 16:06:05,034 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2022-01-18 16:06:05,099 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2022-01-18 16:06:05,158 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2022-01-18 16:06:05,214 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2022-01-18 16:06:05,266 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2022-01-18 16:06:05,313 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2022-01-18 16:06:05,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2022-01-18 16:06:05,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2022-01-18 16:06:05,465 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2022-01-18 16:06:05,521 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2022-01-18 16:06:05,577 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2022-01-18 16:06:05,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2022-01-18 16:06:05,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2022-01-18 16:06:05,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2022-01-18 16:06:05,789 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2022-01-18 16:06:05,831 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2022-01-18 16:06:05,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2022-01-18 16:06:06,034 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2022-01-18 16:06:06,092 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2022-01-18 16:06:06,165 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2022-01-18 16:06:06,211 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2022-01-18 16:06:06,266 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2022-01-18 16:06:06,308 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2022-01-18 16:06:06,359 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2022-01-18 16:06:06,410 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2022-01-18 16:06:06,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2022-01-18 16:06:06,524 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2022-01-18 16:06:06,570 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2022-01-18 16:06:06,611 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2022-01-18 16:06:06,679 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2022-01-18 16:06:06,757 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2022-01-18 16:06:06,806 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2022-01-18 16:06:06,858 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2022-01-18 16:06:06,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2022-01-18 16:06:06,934 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2022-01-18 16:06:06,984 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2022-01-18 16:06:07,028 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2022-01-18 16:06:07,071 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2022-01-18 16:06:07,122 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2022-01-18 16:06:07,170 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2022-01-18 16:06:07,213 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2022-01-18 16:06:07,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2022-01-18 16:06:07,316 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2022-01-18 16:06:07,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2022-01-18 16:06:07,425 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2022-01-18 16:06:07,472 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2022-01-18 16:06:07,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2022-01-18 16:06:07,554 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2022-01-18 16:06:07,603 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2022-01-18 16:06:07,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2022-01-18 16:06:07,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2022-01-18 16:06:07,798 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2022-01-18 16:06:07,850 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2022-01-18 16:06:07,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2022-01-18 16:06:07,996 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2022-01-18 16:06:08,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2022-01-18 16:06:08,156 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2022-01-18 16:06:08,205 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2022-01-18 16:06:08,255 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2022-01-18 16:06:08,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2022-01-18 16:06:08,342 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2022-01-18 16:06:08,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2022-01-18 16:06:08,446 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2022-01-18 16:06:08,500 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2022-01-18 16:06:08,541 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2022-01-18 16:06:08,582 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2022-01-18 16:06:08,620 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2022-01-18 16:06:08,682 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2022-01-18 16:06:08,758 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2022-01-18 16:06:08,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2022-01-18 16:06:08,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2022-01-18 16:06:08,913 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2022-01-18 16:06:08,980 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2022-01-18 16:06:09,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2022-01-18 16:06:09,080 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2022-01-18 16:06:09,132 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2022-01-18 16:06:09,186 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2022-01-18 16:06:09,238 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2022-01-18 16:06:09,280 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2022-01-18 16:06:09,340 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2022-01-18 16:06:09,406 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2022-01-18 16:06:09,451 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2022-01-18 16:06:09,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2022-01-18 16:06:09,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2022-01-18 16:06:09,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2022-01-18 16:06:09,647 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2022-01-18 16:06:09,727 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2022-01-18 16:06:09,778 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2022-01-18 16:06:09,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2022-01-18 16:06:09,875 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2022-01-18 16:06:09,924 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2022-01-18 16:06:09,976 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2022-01-18 16:06:10,025 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2022-01-18 16:06:10,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2022-01-18 16:06:10,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2022-01-18 16:06:10,179 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2022-01-18 16:06:10,216 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2022-01-18 16:06:10,266 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2022-01-18 16:06:10,333 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2022-01-18 16:06:10,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2022-01-18 16:06:10,426 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2022-01-18 16:06:10,478 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2022-01-18 16:06:10,524 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2022-01-18 16:06:10,584 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2022-01-18 16:06:10,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2022-01-18 16:06:10,695 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2022-01-18 16:06:10,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2022-01-18 16:06:10,801 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2022-01-18 16:06:10,845 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2022-01-18 16:06:10,890 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2022-01-18 16:06:10,931 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2022-01-18 16:06:11,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2022-01-18 16:06:11,053 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2022-01-18 16:06:11,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2022-01-18 16:06:11,151 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2022-01-18 16:06:11,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2022-01-18 16:06:11,234 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2022-01-18 16:06:11,280 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2022-01-18 16:06:11,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2022-01-18 16:06:11,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2022-01-18 16:06:11,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2022-01-18 16:06:11,487 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2022-01-18 16:06:11,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2022-01-18 16:06:11,579 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2022-01-18 16:06:11,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2022-01-18 16:06:11,723 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2022-01-18 16:06:11,769 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2022-01-18 16:06:11,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2022-01-18 16:06:11,873 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2022-01-18 16:06:11,919 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2022-01-18 16:06:11,961 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2022-01-18 16:06:12,011 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2022-01-18 16:06:12,054 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2022-01-18 16:06:12,109 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2022-01-18 16:06:12,160 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2022-01-18 16:06:12,212 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2022-01-18 16:06:12,260 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2022-01-18 16:06:12,322 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2022-01-18 16:06:12,371 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2022-01-18 16:06:12,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2022-01-18 16:06:12,460 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2022-01-18 16:06:12,520 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2022-01-18 16:06:12,569 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2022-01-18 16:06:12,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2022-01-18 16:06:12,691 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2022-01-18 16:06:12,741 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2022-01-18 16:06:12,780 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2022-01-18 16:06:12,831 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2022-01-18 16:06:12,870 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2022-01-18 16:06:12,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2022-01-18 16:06:12,974 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2022-01-18 16:06:13,023 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2022-01-18 16:06:13,065 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2022-01-18 16:06:13,124 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2022-01-18 16:06:13,169 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2022-01-18 16:06:13,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2022-01-18 16:06:13,265 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2022-01-18 16:06:13,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2022-01-18 16:06:13,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2022-01-18 16:06:13,397 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2022-01-18 16:06:13,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2022-01-18 16:06:13,513 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2022-01-18 16:06:13,567 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2022-01-18 16:06:13,619 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2022-01-18 16:06:13,669 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2022-01-18 16:06:13,732 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2022-01-18 16:06:13,794 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2022-01-18 16:06:13,853 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2022-01-18 16:06:13,911 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2022-01-18 16:06:13,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2022-01-18 16:06:14,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2022-01-18 16:06:14,061 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2022-01-18 16:06:14,112 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2022-01-18 16:06:14,175 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2022-01-18 16:06:14,202 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2022-01-18 16:06:14,236 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2022-01-18 16:06:14,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2022-01-18 16:06:14,300 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2022-01-18 16:06:14,330 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2022-01-18 16:06:14,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2022-01-18 16:06:14,380 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2022-01-18 16:06:14,406 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2022-01-18 16:06:14,440 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2022-01-18 16:06:14,479 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2022-01-18 16:06:14,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2022-01-18 16:06:14,541 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2022-01-18 16:06:14,569 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2022-01-18 16:06:14,598 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2022-01-18 16:06:14,625 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2022-01-18 16:06:14,681 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2022-01-18 16:06:14,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2022-01-18 16:06:14,794 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2022-01-18 16:06:14,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2022-01-18 16:06:14,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2022-01-18 16:06:14,918 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2022-01-18 16:06:14,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2022-01-18 16:06:15,025 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2022-01-18 16:06:15,077 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2022-01-18 16:06:15,135 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2022-01-18 16:06:15,185 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2022-01-18 16:06:15,248 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2022-01-18 16:06:15,293 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2022-01-18 16:06:15,338 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2022-01-18 16:06:15,386 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2022-01-18 16:06:15,431 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2022-01-18 16:06:15,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2022-01-18 16:06:15,532 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2022-01-18 16:06:15,590 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2022-01-18 16:06:15,635 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2022-01-18 16:06:15,689 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2022-01-18 16:06:15,738 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2022-01-18 16:06:15,789 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2022-01-18 16:06:15,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2022-01-18 16:06:15,883 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2022-01-18 16:06:15,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2022-01-18 16:06:15,984 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2022-01-18 16:06:16,025 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2022-01-18 16:06:16,070 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2022-01-18 16:06:16,124 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2022-01-18 16:06:16,159 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2022-01-18 16:06:16,206 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2022-01-18 16:06:16,253 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2022-01-18 16:06:16,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2022-01-18 16:06:16,353 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2022-01-18 16:06:16,405 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2022-01-18 16:06:16,473 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2022-01-18 16:06:16,517 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2022-01-18 16:06:16,568 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2022-01-18 16:06:16,610 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2022-01-18 16:06:16,678 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2022-01-18 16:06:16,781 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2022-01-18 16:06:16,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2022-01-18 16:06:16,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2022-01-18 16:06:16,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2022-01-18 16:06:16,977 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2022-01-18 16:06:17,030 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2022-01-18 16:06:17,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2022-01-18 16:06:17,150 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2022-01-18 16:06:17,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2022-01-18 16:06:17,254 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2022-01-18 16:06:17,310 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2022-01-18 16:06:17,358 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2022-01-18 16:06:17,401 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2022-01-18 16:06:17,454 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2022-01-18 16:06:17,512 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2022-01-18 16:06:17,567 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2022-01-18 16:06:17,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2022-01-18 16:06:17,683 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2022-01-18 16:06:17,746 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2022-01-18 16:06:17,791 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2022-01-18 16:06:17,839 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2022-01-18 16:06:17,886 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2022-01-18 16:06:17,934 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2022-01-18 16:06:17,985 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2022-01-18 16:06:18,041 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2022-01-18 16:06:18,094 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2022-01-18 16:06:18,151 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2022-01-18 16:06:18,198 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2022-01-18 16:06:18,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2022-01-18 16:06:18,299 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2022-01-18 16:06:18,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2022-01-18 16:06:18,410 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2022-01-18 16:06:18,464 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2022-01-18 16:06:18,517 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2022-01-18 16:06:18,565 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2022-01-18 16:06:18,603 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2022-01-18 16:06:18,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2022-01-18 16:06:18,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2022-01-18 16:06:18,799 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2022-01-18 16:06:18,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2022-01-18 16:06:18,890 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2022-01-18 16:06:18,950 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2022-01-18 16:06:19,003 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2022-01-18 16:06:19,056 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2022-01-18 16:06:19,101 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2022-01-18 16:06:19,147 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2022-01-18 16:06:19,195 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2022-01-18 16:06:19,239 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2022-01-18 16:06:19,291 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2022-01-18 16:06:19,330 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2022-01-18 16:06:19,395 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2022-01-18 16:06:19,445 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2022-01-18 16:06:19,488 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2022-01-18 16:06:19,530 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2022-01-18 16:06:19,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2022-01-18 16:06:19,632 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2022-01-18 16:06:19,712 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2022-01-18 16:06:19,767 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2022-01-18 16:06:19,816 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2022-01-18 16:06:19,865 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2022-01-18 16:06:19,934 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2022-01-18 16:06:19,988 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2022-01-18 16:06:20,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2022-01-18 16:06:20,111 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2022-01-18 16:06:20,145 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2022-01-18 16:06:20,173 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2022-01-18 16:06:20,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2022-01-18 16:06:20,244 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2022-01-18 16:06:20,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2022-01-18 16:06:20,309 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2022-01-18 16:06:20,334 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2022-01-18 16:06:20,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2022-01-18 16:06:20,390 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2022-01-18 16:06:20,427 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2022-01-18 16:06:20,472 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2022-01-18 16:06:20,518 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2022-01-18 16:06:20,567 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2022-01-18 16:06:20,617 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2022-01-18 16:06:20,686 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2022-01-18 16:06:20,748 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2022-01-18 16:06:20,805 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2022-01-18 16:06:20,870 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2022-01-18 16:06:20,931 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2022-01-18 16:06:20,988 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2022-01-18 16:06:21,049 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2022-01-18 16:06:21,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2022-01-18 16:06:21,155 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2022-01-18 16:06:21,208 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2022-01-18 16:06:21,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2022-01-18 16:06:21,308 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2022-01-18 16:06:21,351 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2022-01-18 16:06:21,397 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2022-01-18 16:06:21,450 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2022-01-18 16:06:21,502 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2022-01-18 16:06:21,554 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2022-01-18 16:06:21,619 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2022-01-18 16:06:21,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2022-01-18 16:06:21,765 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2022-01-18 16:06:21,817 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2022-01-18 16:06:21,869 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2022-01-18 16:06:21,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2022-01-18 16:06:21,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2022-01-18 16:06:22,022 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2022-01-18 16:06:22,086 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2022-01-18 16:06:22,142 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2022-01-18 16:06:22,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2022-01-18 16:06:22,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2022-01-18 16:06:22,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2022-01-18 16:06:22,353 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2022-01-18 16:06:22,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2022-01-18 16:06:22,481 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2022-01-18 16:06:22,481 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2022-01-18 16:06:22,488 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2022-01-18 16:06:42,804 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:06:42,805 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:06:42,806 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 16:07:22,951 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:07:22,954 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:07:22,955 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 16:07:30,721 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:07:30,724 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:07:31,196 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:07:31,719 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:10:30,733 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:10:30,735 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:10:31,323 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:12:32,179 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2022-01-18 16:26:42,743 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:26:42,746 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:26:42,746 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 16:27:22,958 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:27:22,962 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:27:22,962 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 16:32:59,294 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�迪ʼ��
2022-01-18 16:32:59,305 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-01-18 16:36:22,497 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�迪ʼ��
2022-01-18 16:36:22,504 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-01-18 16:44:35,508 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-01-18 16:45:01,801 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�������
2022-01-18 16:46:41,111 ERROR com.api.doc.detail.util.DeleteConvertDataThread  - ^^^^^^^^^^^^^ ����ת�������ļ���ʱɾ������(4)(false) ^^^^^^^^^^^^^^^^^^^^^^^
2022-01-18 16:46:43,163 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:46:43,166 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:46:43,167 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 16:47:21,297 ERROR com.api.doc.detail.util.DeleteConvertDataThread  - ^^^^^^^^^^^^^ ����ת�������ļ���ʱɾ������(4)(false) ^^^^^^^^^^^^^^^^^^^^^^^
2022-01-18 16:47:23,021 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 16:47:23,025 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 16:47:23,026 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:06:42,773 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:06:42,783 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:06:42,784 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:07:23,070 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:07:23,073 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:07:23,073 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:15:01,810 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�迪ʼ��
2022-01-18 17:15:01,819 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2022-01-18 17:23:17,857 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-01-18 17:23:32,032 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�������
2022-01-18 17:25:07,374 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2022-01-18 17:25:21,273 ERROR weaver.general.BaseBean  - ��ʱ����ɨ�������
2022-01-18 17:26:42,804 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:26:42,806 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:26:42,806 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:27:23,072 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:27:23,073 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:27:23,074 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:46:41,000 ERROR weaver.conn.RecordSet  - expiresDate in license file is :9999-06-20
2022-01-18 17:46:41,115 ERROR com.api.doc.detail.util.DeleteConvertDataThread  - ^^^^^^^^^^^^^ ����ת�������ļ���ʱɾ������(5)(false) ^^^^^^^^^^^^^^^^^^^^^^^
2022-01-18 17:46:42,714 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:46:42,714 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:46:42,714 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
2022-01-18 17:47:21,269 ERROR weaver.conn.RecordSet  - expiresDate in license file is :9999-06-20
2022-01-18 17:47:21,301 ERROR com.api.doc.detail.util.DeleteConvertDataThread  - ^^^^^^^^^^^^^ ����ת�������ļ���ʱɾ������(5)(false) ^^^^^^^^^^^^^^^^^^^^^^^
2022-01-18 17:47:23,071 ERROR weaver.email.WeavermailUtil  - receiveMail �ʼ����մ��󣬽��մ���������
2022-01-18 17:47:23,072 ERROR weaver.email.WeavermailUtil  - mailAccountId=4,isremind=true, �˺�������1444-�̼ҿ�0622
2022-01-18 17:47:23,072 ERROR weaver.email.WeavermailUtil  - weaver.email.WeavermailUtil
javax.mail.AuthenticationFailedException: INCORRECT ACCOUNT/PASSWORD OR ACCOUNT IS NOT ENABLED FOR POP ACCESS. IF ACCOUNT IS NOT ENABLED, FOR MORE DETAILS PLEASE VISIT: http://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=166
	at com.sun.mail.pop3.POP3Store.protocolConnect(POP3Store.java:159)
	at javax.mail.Service.connect(Service.java:288)
	at javax.mail.Service.connect(Service.java:169)
	at javax.mail.Service.connect(Service.java:118)
	at weaver.email.WeavermailUtil.getMailStore(WeavermailUtil.java:1018)
	at weaver.email.WeavermailUtil.receiveMail(WeavermailUtil.java:707)
	at weaver.email.timer.MailAutoReciveThread.run(MailAutoReciveThread.java:75)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
