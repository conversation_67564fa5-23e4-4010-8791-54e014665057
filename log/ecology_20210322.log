2021-03-22 10:23:18,125 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:23:18,135 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:23:18,135 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:23:18,962 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:23:18,962 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:23:19,782 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:23:20,686 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:23:21,110 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:23:21,219 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:23:21,221 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:23:23,907 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:23:23,952 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-22 10:23:24,007 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5975641797418071385.jar
2021-03-22 10:23:24,008 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent5975641797418071385.jar
2021-03-22 10:23:24,019 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:23:25,120 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:23:25,120 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:23:25,122 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:23:25,123 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:23:25,225 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:23:26,137 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:23:26,156 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:23:26,156 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:23:26,160 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:23:26,164 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:23:26,165 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:23:28,166 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:23:28,167 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:23:28,184 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:23:28,185 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:23:28,185 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:23:28,188 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:23:28,189 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:23:28,192 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:23:28,193 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:23:28,196 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:23:28,499 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:23:28,762 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:23:28,777 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:23:28,792 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:23:28,957 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:23:29,016 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:23:29,022 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:23:29,025 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:23:29,025 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:23:29,030 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:23:29,031 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:23:29,034 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:23:29,076 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:23:29,113 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:23:29,142 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:23:29,207 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:23:29,242 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:23:29,253 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:23:29,322 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:23:29,590 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:23:29,663 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:23:29,666 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:23:29,761 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:23:29,827 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:23:29,957 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:23:29,958 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:23:29,959 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:23:29,973 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:23:29,973 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:23:30,009 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:23:30,026 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:23:30,032 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:23:30,034 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:23:30,037 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:23:30,051 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=23af8973-8aa7-4248-b200-3a78a2218c42,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:23:30,051 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=23af8973-8aa7-4248-b200-3a78a2218c42,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:23:30,056 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:23:30,059 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:23:30,062 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:23:30,107 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=23af8973-8aa7-4248-b200-3a78a2218c42,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:23:30,108 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:23:30,312 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:23:30,312 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:23:30,313 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:23:30,313 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:23:30,313 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:23:30,327 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:23:30,334 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:23:30,626 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:23:30,626 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:23:30,678 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:23:30,682 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:23:32,113 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:23:32,296 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:23:32,296 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:23:32,296 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:23:32,438 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:23:32,503 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:23:45,676 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method...
2021-03-22 10:23:45,828 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-22 10:23:48,396 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:23:48,399 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:23:48,399 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:23:49,376 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:23:49,376 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:23:51,255 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:23:52,859 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:23:53,338 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:23:53,466 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:23:53,468 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:23:53,832 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:23:53,863 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:23:55,892 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:23:55,892 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:23:55,896 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:23:55,896 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:23:55,995 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:23:56,771 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:23:56,781 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:23:56,781 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:23:56,784 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:23:56,787 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:23:56,788 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:23:58,791 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:23:58,792 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:23:58,802 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:23:58,802 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:23:58,802 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:23:58,806 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:23:58,806 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:23:58,808 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:23:58,809 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:23:58,813 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:23:59,141 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:23:59,323 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:23:59,334 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:23:59,348 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:23:59,348 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-22 10:23:59,582 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:23:59,631 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:23:59,638 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:23:59,640 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:23:59,640 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:23:59,643 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:23:59,643 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:23:59,648 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:23:59,691 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:23:59,771 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:23:59,798 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:23:59,839 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:23:59,882 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:23:59,901 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:23:59,947 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:24:00,387 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:24:00,480 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:24:00,487 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:24:00,573 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:24:00,689 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:24:00,841 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:24:00,841 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:24:00,842 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:24:00,860 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:24:00,860 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:24:00,914 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:24:00,946 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:24:01,271 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:24:01,271 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:24:01,271 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:24:01,271 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:24:01,271 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:24:01,305 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:24:01,671 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:24:01,672 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:24:01,671 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:24:01,680 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:24:01,686 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=69238bee-6d5e-4fb7-b1f0-439435d37a8a,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:24:01,686 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=69238bee-6d5e-4fb7-b1f0-439435d37a8a,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:24:01,705 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:24:01,716 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:24:01,717 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:24:01,815 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=69238bee-6d5e-4fb7-b1f0-439435d37a8a,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:24:01,816 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:24:01,831 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:24:01,832 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:24:01,893 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:24:01,896 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:24:02,711 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:24:02,860 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:24:02,860 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:24:02,860 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:24:03,072 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:24:03,681 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:33:56,346 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:33:56,353 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:33:56,353 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:33:57,364 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:33:57,364 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:33:58,279 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:33:59,262 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:33:59,596 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:33:59,798 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:33:59,800 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:34:01,793 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:34:01,839 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-22 10:34:01,890 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4157070907353440275.jar
2021-03-22 10:34:01,890 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4157070907353440275.jar
2021-03-22 10:34:01,898 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:34:02,832 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:34:02,833 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:34:02,837 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:34:02,838 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:34:02,975 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:34:03,965 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:34:03,972 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:34:03,972 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:34:03,974 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:34:03,977 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:34:03,978 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:34:05,980 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:34:05,981 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:34:05,994 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:34:05,994 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:34:05,996 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:34:05,998 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:34:05,999 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:34:06,001 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:34:06,002 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:34:06,006 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:34:06,324 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:34:06,587 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:34:06,598 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:34:06,610 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:34:06,783 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:34:06,848 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:34:06,855 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:34:06,859 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:34:06,859 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:34:06,862 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:34:06,862 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:34:06,864 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:34:06,887 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:34:06,919 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:34:07,125 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:34:07,196 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:34:07,253 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:34:07,275 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:34:07,358 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:34:07,670 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:34:07,725 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:34:07,726 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:34:07,818 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:34:07,835 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:34:07,859 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:34:07,862 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:34:07,864 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:34:07,869 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:34:07,870 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=697b8400-ab22-46fd-8cff-a47ea87597a1,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:34:07,871 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=697b8400-ab22-46fd-8cff-a47ea87597a1,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:34:07,888 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:34:07,889 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:34:07,891 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:34:07,992 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=697b8400-ab22-46fd-8cff-a47ea87597a1,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:34:07,992 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:34:07,995 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:34:07,995 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:34:07,996 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:34:08,012 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:34:08,012 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:34:08,063 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:34:08,109 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:34:08,348 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:34:08,350 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:34:08,350 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:34:08,351 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:34:08,352 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:34:08,385 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:34:08,807 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:34:08,808 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:34:08,871 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:34:08,875 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:34:09,888 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:34:10,057 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:34:10,058 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:34:10,058 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:34:10,249 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:34:10,654 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:34:20,562 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-22 10:34:22,708 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:34:22,711 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:34:22,711 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:34:23,662 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:34:23,662 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:34:24,569 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:34:25,566 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:34:25,958 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:34:26,067 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:34:26,068 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:34:28,025 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:34:28,040 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:34:28,302 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:34:28,302 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:34:28,304 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:34:28,304 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:34:28,434 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:34:29,451 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:34:29,458 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:34:29,458 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:34:29,461 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:34:29,466 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:34:29,467 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:34:31,526 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:34:31,526 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:34:31,537 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:34:31,538 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:34:31,538 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:34:31,542 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:34:31,543 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:34:31,546 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:34:31,546 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:34:31,554 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:34:31,896 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:34:32,121 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:34:32,136 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:34:32,153 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:34:32,153 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-22 10:34:32,303 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:34:32,346 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:34:32,351 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:34:32,353 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:34:32,353 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:34:32,357 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:34:32,359 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:34:32,364 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:34:32,391 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:34:32,443 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:34:32,470 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:34:32,616 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:34:32,656 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:34:32,667 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:34:32,711 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:34:33,088 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:34:33,135 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:34:33,136 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:34:33,222 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:34:33,308 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:34:33,354 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:34:33,358 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:34:33,365 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=124c36d3-47d7-4167-9a5e-351d11735fc9,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:34:33,365 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=124c36d3-47d7-4167-9a5e-351d11735fc9,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:34:33,366 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:34:33,369 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:34:33,369 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:34:33,370 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:34:33,370 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:34:33,372 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:34:33,503 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:34:33,503 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:34:33,504 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:34:33,505 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:34:33,524 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:34:33,531 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=124c36d3-47d7-4167-9a5e-351d11735fc9,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:34:33,531 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:34:33,696 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:34:33,766 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:34:33,767 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:34:33,767 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:34:33,767 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:34:33,767 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:34:33,786 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:34:34,273 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:34:34,275 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:34:34,315 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:34:34,319 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:34:35,391 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:34:35,434 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:34:35,560 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:34:35,560 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:34:35,560 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:34:35,598 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:34:39,104 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-22 10:34:58,937 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-22 10:35:10,452 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,542 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,637 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,637 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,639 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,637 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,647 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,658 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,723 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,820 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,867 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,872 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:10,968 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:35:11,255 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,255 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,322 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:35:11,429 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,430 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,457 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,485 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,500 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,520 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,584 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,635 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,665 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,665 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,688 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:35:11,690 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,702 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,720 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,792 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,812 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,841 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,950 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,953 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:11,984 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,031 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,032 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,047 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,050 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,088 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,106 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,108 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,159 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,195 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:12,571 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor413.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor357.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:35:12,713 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:13,124 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:21,467 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:49,322 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:49,708 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:49,711 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:49,994 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:50,118 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:50,712 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:50,792 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:50,995 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:51,057 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:51,064 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:51,064 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:35:51,197 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:36:20,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:36:32,871 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:36:32,930 ERROR com.api.presettle.util.Prop  - not found file <D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\WEB-INF/prop/presettle.properties>
2021-03-22 10:36:42,871 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:37:09,980 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:37:12,881 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:37:37,416 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:37:40,894 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:37:48,780 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:11,255 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:38,566 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:38,956 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:38,991 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,075 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,075 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,081 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,085 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,086 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,086 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,093 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,109 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,113 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,118 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,130 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,157 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:38:39,203 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:38:39,340 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,368 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,386 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,401 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,424 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,429 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,431 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,438 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,451 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,474 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,493 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,517 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:38:39,519 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,522 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,530 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,550 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,560 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,570 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,648 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,657 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,658 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,668 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,753 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,763 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,908 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,913 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,915 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,918 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:39,954 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:40,190 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor413.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor357.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:38:40,406 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:40,593 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:40,604 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:41,801 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:41,834 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:41,837 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:41,886 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:41,941 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,276 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,282 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,283 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,284 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,287 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,299 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,306 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,320 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,337 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,338 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,371 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,444 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:42,485 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 10:38:42,527 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,136 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,138 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,181 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,188 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,188 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:43,342 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,741 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,741 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,742 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,991 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,991 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:46,997 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:47,017 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:47,019 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:48,642 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:48,642 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:48,646 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:48,646 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:48,677 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:38:49,643 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:09,174 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:09,174 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:09,183 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:10,330 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:10,364 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:10,529 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:10,541 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:10,585 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:24,337 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:24,353 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:24,525 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:24,535 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:24,575 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,446 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,449 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,508 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,510 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,540 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,932 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,936 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:53,962 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,081 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,611 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,787 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,787 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,787 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:54,843 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:56,051 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:57,006 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:57,007 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:57,008 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:57,029 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:57,088 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:39:58,714 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:00,695 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:00,695 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:00,697 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:01,577 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:02,764 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:02,819 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:02,819 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:02,821 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:03,980 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:07,483 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:07,487 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:08,469 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:09,508 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:09,555 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:09,557 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:09,560 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:10,366 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:19,822 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:19,928 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:19,941 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:20,058 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:20,442 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:20,839 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:21,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:24,433 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:24,433 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:25,584 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:27,349 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:27,590 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:27,601 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:31,909 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:31,909 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:31,915 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:32,005 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:32,301 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:32,566 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:40:32,923 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:41:39,788 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:41:39,791 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:41:40,449 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:42:09,915 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:42:33,989 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:43:43,646 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-22 10:43:45,881 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:43:45,895 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:43:45,896 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:43:46,736 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:43:46,737 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:43:47,545 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:43:48,401 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:43:48,772 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:43:48,872 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:43:48,874 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:43:50,924 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:43:50,924 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:43:50,929 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:43:50,930 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:43:51,049 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:43:51,380 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:43:51,421 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-22 10:43:51,506 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4461740962087393281.jar
2021-03-22 10:43:51,507 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent4461740962087393281.jar
2021-03-22 10:43:51,522 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:43:51,968 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:43:51,981 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:43:51,982 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:43:51,988 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:43:51,994 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:43:51,995 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:43:54,008 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:43:54,009 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:43:54,019 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:43:54,019 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:43:54,019 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:43:54,022 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:43:54,023 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:43:54,026 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:43:54,027 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:43:54,030 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:43:54,310 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:43:54,600 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:43:54,616 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:43:54,633 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:43:54,786 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:43:54,832 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:43:54,839 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:43:54,843 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:43:54,844 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:43:54,848 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:43:54,848 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:43:54,853 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:43:54,884 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:43:54,943 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:43:54,971 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:43:55,090 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:43:55,130 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:43:55,145 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:43:55,194 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:43:55,729 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:43:55,732 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:43:55,788 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:43:55,790 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:43:55,844 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:43:55,849 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:43:55,853 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:43:55,862 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:43:55,871 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fccbc5b7-43a3-4a78-93b9-b9f1f53f8a4b,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:43:55,871 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fccbc5b7-43a3-4a78-93b9-b9f1f53f8a4b,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:43:55,879 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:43:55,879 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:43:55,883 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:43:55,915 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:43:55,926 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=fccbc5b7-43a3-4a78-93b9-b9f1f53f8a4b,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:43:55,927 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:43:55,929 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:43:55,930 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:43:55,930 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:43:55,955 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:43:55,955 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:43:56,027 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:43:56,274 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:43:56,493 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:43:56,494 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:43:56,494 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:43:56,495 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:43:56,495 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:43:56,552 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:43:59,163 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:43:59,355 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:43:59,356 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:43:59,359 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:43:59,359 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:43:59,359 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:43:59,399 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:43:59,405 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:43:59,575 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:44:00,747 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:44:11,902 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-22 10:44:14,476 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:44:14,479 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:44:14,479 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:44:15,677 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:44:15,677 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:44:16,989 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:44:18,145 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:44:18,689 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:44:18,820 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:44:18,822 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:44:19,878 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:44:19,897 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:44:21,566 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:44:21,566 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:44:21,569 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:44:21,569 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:44:21,692 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:44:23,108 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:44:23,126 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:44:23,126 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:44:23,129 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:44:23,132 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:44:23,133 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:44:25,137 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:44:25,137 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:44:25,159 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:44:25,160 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:44:25,160 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:44:25,164 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:44:25,164 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:44:25,169 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:44:25,169 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:44:25,174 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:44:25,510 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:44:25,787 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:44:25,803 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:44:25,818 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:44:25,819 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-22 10:44:26,001 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:44:26,084 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:44:26,089 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:44:26,091 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:44:26,092 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:44:26,093 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:44:26,094 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:44:26,095 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:44:26,191 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:44:26,308 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:44:26,343 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:44:26,388 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:44:26,422 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:44:26,445 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:44:26,516 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:44:27,251 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:44:27,338 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:44:27,344 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:44:27,423 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:44:27,569 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:44:28,127 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:44:28,136 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:44:28,171 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:44:28,172 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:44:28,172 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:44:28,174 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:44:28,201 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:44:28,206 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=169eaa9c-1418-4e7e-bd7f-8a774e9ae78d,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:44:28,206 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=169eaa9c-1418-4e7e-bd7f-8a774e9ae78d,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:44:28,270 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=169eaa9c-1418-4e7e-bd7f-8a774e9ae78d,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:44:28,270 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:44:28,761 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:44:28,763 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:44:28,763 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:44:28,791 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:44:28,791 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:44:28,791 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:44:28,835 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:44:29,126 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:44:29,127 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:44:29,127 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:44:29,127 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:44:29,127 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:44:29,153 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:44:29,209 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:44:29,278 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:44:29,286 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:44:29,389 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:44:29,392 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:44:29,449 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:44:29,452 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:44:29,452 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:44:29,539 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:44:32,709 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:44:37,297 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:37,330 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:37,407 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:37,417 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:37,452 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-22 10:44:37,456 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs, /weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-22 10:44:37,457 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs, /weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs, /weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-22 10:44:38,239 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:55,266 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:56,298 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:56,410 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:56,411 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:56,418 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:56,547 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-22 10:44:58,434 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:44:58,434 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:46:36,119 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:47:39,172 ERROR com.api.presettle.util.Prop  - not found file <D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\WEB-INF/prop/presettle.properties>
2021-03-22 10:49:11,313 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2021-03-22 10:49:13,456 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:49:13,463 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:49:13,463 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:49:14,237 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:49:14,237 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:49:14,942 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:49:15,815 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:49:16,132 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:49:16,305 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:49:16,307 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:49:18,755 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:49:18,790 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2021-03-22 10:49:18,834 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent12079010184179730.jar
2021-03-22 10:49:18,834 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent12079010184179730.jar
2021-03-22 10:49:18,844 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:49:19,061 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:49:19,061 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:49:19,063 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:49:19,064 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:49:19,165 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:49:20,061 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:49:20,068 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:49:20,069 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:49:20,072 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:49:20,074 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:49:20,075 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:49:22,078 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:49:22,078 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:49:22,089 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:49:22,089 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:49:22,089 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:49:22,092 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:49:22,093 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:49:22,095 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:49:22,095 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:49:22,097 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:49:22,442 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:49:22,664 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:49:22,678 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:49:22,691 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:49:22,817 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:49:22,857 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:49:22,861 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:49:22,866 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:49:22,867 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:49:22,870 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:49:22,871 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:49:22,874 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:49:22,903 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:49:22,933 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:49:22,953 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:49:22,992 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:49:23,029 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:49:23,041 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:49:23,089 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:49:23,314 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:49:23,341 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:49:23,361 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:49:23,363 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:49:23,524 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:49:23,555 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:49:23,556 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:49:23,556 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:49:23,575 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:49:23,575 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:49:23,651 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:49:23,853 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:49:23,853 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:49:23,854 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:49:23,854 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:49:23,854 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:49:23,869 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:49:23,878 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:49:23,878 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:49:23,882 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:49:23,893 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=06df0147-4f2d-40e6-9d28-feccdf61f0a6,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:49:23,893 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=06df0147-4f2d-40e6-9d28-feccdf61f0a6,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:49:23,895 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:49:23,899 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:49:23,902 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:49:23,904 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:49:23,968 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=06df0147-4f2d-40e6-9d28-feccdf61f0a6,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:49:23,969 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:49:24,280 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:49:24,470 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:49:24,471 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:49:24,561 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:49:24,564 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:49:25,752 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:49:25,895 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:49:26,030 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:49:26,031 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:49:26,031 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:49:26,297 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:49:36,949 ERROR weaver.session.util.RedisTemplate  - ���redis���ӳ�״̬�쳣
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2021-03-22 10:49:36,954 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2021-03-22 10:49:36,955 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2021-03-22 10:49:37,811 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:49:37,811 INFO  weaver.general.InitServer  - init ioc container...
2021-03-22 10:49:38,669 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:49:39,588 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2021-03-22 10:49:39,964 ERROR weaver.general.BaseBean  - ------�Ƿ����ַ�ת��----0---�Ƿ���sql����������-----0----nativepool----ecology
2021-03-22 10:49:40,060 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2021-03-22 10:49:40,062 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2021-03-22 10:49:42,122 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2021-03-22 10:49:42,122 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\upgrade\src
2021-03-22 10:49:42,124 ERROR com.weaver.upgrade.FunctionUpgrade  - ����Ip++++++++++��*************
2021-03-22 10:49:42,124 ERROR com.weaver.upgrade.FunctionUpgrade  - ���ڵ�MainControlIP++++++++++��
2021-03-22 10:49:42,190 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2021-03-22 10:49:42,207 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2021-03-22 10:49:42,227 ERROR weaver.general.BaseBean  - ������־ѹ���߳�....
2021-03-22 10:49:43,145 INFO  weaver.general.InitServer  - end ioc container init...
2021-03-22 10:49:43,152 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2021-03-22 10:49:43,152 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2021-03-22 10:49:43,155 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2021-03-22 10:49:43,158 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2021-03-22 10:49:43,159 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2021-03-22 10:49:45,168 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2021-03-22 10:49:45,168 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2021-03-22 10:49:45,178 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2021-03-22 10:49:45,178 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2021-03-22 10:49:45,178 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2021-03-22 10:49:45,180 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2021-03-22 10:49:45,180 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2021-03-22 10:49:45,183 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2021-03-22 10:49:45,183 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2021-03-22 10:49:45,186 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2021-03-22 10:49:45,521 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ...
2021-03-22 10:49:45,715 ERROR weaver.system.SystemThreadManager  - �Ƿ���ϵͳ��ʱ��true
2021-03-22 10:49:45,732 ERROR weaver.general.BaseBean  - ��ʱ�ж����ڵ�
2021-03-22 10:49:45,745 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2021-03-22 10:49:45,745 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is exist. Don't reload.
2021-03-22 10:49:45,863 INFO  weaver.general.InitServer  - ESB INIT Start.....
2021-03-22 10:49:45,911 INFO  weaver.general.InitServer  - ESB INIT End.....
2021-03-22 10:49:45,916 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2021-03-22 10:49:45,920 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2021-03-22 10:49:45,920 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2021-03-22 10:49:45,922 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2021-03-22 10:49:45,923 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2021-03-22 10:49:45,925 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2021-03-22 10:49:45,950 INFO  weaver.general.InitServer  - end.....
2021-03-22 10:49:45,992 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2021-03-22 10:49:46,013 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2021-03-22 10:49:46,049 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2021-03-22 10:49:46,086 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2021-03-22 10:49:46,098 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2021-03-22 10:49:46,138 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2021-03-22 10:49:46,484 ERROR weaver.page.PageManager  - ��ն�ά��ɨ�軺���¼��
2021-03-22 10:49:46,536 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2021-03-22 10:49:46,537 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2021-03-22 10:49:46,614 ERROR weaver.conn.RecordSet  - ���������ĵ���Ӧ��ϵ���Ѿ���ʼ��������Ҫ�ٴγ�ʼ����
2021-03-22 10:49:46,913 ERROR weaver.conn.RecordSet  - ����ǰ��JSʹ�õ�label��Ϣ�ɹ�...
2021-03-22 10:49:46,921 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:49:46,924 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ��ʼ ...
2021-03-22 10:49:46,928 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ��ʼ ...
2021-03-22 10:49:46,928 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e244fa59-e490-4574-aa22-70c84dc19ec6,��ʼ�ʼ��ڲ��ռ�������
2021-03-22 10:49:46,928 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e244fa59-e490-4574-aa22-70c84dc19ec6,-> ########## ִ�м�ʱ��ʼ ##########
2021-03-22 10:49:46,933 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ��ʼ...
2021-03-22 10:49:46,945 ERROR weaver.general.BaseBean  - ����ģ�� ������ִ�� VotingInitUpgradeThread ���� ...
2021-03-22 10:49:46,949 ERROR weaver.general.BaseBean  - ��ȡ��ά��Ԥ����޸���Ϣ����...
2021-03-22 10:49:46,952 ERROR weaver.general.BaseBean  - ����Ԥ��ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:49:46,960 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, ************, ***************]
2021-03-22 10:49:46,961 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������redis_flag======false
2021-03-22 10:49:46,961 ERROR weaver.general.BaseBean  - ��ʱ�ж��Ƿ�Ϊ��Ⱥ������mainIp_flag=======false
2021-03-22 10:49:46,981 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 10:49:46,981 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 10:49:47,000 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=e244fa59-e490-4574-aa22-70c84dc19ec6,��������ɣ�����Ҫ�ٴ�������needExecuteUpgrade=false
2021-03-22 10:49:47,001 ERROR weaver.general.BaseBean  - �ʼ�ģ�� ������ִ�� InitEmailUpgradeThread ���� ...
2021-03-22 10:49:47,002 ERROR weaver.general.BaseBean  - ������ʱ����
2021-03-22 10:49:47,089 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2021-03-22 10:49:47,089 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy��->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2021-03-22 10:49:47,089 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2021-03-22 10:49:47,089 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target��com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy��->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target��com.api.common.cmd.login.DoUserSessionCmd
>> proxy��->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target��com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy��->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target��com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy��->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2021-03-22 10:49:47,089 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2021-03-22 10:49:47,099 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ѯoperator��sql��SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2021-03-22 10:49:47,143 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2021-03-22 10:49:47,144 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2021-03-22 10:49:47,178 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\WorkSpace\Idea\IdeaProjects\WEVAER\ecology\
2021-03-22 10:49:47,180 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2021-03-22 10:49:47,281 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2021-03-22 10:49:47,390 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,391 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,393 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,395 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,396 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,397 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:47,399 ERROR com.engine.kq.biz.KQWorkTime  - com.engine.kq.biz.KQWorkTime
java.lang.NullPointerException
	at com.engine.kq.biz.KQShiftManagementComInfo.<init>(KQShiftManagementComInfo.java:65)
	at com.engine.kq.biz.KQWorkTime.getWorkDuration(KQWorkTime.java:89)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:116)
	at com.engine.kq.util.KQDurationCalculatorUtil.getWorkTime(KQDurationCalculatorUtil.java:95)
	at com.engine.hrm.biz.ScheduleUtil4Flow.handleNewKQ(ScheduleUtil4Flow.java:257)
	at com.engine.hrm.biz.ScheduleUtil4Flow.workingTimeAdd(ScheduleUtil4Flow.java:152)
	at weaver.workflow.request.OverTimeSetBean.getOverTime(OverTimeSetBean.java:398)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:235)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 10:49:48,191 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>������Ԥ���رտ��ƣ�isThreadWarningStart(zt)=0<<<<
2021-03-22 10:49:48,953 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�start
2021-03-22 10:49:49,149 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�sql��UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2020-09-23' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2020-09-23') or  lastLoginDate<'2020-09-23')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2021-03-22 10:49:49,149 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺��Ƿ�ִ�гɹ���true
2021-03-22 10:49:49,149 ERROR weaver.general.BaseBean  - ��ʱ��δ��¼�����˺�end
2021-03-22 10:49:49,185 ERROR weaver.general.BaseBean  - �������ڵ���Ա�Զ������Ƿ�ɹ���true
2021-03-22 10:49:52,479 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:49:52,569 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2021-03-22 10:49:52,581 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:49:53,658 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:12,827 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:12,829 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:12,827 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:12,835 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:13,103 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:14,900 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:14,909 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:14,947 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:14,987 ERROR weaver.general.BaseBean  - qrcode_config>>>
2021-03-22 10:50:15,694 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:17,605 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:50:30,250 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:51:48,773 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:52:02,270 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:53:46,993 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:54:42,829 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:55:19,720 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:55:27,507 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:55:29,656 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:55:37,253 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:55:56,477 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:56:01,046 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:56:10,430 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:56:25,139 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:56:54,393 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:57:07,499 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:57:15,302 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:57:39,204 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:58:04,328 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:58:45,596 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 10:58:49,850 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2021-03-22 10:59:30,106 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.lang.NullPointerException
	at weaver.interfaces.schedule.WeaverJob.execute(WeaverJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2021-03-22 11:01:13,867 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - ��ʱ��ɨ�����ʱ��������523
2021-03-22 11:01:13,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 09:54:50
2021-03-22 11:01:13,896 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid489684   ����ʱ��㣺2028-12-27 10:54:50
2021-03-22 11:01:13,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 09:08:04
2021-03-22 11:01:13,930 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329126   ����ʱ��㣺2029-03-13 10:08:04
2021-03-22 11:01:13,960 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 13:31:03
2021-03-22 11:01:13,960 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564397   ����ʱ��㣺2029-03-06 14:31:03
2021-03-22 11:01:13,983 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-29 16:00:57
2021-03-22 11:01:13,983 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492585   ����ʱ��㣺2028-12-30 11:22:57
2021-03-22 11:01:14,007 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 16:00:22
2021-03-22 11:01:14,007 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492792   ����ʱ��㣺2029-01-08 17:29:22
2021-03-22 11:01:14,033 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-26 16:00:57
2021-03-22 11:01:14,033 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557405   ����ʱ��㣺2029-01-27 14:21:57
2021-03-22 11:01:14,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-29 16:00:30
2021-03-22 11:01:14,063 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492624   ����ʱ��㣺2028-12-30 10:57:30
2021-03-22 11:01:14,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-02 16:00:49
2021-03-22 11:01:14,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710318   ����ʱ��㣺2029-03-03 14:31:49
2021-03-22 11:01:14,112 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 12:26:10
2021-03-22 11:01:14,112 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689688   ����ʱ��㣺2029-02-06 13:26:10
2021-03-22 11:01:14,135 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 13:20:04
2021-03-22 11:01:14,136 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703571   ����ʱ��㣺2029-02-19 14:20:04
2021-03-22 11:01:14,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 14:34:40
2021-03-22 11:01:14,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301373   ����ʱ��㣺2029-02-26 15:34:40
2021-03-22 11:01:14,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-01 16:00:43
2021-03-22 11:01:14,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483349   ����ʱ��㣺2028-12-03 14:28:43
2021-03-22 11:01:14,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 13:49:06
2021-03-22 11:01:14,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494906   ����ʱ��㣺2029-01-16 14:49:06
2021-03-22 11:01:14,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469810   ����ʱ��㣺2028-10-13 15:04:25
2021-03-22 11:01:14,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-01 16:00:24
2021-03-22 11:01:14,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479812   ����ʱ��㣺2028-12-02 10:29:24
2021-03-22 11:01:14,292 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469839   ����ʱ��㣺2028-10-13 15:04:05
2021-03-22 11:01:14,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 14:38:05
2021-03-22 11:01:14,317 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497797   ����ʱ��㣺2029-01-16 15:38:05
2021-03-22 11:01:14,342 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 13:49:03
2021-03-22 11:01:14,343 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677545   ����ʱ��㣺2029-02-02 14:49:03
2021-03-22 11:01:14,368 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-02 16:00:46
2021-03-22 11:01:14,368 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305351   ����ʱ��㣺2029-03-03 09:53:46
2021-03-22 11:01:14,391 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477586   ����ʱ��㣺2028-11-06 16:17:45
2021-03-22 11:01:14,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 13:49:43
2021-03-22 11:01:14,413 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677529   ����ʱ��㣺2029-02-02 14:49:43
2021-03-22 11:01:14,439 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 13:02:04
2021-03-22 11:01:14,439 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690546   ����ʱ��㣺2029-02-05 14:02:04
2021-03-22 11:01:14,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 09:40:10
2021-03-22 11:01:14,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481169   ����ʱ��㣺2028-11-20 10:40:10
2021-03-22 11:01:14,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-09 16:00:22
2021-03-22 11:01:14,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323305   ����ʱ��㣺2029-03-11 10:37:22
2021-03-22 11:01:14,586 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 10:18:00
2021-03-22 11:01:14,586 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478632   ����ʱ��㣺2028-11-03 11:18:00
2021-03-22 11:01:14,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 13:26:54
2021-03-22 11:01:14,612 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703580   ����ʱ��㣺2029-02-23 14:26:54
2021-03-22 11:01:14,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 13:31:07
2021-03-22 11:01:14,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564399   ����ʱ��㣺2029-03-06 14:31:07
2021-03-22 11:01:14,662 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-02 16:00:33
2021-03-22 11:01:14,662 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305260   ����ʱ��㣺2029-03-04 13:57:33
2021-03-22 11:01:14,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 13:48:56
2021-03-22 11:01:14,696 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677538   ����ʱ��㣺2029-02-02 14:48:56
2021-03-22 11:01:14,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 12:38:32
2021-03-22 11:01:14,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491585   ����ʱ��㣺2029-01-05 13:38:32
2021-03-22 11:01:14,755 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469812   ����ʱ��㣺2028-10-13 15:04:27
2021-03-22 11:01:14,784 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 09:07:55
2021-03-22 11:01:14,784 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314257   ����ʱ��㣺2029-03-13 10:07:55
2021-03-22 11:01:14,809 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 13:29:53
2021-03-22 11:01:14,810 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314198   ����ʱ��㣺2029-03-06 14:29:53
2021-03-22 11:01:14,836 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 09:10:21
2021-03-22 11:01:14,836 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602453   ����ʱ��㣺2029-02-02 10:10:21
2021-03-22 11:01:14,860 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 13:30:13
2021-03-22 11:01:14,861 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305160   ����ʱ��㣺2029-03-06 14:30:13
2021-03-22 11:01:14,891 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477330   ����ʱ��㣺2028-10-31 10:22:08
2021-03-22 11:01:14,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 13:03:26
2021-03-22 11:01:14,916 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494656   ����ʱ��㣺2029-01-09 14:03:26
2021-03-22 11:01:14,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid460447   ����ʱ��㣺2028-09-08 11:13:17
2021-03-22 11:01:14,974 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 10:10:50
2021-03-22 11:01:14,974 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494608   ����ʱ��㣺2029-01-09 11:10:50
2021-03-22 11:01:15,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 10:20:43
2021-03-22 11:01:15,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690580   ����ʱ��㣺2029-02-09 11:20:43
2021-03-22 11:01:15,048 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 13:20:30
2021-03-22 11:01:15,049 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677550   ����ʱ��㣺2029-02-19 14:20:30
2021-03-22 11:01:15,076 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-29 16:00:18
2021-03-22 11:01:15,076 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492622   ����ʱ��㣺2028-12-30 10:57:18
2021-03-22 11:01:15,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 13:20:07
2021-03-22 11:01:15,110 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703612   ����ʱ��㣺2029-02-19 14:20:07
2021-03-22 11:01:15,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 13:48:04
2021-03-22 11:01:15,150 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494908   ����ʱ��㣺2029-01-16 14:48:04
2021-03-22 11:01:15,189 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 13:48:46
2021-03-22 11:01:15,189 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677543   ����ʱ��㣺2029-02-02 14:48:46
2021-03-22 11:01:15,224 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 14:34:10
2021-03-22 11:01:15,226 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301366   ����ʱ��㣺2029-02-26 15:34:10
2021-03-22 11:01:15,264 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-11 16:01:33
2021-03-22 11:01:15,265 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496158   ����ʱ��㣺2029-01-12 09:06:33
2021-03-22 11:01:15,292 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-26 16:00:51
2021-03-22 11:01:15,292 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557403   ����ʱ��㣺2029-01-27 14:22:51
2021-03-22 11:01:15,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 12:38:57
2021-03-22 11:01:15,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491631   ����ʱ��㣺2029-01-05 13:38:57
2021-03-22 11:01:15,338 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-09 16:00:13
2021-03-22 11:01:15,339 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47303189   ����ʱ��㣺2029-03-10 15:15:13
2021-03-22 11:01:15,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-02 16:00:18
2021-03-22 11:01:15,363 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710316   ����ʱ��㣺2029-03-03 14:31:18
2021-03-22 11:01:15,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-09 16:00:17
2021-03-22 11:01:15,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692660   ����ʱ��㣺2029-02-11 12:11:17
2021-03-22 11:01:15,437 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 13:02:07
2021-03-22 11:01:15,437 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690548   ����ʱ��㣺2029-02-05 14:02:07
2021-03-22 11:01:15,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-24 16:00:50
2021-03-22 11:01:15,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479810   ����ʱ��㣺2028-11-26 15:45:50
2021-03-22 11:01:15,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 13:49:25
2021-03-22 11:01:15,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677518   ����ʱ��㣺2029-02-02 14:49:25
2021-03-22 11:01:15,532 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472745   ����ʱ��㣺2028-10-09 15:00:23
2021-03-22 11:01:15,562 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-23 16:00:32
2021-03-22 11:01:15,562 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710041   ����ʱ��㣺2029-02-24 13:25:32
2021-03-22 11:01:15,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 13:30:30
2021-03-22 11:01:15,587 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305189   ����ʱ��㣺2029-03-06 14:30:30
2021-03-22 11:01:15,616 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 15:10:40
2021-03-22 11:01:15,616 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494815   ����ʱ��㣺2029-01-16 16:10:40
2021-03-22 11:01:15,639 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 13:59:01
2021-03-22 11:01:15,639 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479839   ����ʱ��㣺2028-12-04 14:59:01
2021-03-22 11:01:15,664 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 13:49:32
2021-03-22 11:01:15,664 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677525   ����ʱ��㣺2029-02-02 14:49:32
2021-03-22 11:01:15,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 13:24:41
2021-03-22 11:01:15,689 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704819   ����ʱ��㣺2029-02-19 14:24:41
2021-03-22 11:01:15,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 12:55:57
2021-03-22 11:01:15,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689832   ����ʱ��㣺2029-02-09 13:55:57
2021-03-22 11:01:15,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-09 16:00:02
2021-03-22 11:01:15,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323141   ����ʱ��㣺2029-03-10 16:36:02
2021-03-22 11:01:15,767 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471999   ����ʱ��㣺2028-12-02 14:56:44
2021-03-22 11:01:15,792 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-26 16:00:21
2021-03-22 11:01:15,792 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598427   ����ʱ��㣺2029-01-28 16:03:21
2021-03-22 11:01:15,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 13:29:36
2021-03-22 11:01:15,822 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314244   ����ʱ��㣺2029-03-06 14:29:36
2021-03-22 11:01:15,846 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-12 16:00:22
2021-03-22 11:01:15,846 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494740   ����ʱ��㣺2029-01-13 10:29:22
2021-03-22 11:01:15,874 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 15:11:21
2021-03-22 11:01:15,874 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494817   ����ʱ��㣺2029-01-16 16:11:21
2021-03-22 11:01:15,908 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 13:02:00
2021-03-22 11:01:15,908 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690544   ����ʱ��㣺2029-02-05 14:02:00
2021-03-22 11:01:15,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 12:57:01
2021-03-22 11:01:15,941 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689816   ����ʱ��㣺2029-02-09 13:57:01
2021-03-22 11:01:15,968 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 15:17:53
2021-03-22 11:01:15,969 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477588   ����ʱ��㣺2028-11-06 16:17:53
2021-03-22 11:01:15,998 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 14:06:00
2021-03-22 11:01:15,998 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479239   ����ʱ��㣺2028-11-13 15:06:00
2021-03-22 11:01:16,026 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-19 16:00:40
2021-03-22 11:01:16,026 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521350   ����ʱ��㣺2029-01-21 15:58:40
2021-03-22 11:01:16,055 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-09 16:00:51
2021-03-22 11:01:16,055 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323348   ����ʱ��㣺2029-03-10 15:40:51
2021-03-22 11:01:16,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-02 16:00:40
2021-03-22 11:01:16,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305262   ����ʱ��㣺2029-03-04 13:57:40
2021-03-22 11:01:16,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 12:07:36
2021-03-22 11:01:16,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480015   ����ʱ��㣺2029-01-09 13:07:36
2021-03-22 11:01:16,146 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-09 16:00:51
2021-03-22 11:01:16,146 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690066   ����ʱ��㣺2029-02-10 14:18:51
2021-03-22 11:01:16,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-03 16:43:37
2021-03-22 11:01:16,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478857   ����ʱ��㣺2028-11-06 09:48:37
2021-03-22 11:01:16,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 13:46:51
2021-03-22 11:01:16,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491490   ����ʱ��㣺2028-12-29 14:46:51
2021-03-22 11:01:16,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-29 16:00:36
2021-03-22 11:01:16,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492620   ����ʱ��㣺2028-12-30 10:56:36
2021-03-22 11:01:16,260 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-12 16:00:03
2021-03-22 11:01:16,261 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497418   ����ʱ��㣺2029-01-14 15:58:03
2021-03-22 11:01:16,290 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469807   ����ʱ��㣺2028-10-13 15:04:21
2021-03-22 11:01:16,320 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474250   ����ʱ��㣺2028-10-20 14:27:17
2021-03-22 11:01:16,349 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-09 16:00:48
2021-03-22 11:01:16,349 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324491   ����ʱ��㣺2029-03-10 15:14:48
2021-03-22 11:01:16,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 15:15:47
2021-03-22 11:01:16,374 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698543   ����ʱ��㣺2029-02-16 16:15:47
2021-03-22 11:01:16,401 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 13:31:15
2021-03-22 11:01:16,401 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564393   ����ʱ��㣺2029-03-06 14:31:15
2021-03-22 11:01:16,428 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 13:49:23
2021-03-22 11:01:16,428 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677516   ����ʱ��㣺2029-02-02 14:49:23
2021-03-22 11:01:16,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 15:33:49
2021-03-22 11:01:16,455 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689686   ����ʱ��㣺2029-02-05 16:33:49
2021-03-22 11:01:16,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 10:09:27
2021-03-22 11:01:16,486 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494617   ����ʱ��㣺2029-01-09 11:09:27
2021-03-22 11:01:16,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 12:47:33
2021-03-22 11:01:16,520 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481394   ����ʱ��㣺2028-11-20 13:47:33
2021-03-22 11:01:16,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469835   ����ʱ��㣺2028-10-13 15:04:03
2021-03-22 11:01:16,572 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 15:57:16
2021-03-22 11:01:16,572 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301193   ����ʱ��㣺2029-03-09 16:57:16
2021-03-22 11:01:16,599 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 13:56:26
2021-03-22 11:01:16,599 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306135   ����ʱ��㣺2029-02-27 14:56:26
2021-03-22 11:01:16,628 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-08 16:30:09
2021-03-22 11:01:16,628 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495870   ����ʱ��㣺2029-01-09 09:35:09
2021-03-22 11:01:16,655 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 15:52:54
2021-03-22 11:01:16,656 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321173   ����ʱ��㣺2029-03-05 16:52:54
2021-03-22 11:01:16,686 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 09:21:58
2021-03-22 11:01:16,686 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47329124   ����ʱ��㣺2029-03-12 10:21:58
2021-03-22 11:01:16,713 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 13:30:45
2021-03-22 11:01:16,713 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703587   ����ʱ��㣺2029-03-06 14:30:45
2021-03-22 11:01:16,748 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 14:21:36
2021-03-22 11:01:16,748 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692657   ����ʱ��㣺2029-02-12 15:21:36
2021-03-22 11:01:16,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 15:34:57
2021-03-22 11:01:16,772 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710243   ����ʱ��㣺2029-02-26 16:34:57
2021-03-22 11:01:16,803 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471978   ����ʱ��㣺2028-12-02 14:56:28
2021-03-22 11:01:16,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 10:14:12
2021-03-22 11:01:16,830 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494611   ����ʱ��㣺2029-01-09 11:14:12
2021-03-22 11:01:16,853 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-15 16:00:49
2021-03-22 11:01:16,853 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484492   ����ʱ��㣺2028-12-16 14:21:49
2021-03-22 11:01:16,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 13:08:32
2021-03-22 11:01:16,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480092   ����ʱ��㣺2028-12-15 14:08:32
2021-03-22 11:01:16,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-29 16:00:56
2021-03-22 11:01:16,907 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492619   ����ʱ��㣺2028-12-30 10:56:56
2021-03-22 11:01:16,935 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 13:30:11
2021-03-22 11:01:16,935 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305164   ����ʱ��㣺2029-03-06 14:30:11
2021-03-22 11:01:16,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 13:58:51
2021-03-22 11:01:16,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707720   ����ʱ��㣺2029-02-23 14:58:51
2021-03-22 11:01:16,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 10:26:55
2021-03-22 11:01:16,995 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321227   ����ʱ��㣺2029-03-05 11:26:55
2021-03-22 11:01:17,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 13:11:23
2021-03-22 11:01:17,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697552   ����ʱ��㣺2029-02-16 14:11:23
2021-03-22 11:01:17,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 10:20:51
2021-03-22 11:01:17,058 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690577   ����ʱ��㣺2029-02-09 11:20:51
2021-03-22 11:01:17,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 14:20:14
2021-03-22 11:01:17,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710312   ����ʱ��㣺2029-03-02 15:20:14
2021-03-22 11:01:17,113 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 13:29:54
2021-03-22 11:01:17,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314194   ����ʱ��㣺2029-03-06 14:29:54
2021-03-22 11:01:17,155 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-01 16:00:26
2021-03-22 11:01:17,158 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479814   ����ʱ��㣺2028-12-02 10:29:26
2021-03-22 11:01:17,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 12:52:17
2021-03-22 11:01:17,192 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481835   ����ʱ��㣺2028-12-05 13:52:17
2021-03-22 11:01:17,228 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 14:17:30
2021-03-22 11:01:17,230 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695658   ����ʱ��㣺2029-02-16 15:17:30
2021-03-22 11:01:17,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 15:27:45
2021-03-22 11:01:17,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498029   ����ʱ��㣺2029-01-19 16:27:45
2021-03-22 11:01:17,301 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474249   ����ʱ��㣺2028-10-20 14:27:20
2021-03-22 11:01:17,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-12 16:00:19
2021-03-22 11:01:17,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497425   ����ʱ��㣺2029-01-14 15:58:19
2021-03-22 11:01:17,361 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469814   ����ʱ��㣺2028-10-13 15:04:24
2021-03-22 11:01:17,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 09:40:25
2021-03-22 11:01:17,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481296   ����ʱ��㣺2028-11-20 10:40:25
2021-03-22 11:01:17,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-07 16:31:20
2021-03-22 11:01:17,442 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483699   ����ʱ��㣺2028-12-08 09:36:20
2021-03-22 11:01:17,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 13:48:08
2021-03-22 11:01:17,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484429   ����ʱ��㣺2028-12-12 14:48:08
2021-03-22 11:01:17,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 13:49:11
2021-03-22 11:01:17,497 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677511   ����ʱ��㣺2029-02-02 14:49:11
2021-03-22 11:01:17,526 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 15:21:48
2021-03-22 11:01:17,526 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481599   ����ʱ��㣺2028-11-27 16:21:48
2021-03-22 11:01:17,554 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 13:49:02
2021-03-22 11:01:17,554 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677506   ����ʱ��㣺2029-02-02 14:49:02
2021-03-22 11:01:17,580 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469817   ����ʱ��㣺2028-10-13 15:04:17
2021-03-22 11:01:17,604 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 13:06:24
2021-03-22 11:01:17,604 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481338   ����ʱ��㣺2028-11-24 14:06:24
2021-03-22 11:01:17,684 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 13:58:03
2021-03-22 11:01:17,684 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688607   ����ʱ��㣺2029-02-09 14:58:03
2021-03-22 11:01:17,708 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 15:52:57
2021-03-22 11:01:17,708 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321171   ����ʱ��㣺2029-03-05 16:52:57
2021-03-22 11:01:17,729 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474275   ����ʱ��㣺2028-10-20 14:27:04
2021-03-22 11:01:17,755 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 13:20:00
2021-03-22 11:01:17,756 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703609   ����ʱ��㣺2029-02-19 14:20:00
2021-03-22 11:01:17,778 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-02 16:00:35
2021-03-22 11:01:17,778 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680522   ����ʱ��㣺2029-02-04 14:03:35
2021-03-22 11:01:17,802 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-08 16:28:39
2021-03-22 11:01:17,802 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495994   ����ʱ��㣺2029-01-09 09:33:39
2021-03-22 11:01:17,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 13:20:15
2021-03-22 11:01:17,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703578   ����ʱ��㣺2029-02-19 14:20:15
2021-03-22 11:01:17,850 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 13:26:51
2021-03-22 11:01:17,850 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706665   ����ʱ��㣺2029-02-23 14:26:51
2021-03-22 11:01:17,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 13:44:34
2021-03-22 11:01:17,877 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704731   ����ʱ��㣺2029-02-23 14:44:34
2021-03-22 11:01:17,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 14:27:37
2021-03-22 11:01:17,905 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324188   ����ʱ��㣺2029-03-06 15:27:37
2021-03-22 11:01:17,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 15:52:43
2021-03-22 11:01:17,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306156   ����ʱ��㣺2029-02-27 16:52:43
2021-03-22 11:01:17,962 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 12:57:47
2021-03-22 11:01:17,963 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689812   ����ʱ��㣺2029-02-09 13:57:47
2021-03-22 11:01:17,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-08 16:27:42
2021-03-22 11:01:17,992 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495878   ����ʱ��㣺2029-01-09 09:32:42
2021-03-22 11:01:18,019 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 13:20:09
2021-03-22 11:01:18,020 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703594   ����ʱ��㣺2029-02-19 14:20:09
2021-03-22 11:01:18,046 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 13:56:28
2021-03-22 11:01:18,046 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688606   ����ʱ��㣺2029-02-09 14:56:28
2021-03-22 11:01:18,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-02 16:00:27
2021-03-22 11:01:18,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid680508   ����ʱ��㣺2029-02-04 10:17:27
2021-03-22 11:01:18,107 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 13:30:54
2021-03-22 11:01:18,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600437   ����ʱ��㣺2029-03-06 14:30:54
2021-03-22 11:01:18,134 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-12 16:00:52
2021-03-22 11:01:18,134 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497384   ����ʱ��㣺2029-01-13 14:54:52
2021-03-22 11:01:18,163 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469832   ����ʱ��㣺2028-10-13 15:04:11
2021-03-22 11:01:18,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-01 16:00:38
2021-03-22 11:01:18,188 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479819   ����ʱ��㣺2028-12-02 10:29:38
2021-03-22 11:01:18,214 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 13:49:05
2021-03-22 11:01:18,214 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677507   ����ʱ��㣺2029-02-02 14:49:05
2021-03-22 11:01:18,237 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-09 16:00:28
2021-03-22 11:01:18,237 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323238   ����ʱ��㣺2029-03-10 09:47:28
2021-03-22 11:01:18,261 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-26 16:35:46
2021-03-22 11:01:18,261 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301387   ����ʱ��㣺2029-02-27 09:40:46
2021-03-22 11:01:18,284 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 12:21:39
2021-03-22 11:01:18,284 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495127   ����ʱ��㣺2029-01-22 13:21:39
2021-03-22 11:01:18,311 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 12:37:10
2021-03-22 11:01:18,311 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495893   ����ʱ��㣺2029-01-09 13:37:10
2021-03-22 11:01:18,341 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469816   ����ʱ��㣺2028-10-13 15:04:22
2021-03-22 11:01:18,369 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 12:54:26
2021-03-22 11:01:18,370 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689830   ����ʱ��㣺2029-02-09 13:54:26
2021-03-22 11:01:18,396 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 13:49:07
2021-03-22 11:01:18,396 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677508   ����ʱ��㣺2029-02-02 14:49:07
2021-03-22 11:01:18,420 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 13:56:31
2021-03-22 11:01:18,420 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322111   ����ʱ��㣺2029-03-05 14:56:31
2021-03-22 11:01:18,451 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 15:56:48
2021-03-22 11:01:18,451 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301197   ����ʱ��㣺2029-03-09 16:56:48
2021-03-22 11:01:18,484 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 15:21:50
2021-03-22 11:01:18,484 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481610   ����ʱ��㣺2028-11-27 16:21:50
2021-03-22 11:01:18,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-02 16:00:55
2021-03-22 11:01:18,509 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305356   ����ʱ��㣺2029-03-03 09:53:55
2021-03-22 11:01:18,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 13:30:57
2021-03-22 11:01:18,547 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600438   ����ʱ��㣺2029-03-06 14:30:57
2021-03-22 11:01:18,572 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-09 16:00:01
2021-03-22 11:01:18,572 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323139   ����ʱ��㣺2029-03-10 16:36:01
2021-03-22 11:01:18,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 10:20:53
2021-03-22 11:01:18,597 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690576   ����ʱ��㣺2029-02-09 11:20:53
2021-03-22 11:01:18,620 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-10 16:00:40
2021-03-22 11:01:18,620 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478370   ����ʱ��㣺2028-11-12 15:26:40
2021-03-22 11:01:18,647 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 09:08:02
2021-03-22 11:01:18,647 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326122   ����ʱ��㣺2029-03-13 10:08:02
2021-03-22 11:01:18,674 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469815   ����ʱ��㣺2028-10-13 15:04:19
2021-03-22 11:01:18,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 09:07:59
2021-03-22 11:01:18,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323346   ����ʱ��㣺2029-03-13 10:07:59
2021-03-22 11:01:18,731 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 13:30:01
2021-03-22 11:01:18,731 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305159   ����ʱ��㣺2029-03-06 14:30:01
2021-03-22 11:01:18,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 13:49:29
2021-03-22 11:01:18,761 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677523   ����ʱ��㣺2029-02-02 14:49:29
2021-03-22 11:01:18,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-09 16:00:47
2021-03-22 11:01:18,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325151   ����ʱ��㣺2029-03-10 11:00:47
2021-03-22 11:01:18,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-25 16:17:38
2021-03-22 11:01:18,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid535369   ����ʱ��㣺2029-01-26 09:22:38
2021-03-22 11:01:18,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 13:48:43
2021-03-22 11:01:18,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677549   ����ʱ��㣺2029-02-02 14:48:43
2021-03-22 11:01:18,864 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-08 16:28:24
2021-03-22 11:01:18,864 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495995   ����ʱ��㣺2029-01-09 09:33:24
2021-03-22 11:01:18,894 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 13:11:20
2021-03-22 11:01:18,894 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697551   ����ʱ��㣺2029-02-16 14:11:20
2021-03-22 11:01:18,920 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 13:30:29
2021-03-22 11:01:18,920 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305191   ����ʱ��㣺2029-03-06 14:30:29
2021-03-22 11:01:18,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 15:26:30
2021-03-22 11:01:18,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498022   ����ʱ��㣺2029-01-19 16:26:30
2021-03-22 11:01:18,982 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-09 16:00:34
2021-03-22 11:01:18,982 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690068   ����ʱ��㣺2029-02-10 14:18:34
2021-03-22 11:01:19,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-08 16:45:55
2021-03-22 11:01:19,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324317   ����ʱ��㣺2029-03-09 09:50:55
2021-03-22 11:01:19,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 15:00:19
2021-03-22 11:01:19,040 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325108   ����ʱ��㣺2029-03-05 16:00:19
2021-03-22 11:01:19,068 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-12 16:00:01
2021-03-22 11:01:19,068 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494738   ����ʱ��㣺2029-01-13 10:30:01
2021-03-22 11:01:19,101 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-09 16:00:31
2021-03-22 11:01:19,101 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47331116   ����ʱ��㣺2029-03-11 15:52:31
2021-03-22 11:01:19,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 09:46:48
2021-03-22 11:01:19,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690535   ����ʱ��㣺2029-02-05 10:46:48
2021-03-22 11:01:19,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-09 16:00:05
2021-03-22 11:01:19,164 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692673   ����ʱ��㣺2029-02-11 14:40:05
2021-03-22 11:01:19,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 13:29:59
2021-03-22 11:01:19,200 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305163   ����ʱ��㣺2029-03-06 14:29:59
2021-03-22 11:01:19,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-07 16:29:42
2021-03-22 11:01:19,224 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483792   ����ʱ��㣺2028-12-08 09:34:42
2021-03-22 11:01:19,250 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-23 16:00:53
2021-03-22 11:01:19,251 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid701570   ����ʱ��㣺2029-02-24 10:26:53
2021-03-22 11:01:19,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 13:20:24
2021-03-22 11:01:19,282 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703593   ����ʱ��㣺2029-02-19 14:20:24
2021-03-22 11:01:19,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 15:52:53
2021-03-22 11:01:19,314 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306157   ����ʱ��㣺2029-02-27 16:52:53
2021-03-22 11:01:19,341 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-08 16:26:21
2021-03-22 11:01:19,341 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495895   ����ʱ��㣺2029-01-09 09:31:21
2021-03-22 11:01:19,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 13:49:00
2021-03-22 11:01:19,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677505   ����ʱ��㣺2029-02-02 14:49:00
2021-03-22 11:01:19,415 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 13:30:42
2021-03-22 11:01:19,415 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706674   ����ʱ��㣺2029-03-06 14:30:42
2021-03-22 11:01:19,443 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-26 16:00:32
2021-03-22 11:01:19,443 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557419   ����ʱ��㣺2029-01-27 13:19:32
2021-03-22 11:01:19,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-19 16:00:19
2021-03-22 11:01:19,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid521349   ����ʱ��㣺2029-01-21 15:58:19
2021-03-22 11:01:19,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 15:52:42
2021-03-22 11:01:19,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306160   ����ʱ��㣺2029-02-27 16:52:42
2021-03-22 11:01:19,527 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 13:49:40
2021-03-22 11:01:19,528 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480090   ����ʱ��㣺2028-12-15 14:49:40
2021-03-22 11:01:19,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-26 16:00:18
2021-03-22 11:01:19,556 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595463   ����ʱ��㣺2029-01-28 16:03:18
2021-03-22 11:01:19,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-02 16:00:30
2021-03-22 11:01:19,581 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301092   ����ʱ��㣺2029-03-03 15:02:30
2021-03-22 11:01:19,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 15:52:46
2021-03-22 11:01:19,618 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306159   ����ʱ��㣺2029-02-27 16:52:46
2021-03-22 11:01:19,644 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 09:08:00
2021-03-22 11:01:19,644 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326121   ����ʱ��㣺2029-03-13 10:08:00
2021-03-22 11:01:19,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-23 16:00:11
2021-03-22 11:01:19,673 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709541   ����ʱ��㣺2029-02-24 14:58:11
2021-03-22 11:01:19,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 13:31:02
2021-03-22 11:01:19,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600439   ����ʱ��㣺2029-03-06 14:31:02
2021-03-22 11:01:19,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 13:29:56
2021-03-22 11:01:19,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314190   ����ʱ��㣺2029-03-06 14:29:56
2021-03-22 11:01:19,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-05 16:00:26
2021-03-22 11:01:19,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495437   ����ʱ��㣺2029-01-06 11:03:26
2021-03-22 11:01:19,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-09 16:00:06
2021-03-22 11:01:19,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690044   ����ʱ��㣺2029-02-11 14:35:06
2021-03-22 11:01:19,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-01 16:00:30
2021-03-22 11:01:19,814 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479816   ����ʱ��㣺2028-12-02 10:29:30
2021-03-22 11:01:19,839 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477358   ����ʱ��㣺2028-11-24 14:23:41
2021-03-22 11:01:19,871 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-08 16:29:40
2021-03-22 11:01:19,871 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495997   ����ʱ��㣺2029-01-09 09:34:40
2021-03-22 11:01:19,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-09 16:00:48
2021-03-22 11:01:19,897 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692662   ����ʱ��㣺2029-02-11 12:10:48
2021-03-22 11:01:19,925 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 12:44:41
2021-03-22 11:01:19,925 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689676   ����ʱ��㣺2029-02-05 13:44:41
2021-03-22 11:01:19,952 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-09 16:00:38
2021-03-22 11:01:19,952 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326119   ����ʱ��㣺2029-03-11 15:25:38
2021-03-22 11:01:19,981 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-12 16:00:50
2021-03-22 11:01:19,981 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497627   ����ʱ��㣺2029-01-14 14:08:50
2021-03-22 11:01:20,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 13:29:37
2021-03-22 11:01:20,006 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320131   ����ʱ��㣺2029-03-06 14:29:37
2021-03-22 11:01:20,030 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 13:56:59
2021-03-22 11:01:20,030 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47322112   ����ʱ��㣺2029-03-05 14:56:59
2021-03-22 11:01:20,056 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477329   ����ʱ��㣺2028-10-31 10:21:58
2021-03-22 11:01:20,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-23 16:00:14
2021-03-22 11:01:20,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid709540   ����ʱ��㣺2029-02-24 14:58:14
2021-03-22 11:01:20,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-09 16:00:05
2021-03-22 11:01:20,116 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323140   ����ʱ��㣺2029-03-10 16:36:05
2021-03-22 11:01:20,138 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 13:48:51
2021-03-22 11:01:20,138 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677548   ����ʱ��㣺2029-02-02 14:48:51
2021-03-22 11:01:20,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469829   ����ʱ��㣺2028-10-13 15:04:09
2021-03-22 11:01:20,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 13:20:11
2021-03-22 11:01:20,196 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703591   ����ʱ��㣺2029-02-19 14:20:11
2021-03-22 11:01:20,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 14:33:47
2021-03-22 11:01:20,223 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301361   ����ʱ��㣺2029-02-26 15:33:47
2021-03-22 11:01:20,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 13:26:46
2021-03-22 11:01:20,252 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47317116   ����ʱ��㣺2029-03-05 14:26:46
2021-03-22 11:01:20,276 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477357   ����ʱ��㣺2028-10-31 10:21:35
2021-03-22 11:01:20,312 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 12:57:08
2021-03-22 11:01:20,313 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481833   ����ʱ��㣺2028-12-05 13:57:08
2021-03-22 11:01:20,355 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 12:36:45
2021-03-22 11:01:20,356 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688573   ����ʱ��㣺2029-02-05 13:36:45
2021-03-22 11:01:20,389 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 13:49:48
2021-03-22 11:01:20,390 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480088   ����ʱ��㣺2028-12-15 14:49:48
2021-03-22 11:01:20,425 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 12:57:35
2021-03-22 11:01:20,427 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689815   ����ʱ��㣺2029-02-09 13:57:35
2021-03-22 11:01:20,458 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-02 16:00:56
2021-03-22 11:01:20,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305355   ����ʱ��㣺2029-03-03 09:53:56
2021-03-22 11:01:20,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-08 16:46:11
2021-03-22 11:01:20,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324315   ����ʱ��㣺2029-03-09 09:51:11
2021-03-22 11:01:20,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 13:30:47
2021-03-22 11:01:20,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693530   ����ʱ��㣺2029-03-06 14:30:47
2021-03-22 11:01:20,601 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-23 16:00:52
2021-03-22 11:01:20,601 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710259   ����ʱ��㣺2029-02-25 14:23:52
2021-03-22 11:01:20,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-08 16:28:55
2021-03-22 11:01:20,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495996   ����ʱ��㣺2029-01-09 09:33:55
2021-03-22 11:01:20,650 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 13:29:34
2021-03-22 11:01:20,651 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320130   ����ʱ��㣺2029-03-06 14:29:34
2021-03-22 11:01:20,688 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469831   ����ʱ��㣺2028-10-13 15:04:06
2021-03-22 11:01:20,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 13:49:27
2021-03-22 11:01:20,722 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677519   ����ʱ��㣺2029-02-02 14:49:27
2021-03-22 11:01:20,750 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473068   ����ʱ��㣺2028-10-13 10:11:08
2021-03-22 11:01:20,779 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-12 16:00:17
2021-03-22 11:01:20,779 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494737   ����ʱ��㣺2029-01-13 10:30:17
2021-03-22 11:01:20,807 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 13:29:47
2021-03-22 11:01:20,807 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314200   ����ʱ��㣺2029-03-06 14:29:47
2021-03-22 11:01:20,834 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-02 16:00:13
2021-03-22 11:01:20,834 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305264   ����ʱ��㣺2029-03-04 13:57:13
2021-03-22 11:01:20,859 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 12:20:25
2021-03-22 11:01:20,859 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495128   ����ʱ��㣺2029-01-22 13:20:25
2021-03-22 11:01:20,885 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 15:52:51
2021-03-22 11:01:20,885 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306158   ����ʱ��㣺2029-02-27 16:52:51
2021-03-22 11:01:20,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-07 16:32:31
2021-03-22 11:01:20,914 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483793   ����ʱ��㣺2028-12-08 09:37:31
2021-03-22 11:01:20,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-09 16:00:28
2021-03-22 11:01:20,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324164   ����ʱ��㣺2029-03-11 10:38:28
2021-03-22 11:01:20,983 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 09:48:09
2021-03-22 11:01:20,983 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690534   ����ʱ��㣺2029-02-05 10:48:09
2021-03-22 11:01:21,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 13:30:50
2021-03-22 11:01:21,018 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693528   ����ʱ��㣺2029-03-06 14:30:50
2021-03-22 11:01:21,050 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 13:20:17
2021-03-22 11:01:21,050 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703577   ����ʱ��㣺2029-02-19 14:20:17
2021-03-22 11:01:21,077 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-12 16:00:33
2021-03-22 11:01:21,077 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497422   ����ʱ��㣺2029-01-14 15:58:33
2021-03-22 11:01:21,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-26 16:00:14
2021-03-22 11:01:21,104 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595464   ����ʱ��㣺2029-01-28 16:03:14
2021-03-22 11:01:21,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 13:15:44
2021-03-22 11:01:21,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689793   ����ʱ��㣺2029-02-09 14:15:44
2021-03-22 11:01:21,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 13:30:40
2021-03-22 11:01:21,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305098   ����ʱ��㣺2029-03-06 14:30:40
2021-03-22 11:01:21,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 13:48:49
2021-03-22 11:01:21,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677547   ����ʱ��㣺2029-02-02 14:48:49
2021-03-22 11:01:21,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 13:48:58
2021-03-22 11:01:21,219 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677534   ����ʱ��㣺2029-02-02 14:48:58
2021-03-22 11:01:21,250 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 14:13:47
2021-03-22 11:01:21,250 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695758   ����ʱ��㣺2029-02-16 15:13:47
2021-03-22 11:01:21,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474274   ����ʱ��㣺2028-10-20 14:27:01
2021-03-22 11:01:21,299 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 12:56:46
2021-03-22 11:01:21,299 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689801   ����ʱ��㣺2029-02-09 13:56:46
2021-03-22 11:01:21,324 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid470136   ����ʱ��㣺2029-02-02 14:49:49
2021-03-22 11:01:21,353 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-01 16:00:32
2021-03-22 11:01:21,353 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479817   ����ʱ��㣺2028-12-02 10:29:32
2021-03-22 11:01:21,378 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-09 16:00:51
2021-03-22 11:01:21,378 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326120   ����ʱ��㣺2029-03-11 15:25:51
2021-03-22 11:01:21,401 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 15:32:58
2021-03-22 11:01:21,402 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689690   ����ʱ��㣺2029-02-05 16:32:58
2021-03-22 11:01:21,432 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-26 16:00:20
2021-03-22 11:01:21,432 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid563440   ����ʱ��㣺2029-01-27 13:11:20
2021-03-22 11:01:21,457 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 09:09:18
2021-03-22 11:01:21,457 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523362   ����ʱ��㣺2029-01-23 10:09:18
2021-03-22 11:01:21,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 13:45:33
2021-03-22 11:01:21,480 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301411   ����ʱ��㣺2029-02-26 14:45:33
2021-03-22 11:01:21,506 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 13:48:48
2021-03-22 11:01:21,506 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677544   ����ʱ��㣺2029-02-02 14:48:48
2021-03-22 11:01:21,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 13:30:52
2021-03-22 11:01:21,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693527   ����ʱ��㣺2029-03-06 14:30:52
2021-03-22 11:01:21,558 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474253   ����ʱ��㣺2028-10-20 14:27:12
2021-03-22 11:01:21,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-01 16:00:22
2021-03-22 11:01:21,593 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479813   ����ʱ��㣺2028-12-02 10:29:22
2021-03-22 11:01:21,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-24 16:00:02
2021-03-22 11:01:21,621 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481323   ����ʱ��㣺2028-11-25 15:01:02
2021-03-22 11:01:21,646 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-10 16:00:17
2021-03-22 11:01:21,646 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480418   ����ʱ��㣺2028-11-12 14:52:17
2021-03-22 11:01:21,675 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 15:27:30
2021-03-22 11:01:21,675 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498028   ����ʱ��㣺2029-01-19 16:27:30
2021-03-22 11:01:21,701 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 09:10:01
2021-03-22 11:01:21,701 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid602447   ����ʱ��㣺2029-02-02 10:10:01
2021-03-22 11:01:21,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 13:49:38
2021-03-22 11:01:21,728 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677528   ����ʱ��㣺2029-02-02 14:49:38
2021-03-22 11:01:21,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 13:29:32
2021-03-22 11:01:21,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314202   ����ʱ��㣺2029-03-06 14:29:32
2021-03-22 11:01:21,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 13:02:06
2021-03-22 11:01:21,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690547   ����ʱ��㣺2029-02-05 14:02:06
2021-03-22 11:01:21,820 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 14:19:46
2021-03-22 11:01:21,820 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710326   ����ʱ��㣺2029-03-02 15:19:46
2021-03-22 11:01:21,849 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 13:30:25
2021-03-22 11:01:21,849 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305188   ����ʱ��㣺2029-03-06 14:30:25
2021-03-22 11:01:21,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 15:21:46
2021-03-22 11:01:21,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481607   ����ʱ��㣺2028-11-27 16:21:46
2021-03-22 11:01:21,909 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 13:19:57
2021-03-22 11:01:21,909 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703611   ����ʱ��㣺2029-02-19 14:19:57
2021-03-22 11:01:21,932 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 13:39:22
2021-03-22 11:01:21,932 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47320119   ����ʱ��㣺2029-03-06 14:39:22
2021-03-22 11:01:21,962 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 15:21:42
2021-03-22 11:01:21,962 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481575   ����ʱ��㣺2028-11-27 16:21:42
2021-03-22 11:01:21,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 13:31:08
2021-03-22 11:01:21,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564396   ����ʱ��㣺2029-03-06 14:31:08
2021-03-22 11:01:22,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 13:29:51
2021-03-22 11:01:22,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314199   ����ʱ��㣺2029-03-06 14:29:51
2021-03-22 11:01:22,047 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 13:56:32
2021-03-22 11:01:22,047 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301249   ����ʱ��㣺2029-02-27 14:56:32
2021-03-22 11:01:22,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 12:55:33
2021-03-22 11:01:22,074 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689819   ����ʱ��㣺2029-02-09 13:55:33
2021-03-22 11:01:22,100 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 13:49:47
2021-03-22 11:01:22,100 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598453   ����ʱ��㣺2029-02-02 14:49:47
2021-03-22 11:01:22,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-08 16:24:37
2021-03-22 11:01:22,128 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496046   ����ʱ��㣺2029-01-09 09:29:37
2021-03-22 11:01:22,158 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-12 16:00:38
2021-03-22 11:01:22,158 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497462   ����ʱ��㣺2029-01-13 15:22:38
2021-03-22 11:01:22,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 14:14:46
2021-03-22 11:01:22,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692827   ����ʱ��㣺2029-02-16 15:14:46
2021-03-22 11:01:22,207 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 13:20:32
2021-03-22 11:01:22,207 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677551   ����ʱ��㣺2029-02-19 14:20:32
2021-03-22 11:01:22,233 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 16:00:21
2021-03-22 11:01:22,233 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492793   ����ʱ��㣺2029-01-08 17:29:21
2021-03-22 11:01:22,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 09:07:35
2021-03-22 11:01:22,268 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492932   ����ʱ��㣺2029-01-05 10:07:35
2021-03-22 11:01:22,306 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 13:49:44
2021-03-22 11:01:22,307 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494907   ����ʱ��㣺2029-01-16 14:49:44
2021-03-22 11:01:22,347 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 14:53:14
2021-03-22 11:01:22,348 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482759   ����ʱ��㣺2028-12-05 15:53:14
2021-03-22 11:01:22,383 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-09 16:00:32
2021-03-22 11:01:22,384 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689928   ����ʱ��㣺2029-02-11 14:39:32
2021-03-22 11:01:22,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-01 16:00:13
2021-03-22 11:01:22,423 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482614   ����ʱ��㣺2028-12-03 13:47:13
2021-03-22 11:01:22,459 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 12:56:25
2021-03-22 11:01:22,461 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689826   ����ʱ��㣺2029-02-09 13:56:25
2021-03-22 11:01:22,493 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 15:53:17
2021-03-22 11:01:22,493 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid707650   ����ʱ��㣺2029-02-23 16:53:17
2021-03-22 11:01:22,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 10:10:49
2021-03-22 11:01:22,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496080   ����ʱ��㣺2029-01-12 11:10:49
2021-03-22 11:01:22,552 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 13:29:39
2021-03-22 11:01:22,553 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314204   ����ʱ��㣺2029-03-06 14:29:39
2021-03-22 11:01:22,583 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-23 16:51:29
2021-03-22 11:01:22,583 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480768   ����ʱ��㣺2028-11-24 09:56:29
2021-03-22 11:01:22,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 10:10:09
2021-03-22 11:01:22,608 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494607   ����ʱ��㣺2029-01-09 11:10:09
2021-03-22 11:01:22,636 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469838   ����ʱ��㣺2028-10-13 15:04:02
2021-03-22 11:01:22,678 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 15:43:41
2021-03-22 11:01:22,678 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497539   ����ʱ��㣺2029-01-16 16:43:41
2021-03-22 11:01:22,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 14:33:56
2021-03-22 11:01:22,702 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301363   ����ʱ��㣺2029-02-26 15:33:56
2021-03-22 11:01:22,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 13:07:21
2021-03-22 11:01:22,725 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497651   ����ʱ��㣺2029-01-15 14:07:21
2021-03-22 11:01:22,751 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 09:39:31
2021-03-22 11:01:22,751 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478578   ����ʱ��㣺2028-11-03 10:39:31
2021-03-22 11:01:22,774 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 13:02:02
2021-03-22 11:01:22,774 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690545   ����ʱ��㣺2029-02-05 14:02:02
2021-03-22 11:01:22,799 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 13:56:29
2021-03-22 11:01:22,799 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306134   ����ʱ��㣺2029-02-27 14:56:29
2021-03-22 11:01:22,831 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 12:55:01
2021-03-22 11:01:22,832 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689833   ����ʱ��㣺2029-02-09 13:55:01
2021-03-22 11:01:22,857 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472655   ����ʱ��㣺2028-10-09 13:28:43
2021-03-22 11:01:22,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-09 16:00:51
2021-03-22 11:01:22,882 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323349   ����ʱ��㣺2029-03-10 15:41:51
2021-03-22 11:01:22,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 13:20:29
2021-03-22 11:01:22,906 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703570   ����ʱ��㣺2029-02-19 14:20:29
2021-03-22 11:01:22,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 15:32:40
2021-03-22 11:01:22,928 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689687   ����ʱ��㣺2029-02-05 16:32:40
2021-03-22 11:01:22,956 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-02 16:00:28
2021-03-22 11:01:22,957 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305225   ����ʱ��㣺2029-03-03 15:38:28
2021-03-22 11:01:22,985 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477587   ����ʱ��㣺2028-11-06 16:17:44
2021-03-22 11:01:23,009 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 10:12:17
2021-03-22 11:01:23,009 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494612   ����ʱ��㣺2029-01-09 11:12:17
2021-03-22 11:01:23,035 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid471816   ����ʱ��㣺2028-12-02 14:56:11
2021-03-22 11:01:23,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 13:30:48
2021-03-22 11:01:23,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid693529   ����ʱ��㣺2029-03-06 14:30:48
2021-03-22 11:01:23,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-08 16:29:18
2021-03-22 11:01:23,089 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495998   ����ʱ��㣺2029-01-09 09:34:18
2021-03-22 11:01:23,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 10:26:57
2021-03-22 11:01:23,115 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321226   ����ʱ��㣺2029-03-05 11:26:57
2021-03-22 11:01:23,143 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477328   ����ʱ��㣺2028-10-31 10:21:50
2021-03-22 11:01:23,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 16:00:14
2021-03-22 11:01:23,174 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307105   ����ʱ��㣺2029-02-28 17:29:14
2021-03-22 11:01:23,197 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 09:09:58
2021-03-22 11:01:23,198 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477796   ����ʱ��㣺2028-11-17 10:09:58
2021-03-22 11:01:23,226 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid473451   ����ʱ��㣺2028-10-13 14:26:13
2021-03-22 11:01:23,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 13:46:53
2021-03-22 11:01:23,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491491   ����ʱ��㣺2028-12-29 14:46:53
2021-03-22 11:01:23,290 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 12:37:59
2021-03-22 11:01:23,290 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491630   ����ʱ��㣺2029-01-05 13:37:59
2021-03-22 11:01:23,318 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-12 16:00:45
2021-03-22 11:01:23,319 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494739   ����ʱ��㣺2029-01-13 10:29:45
2021-03-22 11:01:23,350 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-12 16:00:37
2021-03-22 11:01:23,350 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497542   ����ʱ��㣺2029-01-14 14:09:37
2021-03-22 11:01:23,373 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 12:52:29
2021-03-22 11:01:23,373 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481834   ����ʱ��㣺2028-12-05 13:52:29
2021-03-22 11:01:23,411 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 12:55:47
2021-03-22 11:01:23,412 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689840   ����ʱ��㣺2029-02-09 13:55:47
2021-03-22 11:01:23,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 15:48:57
2021-03-22 11:01:23,448 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495960   ����ʱ��㣺2029-01-08 16:48:57
2021-03-22 11:01:23,482 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 15:32:44
2021-03-22 11:01:23,482 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557469   ����ʱ��㣺2029-01-26 16:32:44
2021-03-22 11:01:23,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-07 16:29:23
2021-03-22 11:01:23,510 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483791   ����ʱ��㣺2028-12-08 09:34:23
2021-03-22 11:01:23,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 15:09:46
2021-03-22 11:01:23,539 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494816   ����ʱ��㣺2029-01-16 16:09:46
2021-03-22 11:01:23,563 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 13:49:24
2021-03-22 11:01:23,563 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677517   ����ʱ��㣺2029-02-02 14:49:24
2021-03-22 11:01:23,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 13:56:25
2021-03-22 11:01:23,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306136   ����ʱ��㣺2029-02-27 14:56:25
2021-03-22 11:01:23,614 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-26 16:00:16
2021-03-22 11:01:23,614 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid595462   ����ʱ��㣺2029-01-28 16:03:16
2021-03-22 11:01:23,637 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 09:09:17
2021-03-22 11:01:23,638 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703581   ����ʱ��㣺2029-02-19 10:09:17
2021-03-22 11:01:23,659 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-15 16:00:07
2021-03-22 11:01:23,659 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484498   ����ʱ��㣺2028-12-16 14:22:07
2021-03-22 11:01:23,685 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469813   ����ʱ��㣺2028-10-13 15:04:29
2021-03-22 11:01:23,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 13:20:12
2021-03-22 11:01:23,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703588   ����ʱ��㣺2029-02-19 14:20:12
2021-03-22 11:01:23,733 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 13:30:23
2021-03-22 11:01:23,733 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305193   ����ʱ��㣺2029-03-06 14:30:23
2021-03-22 11:01:23,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-12 16:00:34
2021-03-22 11:01:23,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492729   ����ʱ��㣺2029-01-13 14:55:34
2021-03-22 11:01:23,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 13:01:59
2021-03-22 11:01:23,782 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690543   ����ʱ��㣺2029-02-05 14:01:59
2021-03-22 11:01:23,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 14:06:01
2021-03-22 11:01:23,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47307097   ����ʱ��㣺2029-02-27 15:06:01
2021-03-22 11:01:23,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 10:26:49
2021-03-22 11:01:23,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321228   ����ʱ��㣺2029-03-05 11:26:49
2021-03-22 11:01:23,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 15:17:42
2021-03-22 11:01:23,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477589   ����ʱ��㣺2028-11-06 16:17:42
2021-03-22 11:01:23,898 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-12 16:00:45
2021-03-22 11:01:23,898 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497433   ����ʱ��㣺2029-01-14 15:57:45
2021-03-22 11:01:23,924 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-02 16:00:36
2021-03-22 11:01:23,924 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305261   ����ʱ��㣺2029-03-04 13:57:36
2021-03-22 11:01:23,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-09 16:00:27
2021-03-22 11:01:23,951 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324547   ����ʱ��㣺2029-03-11 09:47:27
2021-03-22 11:01:23,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 13:49:29
2021-03-22 11:01:23,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480091   ����ʱ��㣺2028-12-15 14:49:29
2021-03-22 11:01:24,004 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 09:07:57
2021-03-22 11:01:24,004 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323347   ����ʱ��㣺2029-03-13 10:07:57
2021-03-22 11:01:24,034 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474251   ����ʱ��㣺2028-10-20 14:27:15
2021-03-22 11:01:24,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 13:30:44
2021-03-22 11:01:24,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703631   ����ʱ��㣺2029-03-06 14:30:44
2021-03-22 11:01:24,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 09:48:30
2021-03-22 11:01:24,085 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid599508   ����ʱ��㣺2029-01-29 10:48:30
2021-03-22 11:01:24,117 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-07 16:31:53
2021-03-22 11:01:24,117 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483789   ����ʱ��㣺2028-12-08 09:36:53
2021-03-22 11:01:24,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 09:45:29
2021-03-22 11:01:24,148 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710296   ����ʱ��㣺2029-02-26 10:45:29
2021-03-22 11:01:24,176 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 13:20:02
2021-03-22 11:01:24,176 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703608   ����ʱ��㣺2029-02-19 14:20:02
2021-03-22 11:01:24,202 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 14:15:47
2021-03-22 11:01:24,202 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692823   ����ʱ��㣺2029-02-16 15:15:47
2021-03-22 11:01:24,227 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 13:25:26
2021-03-22 11:01:24,227 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid704752   ����ʱ��㣺2029-02-19 14:25:26
2021-03-22 11:01:24,254 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 14:29:58
2021-03-22 11:01:24,254 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301406   ����ʱ��㣺2029-02-26 15:29:58
2021-03-22 11:01:24,275 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-12 16:00:02
2021-03-22 11:01:24,275 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494741   ����ʱ��㣺2029-01-13 10:29:02
2021-03-22 11:01:24,304 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-23 16:00:03
2021-03-22 11:01:24,304 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710255   ����ʱ��㣺2029-02-25 14:34:03
2021-03-22 11:01:24,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-26 16:00:19
2021-03-22 11:01:24,329 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598428   ����ʱ��㣺2029-01-28 16:03:19
2021-03-22 11:01:24,356 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-08 16:53:22
2021-03-22 11:01:24,357 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323196   ����ʱ��㣺2029-03-09 09:58:22
2021-03-22 11:01:24,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-02 16:00:11
2021-03-22 11:01:24,381 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305129   ����ʱ��㣺2029-03-03 09:44:11
2021-03-22 11:01:24,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 13:49:35
2021-03-22 11:01:24,408 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677531   ����ʱ��㣺2029-02-02 14:49:35
2021-03-22 11:01:24,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 12:22:12
2021-03-22 11:01:24,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692677   ����ʱ��㣺2029-02-12 13:22:12
2021-03-22 11:01:24,462 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-08 16:53:18
2021-03-22 11:01:24,462 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314256   ����ʱ��㣺2029-03-09 09:58:18
2021-03-22 11:01:24,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-09 16:00:08
2021-03-22 11:01:24,489 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323142   ����ʱ��㣺2029-03-10 16:36:08
2021-03-22 11:01:24,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-09 16:59:29
2021-03-22 11:01:24,519 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47327127   ����ʱ��㣺2029-03-12 10:04:29
2021-03-22 11:01:24,543 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 14:27:26
2021-03-22 11:01:24,543 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324187   ����ʱ��㣺2029-03-06 15:27:26
2021-03-22 11:01:24,567 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-08 16:00:56
2021-03-22 11:01:24,567 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482439   ����ʱ��㣺2028-12-09 10:50:56
2021-03-22 11:01:24,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-08 16:26:44
2021-03-22 11:01:24,596 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495889   ����ʱ��㣺2029-01-09 09:31:44
2021-03-22 11:01:24,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 09:09:15
2021-03-22 11:01:24,631 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid523365   ����ʱ��㣺2029-01-23 10:09:15
2021-03-22 11:01:24,657 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-02 16:00:05
2021-03-22 11:01:24,658 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678515   ����ʱ��㣺2029-02-03 11:00:05
2021-03-22 11:01:24,682 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 09:10:32
2021-03-22 11:01:24,682 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492895   ����ʱ��㣺2029-01-16 10:10:32
2021-03-22 11:01:24,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 13:02:58
2021-03-22 11:01:24,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494657   ����ʱ��㣺2029-01-09 14:02:58
2021-03-22 11:01:24,732 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-09 16:00:50
2021-03-22 11:01:24,732 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47325152   ����ʱ��㣺2029-03-10 11:00:50
2021-03-22 11:01:24,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-29 16:00:11
2021-03-22 11:01:24,759 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492621   ����ʱ��㣺2028-12-30 10:57:11
2021-03-22 11:01:24,792 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 13:48:54
2021-03-22 11:01:24,792 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677537   ����ʱ��㣺2029-02-02 14:48:54
2021-03-22 11:01:24,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 15:17:49
2021-03-22 11:01:24,826 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477590   ����ʱ��㣺2028-11-06 16:17:49
2021-03-22 11:01:24,856 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 10:11:33
2021-03-22 11:01:24,857 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494609   ����ʱ��㣺2029-01-09 11:11:33
2021-03-22 11:01:24,881 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 13:31:05
2021-03-22 11:01:24,881 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564398   ����ʱ��㣺2029-03-06 14:31:05
2021-03-22 11:01:24,907 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-25 16:24:26
2021-03-22 11:01:24,908 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid484578   ����ʱ��㣺2028-12-26 09:29:26
2021-03-22 11:01:24,938 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-24 16:00:06
2021-03-22 11:01:24,938 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481838   ����ʱ��㣺2028-11-25 10:41:06
2021-03-22 11:01:24,966 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 10:20:48
2021-03-22 11:01:24,966 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690578   ����ʱ��㣺2029-02-09 11:20:48
2021-03-22 11:01:25,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 16:00:26
2021-03-22 11:01:25,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492670   ����ʱ��㣺2029-01-08 17:29:26
2021-03-22 11:01:25,032 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-26 16:00:48
2021-03-22 11:01:25,032 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564414   ����ʱ��㣺2029-01-27 11:16:48
2021-03-22 11:01:25,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 13:31:00
2021-03-22 11:01:25,059 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid600436   ����ʱ��㣺2029-03-06 14:31:00
2021-03-22 11:01:25,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 13:56:35
2021-03-22 11:01:25,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301250   ����ʱ��㣺2029-02-27 14:56:35
2021-03-22 11:01:25,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469825   ����ʱ��㣺2028-10-13 15:04:13
2021-03-22 11:01:25,139 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 13:03:08
2021-03-22 11:01:25,139 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689721   ����ʱ��㣺2029-02-09 14:03:08
2021-03-22 11:01:25,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 13:30:03
2021-03-22 11:01:25,166 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305165   ����ʱ��㣺2029-03-06 14:30:03
2021-03-22 11:01:25,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 13:49:21
2021-03-22 11:01:25,191 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677514   ����ʱ��㣺2029-02-02 14:49:21
2021-03-22 11:01:25,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-09 16:00:08
2021-03-22 11:01:25,215 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692521   ����ʱ��㣺2029-02-10 14:17:08
2021-03-22 11:01:25,244 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 13:19:55
2021-03-22 11:01:25,244 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703709   ����ʱ��㣺2029-02-19 14:19:55
2021-03-22 11:01:25,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-08 16:27:22
2021-03-22 11:01:25,273 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495886   ����ʱ��㣺2029-01-09 09:32:22
2021-03-22 11:01:25,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 13:20:20
2021-03-22 11:01:25,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703586   ����ʱ��㣺2029-02-19 14:20:20
2021-03-22 11:01:25,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 09:09:14
2021-03-22 11:01:25,326 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703617   ����ʱ��㣺2029-02-19 10:09:14
2021-03-22 11:01:25,348 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469809   ����ʱ��㣺2028-10-13 15:04:33
2021-03-22 11:01:25,373 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 15:52:55
2021-03-22 11:01:25,373 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306155   ����ʱ��㣺2029-02-27 16:52:55
2021-03-22 11:01:25,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-02 16:00:51
2021-03-22 11:01:25,398 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305349   ����ʱ��㣺2029-03-03 09:53:51
2021-03-22 11:01:25,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 15:28:16
2021-03-22 11:01:25,421 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498030   ����ʱ��㣺2029-01-19 16:28:16
2021-03-22 11:01:25,456 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 13:49:17
2021-03-22 11:01:25,456 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677513   ����ʱ��㣺2029-02-02 14:49:17
2021-03-22 11:01:25,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 13:56:30
2021-03-22 11:01:25,483 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301251   ����ʱ��㣺2029-02-27 14:56:30
2021-03-22 11:01:25,515 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 13:20:14
2021-03-22 11:01:25,517 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703569   ����ʱ��㣺2029-02-19 14:20:14
2021-03-22 11:01:25,562 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-02 16:00:59
2021-03-22 11:01:25,564 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306564   ����ʱ��㣺2029-03-03 14:25:59
2021-03-22 11:01:25,604 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 13:58:57
2021-03-22 11:01:25,605 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688592   ����ʱ��㣺2029-02-09 14:58:57
2021-03-22 11:01:25,649 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-02 16:00:18
2021-03-22 11:01:25,650 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305258   ����ʱ��㣺2029-03-04 13:57:18
2021-03-22 11:01:25,676 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 12:55:18
2021-03-22 11:01:25,676 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689821   ����ʱ��㣺2029-02-09 13:55:18
2021-03-22 11:01:25,710 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472879   ����ʱ��㣺2029-02-02 14:49:54
2021-03-22 11:01:25,735 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-02 16:00:26
2021-03-22 11:01:25,735 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677567   ����ʱ��㣺2029-02-03 10:47:26
2021-03-22 11:01:25,763 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 15:01:32
2021-03-22 11:01:25,763 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710404   ����ʱ��㣺2029-02-26 16:01:32
2021-03-22 11:01:25,787 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-09 16:00:24
2021-03-22 11:01:25,788 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324495   ����ʱ��㣺2029-03-10 15:14:24
2021-03-22 11:01:25,815 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475212   ����ʱ��㣺2028-11-24 14:39:33
2021-03-22 11:01:25,846 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-08 16:25:16
2021-03-22 11:01:25,846 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495993   ����ʱ��㣺2029-01-09 09:30:16
2021-03-22 11:01:25,879 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-08 16:27:06
2021-03-22 11:01:25,879 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495887   ����ʱ��㣺2029-01-09 09:32:06
2021-03-22 11:01:25,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 14:16:35
2021-03-22 11:01:25,904 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692820   ����ʱ��㣺2029-02-16 15:16:35
2021-03-22 11:01:25,930 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 09:21:59
2021-03-22 11:01:25,931 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47326125   ����ʱ��㣺2029-03-12 10:21:59
2021-03-22 11:01:25,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 13:26:50
2021-03-22 11:01:25,964 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706666   ����ʱ��㣺2029-02-23 14:26:50
2021-03-22 11:01:25,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-09 16:00:05
2021-03-22 11:01:25,989 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323143   ����ʱ��㣺2029-03-10 16:36:05
2021-03-22 11:01:26,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-02 16:00:53
2021-03-22 11:01:26,016 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305348   ����ʱ��㣺2029-03-03 09:53:53
2021-03-22 11:01:26,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 13:49:15
2021-03-22 11:01:26,051 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677512   ����ʱ��㣺2029-02-02 14:49:15
2021-03-22 11:01:26,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 15:52:49
2021-03-22 11:01:26,078 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306154   ����ʱ��㣺2029-02-27 16:52:49
2021-03-22 11:01:26,107 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-16 16:00:41
2021-03-22 11:01:26,107 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699567   ����ʱ��㣺2029-02-17 09:15:41
2021-03-22 11:01:26,131 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474209   ����ʱ��㣺2028-10-20 15:00:04
2021-03-22 11:01:26,156 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 13:30:27
2021-03-22 11:01:26,157 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305171   ����ʱ��㣺2029-03-06 14:30:27
2021-03-22 11:01:26,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-02 16:00:07
2021-03-22 11:01:26,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid678514   ����ʱ��㣺2029-02-03 11:00:07
2021-03-22 11:01:26,210 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 13:20:27
2021-03-22 11:01:26,210 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703568   ����ʱ��㣺2029-02-19 14:20:27
2021-03-22 11:01:26,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-29 16:00:58
2021-03-22 11:01:26,235 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492849   ����ʱ��㣺2028-12-31 14:44:58
2021-03-22 11:01:26,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 13:26:48
2021-03-22 11:01:26,272 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706667   ����ʱ��㣺2029-02-23 14:26:48
2021-03-22 11:01:26,294 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 13:29:30
2021-03-22 11:01:26,294 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314207   ����ʱ��㣺2029-03-06 14:29:30
2021-03-22 11:01:26,323 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-08 16:26:01
2021-03-22 11:01:26,323 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495992   ����ʱ��㣺2029-01-09 09:31:01
2021-03-22 11:01:26,348 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-02 16:00:29
2021-03-22 11:01:26,348 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305259   ����ʱ��㣺2029-03-04 13:57:29
2021-03-22 11:01:26,375 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 13:01:54
2021-03-22 11:01:26,375 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690542   ����ʱ��㣺2029-02-05 14:01:54
2021-03-22 11:01:26,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475291   ����ʱ��㣺2028-11-03 16:06:21
2021-03-22 11:01:26,423 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-12 16:30:19
2021-03-22 11:01:26,423 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid692680   ����ʱ��㣺2029-02-13 09:35:19
2021-03-22 11:01:26,450 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 13:30:15
2021-03-22 11:01:26,450 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305156   ����ʱ��㣺2029-03-06 14:30:15
2021-03-22 11:01:26,471 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469823   ����ʱ��㣺2028-10-13 15:04:14
2021-03-22 11:01:26,496 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 13:30:59
2021-03-22 11:01:26,496 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid606438   ����ʱ��㣺2029-03-06 14:30:59
2021-03-22 11:01:26,521 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-09 16:00:21
2021-03-22 11:01:26,521 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690579   ����ʱ��㣺2029-02-10 14:38:21
2021-03-22 11:01:26,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 13:49:40
2021-03-22 11:01:26,545 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677530   ����ʱ��㣺2029-02-02 14:49:40
2021-03-22 11:01:26,570 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-26 16:00:05
2021-03-22 11:01:26,570 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564415   ����ʱ��㣺2029-01-27 11:17:05
2021-03-22 11:01:26,594 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 15:50:54
2021-03-22 11:01:26,594 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid695651   ����ʱ��㣺2029-02-26 16:50:54
2021-03-22 11:01:26,626 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 13:56:39
2021-03-22 11:01:26,627 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301253   ����ʱ��㣺2029-02-27 14:56:39
2021-03-22 11:01:26,676 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 13:30:32
2021-03-22 11:01:26,679 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305102   ����ʱ��㣺2029-03-06 14:30:32
2021-03-22 11:01:26,713 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 12:58:08
2021-03-22 11:01:26,715 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689808   ����ʱ��㣺2029-02-09 13:58:08
2021-03-22 11:01:26,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 13:29:57
2021-03-22 11:01:26,753 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305166   ����ʱ��㣺2029-03-06 14:29:57
2021-03-22 11:01:26,789 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-02 16:00:00
2021-03-22 11:01:26,790 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677566   ����ʱ��㣺2029-02-03 10:47:00
2021-03-22 11:01:26,823 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 16:00:18
2021-03-22 11:01:26,824 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492789   ����ʱ��㣺2029-01-08 17:29:18
2021-03-22 11:01:26,849 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 12:36:13
2021-03-22 11:01:26,849 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid688568   ����ʱ��㣺2029-02-05 13:36:13
2021-03-22 11:01:26,875 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 15:10:13
2021-03-22 11:01:26,876 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494818   ����ʱ��㣺2029-01-16 16:10:13
2021-03-22 11:01:26,902 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 15:16:06
2021-03-22 11:01:26,903 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid698542   ����ʱ��㣺2029-02-16 16:16:06
2021-03-22 11:01:26,926 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-08 16:53:20
2021-03-22 11:01:26,926 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47323197   ����ʱ��㣺2029-03-09 09:58:20
2021-03-22 11:01:26,948 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 14:34:32
2021-03-22 11:01:26,948 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301369   ����ʱ��㣺2029-02-26 15:34:32
2021-03-22 11:01:26,979 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469808   ����ʱ��㣺2028-10-13 15:04:35
2021-03-22 11:01:27,001 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-26 16:00:02
2021-03-22 11:01:27,002 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid557454   ����ʱ��㣺2029-01-27 14:53:02
2021-03-22 11:01:27,028 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 12:56:31
2021-03-22 11:01:27,029 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689823   ����ʱ��㣺2029-02-09 13:56:31
2021-03-22 11:01:27,056 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 15:01:54
2021-03-22 11:01:27,056 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710406   ����ʱ��㣺2029-02-26 16:01:54
2021-03-22 11:01:27,083 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 15:33:18
2021-03-22 11:01:27,083 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid689684   ����ʱ��㣺2029-02-05 16:33:18
2021-03-22 11:01:27,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 13:56:33
2021-03-22 11:01:27,114 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306137   ����ʱ��㣺2029-02-27 14:56:33
2021-03-22 11:01:27,141 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 15:26:46
2021-03-22 11:01:27,141 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498025   ����ʱ��㣺2029-01-19 16:26:46
2021-03-22 11:01:27,170 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 13:30:34
2021-03-22 11:01:27,170 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305101   ����ʱ��㣺2029-03-06 14:30:34
2021-03-22 11:01:27,194 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472914   ����ʱ��㣺2029-02-02 14:49:50
2021-03-22 11:01:27,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 13:34:37
2021-03-22 11:01:27,218 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid482617   ����ʱ��㣺2028-12-14 14:34:37
2021-03-22 11:01:27,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 13:59:05
2021-03-22 11:01:27,243 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479840   ����ʱ��㣺2028-12-04 14:59:05
2021-03-22 11:01:27,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 13:31:19
2021-03-22 11:01:27,271 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564394   ����ʱ��㣺2029-03-06 14:31:19
2021-03-22 11:01:27,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 13:10:22
2021-03-22 11:01:27,297 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid694574   ����ʱ��㣺2029-02-16 14:10:22
2021-03-22 11:01:27,325 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-17 16:00:41
2021-03-22 11:01:27,325 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid478026   ����ʱ��㣺2028-11-19 13:24:41
2021-03-22 11:01:27,350 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 16:00:25
2021-03-22 11:01:27,351 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492673   ����ʱ��㣺2029-01-08 17:29:25
2021-03-22 11:01:27,376 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 13:20:26
2021-03-22 11:01:27,376 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703597   ����ʱ��㣺2029-02-19 14:20:26
2021-03-22 11:01:27,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 13:30:09
2021-03-22 11:01:27,409 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305170   ����ʱ��㣺2029-03-06 14:30:09
2021-03-22 11:01:27,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-16 16:00:07
2021-03-22 11:01:27,436 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid699566   ����ʱ��㣺2029-02-17 09:15:07
2021-03-22 11:01:27,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 13:30:05
2021-03-22 11:01:27,470 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305168   ����ʱ��㣺2029-03-06 14:30:05
2021-03-22 11:01:27,502 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-02 16:00:42
2021-03-22 11:01:27,503 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid710314   ����ʱ��㣺2029-03-03 14:30:42
2021-03-22 11:01:27,548 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 15:52:50
2021-03-22 11:01:27,549 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47321176   ����ʱ��㣺2029-03-05 16:52:50
2021-03-22 11:01:27,590 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 15:21:44
2021-03-22 11:01:27,591 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481605   ����ʱ��㣺2028-11-27 16:21:44
2021-03-22 11:01:27,629 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 16:00:24
2021-03-22 11:01:27,630 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid492790   ����ʱ��㣺2029-01-08 17:29:24
2021-03-22 11:01:27,660 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 13:59:04
2021-03-22 11:01:27,662 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479838   ����ʱ��㣺2028-12-04 14:59:04
2021-03-22 11:01:27,692 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-08 16:53:16
2021-03-22 11:01:27,692 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314258   ����ʱ��㣺2029-03-09 09:58:16
2021-03-22 11:01:27,719 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474612   ����ʱ��㣺2028-10-22 14:46:46
2021-03-22 11:01:27,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 12:39:29
2021-03-22 11:01:27,752 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid491584   ����ʱ��㣺2029-01-05 13:39:29
2021-03-22 11:01:27,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 13:48:57
2021-03-22 11:01:27,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677541   ����ʱ��㣺2029-02-02 14:48:57
2021-03-22 11:01:27,805 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 13:29:43
2021-03-22 11:01:27,805 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314245   ����ʱ��㣺2029-03-06 14:29:43
2021-03-22 11:01:27,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-08 16:27:59
2021-03-22 11:01:27,840 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid495874   ����ʱ��㣺2029-01-09 09:32:59
2021-03-22 11:01:27,872 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477560   ����ʱ��㣺2029-02-05 11:24:41
2021-03-22 11:01:27,903 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 13:45:46
2021-03-22 11:01:27,903 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301293   ����ʱ��㣺2029-02-26 14:45:46
2021-03-22 11:01:27,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 10:08:42
2021-03-22 11:01:27,929 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496381   ����ʱ��㣺2029-01-09 11:08:42
2021-03-22 11:01:27,958 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-11 16:01:14
2021-03-22 11:01:27,958 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496159   ����ʱ��㣺2029-01-12 09:06:14
2021-03-22 11:01:27,988 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 15:52:58
2021-03-22 11:01:27,988 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306152   ����ʱ��㣺2029-02-27 16:52:58
2021-03-22 11:01:28,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 13:02:23
2021-03-22 11:01:28,014 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid494655   ����ʱ��㣺2029-01-09 14:02:23
2021-03-22 11:01:28,036 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 13:19:59
2021-03-22 11:01:28,036 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703598   ����ʱ��㣺2029-02-19 14:19:59
2021-03-22 11:01:28,062 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid475292   ����ʱ��㣺2028-11-03 16:05:59
2021-03-22 11:01:28,084 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid477544   ����ʱ��㣺2028-11-06 16:17:48
2021-03-22 11:01:28,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-24 16:00:48
2021-03-22 11:01:28,108 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid479809   ����ʱ��㣺2028-11-26 15:45:48
2021-03-22 11:01:28,133 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472643   ����ʱ��㣺2028-10-08 09:37:49
2021-03-22 11:01:28,162 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 13:56:41
2021-03-22 11:01:28,162 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47301252   ����ʱ��㣺2029-02-27 14:56:41
2021-03-22 11:01:28,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-11 16:00:43
2021-03-22 11:01:28,184 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid496161   ����ʱ��㣺2029-01-12 09:05:43
2021-03-22 11:01:28,209 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 13:30:19
2021-03-22 11:01:28,209 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305167   ����ʱ��㣺2029-03-06 14:30:19
2021-03-22 11:01:28,232 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid474213   ����ʱ��㣺2028-10-20 15:00:40
2021-03-22 11:01:28,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 13:48:45
2021-03-22 11:01:28,259 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677542   ����ʱ��㣺2029-02-02 14:48:45
2021-03-22 11:01:28,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 13:19:53
2021-03-22 11:01:28,335 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid703708   ����ʱ��㣺2029-02-19 14:19:53
2021-03-22 11:01:28,370 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 13:49:37
2021-03-22 11:01:28,370 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid677527   ����ʱ��㣺2029-02-02 14:49:37
2021-03-22 11:01:28,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 13:31:11
2021-03-22 11:01:28,399 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid564395   ����ʱ��㣺2029-03-06 14:31:11
2021-03-22 11:01:28,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 14:20:32
2021-03-22 11:01:28,429 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305126   ����ʱ��㣺2029-03-02 15:20:32
2021-03-22 11:01:28,452 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 13:49:45
2021-03-22 11:01:28,452 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid598452   ����ʱ��㣺2029-02-02 14:49:45
2021-03-22 11:01:28,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 10:26:10
2021-03-22 11:01:28,477 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid708548   ����ʱ��㣺2029-02-20 11:26:10
2021-03-22 11:01:28,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 13:01:57
2021-03-22 11:01:28,505 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690539   ����ʱ��㣺2029-02-05 14:01:57
2021-03-22 11:01:28,531 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469837   ����ʱ��㣺2028-10-13 15:04:08
2021-03-22 11:01:28,557 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469811   ����ʱ��㣺2028-10-13 15:04:31
2021-03-22 11:01:28,583 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid469824   ����ʱ��㣺2028-10-13 15:04:16
2021-03-22 11:01:28,613 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 15:27:00
2021-03-22 11:01:28,613 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid498026   ����ʱ��㣺2029-01-19 16:27:00
2021-03-22 11:01:28,638 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 15:21:51
2021-03-22 11:01:28,638 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid481583   ����ʱ��㣺2028-11-27 16:21:51
2021-03-22 11:01:28,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 13:11:25
2021-03-22 11:01:28,677 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid697555   ����ʱ��㣺2029-02-16 14:11:25
2021-03-22 11:01:28,713 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-02 16:00:08
2021-03-22 11:01:28,713 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47305257   ����ʱ��㣺2029-03-04 13:57:08
2021-03-22 11:01:28,739 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 15:52:56
2021-03-22 11:01:28,740 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47306153   ����ʱ��㣺2029-02-27 16:52:56
2021-03-22 11:01:28,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 13:29:41
2021-03-22 11:01:28,776 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314246   ����ʱ��㣺2029-03-06 14:29:41
2021-03-22 11:01:28,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-09 16:00:01
2021-03-22 11:01:28,813 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47324400   ����ʱ��㣺2029-03-10 15:14:01
2021-03-22 11:01:28,841 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 13:26:46
2021-03-22 11:01:28,841 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid706668   ����ʱ��㣺2029-02-23 14:26:46
2021-03-22 11:01:28,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 15:43:38
2021-03-22 11:01:28,866 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid497663   ����ʱ��㣺2029-01-16 16:43:38
2021-03-22 11:01:28,895 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid472913   ����ʱ��㣺2029-02-02 14:49:52
2021-03-22 11:01:28,920 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-08 16:00:12
2021-03-22 11:01:28,920 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid480026   ����ʱ��㣺2028-12-10 13:29:12
2021-03-22 11:01:28,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 09:07:53
2021-03-22 11:01:28,946 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid47314259   ����ʱ��㣺2029-03-13 10:07:53
2021-03-22 11:01:28,970 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-07 16:31:37
2021-03-22 11:01:28,971 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid483786   ����ʱ��㣺2028-12-08 09:36:37
2021-03-22 11:01:28,997 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 13:01:52
2021-03-22 11:01:28,997 ERROR weaver.general.BaseBean  - ��ӳ�ʱ��������requestid690541   ����ʱ��㣺2029-02-05 14:01:52
2021-03-22 11:01:29,017 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2021-03-22 11:01:29,017 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2021-03-22 11:01:29,018 ERROR weaver.general.BaseBean  - ��ʱ����ɨ���߳���������˯��ʱ�䣺1800000
2021-03-22 11:05:34,918 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:34,992 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,026 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,027 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,094 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,136 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,298 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,479 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,518 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,521 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,526 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:35,766 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:36,597 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:36,977 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,045 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,101 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,129 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,134 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,137 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,164 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,167 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,206 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,206 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,317 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,330 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,436 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,460 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='top' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM mainmenuinfo t1  left join (select count(0) c,parentId from mainmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from mainmenuinfo it1 inner join mainmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , mainmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='top'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='top') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 11:05:37,460 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' )  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) )  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 11:05:37,593 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,668 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,811 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,898 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,953 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:37,993 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,020 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,122 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,123 ERROR weaver.systeminfo.menuconfig.MenuUtil  - �˵�szy��ѯsql:select * from (select isnull(c.isopen,m.infoid+(972334)+1) as isopen,m.* from ( ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id in (SELECT infoid FROM menushareinfo WHERE (  CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 1 AND sharevalue in (268)  and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 3 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR   CASE WHEN (sharetype = 5 AND sharevalue = 2069) THEN  1 ELSE 0 END   = 1   OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  OR  CASE WHEN (sharetype = 6 AND sharevalue in (6) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end) <=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1 OR CASE WHEN (sharetype = 7 AND sharevalue = 300  and   ( case when jobtitlelevel=1  Then 1 else 0 end = 1     or       case when (jobtitlelevel=2 and ( '%,'+jobtitlesharevalue+',%' like '%,268,%' ) ) Then 1 else 0 end = 1        or       case when (jobtitlelevel=3 and ( '%,'+jobtitlesharevalue+',%' like '%,6,%' ) ) Then 1 else 0 end = 1    )) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1-0.1'),'-',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1   OR   CASE WHEN (sharetype = 2 AND ((sharevalue=2 and rolelevel<= 2)  or  (sharevalue=8 and rolelevel<= 2)  or  (sharevalue=55 and rolelevel<= 2)  or  (sharevalue=58 and rolelevel<= 2)  or  (sharevalue=77 and rolelevel<= 2)  or  (sharevalue=87 and rolelevel<= 2)  or  (sharevalue=102 and rolelevel<= 2)  or  (sharevalue=122 and rolelevel<= 2)  or  (sharevalue=127 and rolelevel<= 2) ) and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',1) else 999999 end)<=5 and (case when ISNUMERIC(dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2))=1 then dbo.Get_StrArrayStrOfIndex (isnull(seclevel, '0.1&0.1'),'&',2) else 999999 end)>=5 ) THEN  1 ELSE 0 END   = 1  )  and resourcetype='2' and resourceid in(6) and menutype='left' ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) union ( SELECT t1.id as infoId, t1.labelId , t1.isCustom, t1.refersubid,t1.iconUrl,t1.TopIconUrl,t1.linkAddress,t1.routeurl,t1.fullrouteurl,t1.mobxrouteurl,t1.iconClassName,t1.baseTarget,t1.parentId,t1.useCustomName as infoUseCustomName,t1.customName as infoCustomName,t1.customName_e as infoCustomName_e,t1.customName_t as infoCustomName_t,t1.module, t2.id,t2.visible,t2.viewIndex,t2.locked,t2.lockedById,t2.useCustomName,t2.customName,t2.customName_e,t2.customName_t,t2.resourcetype,t2.resourceid ,case when t1.isCustom=2 then t1.topmenuname else t2.topmenuname end topmenuname,case when t1.isCustom=2 then t1.topname_e else t2.topname_e end topname_e,case when t1.isCustom=2 then t1.topname_t else t2.topname_t end topname_t ,allt.c as  allParent,curt.c as curParent  FROM leftmenuinfo t1  left join (select count(0) c,parentId from leftmenuinfo group by  parentId )allt on t1.id=allt.parentId  left join (select count(0) c,parentId from leftmenuinfo it1 inner join leftmenuconfig it2 on it2.infoId = it1.id where it2.resourcetype = 3  and it2.resourceid = 2069 group by it1.parentId)curt on t1.id=curt.parentId , leftmenuconfig t2 , SystemModule t3  WHERE t2.infoId = t1.id  AND t1.relatedModuleId = t3.id   and ((t2.resourcetype='2' and t2.resourceid in(6)) or (t2.resourcetype='3' and t2.resourceid in(2069) and t2.infoid<0)) AND t2.visible=1  AND t3.moduleReleased = 1 and t1.id not in (SELECT infoid FROM menushareinfo where menutype='left'  and resourcetype='2' and resourceid in(6) ) and (t1.parentid=0 or  t1.parentid is null or t1.parentid='')  ) ) m left join menucontrollist c on m.infoid+972268=c.menuid and c.type='left') a where a.isopen=a.infoid+(972334)+1 ORDER BY ISNULL(a.parentId,0),a.viewIndex,a.infoId
2021-03-22 11:05:38,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,197 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,246 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,255 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,274 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,297 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,332 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,360 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,446 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,461 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,475 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,515 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,524 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,536 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,631 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,632 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,643 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,658 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,684 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,685 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,691 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,703 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:38,933 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:39,044 ERROR com.engine.portal.biz.homepage.HomepageCommonBiz  - com.engine.portal.biz.homepage.HomepageCommonBiz
java.lang.NullPointerException
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getCustomLayoutHtmlStr(HomepageCommonBiz.java:986)
	at com.engine.portal.biz.homepage.HomepageCommonBiz.getBaseHpStr(HomepageCommonBiz.java:79)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:88)
	at com.engine.portal.cmd.homepage.GetHpAllElementJsonCmd.execute(GetHpAllElementJsonCmd.java:27)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.portal.service.impl.HomepageServiceImpl.getHpAllElementJson(HomepageServiceImpl.java:26)
	at com.engine.portal.web.HomepageAction.getHpDataJson(HomepageAction.java:43)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)
	at com.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$TypeOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:185)
	at com.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)
	at com.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)
	at com.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)
	at com.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at wscheck.FileCheckFilter.doFilter(FileCheckFilter.java:334)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.WGzipFilter.doFilter(WGzipFilter.java:126)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor414.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:794)
	at sun.reflect.GeneratedMethodAccessor358.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.SessionFilter.doFilter(SessionFilter.java:469)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 11:05:39,169 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:39,197 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.io.FileNotFoundException: \u01\ecology\filesystem\201809\F\c1b36ef0-2f15-4515-97e7-6369f66bd969.zip (ϵͳ�Ҳ���ָ����·����)
	at java.io.FileInputStream.open0(Native Method)
	at java.io.FileInputStream.open(FileInputStream.java:195)
	at java.io.FileInputStream.<init>(FileInputStream.java:138)
	at weaver.file.FileDownload.doGet(FileDownload.java:1473)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:114)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at weaver.filter.PFixFilter.doFilter(PFixFilter.java:130)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MonitorXFixIPFilter.doFilter(MonitorXFixIPFilter.java:30)
	at weaver.filter.MonitorForbiddenUrlFilter.doFilter(MonitorForbiddenUrlFilter.java:133)
	at weaver.filter.MonitorCheckdbFilter.doFilter(MonitorCheckdbFilter.java:49)
	at weaver.filter.MonitorXUrlTimeFilter.doFilter(MonitorXUrlTimeFilter.java:66)
	at weaver.filter.DynamicMonitorXFilter.doFilter(DynamicMonitorXFilter.java:86)
	at weaver.filter.MonitorXFilter.doFilter(MonitorXFilter.java:83)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.XssFilter.doFilterInternal(XssFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor414.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:731)
	at sun.reflect.GeneratedMethodAccessor358.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 11:05:39,200 ERROR weaver.general.BaseBean  - weaver.general.BaseBean
java.io.FileNotFoundException: \u01\ecology\filesystem\201811\K\cc9fd7cf-fd1f-4ab5-8083-3535d15dfa30.zip (ϵͳ�Ҳ���ָ����·����)
	at java.io.FileInputStream.open0(Native Method)
	at java.io.FileInputStream.open(FileInputStream.java:195)
	at java.io.FileInputStream.<init>(FileInputStream.java:138)
	at weaver.file.FileDownload.doGet(FileDownload.java:1473)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:114)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:91)
	at com.caucho.server.dispatch.ServletFilterChain.doFilter(ServletFilterChain.java:103)
	at weaver.filter.PFixFilter.doFilter(PFixFilter.java:130)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MonitorXFixIPFilter.doFilter(MonitorXFixIPFilter.java:30)
	at weaver.filter.MonitorForbiddenUrlFilter.doFilter(MonitorForbiddenUrlFilter.java:133)
	at weaver.filter.MonitorCheckdbFilter.doFilter(MonitorCheckdbFilter.java:49)
	at weaver.filter.MonitorXUrlTimeFilter.doFilter(MonitorXUrlTimeFilter.java:66)
	at weaver.filter.DynamicMonitorXFilter.doFilter(DynamicMonitorXFilter.java:86)
	at weaver.filter.MonitorXFilter.doFilter(MonitorXFilter.java:83)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.dateformat.DateFormatFilter.doFilter(DateFormatFilter.java:51)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.MultiLangFilter.doFilter(MultiLangFilter.java:349)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.filter.XssFilter.doFilterInternal(XssFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.security.webcontainer.XssRequestForWeblogic.doFilter(XssRequestForWeblogic.java:33)
	at sun.reflect.GeneratedMethodAccessor414.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.security.filter.SecurityMain.process(SecurityMain.java:731)
	at sun.reflect.GeneratedMethodAccessor358.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at weaver.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:76)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.cloudstore.dev.api.service.EMFilter.doFilter(EMFilter.java:394)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at weaver.session.WSessionClusterFilter.doFilter(WSessionClusterFilter.java:90)
	at com.caucho.server.dispatch.FilterFilterChain.doFilter(FilterFilterChain.java:87)
	at com.caucho.server.webapp.WebAppFilterChain.doFilter(WebAppFilterChain.java:187)
	at com.caucho.server.dispatch.ServletInvocation.service(ServletInvocation.java:265)
	at com.caucho.server.http.HttpRequest.handleRequest(HttpRequest.java:273)
	at com.caucho.server.port.TcpConnection.run(TcpConnection.java:682)
	at com.caucho.util.ThreadPool$Item.runTasks(ThreadPool.java:730)
	at com.caucho.util.ThreadPool$Item.run(ThreadPool.java:649)
	at java.lang.Thread.run(Thread.java:745)
2021-03-22 11:05:41,027 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:41,048 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:41,457 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:42,205 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:42,432 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:42,564 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:42,695 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:42,699 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:43,060 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:43,549 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:43,710 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:44,693 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,206 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,209 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,347 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,361 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,365 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,369 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,369 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,373 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,439 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:45,840 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:46,469 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:47,004 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,079 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,123 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,164 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,422 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,530 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,680 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:49,766 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:50,180 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:50,331 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:50,362 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,403 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,611 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,664 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,705 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,707 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,741 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:52,857 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,007 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,035 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,035 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,090 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,093 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,125 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,127 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,128 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,131 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,132 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,158 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,162 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,192 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,194 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,195 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,350 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,353 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,356 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,356 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:53,356 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:54,009 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:54,009 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:54,009 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:54,010 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
2021-03-22 11:05:54,264 ERROR weaver.general.BaseBean  - >>>>>>>>> userid==2069��ǿ������
