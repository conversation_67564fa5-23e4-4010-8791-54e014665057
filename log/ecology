2025-06-12 10:40:13,526 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... classname is weaver.session.util.RedisSessionUtil
2025-06-12 10:40:15,615 ERROR weaver.session.util.RedisTemplate  - 检查redis连接池状态异常
redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.util.Pool.getResource(Pool.java:53)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:226)
	at weaver.session.util.RedisTemplate.jedisPoolConnected(RedisTemplate.java:169)
	at weaver.session.util.RedisTemplate.isConnected(RedisTemplate.java:153)
	at weaver.session.WSessionClusterFilter.init(WSessionClusterFilter.java:150)
	at com.caucho.server.dispatch.FilterManager.createFilter(FilterManager.java:144)
	at com.caucho.server.dispatch.FilterManager.init(FilterManager.java:91)
	at com.caucho.server.webapp.WebApp.start(WebApp.java:1873)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.webapp.WebAppContainer.start(WebAppContainer.java:659)
	at com.caucho.server.host.Host.start(Host.java:450)
	at com.caucho.server.deploy.DeployController.startImpl(DeployController.java:667)
	at com.caucho.server.deploy.StartAutoRedeployAutoStrategy.startOnInit(StartAutoRedeployAutoStrategy.java:72)
	at com.caucho.server.deploy.DeployController.startOnInit(DeployController.java:549)
	at com.caucho.server.deploy.DeployContainer.start(DeployContainer.java:160)
	at com.caucho.server.host.HostContainer.start(HostContainer.java:484)
	at com.caucho.server.cluster.Server.start(Server.java:1319)
	at com.caucho.server.cluster.Cluster.startServer(Cluster.java:710)
	at com.caucho.server.cluster.ClusterServer.startServer(ClusterServer.java:542)
	at com.caucho.server.resin.Resin.start(Resin.java:703)
	at com.caucho.server.resin.Resin.initMain(Resin.java:1162)
	at com.caucho.server.resin.Resin.main(Resin.java:1365)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: connect timed out
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:106)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:868)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:435)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:363)
	at redis.clients.util.Pool.getResource(Pool.java:49)
	... 24 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at redis.clients.jedis.Connection.connect(Connection.java:184)
	... 31 more
2025-06-12 10:40:15,624 ERROR weaver.session.WSessionClusterFilter  - ##session log##  redis server is not start,please check redis server!Now switch to DB mode!
2025-06-12 10:40:15,625 ERROR weaver.general.BaseBean  - ##session log ## invoke WSessionCluserFilter.init() method... useCustomSessionId is false
2025-06-12 10:40:16,801 INFO  weaver.general.InitServer  - System Init Message:mainControlIp= localIp:[*************, *************, *************]
2025-06-12 10:40:16,802 INFO  weaver.general.InitServer  - init ioc container...
2025-06-12 10:40:18,151 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2025-06-12 10:40:19,465 ERROR weaver.system.GetPhysicalAddress  - getMACAddrByLsh >>>00-50-56-C0-00-08
2025-06-12 10:40:19,969 ERROR weaver.general.BaseBean  - ------是否开启字符转码----0---是否开启sql参数化解析-----0----nativepool----ecology
2025-06-12 10:40:20,038 ERROR weaver.general.BaseBean  - >>>>>>>>>>>>>>>isCollectCUDApi>>>>>false
2025-06-12 10:40:20,039 ERROR weaver.general.BaseBean  - ExecuteSqlLogger start log write to file thread success...
2025-06-12 10:40:20,793 INFO  net.sf.ehcache.pool.sizeof.filter.AnnotationSizeOfFilter  - Using regular expression provided through VM argument net.sf.ehcache.pool.sizeof.ignore.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-06-12 10:40:20,810 INFO  net.sf.ehcache.pool.sizeof.JvmInformation  - Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-06-12 10:40:20,902 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Extracted agent jar to temporary file C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent1008467049192529478.jar
2025-06-12 10:40:20,903 INFO  net.sf.ehcache.pool.sizeof.AgentLoader  - Trying to load agent @ C:\Users\<USER>\AppData\Local\Temp\ehcache-sizeof-agent1008467049192529478.jar
2025-06-12 10:40:20,911 INFO  net.sf.ehcache.pool.impl.DefaultSizeOfEngine  - using Agent sizeof engine
2025-06-12 10:40:35,783 ERROR com.weaver.upgrade.FunctionUpgrade  - OS.name+++++++++++++++++++++:Windows 10
2025-06-12 10:40:35,783 ERROR com.weaver.upgrade.FunctionUpgrade  - srcfilePath+++++++++++++++++++++:D:\ecology\ecology\ecology\upgrade\src
2025-06-12 10:40:35,793 ERROR com.weaver.upgrade.FunctionUpgrade  - 本地Ip++++++++++：*************
2025-06-12 10:40:35,793 ERROR com.weaver.upgrade.FunctionUpgrade  - 主节点MainControlIP++++++++++：
2025-06-12 10:40:36,373 ERROR weaver.general.BaseBean  - 启动日志压缩线程....
2025-06-12 10:40:44,065 INFO  weaver.general.InitServer  - end ioc container init...
2025-06-12 10:40:44,077 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init start
2025-06-12 10:40:44,077 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init upgrade
2025-06-12 10:40:44,079 ERROR weaver.general.BaseBean  - SysUpgradeCominfo get ipmessage
2025-06-12 10:40:44,081 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has updated sub node
2025-06-12 10:40:44,082 ERROR weaver.general.BaseBean  - SysUpgradeCominfo check has deployed Monitor
2025-06-12 10:40:46,120 ERROR weaver.general.BaseBean  - SysUpgradeCominfo init end
2025-06-12 10:40:46,120 INFO  weaver.general.InitServer  - SystemUpgrade Start.....
2025-06-12 10:40:46,133 INFO  weaver.general.InitServer  - SystemUpgrade Loaded.....
2025-06-12 10:40:46,134 INFO  weaver.general.InitServer  - SystemUpgrade End.....
2025-06-12 10:40:46,134 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread Start.....
2025-06-12 10:40:46,135 INFO  weaver.general.InitServer  - InitServerXMLtoDBThread End.....
2025-06-12 10:40:46,135 INFO  weaver.general.InitServer  - InitServerHrmDBThread Start.....
2025-06-12 10:40:46,137 INFO  weaver.general.InitServer  - InitServerHrmDBThread End.....
2025-06-12 10:40:46,137 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread Start.....
2025-06-12 10:40:46,138 INFO  weaver.general.InitServer  - InitServerWorkflowDBThread End.....
2025-06-12 10:40:47,839 ERROR weaver.conn.RecordSet  - 生成前端JS使用的label信息...
2025-06-12 10:40:54,901 ERROR weaver.system.SystemThreadManager  - 是否开启系统超时：true
2025-06-12 10:40:54,914 ERROR weaver.general.BaseBean  - 超时判断主节点
2025-06-12 10:40:54,921 ERROR weaver.system.SystemThreadManager  - SystemThreadManager: HrmSyncThreadManager is not exist. Do load.
2025-06-12 10:40:55,508 INFO  weaver.general.InitServer  - ESB INIT Start.....
2025-06-12 10:40:55,858 INFO  weaver.general.InitServer  - ESB INIT End.....
2025-06-12 10:40:55,860 INFO  weaver.general.InitServer  - InitEmailUpgradeThread Start.....
2025-06-12 10:40:55,861 INFO  weaver.general.InitServer  - InitEmailUpgradeThread End.....
2025-06-12 10:40:55,861 INFO  weaver.general.InitServer  - VotingInitUpgradeThread Start.....
2025-06-12 10:40:55,862 INFO  weaver.general.InitServer  - VotingInitUpgradeThread End.....
2025-06-12 10:40:55,862 INFO  weaver.general.InitServer  - InitFnaUpgradeThread Start.....
2025-06-12 10:40:55,863 INFO  weaver.general.InitServer  - InitFnaUpgradeThread End.....
2025-06-12 10:40:55,897 ERROR weaver.hrm.cachecenter.bean.KVResourceComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVResourceComInfo
2025-06-12 10:40:56,008 INFO  weaver.general.InitServer  - end.....
2025-06-12 10:40:56,086 ERROR weaver.general.BaseBean  - System Init Message:mainControlIp= localIp:[*************, *************, *************]
2025-06-12 10:40:56,086 ERROR weaver.general.BaseBean  - 超时判断是否为集群环境：redis_flag======false
2025-06-12 10:40:56,086 ERROR weaver.general.BaseBean  - 超时判断是否为集群环境：mainIp_flag=======false
2025-06-12 10:40:56,112 ERROR weaver.hrm.cachecenter.bean.KVRoleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.KVRoleComInfo
2025-06-12 10:40:56,287 ERROR weaver.general.BaseBean  - ========isSampleMode=========:true
2025-06-12 10:40:56,287 ERROR weaver.general.BaseBean  - ========isMainIp=============:true
2025-06-12 10:40:56,297 ERROR weaver.general.BaseBean  - 启动超时任务！
2025-06-12 10:40:56,339 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(SERVICE-METHOD) dynamic proxy...
2025-06-12 10:40:56,340 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target：com.engine.workflow.service.impl.WorkflowTypeServiceImpl.doSaveOperation(java.util.Map,weaver.hrm.User)
>> proxy：->com.engine.plugin.workflow.service.CustomWorkflowTypeService->com.engine.plugin.workflow.service.CustomWorkflowTypeService2->com.engine.plugin.workflow.service.CustomWorkflowTypeService3->com.engine.plugin.workflow.service.CustomWorkflowTypeService4
2025-06-12 10:40:56,340 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - Loading business class(COMMAND) dynamic proxy...
2025-06-12 10:40:56,340 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - 
>> target：com.engine.workflow.cmd.requestLog.LoadRequestLogBaseInfoCmd
>> proxy：->com.engine.plugin.workflow.command.CustomLoadRequestLogBaseInfoCmd
>> target：com.api.common.cmd.login.DoUserSessionCmd
>> proxy：->com.engine.plugin.hrm.command.MyDoUserSessionCmd
>> target：com.engine.workflow.cmd.workflowType.DoSaveCmd
>> proxy：->com.engine.plugin.workflow.command.CustomDoSave2Cmd->com.engine.plugin.workflow.command.CustomDoSave3Cmd->com.engine.plugin.workflow.command.CustomDoSaveCmd
>> target：com.engine.hrm.cmd.test.GetSearchConditionCmd
>> proxy：->com.engine.plugin.hrm.command.GetMySearchConditionCmd
2025-06-12 10:40:56,340 INFO  com.engine.core.cfg.DynamicProxyConfiguration  - The business class dynamic proxy is loaded.
2025-06-12 10:40:56,346 ERROR com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd  - 查询operator的sql：SELECT * FROM workflow_currentoperator t JOIN (SELECT DISTINCT wc.requestid,wc.nodeid FROM workflow_currentoperator wc INNER JOIN (SELECT t1.requestid,t1.nodeid FROM workflow_nodeOverTime t1 INNER JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE (t2.isvalid = '1' OR t2.isvalid = '3') AND (t2.istemplate IS NULL  OR t2.istemplate <> '1') AND (t1.nodepasshour > 0 OR t1.nodepassminute > 0 OR t1.nodepasstime is not null OR (t1.dateField IS NOT NULL AND t1.dateField != ' ' ))) tmp ON wc.nodeid = tmp.nodeid AND ((wc.REQUESTID = tmp.requestid  OR tmp.requestid IS NULL )) ) t1 ON t.requestid = t1.requestid AND t.isremark = 0 AND t.nodeid = t1.nodeid  order by t.requestid,t.nodeid desc,t.id
2025-06-12 10:40:56,370 ERROR weaver.hrm.cachecenter.bean.SuperDepartmentComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperDepartmentComInfo
2025-06-12 10:40:56,518 ERROR weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.SuperSubCompanyComInfo
2025-06-12 10:40:56,552 ERROR weaver.page.PageManager  - 清空二维码扫描缓存记录表
2025-06-12 10:40:56,661 ERROR weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo
2025-06-12 10:40:57,015 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running start...
2025-06-12 10:40:57,016 ERROR weaver.cpt.util.CptInitManager  - weaver.cpt.util.CptInitManager is running end...
2025-06-12 10:40:58,877 ERROR weaver.general.BaseBean  - 长时间未登录锁定账号start
2025-06-12 10:40:59,058 ERROR weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo  - init cache over>>class weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo
2025-06-12 10:41:00,021 ERROR weaver.conn.RecordSet  - 公文流程文档对应关系表已经初始化，不需要再次初始化。
2025-06-12 10:41:00,843 ERROR org.directwebremoting.util.LogErrorHandler  - Line=52 Element type "filter" must be declared.
2025-06-12 10:41:00,843 ERROR org.directwebremoting.util.LogErrorHandler  - Line=53 The content of element type "allow" must match "(create|convert)*".
2025-06-12 10:41:00,857 ERROR weaver.general.BaseBean  - ssoConfigCheck basePath = D:\ecology\ecology\ecology\
2025-06-12 10:41:00,858 ERROR weaver.general.BaseBean  - ssoConfigCheck end 
2025-06-12 10:41:01,579 ERROR weaver.system.MonitorThreadManager  - >>>>MonitorThreadManager>>单独的预警关闭控制：isThreadWarningStart(zt)=0<<<<
2025-06-12 10:41:34,135 ERROR weaver.general.BaseBean  - 长时间未登录锁定账号sql：UPDATE HrmResource SET passwordlock=1,passwordLockReason='D'  where 1=1  and id in (select temp1.userId from (SELECT userId FROM UserLastLoginDate WHERE lastLoginDate<'2024-12-14' union all select id userId from HrmResource where id not in (select distinct userId from UserLastLoginDate) and (((lastLoginDate='' or lastLoginDate is null) and createDate<'2024-12-14') or  lastLoginDate<'2024-12-14')) temp1) and id not in (select temp2.id from ( select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.id and t1.objectType=1  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.subcompanyId1 and t1.objectType=2 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.departmentId and t1.objectType=3 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.id=t3.resourceId and t1.objectType=4 and t3.resourceType=1 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.subcompanyId1=t3.resourceId and t1.objectType=4 and t3.resourceType=2 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.departmentId=t3.resourceId and t1.objectType=4 and t3.resourceType=3 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2,HrmRoleMembers t3 where t1.objectId=t3.roleId and t2.jobtitle=t3.resourceId and t1.objectType=4 and t3.resourceType=5 and t1.roleLevel>=t3.roleLevel and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo and (t3.jobtitlelevel=1 or (t3.jobtitlelevel=2 and t2.subcompanyid1 in (t3.subdepid)) or (t3.jobtitlelevel=3 and t2.departmentid in (t3.subdepid)))  union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectId=t2.jobtitle and t1.objectType=7  and (t1.jobtitleLevel=0 or (t1.jobtitleLevel=1 and t2.subcompanyId1 in (t1.subcomIdOfJob)) or (t1.jobtitleLevel=2 and t2.departmentId in (t1.deptIdOfJob))) union all  select t2.id from HrmWhiteListOfSecSetting t1,HrmResource t2 where t1.objectType=5 and t2.seclevel>=t1.seclevelFrom and t2.seclevel<=t1.seclevelTo ) temp2) 
2025-06-12 10:41:34,136 ERROR weaver.general.BaseBean  - 长时间未登录锁定账号是否执行成功：true
2025-06-12 10:41:34,136 ERROR weaver.general.BaseBean  - 长时间未登录锁定账号end
2025-06-12 10:41:34,508 ERROR weaver.general.BaseBean  - 白名单内的人员自动解锁是否成功：true
2025-06-12 10:42:33,734 ERROR weaver.general.BaseBean  - whiteUrls>>>[/weaver/weaver.common.util.taglib.splitpagexmlservlet, /weaver/weaver.common.util.taglib.splitpagexmlservletnew, /workflow/request/workflowviewsignmore.jsp, /wui/theme/ecology8/page/main.jsp, /wui/theme/ecology7/page/main.jsp, /middlecenter/index.jsp, /docs/docs/docdsp.jsp, /docs/docs/docdsphtmlshow.jsp, /docs/docs/docdspext.jsp, /docs/docs/doceditext.jsp, /docs/docdetaillogtab.jsp, /docs/docdetaillognoreadnew.jsp, /docs/docdetaillogallreadnew.jsp, /docs/docdetaillogprintnew.jsp, /docs/docdetaillogdownloadnew.jsp, /docs/docdetaillogreadednew.jsp, /docs/docs/docdspbaseinfo.jsp, /docs/docs/docdspextbaseinfo.jsp, /docs/search/doccommoncontent.jsp, /docs/search/ext/docsearchviewcolumnext.jsp, /workflow/report/reportresultdata.jsp, /workflow/request/wfagentlist.jsp, /workflow/search/wfsearchresult.jsp, /workflow/search/wfsearchshow.jsp, /workflow/request/requestbrowser.jsp, /workflow/request/multirequestbrowser.jsp, /workflow/request/multirequestbrowserajax.jsp, /workflow/request/multirequestedbrowser.jsp, /workflow/search/wfsuperviselist.jsp, /workflow/request/workflowrequestpictureframe.jsp, /api/workflow/agent/getlist, /api/workflow/agent/getsearchcondition, /api/workflow/customquery/getquerytree, /api/workflow/customquery/getfixedcondition, /api/workflow/customquery/getqueryresultkey, /api/public/browser/data/, /api/public/browser/destdata/, /api/public/browser/complete/, /api/public/browser/condition/, /api/workflow/reqform/wfstatusnew, /api/workflow/reqform/wfstatuscount, /api/ec/dev/table/datas, /api/ec/dev/table/counts, /api/ec/dev/table/checks, /api/crm/customer/contactlogs]
2025-06-12 10:42:42,221 ERROR weaver.general.BaseBean  - qrcode_config>>>
2025-06-12 10:43:02,828 ERROR com.cloudstore.dev.api.util.HttpManager  - 请求地址:http://192.168.1.226/emp/api/gettoken,请求参数：{corpid=em1eeb058634f211e9be4dfa163efdc61a, corpsecret=dee473bf-2ea7-43a8-bfb2-a03bd5262e01},响应数据：null
2025-06-12 10:44:37,804 ERROR weaver.conn.RecordSet  -  update ECOLOGY_BIZ_MOBILE_CONFIG set NAME='~`~`7 生日提醒`~`8 Birthday Reminder`~`9 生日提醒`~`~' where NAME='生日提醒' and id=23
2025-06-12 10:44:37,804 ERROR weaver.conn.RecordSet  - weaver.conn.RecordSet
com.microsoft.sqlserver.jdbc.SQLServerException: 在将 varchar 值 'MboPOakY' 转换成数据类型 int 时失败。
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:196)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1454)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:388)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:338)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:185)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:160)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:306)
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.logicalcobwebs.proxool.ProxyStatement.invoke(ProxyStatement.java:100)
	at org.logicalcobwebs.proxool.ProxyStatement.intercept(ProxyStatement.java:57)
	at $java.sql.Statement$$EnhancerByProxool$$3ad3ee74.executeUpdate(<generated>)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1126)
	at weaver.conn.RecordSet.executeUpdate(RecordSet.java:810)
	at weaver.general.CreateJSLanguage.executeMultiLangScripts(CreateJSLanguage.java:334)
	at weaver.general.CreateJSLanguage.createJSLanguage(CreateJSLanguage.java:24)
	at weaver.system.SystemUpgrade.procSql(SystemUpgrade.java:910)
	at weaver.system.SystemUpgrade.run(SystemUpgrade.java:335)
	at java.lang.Thread.run(Thread.java:748)
2025-06-12 10:45:18,654 ERROR weaver.conn.RecordSet  - 生成前端JS使用的label信息成功...
2025-06-12 10:45:18,799 ERROR weaver.general.BaseBean  - 邮件模块 升级，执行 InitEmailUpgradeThread 开始 ...
2025-06-12 10:45:18,799 ERROR weaver.general.BaseBean  - 财务预算模块 升级，执行 InitEmailUpgradeThread 开始 ...
2025-06-12 10:45:18,799 ERROR weaver.general.BaseBean  - 调查模块 升级，执行 VotingInitUpgradeThread 开始 ...
2025-06-12 10:45:18,807 ERROR weaver.general.BaseBean  - 读取多维度预算表修改信息开始...
2025-06-12 10:45:18,807 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=054c2bd7-a277-460b-9e2e-4b205e8b6347,开始邮件内部收件人升级
2025-06-12 10:45:18,807 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=054c2bd7-a277-460b-9e2e-4b205e8b6347,-> ########## 执行计时开始 ##########
2025-06-12 10:45:18,821 ERROR weaver.hrm.attendance.task.HrmPaidLeaveTask  - manager count != 0
2025-06-12 10:45:18,961 ERROR weaver.general.BaseBean  - 读取多维度预算表修改信息结束...
2025-06-12 10:45:18,963 ERROR weaver.general.BaseBean  - 财务预算模块 升级，执行 InitEmailUpgradeThread 结束 ...
2025-06-12 10:45:18,987 ERROR weaver.general.BaseBean  - 调查模块 升级，执行 VotingInitUpgradeThread 结束 ...
2025-06-12 10:45:19,286 ERROR weaver.general.BaseBean  - EmailInternalToUpgrade loguuid=054c2bd7-a277-460b-9e2e-4b205e8b6347,升级已完成，不需要再次升级。needExecuteUpgrade=false
2025-06-12 10:45:19,286 ERROR weaver.general.BaseBean  - 邮件模块 升级，执行 InitEmailUpgradeThread 结束 ...
2025-06-12 10:46:28,989 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1368)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1355)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:1532)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:3274)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4437)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4389)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection$1ConnectionCommand.doExecute(SQLServerConnection.java:1457)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectionCommand(SQLServerConnection.java:1462)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.setTransactionIsolation(SQLServerConnection.java:1792)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.logicalcobwebs.proxool.WrappedConnection.invoke(WrappedConnection.java:162)
	at org.logicalcobwebs.proxool.WrappedConnection.intercept(WrappedConnection.java:87)
	at $java.sql.Connection$$EnhancerByProxool$$c442aaaa.setTransactionIsolation(<generated>)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:316)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1435)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:795)
	at weaver.hrm.schedule.dao.HrmScheduleSetDetailDao.find(HrmScheduleSetDetailDao.java:178)
	at weaver.framework.BaseManager.find(BaseManager.java:123)
	at weaver.framework.BaseManager.find(BaseManager.java:128)
	at weaver.hrm.schedule.manager.HrmScheduleSetManager.getScheduleDetail(HrmScheduleSetManager.java:499)
	at weaver.hrm.schedule.manager.HrmScheduleSetManager.getScheduleDetail(HrmScheduleSetManager.java:466)
	at weaver.hrm.schedule.manager.HrmScheduleManager.initUser(HrmScheduleManager.java:266)
	at weaver.hrm.schedule.manager.HrmScheduleManager.initUser(HrmScheduleManager.java:233)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:187)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:168)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:152)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:144)
	at weaver.workflow.request.OverTimeSetBean.getOverWorkTime(OverTimeSetBean.java:455)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:237)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:748)
2025-06-12 10:46:28,989 ERROR weaver.conn.RecordSet  - select * from uf_cghtspbd where 1=1 and zt=0
2025-06-12 10:46:28,991 ERROR weaver.conn.RecordSet  - weaver.conn.RecordSet
com.microsoft.sqlserver.jdbc.SQLServerException: socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1368)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1355)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:1532)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:3317)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:3227)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:3203)
	at com.microsoft.sqlserver.jdbc.TDSReader.skip(IOBuffer.java:3828)
	at com.microsoft.sqlserver.jdbc.ServerDTVImpl.getValuePrep(dtv.java:1906)
	at com.microsoft.sqlserver.jdbc.ServerDTVImpl.getValue(dtv.java:1941)
	at com.microsoft.sqlserver.jdbc.DTV.getValue(dtv.java:175)
	at com.microsoft.sqlserver.jdbc.Column.getValue(Column.java:113)
	at com.microsoft.sqlserver.jdbc.SQLServerResultSet.getValue(SQLServerResultSet.java:1982)
	at com.microsoft.sqlserver.jdbc.SQLServerResultSet.getValue(SQLServerResultSet.java:1967)
	at com.microsoft.sqlserver.jdbc.SQLServerResultSet.getObject(SQLServerResultSet.java:2256)
	at weaver.conn.RecordSet.parseResultSet(RecordSet.java:2195)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1122)
	at weaver.conn.RecordSet.executeQueryWithDatasource(RecordSet.java:821)
	at weaver.formmode.task.action.TaskAllJob.doIt(TaskAllJob.java:135)
	at weaver.formmode.task.action.TaskAllJob.execute(TaskAllJob.java:229)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-06-12 10:46:28,994 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1368)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1355)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:1532)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:3274)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4437)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4389)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection$1ConnectionCommand.doExecute(SQLServerConnection.java:1457)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectionCommand(SQLServerConnection.java:1462)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.setTransactionIsolation(SQLServerConnection.java:1792)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.logicalcobwebs.proxool.WrappedConnection.invoke(WrappedConnection.java:162)
	at org.logicalcobwebs.proxool.WrappedConnection.intercept(WrappedConnection.java:87)
	at $java.sql.Connection$$EnhancerByProxool$$c442aaaa.setTransactionIsolation(<generated>)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:316)
	at weaver.conn.DBConnectionPool.getNewConnection(DBConnectionPool.java:355)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:237)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:1435)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:795)
	at weaver.hrm.schedule.dao.HrmScheduleSetDetailDao.find(HrmScheduleSetDetailDao.java:178)
	at weaver.framework.BaseManager.find(BaseManager.java:123)
	at weaver.framework.BaseManager.find(BaseManager.java:128)
	at weaver.hrm.schedule.manager.HrmScheduleSetManager.getScheduleDetail(HrmScheduleSetManager.java:499)
	at weaver.hrm.schedule.manager.HrmScheduleSetManager.getScheduleDetail(HrmScheduleSetManager.java:466)
	at weaver.hrm.schedule.manager.HrmScheduleManager.initUser(HrmScheduleManager.java:266)
	at weaver.hrm.schedule.manager.HrmScheduleManager.initUser(HrmScheduleManager.java:233)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:187)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:168)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:152)
	at weaver.hrm.schedule.manager.HrmScheduleManager.<init>(HrmScheduleManager.java:144)
	at weaver.workflow.request.OverTimeSetBean.getOverWorkTime(OverTimeSetBean.java:455)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:237)
	at com.engine.workflow.cmd.workflowOvertime.GetOvertimeListCmd.execute(GetOvertimeListCmd.java:24)
	at com.engine.core.interceptor.CommandInvoker.execute(CommandInvoker.java:38)
	at com.engine.core.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:44)
	at com.engine.core.interceptor.LogInterceptor.execute(LogInterceptor.java:19)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:37)
	at com.engine.core.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:42)
	at com.engine.workflow.service.impl.WorkflowOvertimeServiceImpl.getOvertimeList(WorkflowOvertimeServiceImpl.java:17)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.addAllOvertimeTask(OvertimeBiz.java:257)
	at com.engine.workflow.biz.workflowOvertime.OvertimeBiz.run(OvertimeBiz.java:96)
	at weaver.system.SystemThreadManager$1.run(SystemThreadManager.java:265)
	at java.lang.Thread.run(Thread.java:748)
2025-06-12 10:46:28,994 ERROR weaver.conn.DBConnectionPool  - weaver.conn.DBConnectionPool
com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1368)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:1355)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:1532)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:3274)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4437)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:4389)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection$1ConnectionCommand.doExecute(SQLServerConnection.java:1457)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:4026)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:1416)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectionCommand(SQLServerConnection.java:1462)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.setTransactionIsolation(SQLServerConnection.java:1792)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.logicalcobwebs.proxool.WrappedConnection.invoke(WrappedConnection.java:162)
	at org.logicalcobwebs.proxool.WrappedConnection.intercept(WrappedConnection.java:87)
	at $java.sql.Connection$$EnhancerByProxool$$c442aaaa.setTransactionIsolation(<generated>)
	at weaver.conn.DBConnectionPool.getConnection(DBConnectionPool.java:316)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:236)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:209)
	at weaver.conn.ConnectionPool.getConnection(ConnectionPool.java:193)
	at weaver.conn.RecordSet.getConnection(RecordSet.java:2270)
	at weaver.conn.RecordSet.executeSql(RecordSet.java:920)
	at weaver.conn.RecordSet.executeQuery(RecordSet.java:806)
	at weaver.interfaces.cache.impl.IntegrationCache4DataSource.getObjectFromDB(IntegrationCache4DataSource.java:163)
	at weaver.interfaces.cache.impl.CacheHook.getCacheByKey(CacheHook.java:75)
	at weaver.interfaces.cache.impl.IntegrationCache4DataSource.getCacheByKey(IntegrationCache4DataSource.java:155)
	at weaver.general.InitFormmodeBrowser.getDataSource(InitFormmodeBrowser.java:466)
	at weaver.general.InitFormmodeBrowser.initCache(InitFormmodeBrowser.java:381)
	at weaver.formmode.browser.ResetFormmodeBrowserCache.initCache(ResetFormmodeBrowserCache.java:24)
	at weaver.formmode.browser.ResetFormmodeBrowserCache.initCache(ResetFormmodeBrowserCache.java:144)
	at weaver.formmode.excel.ModeCacheManager.loadBrowserCacheBaseInfo(ModeCacheManager.java:755)
	at weaver.formmode.excel.ModeCacheManager.setBrowserSetMap(ModeCacheManager.java:681)
	at weaver.formmode.excel.ModeCacheManager.loadBrowserBase(ModeCacheManager.java:109)
	at weaver.formmode.excel.ModeCacheManager.run(ModeCacheManager.java:75)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
