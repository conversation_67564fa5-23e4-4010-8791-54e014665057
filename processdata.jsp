<%@ page language="java" contentType="text/html; charset=GBK" %>
<%@ page import="weaver.conn.*" %>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<HTML>
<HEAD>
</head>
<body>
<%
		String sql="select indexid,labelname from htmllabelinfo where indexid<0 and labelname like '%~`~%' and languageId=7";
		String sql_del="delete from htmllabelinfo where indexid=?";
		//indexid,name,languageId
		String sql_insert="insert into htmllabelinfo values(?,?,?)";
		String baksql="";
		RecordSetTrans rst = new RecordSetTrans();
		rst.setAutoCommit(false);			
		try{
			List<List<Object>> params = new ArrayList<List<Object>>();
			List<List<Object>> delparams = new ArrayList<List<Object>>();
			if(rst.getDBType().indexOf("oracle")!=-1){
				baksql = "create table htmllabelinfo_databk22 as select * from htmllabelinfo ";
			}else if(rst.getDBType().indexOf("sqlserver")!=-1){
				baksql = "select * into htmllabelinfo_databk22 from htmllabelinfo ";
			}else{
				baksql = "create table htmllabelinfo_databk22 select * from htmllabelinfo";
			}
			RecordSet rs = new RecordSet();
			rs.execute(baksql);
			rst.execute(sql);
			while(rst.next()){
				String indexid = rst.getString("indexid");
				String labelname = rst.getString("labelname");
				String languageid = rst.getString("languageid");
				String name7=Util.formatMultiLang(labelname,"7");
				String name8=Util.formatMultiLang(labelname,"8");
				String name9=Util.formatMultiLang(labelname,"9");
				ArrayList<Object> tmplistdel = new ArrayList<Object>();
				tmplistdel.add(indexid);
				delparams.add(tmplistdel);
				ArrayList<Object> tmplist7 = new ArrayList<Object>();
				tmplist7.add(indexid);
				tmplist7.add(name7);
				tmplist7.add("7");
				ArrayList<Object> tmplist8 = new ArrayList<Object>();
				tmplist8.add(indexid);
				tmplist8.add(name8);
				tmplist8.add("8");
				ArrayList<Object> tmplist9 = new ArrayList<Object>();
				tmplist9.add(indexid);
				tmplist9.add(name9);
				tmplist9.add("9");
				params.add(tmplist7);
				params.add(tmplist8);
				params.add(tmplist9);
			}
			List<List<Object>> tmpparams = new ArrayList<List<Object>>();
			for(int i=0;i<delparams.size();i++){
				tmpparams.add(delparams.get(i));
				if((i+1)%5000==0){
					rst.executeBatchSql(sql_del, tmpparams);
					tmpparams.clear();
				}
			}
			if(delparams.size()%5000!=0){
				rst.executeBatchSql(sql_del, tmpparams);
				tmpparams.clear();
			}
			for(int i=0;i<params.size();i++){
				tmpparams.add(params.get(i));
				if((i+1)%5000==0){
					rst.executeBatchSql(sql_insert, tmpparams);
					tmpparams.clear();
				}
			}
			if(params.size()%5000!=0){
				rst.executeBatchSql(sql_insert, tmpparams);
				tmpparams.clear();
			}
			rst.commit();
			rst.writeLog("***htmllabelinfo_sql:"+baksql);
		} catch (Exception e) {
			rst.rollback();
			rst.writeLog(e);
		}

%>
</BODY>
</HTML>