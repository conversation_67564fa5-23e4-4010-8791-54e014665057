<%@ page import="weaver.general.Util,weaver.hrm.User,weaver.rtx.RTXConfig" %>

<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ include file="/systeminfo/init_wev8.jsp" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%
	// 由于user.getLoginid() 和数据库中longid不一致，而ocs是区分大小写的。所以采取该处理办法，仅考虑sqlserver数据库ocs单点登录情况
	String rtxlogin =Util.null2String((String)request.getSession(true).getAttribute("rtxlogin"));
	//rtx反向登陆到oa就不需要单点登录了
	if("1".equals(rtxlogin)){
		return;
	}
	RTXConfig rtxConfig = new RTXConfig();
	String login = "";
	if(!user.getLoginid().equals("sysadmin")){
		login = rtxConfig.getRtxLoginFiled(user.getUID());
	}
	String isDownload = Util.null2String(rtxConfig.getPorp(RTXConfig.RTX_ISDownload));
%>
<HTML>
<HEAD>
<TITLE> New Document </TITLE>
<script type="text/javascript" src="/js/EimUtil_wev8.js"></script>
<script type="text/javascript">
	function on_load(){
		<%if(rtxConfig.isSystemUser(user.getLoginid())){%>
			//top.Dialog.alert('<%=SystemEnv.getHtmlLabelName(27462,user.getLanguage())%>');
			return;
		<%}%>
		if(EimUtil.isInstall()){
			EimUtil.engine.Runeim('<%=login%>', '<%=(String)session.getAttribute("password") %>');
		}else{
			<%if(isDownload.equals("1")){%>
				top.Dialog.confirm("<%=SystemEnv.getHtmlLabelName(27461,user.getLanguage())%>", function (){
			       window.open('/weaverplugin/PluginMaintenance.jsp',"","height=800,width=600,scrollbars,resizable=yes,status=yes,Minimize=yes,Maximize=yes");
			    }, function () {}, 320, 90);
			<%}%>
		}
	}
</script>
</HEAD>

<BODY onLoad="on_load()">
</BODY>
</HTML>
