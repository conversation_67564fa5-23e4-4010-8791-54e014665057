<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>


<%@page import="weaver.general.*"%>
<%@page import="weaver.conn.ExternalDataSourceManager"%>
<%@page import="java.util.*"%>
<%@page import="weaver.cluster.SyncDataSource"%>

<%
try{
	    String operation = SecurityHelper.decrypt(SecurityHelper.KEY,Util.null2String(request.getParameter("operation")));
		String params = SecurityHelper.decrypt(SecurityHelper.KEY,Util.null2String(request.getParameter("params")));
	    if(operation.equals("destoryDataSource")){
				     String poolName = params;
                     ExternalDataSourceManager.destoryDataSource(poolName);
		}else if(operation.equals("createPool")){
			         String[] paramsStr = params.split(ExternalDataSourceManager.splitFlag);
                     if(paramsStr.length!=10){
						 return;
					 }
					 String type = paramsStr[0];
					 String poolName = paramsStr[1]; 
					 String	driverClasses = paramsStr[2];
					 String turl = paramsStr[3];
					 String user = paramsStr[4];
					 String password = paramsStr[5];
					 String provider = paramsStr[6];
					 String maxconn = paramsStr[7];
					 String minconn = paramsStr[8];
					 String usepool = paramsStr[9];
					 ExternalDataSourceManager.createPool(type,poolName,driverClasses,turl,user,password,provider,
						 Util.getIntValue(maxconn),Util.getIntValue(minconn),Util.getIntValue(usepool));
		}else if(operation.equals("syncMap")){
			         String[] paramsStr = params.split(ExternalDataSourceManager.splitFlag);
                     			if(paramsStr.length!=2){
						 return;
					 }
					 String poolName = paramsStr[0]; 
					 String newdatasourcename = paramsStr[1]; 				
					 SyncDataSource.syncDatasourceMapAdd(poolName,newdatasourcename);
		}				
	} catch(Exception e){
		new BaseBean().writeLog(e);
	}finally {
	    try{
	        session.invalidate();
	    } catch(Throwable t){
	    }
	}   
%>